package com.stpl.tech.kettle.controller;

import com.stpl.tech.kettle.domain.model.*;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.cache.UnitSessionCache;
import com.stpl.tech.kettle.converter.OrderConverter;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.domain.ApiResponse;
import com.stpl.tech.kettle.service.LoyaltyService;
import com.stpl.tech.kettle.service.OrderManagementService;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.kettle.exceptions.*;
import com.stpl.tech.util.EnvType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import javax.jms.JMSException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OrderManagementResourceTest {

    @Mock
    private OrderManagementService orderManagementService;

    @Mock
    private UnitSessionCache unitSessionCache;

    @Mock
    private UnitCacheService unitCacheService;

    @Mock
    private EnvironmentProperties properties;

    @Mock
    private OrderConverter orderConverter;

    @Mock
    private LoyaltyService loyaltyService;

    @InjectMocks
    private OrderManagementResource orderManagementResource;

    private OrderDomain orderDomain;
    private OrderInfo orderInfo;
    private EnvType environmentType;

    @BeforeEach
    void setUp() {
        // Initialize test data
        orderDomain = new OrderDomain();
        Order order = new Order();
        order.setGenerateOrderId("TEST-ORDER-123");
        orderDomain.setOrder(order);

        orderInfo = new OrderInfo();
        orderInfo.setOrder(order);
        orderInfo.setOrderNotificationMap(new HashMap<>());

        environmentType = EnvType.DEV;
    }

    @Test
    void createOrder_Success() throws Exception {
        // Arrange
        when(orderManagementService.processOrder(any(OrderDomain.class))).thenReturn(orderInfo);
        when(properties.getEnvironmentType()).thenReturn(environmentType);
        doNothing().when(orderManagementService).publishToOrderInfoCacheSQS(anyString(), any(OrderInfo.class));
        when(TransactionUtils.isTableOrder(any(Order.class))).thenReturn(false);
        doNothing().when(orderManagementService).pushDataToThirdPartyAnalytics(any(OrderInfo.class), any(Map.class));

        // Act
        ResponseEntity<ApiResponse> response = orderManagementResource.createOrder(orderDomain);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(orderInfo, response.getBody().getData());
        
        verify(orderManagementService).processOrder(orderDomain);
        verify(orderManagementService).publishToOrderInfoCacheSQS(environmentType.name(), orderInfo);
        verify(orderManagementService).pushDataToThirdPartyAnalytics(orderInfo, orderInfo.getOrderNotificationMap());
    }

    @Test
    void createOrder_TableOrder_Success() throws Exception {
        // Arrange
        when(orderManagementService.processOrder(any(OrderDomain.class))).thenReturn(orderInfo);
        when(properties.getEnvironmentType()).thenReturn(environmentType);
        doNothing().when(orderManagementService).publishToOrderInfoCacheSQS(anyString(), any(OrderInfo.class));
        when(TransactionUtils.isTableOrder(any(Order.class))).thenReturn(true);

        // Act
        ResponseEntity<ApiResponse> response = orderManagementResource.createOrder(orderDomain);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(orderInfo, response.getBody().getData());
        
        verify(orderManagementService).processOrder(orderDomain);
        verify(orderManagementService).publishToOrderInfoCacheSQS(environmentType.name(), orderInfo);
        verify(orderManagementService, never()).pushDataToThirdPartyAnalytics(any(), any());
    }

    @Test
    void createOrder_AuthenticationFailure() throws Exception {
        // Arrange
        when(orderManagementService.processOrder(any(OrderDomain.class)))
            .thenThrow(new AuthenticationFailureException("Authentication failed"));

        // Act & Assert
        assertThrows(AuthenticationFailureException.class, () -> 
            orderManagementResource.createOrder(orderDomain)
        );
    }

    @Test
    void createOrder_DataUpdationException() throws Exception {
        // Arrange
        when(orderManagementService.processOrder(any(OrderDomain.class)))
            .thenThrow(new DataUpdationException("Data update failed"));

        // Act & Assert
        assertThrows(DataUpdationException.class, () -> 
            orderManagementResource.createOrder(orderDomain)
        );
    }

    @Test
    void createOrder_TemplateRenderingException() throws Exception {
        // Arrange
        when(orderManagementService.processOrder(any(OrderDomain.class)))
            .thenThrow(new TemplateRenderingException("Template rendering failed"));

        // Act & Assert
        assertThrows(TemplateRenderingException.class, () -> 
            orderManagementResource.createOrder(orderDomain)
        );
    }

    @Test
    void createOrder_DataNotFoundException() throws Exception {
        // Arrange
        when(orderManagementService.processOrder(any(OrderDomain.class)))
            .thenThrow(new DataNotFoundException("Data not found"));

        // Act & Assert
        assertThrows(DataNotFoundException.class, () -> 
            orderManagementResource.createOrder(orderDomain)
        );
    }

    @Test
    void createOrder_CardValidationException() throws Exception {
        // Arrange
        when(orderManagementService.processOrder(any(OrderDomain.class)))
            .thenThrow(new CardValidationException("Card validation failed"));

        // Act & Assert
        assertThrows(CardValidationException.class, () -> 
            orderManagementResource.createOrder(orderDomain)
        );
    }

    @Test
    void createOrder_WebServiceCallException() throws Exception {
        // Arrange
        when(orderManagementService.processOrder(any(OrderDomain.class)))
            .thenThrow(new WebServiceCallException("Web service call failed"));

        // Act & Assert
        assertThrows(WebServiceCallException.class, () -> 
            orderManagementResource.createOrder(orderDomain)
        );
    }

    @Test
    void createOrder_JMSException() throws Exception {
        // Arrange
        when(orderManagementService.processOrder(any(OrderDomain.class)))
            .thenThrow(new JMSException("JMS error"));

        // Act & Assert
        assertThrows(JMSException.class, () -> 
            orderManagementResource.createOrder(orderDomain)
        );
    }

    @Test
    void createOrder_OfferValidationException() throws Exception {
        // Arrange
        when(orderManagementService.processOrder(any(OrderDomain.class)))
            .thenThrow(new OfferValidationException("Offer validation failed"));

        // Act & Assert
        assertThrows(OfferValidationException.class, () ->
            orderManagementResource.createOrder(orderDomain)
        );
    }

    @Test
    void createOrder_WithRuleNumber_Success() throws Exception {
        // Arrange
        OrderItem orderItem = new OrderItem();
        orderItem.setProductId(123);
        orderItem.setProductName("Test Product");
        orderItem.setQuantity(2);
        orderItem.setPrice(BigDecimal.valueOf(100.00));
        orderItem.setRuleNumber(456); // Set rule number

        List<OrderItem> orderItems = new ArrayList<>();
        orderItems.add(orderItem);

        Order order = new Order();
        order.setGenerateOrderId("TEST-ORDER-WITH-RULE");
        order.setOrders(orderItems);

        OrderDomain orderDomainWithRule = new OrderDomain();
        orderDomainWithRule.setOrder(order);

        OrderInfo orderInfoWithRule = new OrderInfo();
        orderInfoWithRule.setOrder(order);
        orderInfoWithRule.setOrderNotificationMap(new HashMap<>());

        when(orderManagementService.processOrder(any(OrderDomain.class))).thenReturn(orderInfoWithRule);
        when(properties.getEnvironmentType()).thenReturn(environmentType);
        doNothing().when(orderManagementService).publishToOrderInfoCacheSQS(anyString(), any(OrderInfo.class));
        when(TransactionUtils.isTableOrder(any(Order.class))).thenReturn(false);
        doNothing().when(orderManagementService).pushDataToThirdPartyAnalytics(any(OrderInfo.class), any(Map.class));

        // Act
        ResponseEntity<ApiResponse> response = orderManagementResource.createOrder(orderDomainWithRule);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(orderInfoWithRule, response.getBody().getData());

        // Verify that the order item still has the rule number
        OrderInfo resultOrderInfo = (OrderInfo) response.getBody().getData();
        assertNotNull(resultOrderInfo.getOrder().getOrders());
        assertEquals(1, resultOrderInfo.getOrder().getOrders().size());
        assertEquals(Integer.valueOf(456), resultOrderInfo.getOrder().getOrders().get(0).getRuleNumber());

        verify(orderManagementService).processOrder(orderDomainWithRule);
        verify(orderManagementService).publishToOrderInfoCacheSQS(environmentType.name(), orderInfoWithRule);
        verify(orderManagementService).pushDataToThirdPartyAnalytics(orderInfoWithRule, orderInfoWithRule.getOrderNotificationMap());
    }
}