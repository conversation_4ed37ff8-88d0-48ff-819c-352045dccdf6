spring.application.name=transaction-service

server.port=8790
server.servlet.context-path=/${spring.application.name}
spring.mvc.servlet.path=/rest

environment.type=LOCAL

#spring.datasource.url=**********************************************************************
#spring.datasource.username=root
#spring.datasource.password=Chaayos123#@!
#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.hikari.username=root
#spring.datasource.hikari.password=Chaayos123#@!
#spring.datasource.hikari.jdbc-url=**********************************************************************
#spring.datasource.hikari.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.jpa.hibernate.ddl-auto=validate
#spring.jpa.show-sql=false
#spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
#spring.jpa.properties.hibernate.generate_statistics=true

#MASTER_CONFIG
master.jdbc.driver-class=com.mysql.cj.jdbc.Driver
master.jdbc.url=****************************************************************
master.jdbc.user-name=root
master.jdbc.password=321in#@!

#KETTLE_CONFIG
kettle.jdbc.driver-class=com.mysql.cj.jdbc.Driver
kettle.jdbc.url=*********************************************************
kettle.jdbc.user-name=root
kettle.jdbc.password=321in#@!

#HIBERNATE
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.show-sql=true
hibernate.hbm-to-ddl-auto=validate

springdoc.api-docs.enabled=true

env.type=local

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss

logging.file.path=transaction-service/logs

ignite.server.ip.address = 127.0.0.1:47500..47509

spring.security.user.name=root
spring.security.user.password=kettle123!@#


verify.price.data=http://localhost:9595/scm-service/rest/v1/stock-management/verify-price-data
add.wastage.url=http://localhost:9595/scm-service/rest/v1/stock-management/kettle-wastage-event


employee.meal.amount.limit=45
employee.meal.day.limit=26
employee.meal.monthly.start.date=25
cash.back.start.date=2021-03-01
cash.back.end.date=2021-12-31
cash.back.percentage=0
mail.dummy.customer.id = 5
server.base.dir=/data/app/kettle/dev
inventory.track=true

inventory.base.url=http://dev.kettle.chaayos.com:9699

interceptor.secure.url=/**
interceptor.open.url=/v2/order/**,/v2/customer/**,/v2/external/**,/actuator/**,/user/lookup,/user/signup,/user/login,/error/*,/error,/*/*.html,/*.html,/v3/api-docs,/v3/api-docs/*,/swagger-ui/*,/admin/login,/user/forgot-pin,/user/reset-pin 

chaayos.base.url=https://cafes.chaayos.com

run.validate.filter=true

raw.print.enabled=true

order.feedback.type=internal
subscription.valid.buy.n.days.value=0
send.automated.delivery.nps.sms=false
send.feedback.message.delivery.swiggy=false
dinein.post.order.offer.enabled=false

micro.wallet.id=2
direct.wallet.id=2

server.node.type = false
server.node.ip.details = 127.0.0.1

kettle.server.node.cluster.ip=127.0.0.1

order.info.queue.region=eu-west-1
offer.drools.s3.bucket=com.chaayos.drool.dev