<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<TransitionStateData>
	<states>
		<stateCode>INITIATED</stateCode>
		<stateName>Initiated</stateName>
		<stateType>START</stateType>
	</states>
	<states>
		<stateCode>CREATED</stateCode>
		<stateName>Created</stateName>
		<stateType>INTERMEDIATE</stateType>
	</states>
	<states>
		<stateCode>PROCESSING</stateCode>
		<stateName>Processing</stateName>
		<stateType>INTERMEDIATE</stateType>
	</states>
	<states>
		<stateCode>READY_TO_PARTIALLY_DISPATCH</stateCode>
		<stateName>Ready To Dispatch Partially</stateName>
		<stateType>TRANSIENT</stateType>
	</states>
	<states>
		<stateCode>READY_TO_DISPATCH</stateCode>
		<stateName>Ready To Dispatch</stateName>
		<stateType>INTERMEDIATE</stateType>
	</states>
	<states>
		<stateCode>SETTLED</stateCode>
		<stateName>Settled</stateName>
		<stateType>INTERMEDIATE</stateType>
	</states>
	<states>
		<stateCode>DELIVERED</stateCode>
		<stateName>Out For Delivery</stateName>
		<stateType>INTERMEDIATE</stateType>
	</states>
	<states>
		<stateCode>CANCELLED_REQUESTED</stateCode>
		<stateName>Cancelled Requested</stateName>
		<stateType>INTERMEDIATE</stateType>
	</states>
	<states>
		<stateCode>CANCELLED</stateCode>
		<stateName>Cancelled</stateName>
		<stateType>END</stateType>
	</states>
	<states>
		<stateCode>CLOSED</stateCode>
		<stateName>Closed</stateName>
		<stateType>END</stateType>
	</states>
	<transitions>
		<state>INITIATED</state>
		<nextStates>CREATED</nextStates>
		<nextStates>SETTLED</nextStates>
	</transitions>
	<transitions>
		<state>CREATED</state>
		<nextStates>PROCESSING</nextStates>
		<nextStates>CANCELLED_REQUESTED</nextStates>
	</transitions>
	<transitions>
		<state>PROCESSING</state>
		<nextStates>READY_TO_PARTIALLY_DISPATCH</nextStates>
		<nextStates>READY_TO_DISPATCH</nextStates>
		<nextStates>CANCELLED_REQUESTED</nextStates>
		<nextStates>SETTLED</nextStates>
	</transitions>
	<transitions>
		<state>CANCELLED_REQUESTED</state>
		<nextStates>CANCELLED</nextStates>
	</transitions>
	<transitions>
		<state>READY_TO_PARTIALLY_DISPATCH</state>
		<nextStates>READY_TO_DISPATCH</nextStates>
		<nextStates>CANCELLED_REQUESTED</nextStates>
	</transitions>
	<transitions>
		<state>READY_TO_DISPATCH</state>
		<nextStates>CANCELLED_REQUESTED</nextStates>
		<nextStates>SETTLED</nextStates>
	</transitions>
	<transitions>
		<state>SETTLED</state>
		<nextStates>DELIVERED</nextStates>
		<nextStates>CLOSED</nextStates>
		<nextStates>CANCELLED_REQUESTED</nextStates>
		<nextStates>CANCELLED</nextStates>
	</transitions>
</TransitionStateData>