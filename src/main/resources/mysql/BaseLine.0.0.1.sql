ALTER TABLE `KETTLE_DEV`.`ORDER_DETAIL` 
ADD COLUMN `REF_ORDER_ID` INT(11) NULL AFTER `COLLECTION_AMOUNT`;


ALTER TABLE `KETTLE_DEV`.`ORDER_DETAIL` 
ADD COLUMN `SOURCE_VERSION` VARCHAR(10) NULL AFTER `REF_ORDER_ID`;

CREATE TABLE `KETTLE_DEV.ORDER_METRIC_DETAIL_DATA` (
  `METRIC_ID` int(11) NOT NULL AUTO_INCREMENT,
  `ORDER_ID` int(11) NOT NULL,
  `BILLING_SERVER_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `POS_VERSION` varchar(10) DEFAULT NULL,
  `CAFE_APP_VERSION` varchar(10) DEFAULT NULL,
  `CUSTOMER_ID` int(11) NOT NULL,
  `NEW_CUSTOMER` varchar(1) DEFAULT NULL,
  `VALID_CUSTOMER` varchar(1) NOT NULL,
  `TAXABLE_AMOUNT` decimal(10,6) NOT NULL,
  `TOTAL_AMOUNT` decimal(10,6) NOT NULL,
  `ORDER_START_TIME` timestamp NULL DEFAULT NULL,
  `ORDER_END_TIME` timestamp NULL DEFAULT NULL,
  `LOGIN_START_TIME` timestamp NULL DEFAULT NULL,
  `LOGIN_END_TIME` timestamp NULL DEFAULT NULL,
  `FACE_IT_LOGIN` varchar(1) NOT NULL,
  `OTP_REQUESTED` varchar(1) NOT NULL,
  `CUSTOMER_ORDER_NUMBER` int(11) DEFAULT NULL,
  `RECOMMENDED_PRODUCT_ADDED` varchar(1) DEFAULT NULL,
  `CUSTOMIZATION_DONE` varchar(1) NOT NULL,
  `CUSTOMIZATION_REQUIRED` varchar(1) NOT NULL,
  `CUSTOMIZED_PRODUCT_ADDED` varchar(1) NOT NULL,
  `ADDED_VIA_CUSTOMIZATION` varchar(1) NOT NULL,
  `CUSTOMIZATION_START_TIME` timestamp NULL DEFAULT NULL,
  `CUSTOMIZATION_END_TIME` timestamp NULL DEFAULT NULL,
  `WALLET_SUGGESTED` varchar(1) NOT NULL,
  `WALLET_PURCHASED` varchar(1) NOT NULL,
  `WALLET_REDEEMED` varchar(1) NOT NULL,
  `WALLET_START_TIME` timestamp NULL DEFAULT NULL,
  `WALLET_END_TIME` timestamp NULL DEFAULT NULL,
  `LOYALTY_REDEEMED` varchar(1) NOT NULL,
  `LOYALTY_REDEMPTION_DISPLAYED` varchar(1) NOT NULL,
  `AWARDED_LOYALTY_POINTS` int(11) NOT NULL DEFAULT '0',
  `REDEEMED_LOYALTY_POINTS` int(11) NOT NULL DEFAULT '0',
  `SECOND_FREE_CHAI_REDEEMED` varchar(1) NOT NULL,
  `SECOND_FREE_CHAI_AVAILABLE` varchar(1) NOT NULL,
  `MEMBERSHIP_SUGGESTED` varchar(1) NOT NULL,
  `MEMBERSHIP_PURCHASED` varchar(1) NOT NULL,
  `MEMBERSHIP_USED` varchar(1) NOT NULL,
  `MEMBERSHIP_TYPE` varchar(30) DEFAULT NULL,
  `PAYMENT_START_TIME` timestamp NULL DEFAULT NULL,
  `PAYMENT_END_TIME` timestamp NULL DEFAULT NULL,
  `EDC_PAYMENT` varchar(1) NOT NULL,
  `PREVIOUS_ORDER_ID` int(11) DEFAULT NULL,
  `PREVIOUS_ORDER_BUSINESS_DATE` timestamp NULL DEFAULT NULL,
  `PREVIOUS_ORDER_UNIT_ID` int(11) DEFAULT NULL,
  `SAVED_CHAI_ADDED` varchar(1) NOT NULL,
  `PREVIOUS_ORDER_ADDED` varchar(1) NOT NULL,
  `OFFER_AVAILABLE` varchar(1) NOT NULL,
  `OFFER_AVAILED` varchar(1) NOT NULL,
  `SETTLED_AMOUNT` decimal(10,6) DEFAULT NULL,
  `OTP_DELIVERED_TIME` int(11) DEFAULT NULL,
  PRIMARY KEY (`METRIC_ID`),
  KEY `ORDER_METRIC_DETAIL_DATA_ORDER_ID` (`ORDER_ID`) USING BTREE,
  KEY `ORDER_METRIC_DETAIL_DATA_CUSTOMER_ID` (`CUSTOMER_ID`) USING BTREE,
  KEY `ORDER_METRIC_DETAIL_DATA_BILLING_TIME` (`BILLING_SERVER_TIME`) USING BTREE,
  KEY `ORDER_METRIC_DETAIL_DATA_POS_VERSION` (`POS_VERSION`) USING BTREE,
  KEY `ORDER_METRIC_DETAIL_DATA_CAFE_APP_VERSION` (`CAFE_APP_VERSION`) USING BTREE,
  KEY `ORDER_METRIC_DETAIL_DATA_CAFE_WALLET_PURCHASED` (`WALLET_PURCHASED`) USING BTREE,
  KEY `ORDER_METRIC_DETAIL_DATA_CAFE_CUSTOMIZED_PRODUCT_ADDED` (`CUSTOMIZED_PRODUCT_ADDED`) USING BTREE,
  KEY `ORDER_METRIC_DETAIL_DATA_CAFE_MEMBERSHIP_PURCHASED` (`MEMBERSHIP_PURCHASED`) USING BTREE
);

ALTER TABLE KETTLE_DEV.ORDER_METRIC_DETAIL_DATA ADD COLUMN ORDER_SOURCE VARCHAR(10);
ALTER TABLE KETTLE_DEV.ORDER_METRIC_DETAIL_DATA ADD COLUMN UNIT_ID INTEGER;
ALTER TABLE KETTLE_DEV.ORDER_METRIC_DETAIL_DATA ADD COLUMN TERMINAL_ID INTEGER;

ALTER TABLE KETTLE_DEV.CUSTOMER_BRAND_MAPPING ADD COLUMN LAST_SPECIAL_ORDER_ID INTEGER;
ALTER TABLE KETTLE_DEV.CUSTOMER_BRAND_MAPPING ADD COLUMN TOTAL_SPECIAL_ORDER INTEGER;
ALTER TABLE KETTLE_DEV.CUSTOMER_BRAND_MAPPING ADD COLUMN LAST_SPECIAL_ORDER_TIME timestamp;

ALTER TABLE KETTLE_DUMP.ORDER_METRIC_DETAIL_DATA MODIFY TAXABLE_AMOUNT decimal(10,2);
ALTER TABLE KETTLE_DUMP.ORDER_METRIC_DETAIL_DATA MODIFY TOTAL_AMOUNT decimal(10,2);
ALTER TABLE KETTLE_DUMP.ORDER_METRIC_DETAIL_DATA MODIFY SETTLED_AMOUNT decimal(10,2);

ALTER TABLE KETTLE_STAGE.ORDER_ITEM_METADATA_DETAIL ADD COLUMN IS_SAVED_CHAI VARCHAR(1);
ALTER TABLE KETTLE_STAGE.ORDER_ITEM_METADATA_DETAIL ADD COLUMN SAVED_CHAI_ADDED INTEGER;


ALTER TABLE `KETTLE_STAGE`.`ORDER_DETAIL`
ADD COLUMN `NO_OF_PAX` INT(11) NULL ;

ALTER TABLE `KETTLE_STAGE`.`UNIT_TABLE_MAPPING`
ADD COLUMN `NO_OF_PAX` INT(11) NULL AFTER `TOTAL_AMOUNT`;

ALTER TABLE `KETTLE_STAGE`.`UNIT_TABLE_MAPPING`
ADD COLUMN `CUSTOMER_TYPE` VARCHAR(100) NULL AFTER `TABLE_STATUS`;

ALTER TABLE `KETTLE_STAGE`.`UNIT_TABLE_MAPPING`
ADD COLUMN `LOYALTEA_TO_BE_REDEEMED` VARCHAR(1) NULL AFTER `SETTLEMENT_ORDER_ID`;



CREATE TABLE SUPERU_EVENT_TRACK (
    EVENT_ID INT AUTO_INCREMENT PRIMARY KEY,
    ORDER_ID INT,
    EVENT_QPUSH_TIME DATETIME,
    EVENT_API_PUSH_TIME DATETIME,
    METADATA TEXT,
    STATUS VARCHAR(255)
);



