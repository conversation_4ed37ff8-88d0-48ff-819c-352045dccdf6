<#setting locale="en_US">
<#setting date_format="dd/MM/yyyy HH:mm:ss">
<#setting number_format="0.##">
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Chai Receipt No: ${order.generateOrderId}</title>
    <meta http-equiv=Content-Type content="text/html; charset=UTF-8">
</head>
<body>

<!--<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="max-width:600px;margin:auto;border-spacing:0;border-collapse:collapse;">
    <tbody>
        <tr>
            <td style="text-align:center;vertical-align:top;font-size:0;border-collapse:collapse">
                <a href="https://cafes.chaayos.com">
                    <img src="${urlBasePath}/img/banner/chaayosBMS2.jpg" width="600px" />
                </a>
            </td>
        </tr>
    </tbody>
</table>-->
<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="max-width:600px;margin:auto;border-spacing:0;border-collapse:collapse;background:white">
    <tbody>
    <tr>
        <td valign="top" style="text-align:center;font-size:0;border-collapse:collapse">
            <a href="https://stpl.page.link/wc_home_list">
                <img alt="Chaayos" src="${urlBasePath}/chaayos/logo/chaayos-logo.png"
                     style="padding-top: 9px" width="350" />
            </a>
        </td>
    </tr>
    <tr>
        <td valign="top" style="font-size:0;border-collapse:collapse;padding-left:15px;padding-right:15px">
    <tr style="margin-left: 20px;">
        <td valign="middle" style="background-color:white;">
                        <span style="font-size:22px;color:#2d2d2d; font-weight: 500;">Hi <#if (customer.firstName)??>${customer.firstName}</#if>
                                    <#if (customer.middleName)??>${customer.middleName}</#if>
            <#if (customer.lastName)??>${customer.lastName}</#if>, </span>
        </td>
    </tr>
    <tr>
        <td valign="middle" style="background-color:white;">
            <span style="font-size:22px;color:#2d2d2d;font-weight: 500;">Thank you for ordering at Chaayos!</span>
        </td>
    </tr>
    <tr align="center" valign="middle" width="100%" cellpadding="0" cellspacing="0" style="padding-bottom:10px; background-color:#ffffff;border: 2px;">
        <table cellspacing="0">
            <tbody>
            <tr style="background: #F8F9FB;border-radius: 50px; text-align: center;font-size: 18px; border-style: solid;">
                    <td width="50%" style="border-top-left-radius: 50px; border-bottom-left-radius: 50px;">
                        <!--Chai Image-->
                        <a href="https://stpl.page.link/wc_home_list">
                            <img alt="Desi Chai" src="https://d83sxb99gbu06.cloudfront.net/email/order_receipt/desi_chai.jpg" width="100px"
                                 style="width: 51%;margin-top: 10px;border-radius: 20px;"
                            />
                        </a>
                    </td>
                    <td width="50%" style="word-wrap: normal; border-top-right-radius: 50px; border-bottom-right-radius: 50px">
                        <!--Unit/ Cafe Name-->
                        ${order.unitName}
                    </td>
            </tr>
            </tbody>
        </table>
    </tr>
    <tr align="center" valign="middle" width="100%" cellpadding="0" cellspacing="0" style="margin-bottom:10px; background-color:#ffffff;border: 2px;">
        <center>
            <table cellspacing="10%">
            <tr>
                <td width="60%">
                    Order ID: <br> ${order.generateOrderId}
                </td>
                <td width="50%">
                    Contact: <br> ${customer.countryCode}-${customer.contactNumber}
                </td>
            </tr>
            <tr>
                <td width="60%">
                    Order Time: <br> ${order.billCreationTime?date}
                </td>
                <td width="50%">
                    Email ID: <br> ${(customer.emailId)!" "}
                </td>
            </tr>
        </table>
        </center>
    </tr>
    <div style="margin:0;padding:0;height: 100% !important;width: 100% !important;" bgcolor="#f4f4f4">
        <table cellspacing="0" cellpadding="0" border="0" height="100%" width="100%" style="border-collapse: collapse;
                                    table-layout: fixed; margin: 0 auto; border-spacing: 0;padding: 0;height:100% !important;width:100% !important;">
            <tbody>
            <tr>
                <#if isEmailVerified>
                <td style="border-collapse: collapse; padding-top: 5px; padding-bottom: 5px;border-radius: 50px">
                <#else>
                <td style="border-collapse: collapse; padding-top: 5px; padding-bottom: 5px;border-radius: 50px; margin-top: 10px">
                <div style="margin-top: 15px; text-align:center;  background: #FCF6E8; font-size: 18px;border-color: black;padding-top: 10px; padding-bottom: 10px; padding-left: 15px; padding-right: 15px; border-radius: 30px; border-style: dashed; border-width: 0.5px">
                        <#if (verifyEmailLink)??>
                        <div style="font-size:15px;line-height: 30px;">
                            Verify this email address and earn a free Desi-Chai on your next order with us.
                        </div>
                        <br />
                        <img alt="Chaayos Free Desi Chai" src="${urlBasePath}/img/chaiGlass.png" width="100px" /><br />
                        <center>
                            <a href="${verifyEmailLink}?token=${token}" style="text-decoration:none;">
                                <div style="background:#108a45;border-radius: 50px;text-align:center;color:#fafafa;font-size:18px;padding: 5px;width: 200px;">
                                    <div style="color:#fafafa">Verify Now </div>
                                </div>
                            </a>
                        </center>
                    </#if>
    </div>
    </#if>
    <#if (order.transactionDetail.savings)?? && (order.transactionDetail.savings != 0) && (order.transactionDetail.discountDetail.discountReason) ??>
            <table style="border-radius: 10px" width="100%">
                <td style="padding:20px;margin:0 15px;text-align:center;color:#FCF6E8;font-size:18px" width="100%">
                    <div style="background: #FCF6E8;border-radius: 50px; text-align: center;font-size: 18px;border: 0.5px dashed black;padding: 15px;">
                        <div style="word-wrap:normal;border-collapse:collapse;line-height:30px;font-weight: 700;font-size:24px;font-family:sans-serif;">
                            <span style="color:#CB5928;">You have saved &#8377;${order.transactionDetail.savings} </span>
                            <span style="color:#108A45;">in this order using </span>
                            <#if (order.transactionDetail.discountDetail.discountReason == "Loyalty")>
                            <span style="color:#108A45;">Chaayos LoyalTea!</span>
                            <#elseif (order.transactionDetail.discountDetail.discountReason == "LOYALTEA")>
                            <span style="color:#108A45;">Chaayos LoyalTea!</span>
                            <#elseif (order.transactionDetail.discountDetail.discountReason == "CHAAYOS_SELECT")>
                            <span style="color:#108A45;">Chaayos Select!</span>
                            <#elseif (order.transactionDetail.discountDetail.discountReason == "CHAAYOS_CASH")>
                            <span style="color:#108A45;">Chaayos Cash!</span>
                            <#elseif (order.transactionDetail.discountDetail.discountReason == "EMP35")>
                            <span style="color:#108A45;">Coupon!</span>
                            <#elseif (order.transactionDetail.discountDetail.discountReason == "CHAAYOS_WALLET")>
                            <span style="color:#108A45;">Chaayos Wallet!</span>
                            <#else>
                            <span style="color:#108A45;">${order.transactionDetail.discountDetail.discountReason}!</span>
                        </#if>
                        </div>
                    </div>
                </td>
            </table>
    <#elseif (promotionalDiscount)?? && promotionalDiscount gt 0>
        <table style="border-radius: 10px" width="100%">
            <td style="padding:20px;margin:0 15px;text-align:center;color:#FCF6E8;font-size:18px" width="100%">
                <div style="background: #FCF6E8;border-radius: 50px; text-align: center;font-size: 18px;border: 0.5px dashed black;padding: 15px;">
                    <div style="word-wrap:normal;border-collapse:collapse;line-height:30px;font-weight: 700;font-size:24px;font-family:sans-serif;">
                        <span style="color:#CB5928;">You have saved &#8377;${promotionalDiscount} </span>
                        <span style="color:#108A45;">in this order using Promotional Offer!</span>
                </div>
                </div>
            </td>
        </table>
    <#elseif (order.offerCode)?? && (order.transactionDetail.savings)?? && (order.transactionDetail.savings != 0)>
    <table style="border-radius: 10px" width="100%">
        <td style="padding:20px;margin:0 15px;text-align:center;color:#FCF6E8;font-size:18px" width="100%">
            <div style="background: #FCF6E8;border-radius: 50px; text-align: center;font-size: 18px;border: 0.5px dashed black;padding: 15px;">
                <div style="word-wrap:normal;border-collapse:collapse;line-height:30px;font-weight: 700;font-size:24px;font-family:sans-serif;">
                    <span style="color:#CB5928;">You have saved &#8377;${order.transactionDetail.savings} </span>
                    <span style="color:#108A45;">in this order using ${order.offerCode}!</span>
                </div>
            </div>
        </td>
    </table>
</#if>
<center>
<!--VISITS, CONSUMPTION, ORDERS & SAVINGS CARD-->
    <#if customerEmailData??>
    <tr align="center" valign="middle" width="100%" cellpadding="0" cellspacing="0" style="padding-bottom:10px; background-color:#ffffff;border: 2px;">
    <table style="border-radius: 10px" width="100%">
        <td style="padding:11px;margin:0 15px;text-align:center;color:#FCF6E8;font-size:18px" width="100%">
            <table style="width: 100%; border-radius: 30px; background: #FCF6E8; text-align: center;font-size: 18px;border: 0.5px dashed black;padding: 11px;">
                <tbody>
                <tr>
                    <td width="25%" style="border-top-left-radius: 30px">
                        <a href="https://stpl.page.link/wc_orders">
                            <img alt="Daily Visits"
                                 src="https://d83sxb99gbu06.cloudfront.net/email/order_receipt/daily_visits.png"
                                 width="52.47%"/>
                        </a>
                    </td>
                    <td width="25%">
                        <a href="https://stpl.page.link/wc_loyaltea">
                            <img alt="Loyal Tea"
                                 src="https://d83sxb99gbu06.cloudfront.net/email/order_receipt/loyaltea.png"
                                 width="48.65%"/>
                        </a>
                    </td>
                    <td width="25%">
                        <a href="https://stpl.page.link/wc_orders">
                            <img alt="Orders Placed"
                                 src="https://d83sxb99gbu06.cloudfront.net/email/order_receipt/orders_placed.png"
                                 width="50.52%"/>
                        </a>
                    </td>
                    <td width="25%" style="border-top-right-radius: 30px">
                        <a href="https://stpl.page.link/wc_home_list">
                            <img alt="Overall Savings"
                                 src="https://d83sxb99gbu06.cloudfront.net/email/order_receipt/overall_savings.png"
                                 width="49.70%"/>
                        </a>
                    </td>
                </tr>
                <tr>
                    <td style="border-collapse:collapse;line-height:20px;font-size:14px;color:#70777D;font-family:sans-serif;">
                        ${customerEmailData.overallVisits}
                    </td>
                    <td style="border-collapse:collapse;line-height:20px;font-size:14px;color:#70777D;font-family:sans-serif;">
                        ${(customerEmailData.acquiredLoyaltyPoints - (customerEmailData.acquiredLoyaltyPoints % 60))/ 60}
                    </td>
                    <td style="border-collapse:collapse;line-height:20px;font-size:14px;color:#70777D;font-family:sans-serif;">
                        ${customerEmailData.overallOrders}
                    </td>
                    <td style="border-collapse:collapse;line-height:20px;font-size:14px;color:#70777D;font-family:sans-serif;">
                        ${customerEmailData.overallSavings}
                    </td>
                </tr>
                <tr>
                    <td style="border-bottom-left-radius:30px; border-collapse:collapse;line-height:20px;font-size:14px;color:#70777D;font-family:sans-serif;">
                        VISITS
                    </td>
                    <td style="border-collapse:collapse;line-height:20px;font-size:14px;color:#70777D;font-family:sans-serif;">
                        LOYALTEA <br> ACQUIRED
                    </td>
                    <td style="border-collapse:collapse;line-height:20px;font-size:14px;color:#70777D;font-family:sans-serif;">
                        ORDERS <br> PLACED
                    </td>
                    <td style="border-bottom-right-radius: 30px; border-collapse:collapse;line-height:20px;font-size:14px;color:#70777D;font-family:sans-serif;">
                        OVERALL <br> SAVINGS
                    </td>
                </tr>
                </tbody>
            </table>
        </td>
    </table>
</tr>
</#if>

</center>
<#if billPromotion??>
<tr>
    <td>
        <div style="width: 100%; background: #F8F9FB;">
            <p style="padding:5px; padding-left: 13px;text-align: center; font-size: 18px; font-style : bold;">
                ${billPromotion}
            </p>
        </div>
    </td>
</tr>
</#if>

<tr style="background-color: white">
    <td style="border-collapse: collapse;text-align:left;padding: 0;">
        <div style="background-color: #F8F9FB;border-top-left-radius: 50px;border-top-right-radius: 50px;border: 0.5px;font-size: 18px;border-color: black;padding: 15px;">
            <table border="0" width="100%" cellpadding="0" cellspacing="0"
                   style="margin:10px 0;border-spacing:0;border-collapse:collapse">
                <tbody>
                <tr>
                    <td style="font-size:20px;line-height: 30px;">
                        Order Summary
                    </td>
                </tr>
                </tbody>
            </table>
            <table border="0" width="100%" cellpadding="0" cellspacing="0"style="margin:10px 0;border-spacing:0;border-collapse:collapse;">
                <tbody>
                <#list order.orders>
                <#items as orderItem>
                <#if orderItem.productId != 1043 && orderItem.productId != 1044>
                <tr>
                    <td style="border-collapse:collapse;line-height:30px;font-size:16px;color:#2d2d2a;font-family:sans-serif;">
                        ${orderItem.productName}
                        <#if orderItem.dimension != "None"> ${orderItem.dimension}</#if>
                    <#if (orderItem.complimentaryDetail)?? && (orderItem.complimentaryDetail.isComplimentary)>
                    (Complimentary)
                    <#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.discountCode)?? && (orderItem.discountDetail.discountCode > 0))>
                    *
                    <#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.promotionalOffer)?? && (orderItem.discountDetail.promotionalOffer > 0))>
                    *
                </#if>
                <!--<br>-->
                <div style="color: darkgrey;line-height:20px;font-size:12px;font-style: italic;font-family: sans-serif;margin-top: -10px;">
                    (${orderItem.quantity} X &#8377;${orderItem.price})
                </div>
                </td>
                <td style="text-align: right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
                    &#8377;${orderItem.totalAmount}
                </td>
                </tr>
                <#if orderItem.productCategory.id == 8>
                <#list orderItem.composition.menuProducts>
                <#items as menuItem>
                <tr>
                    <td colspan="2" style="font-size: 12px; padding: 2pt 0; color: #7d7d76; padding-left: 5px;">
                        (${menuItem.code}) ${menuItem.productName}
                        <#if menuItem.dimension != "None">${menuItem.dimension}</#if>
                    </td>
                </tr>
                </#items>
            </#list>
        </#if>
    </#if>
</#items>
</#list>
</tbody>
</table>
<table border="0" width="100%" cellpadding="0" cellspacing="0"
       style="border-top: 2pt solid #cbcbc8; margin:10px 0;border-spacing:0;border-collapse:collapse;">
    <tbody>

    <#list order.orders>
    <#items as orderItem>
    <#if orderItem.productId == 1043 || orderItem.productId == 1044>
    <tr>
        <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;text-transform:uppercase;">
            ${orderItem.productName}
            <#if (orderItem.complimentaryDetail)?? && (orderItem.complimentaryDetail.isComplimentary)>
            (Complimentary)
            <#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.discountCode)?? && (orderItem.discountDetail.discountCode > 0))>
            *
            <#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.promotionalOffer)?? && (orderItem.discountDetail.promotionalOffer > 0))>
            *
        </#if>
        </td>
        <td style="text-align: right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
            &#8377;${orderItem.totalAmount}
        </td>
    </tr>
    </#if>
</#items>
</#list>

<tr>
    <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif">
        TOTAL
    </td>
    <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
        &#8377;${order.transactionDetail.totalAmount}
    </td>
</tr>
<#if promotionalDiscount gt 0>
<tr>
    <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
        * PROMOTIONAL OFFER
    </td>
    <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
        &#8377;${promotionalDiscount}
    </td>
</tr>
</#if>
<#if (discountValue > 0)>
<tr>
    <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
        * DISCOUNT (${discountPercent}%)
    </td>
    <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
        &#8377;${discountValue}
    </td>
</tr>
</#if>
<#list order.transactionDetail.taxes> <#items as tax>
<tr>
    <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
        ${tax.code} (${tax.percentage}%)
    </td>
    <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
        &#8377;${tax.value}
    </td>
</tr>
</#items></#list>
<#if order.transactionDetail.roundOffValue != 0>
<tr>
    <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
        ROUNDING OFF
    </td>
    <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
        &#8377;${order.transactionDetail.roundOffValue}
    </td>
</tr>
</#if>
</tbody>
</table>
<table border="0" width="100%" cellpadding="0" cellspacing="0"
       style="margin:10px 0;border-spacing:0;border-collapse:collapse;border-top:1pt solid #cbcbc8;">
    <tbody>
    <tr>
        <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
            <strong>BILL TOTAL</strong>
        </td>
        <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
            <strong>&#8377;${order.transactionDetail.paidAmount}</strong>
        </td>
    </tr>
    <tr align="right">
        <td colspan="2" style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
            <strong>Rs. ${paidAmountInWords}</strong>
        </td>
    </tr>
    <#if (order.transactionDetail.savings)?? && order.transactionDetail.savings != 0 && (order.transactionDetail.discountDetail)??
    && (order.transactionDetail.discountDetail.discountReason)??>
    <tr>
        <td style="border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
            Net Savings (through
            <#if (order.transactionDetail.discountDetail.discountReason == "Loyalty")>
            <span>Chaayos LoyalTea)</span>
            <#elseif (order.transactionDetail.discountDetail.discountReason == "CHAAYOS_SELECT")>
            <span>Chaayos Select)</span>
            <#elseif (order.transactionDetail.discountDetail.discountReason == "CHAAYOS_CASH")>
            <span>Chaayos Cash)</span>
            <#elseif (order.transactionDetail.discountDetail.discountReason == "EMP35")>
            <span>Coupon)</span>
            <#elseif (order.transactionDetail.discountDetail.discountReason == "CHAAYOS_WALLET")>
            <span>Chaayos Wallet)</span>
            <#else>
            <span>${order.transactionDetail.discountDetail.discountReason})</span>
        </#if>
        </td>
        <td style="text-align:right;border-collapse:collapse;line-height:30px;font-size:14px;color:#2d2d2a;font-family:sans-serif;">
            <strong>&#8377;${order.transactionDetail.savings}</strong>
        </td>
    </tr>
</#if>
<tr>
    <td style="text-align:left;border-collapse:collapse;line-height:30px;font-size:14px;color:#108a45;font-family:sans-serif;">
        Order placed via
        <#if (order.channelPartner == 1 || order.channelPartner == 9)>
            Chaayos Cafe App
        <#elseif (order.channelPartner == 3 || order.channelPartner == 20)>
            Zomato
        <#elseif (order.channelPartner == 6 || order.channelPartner == 19)>
            Swiggy
        <#elseif (order.channelPartner == 21 || order.channelPartner == 2)>
            Chaayos Mobile App
        <#elseif (order.channelPartner == 4)>
            Tiny Owl
        <#elseif (order.channelPartner == 5)>
            Food Panda
        <#elseif (order.channelPartner == 7)>
            Ola Cafe
        <#elseif (order.channelPartner == 8)>
            Groupon
        <#elseif (order.channelPartner == 12)>
            Runnr
        <#elseif (order.channelPartner == 13)>
            Scootsy
        <#elseif (order.channelPartner == 14)>
            Chaayos Web
        <#elseif (order.channelPartner == 15)>
            UBER Eats
        <#elseif (order.channelPartner == 16)>
            Big Basket
        <#elseif (order.channelPartner == 17)>
            Dunzo
        <#elseif (order.channelPartner == 18)>
            Amazon Prime Food
        <#elseif (order.channelPartner == 22)>
        EazyDiner
        <#elseif (order.channelPartner == 23)>
        Chaayos Bazaar
        <#elseif (order.channelPartner == 24)>
        MagicPin
        <#elseif (order.channelPartner == 25)>
        Chaayos Monk
    </#if>
    </td>
</tr>

</tbody>
</table>
</td>
</tr>
<tr>
    <div style="width: 100%;height:30px;position:absolute;background:white url('https://d83sxb99gbu06.cloudfront.net/email/order_receipt/ticket_cut_x.jpg') repeat scroll 0% 0%;">
        <img alt="Ticket Cutting" src="https://d83sxb99gbu06.cloudfront.net/email/order_receipt/ticket_cut_x.jpg">
    </div>
</tr>

<!--</tbody>-->
<!--</div>-->
<table width="100%" style="align-self: center;margin-top: 30px">
    <tbody>
    <!--Redeem, Chaayos Select, Wallet Data-->
    <table style="margin-top:30px; background: #FCF6E8;border-radius: 30px; text-align: center;font-size: 18px;padding: 11px; border: 0.5px dashed black;">
        <tbody>
    <tr align="center" valign="middle" cellpadding="0" cellspacing="0" style="border-radius: 30px; padding-bottom:10px; background-color:#FCF6E8;border: 2px;">
        <td>
            <div style="border-collapse:collapse;line-height:30px;font-size:14px;color:#CB5928;font-family:sans-serif;">
                <a href="https://stpl.page.link/wc_loyaltea">
                    <img alt="Loyal Tea" src="https://d83sxb99gbu06.cloudfront.net/email/order_receipt/loyaltea.png"
                         width="40%" />
                </a>
            </div>
        </td>
        <td>
            <div style="border-collapse:collapse;line-height:30px;font-size:14px;color:#CB5928;font-family:sans-serif;">
                <a href="https://stpl.page.link/wc_wallet">
                    <img alt="Wallet Balance" src="https://d83sxb99gbu06.cloudfront.net/email/order_receipt/wallet.png"
                         width="40%" />
                </a>
            </div>
        </td>
        <!--<td>-->
            <!--<div style="border-collapse:collapse;line-height:30px;font-size:14px;color:#CB5928;font-family:sans-serif;">-->
                <!--<a href="https://stpl.page.link/wc_chaayos_cash">-->
                    <!--<img alt="Chaayos Cash" src="https://d83sxb99gbu06.cloudfront.net/email/order_receipt/chaayos_cash.png"-->
                         <!--width="50%" />-->
                <!--</a>-->
            <!--</div>-->
        <!--</td>-->
        <td>
            <div style="border-collapse:collapse;line-height:30px;font-size:14px;color:#CB5928;font-family:sans-serif;">
                <a href="https://stpl.page.link/wc_chaayos_select">
                    <img alt="Chaayos Select" src="https://d83sxb99gbu06.cloudfront.net/email/order_receipt/chaayos_select.png"
                         width="40%" />
                </a>
            </div>
        </td>
    </tr>
    <#if customerEmailData ??>
    <tr>
        <td valign="initial" style="width: 25%; word-wrap: normal;border-collapse:collapse;line-height:20px;font-size:14px;color:#108A45;font-family:sans-serif;">
            <#if (customerEmailData.overallOrders)?? && (customerEmailData.acquiredLoyaltyPoints)?? &&  (customerEmailData.overallOrders == 1 && customerEmailData.acquiredLoyaltyPoints == 10)>
            You can Redeem 2nd Free Chai in next order.
            <#else>
            You are ${(60 - (customerEmailData.acquiredLoyaltyPoints % 60))/ 10}  visits away to earn a free chai.
        </#if>
        </td>
        <td valign="initial" style="width: 25%; word-wrap: normal;border-collapse:collapse;line-height:20px;font-size:14px;color:#108A45;font-family:sans-serif;">
            <#if (customerEmailData.walletBalance)?? && (customerEmailData.walletBalance < 1000)>
            <#if (customerEmailData.walletBalance == 0)>
            Recharge your wallet and get Extra 10%.
            <#else> Wallet balance is low &#8377;${customerEmailData.walletBalance}
        </#if>
        <#else>
        Wallet balance is &#8377;${customerEmailData.walletBalance}
    </#if>
        </td>
        <!--<td style="vertical-align: initial; width: 25%; word-wrap: normal;border-collapse:collapse;line-height:20px;font-size:14px;color:#108A45;font-family:sans-serif;">-->
            <!--<#if (customerEmailData.chaayosCashBalance == 0) >-->
            <!--Refer a Friend and get &#8377;300 Chaayos Cash-->
            <!--<#else>-->
            <!--You have unused &#8377;${customerEmailData.chaayosCashBalance} Chaayos Cash.-->
        <!--</#if>-->
        <!--</td>-->
        <td style="width: 25%; word-wrap: normal;border-collapse:collapse;line-height:20px;font-size:14px;color:#108A45;font-family:sans-serif;">
            <#if (customerEmailData.membershipAvailable == "Y")>
                Your Chaayos Select Expires on ${customerEmailData.membershipEndDate}.
            <#else>
                Save 15% on each order from Chaayos Select.
        </#if>
        </td>
    </tr>
    </#if>
    </tbody>
    </table>
        </table>
<center>

<div style="margin-top: 30px; text-align: center;width: 100%;">
    <p style="margin: 0;font-weight: 300;font-size: 14px;">
        Note: This is an electronically generated receipt and does not require signature. For any queries,
        please drop an email at <a href="mailto:<EMAIL>" target="_blank"><EMAIL></a>.
    </p>
</div>

<!--Download the app now Section-->
<div style="text-align: center;font-weight: 500;font-size: 20px;margin: 15px 15px 0;">
        Download the app now
</div>

<table style="justify-content: center;padding: 10px; text-align: center;">
    <div style="display: flex;align-content: center;flex-direction: row;">
        <center>
            <a href="https://play.google.com/store/apps/details?id=com.chaayos&hl=en_IN&gl=US" style="text-decoration:none;margin:0 5px" target="_blank">
                <img alt="Google Play Store Chaayos Link" src="https://d83sxb99gbu06.cloudfront.net/email/order_receipt/android.png" width="30%" />
            </a>
            <a href="https://apps.apple.com/in/app/chaayos-india/id1521406820" style="text-decoration:none;margin:0 5px" target="_blank">
                <img alt="Apple App Store Chaayos Link" src="https://d83sxb99gbu06.cloudfront.net/email/order_receipt/apple.png" width="30%" />
            </a>
        </center>
    </div>
</table>

<!--Follow Us Section-->
    <table>
        <tbody>
            <tr>
                <td style="display: flex;">
                    <p style="font-size: 20px; font-weight: 500;"> Follow Us </p>
                    <a href="https://facebook.com/chaayos" style="text-decoration:none;margin:0 5px" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://facebook.com/chaayos&amp;source=gmail&amp;ust=1658289442106000&amp;usg=AOvVaw3zFyFZwtFTaNez4Bbqr52z">
                        <img src="https://ci6.googleusercontent.com/proxy/gA2CSZ2qQSgHNE8C2_H_vGgO5KQKN_OsBzl4X5sl3lfsyFm9XFaYTxk0RZ7WieOFT-0A_0NuIRko-wwqmrMEUUjqEyhD=s0-d-e1-ft#https://cafes.chaayos.com/img/desktop/facebook.png" style="width:25px;padding-top: 20px;" class="CToWUd">
                    </a>    
                    <a href="https://twitter.com/chaayos" style="text-decoration:none;margin:0 5px; padding-right: 20px" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://twitter.com/chaayos&amp;source=gmail&amp;ust=1658289442106000&amp;usg=AOvVaw1lxtBfIqRQF0Hh-0vUgcKw">
                        <img src="https://ci3.googleusercontent.com/proxy/AgP9F2KIbqFMafm1pW3-9_yWPe5RfDeZXLukyZaxZ8lKugDSS0PjgSr2IEMk1JFOEAf5wNMxRUuDL7VC4Mx_huWgDOg=s0-d-e1-ft#https://cafes.chaayos.com/img/desktop/twitter.png" style="width:25px;padding-top: 20px;" class="CToWUd">
                    </a>  
                    <a href="https://instagram.com/chaayos" style="text-decoration:none;margin:0 5px" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://instagram.com/chaayos&amp;source=gmail&amp;ust=1658289442106000&amp;usg=AOvVaw1MFiR4n3za8uqRmRbRj9Qb">
                        <img src="https://ci3.googleusercontent.com/proxy/CjqUjptNMgGMffIemvBZ6-E82YANZznYGUhFH656fwl5PrkJfhe8bLCaqSAZxS1VBmVN6wQG-dh7Wo4tOXtP8BD2r9HRBg=s0-d-e1-ft#https://cafes.chaayos.com/img/desktop/instagram.png" style="width:25px;padding-top: 20px;" class="CToWUd">
                    </a>
                </td>
            </tr>
        </tbody>
    </table>
    <table width="100%" style="border-bottom: #cbcbc8 1px solid;">
    </table>
<tr>
<div>
    <div style="padding:10px;text-align:center;">
        <table border="0" width="100%" cellpadding="0" cellspacing="0" style="text-align: center;">
            <tbody>
            <tr>
                <td style="font-size:12px;line-height:15px;color:#7d7d76;">
                    Company Identification Number: U55204DL2012PTC304447
                </td>
            </tr>
            <tr>
                <td style="font-size:12px;line-height:15px;color:#7d7d76;">
                    GSTIN: ${unit.tin}
                </td>
            </tr>
            <tr>
                <td style="font-size:12px;line-height:15px;color:#7d7d76;">
                    <#if (unit.fssai)??>
                    FSSAI: ${unit.fssai}</#if>
                </td>
            </tr>
            <tr>
                <td style="font-size:12px;line-height:15px;color:#7d7d76;">
                    Sunshine Teahouse Private Limited, 1st Fl, #382, 100' Rd, Ghitorni, New Delhi 30
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>

</tr>
</center>
</tbody>
</table>
</td>
</table>
</tr>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
<!---->
</div>
</div>

</body>
</html>
