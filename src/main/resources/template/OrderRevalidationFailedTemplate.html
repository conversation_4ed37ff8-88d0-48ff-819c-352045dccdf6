<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f8f8;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            padding: 10px 0;
            border-bottom: 1px solid #dddddd;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #333333;
        }
        .content {
            padding: 20px;
        }
        .content p {
            font-size: 16px;
            color: #666666;
            line-height: 1.5;
        }
        .image-container {
            text-align: center;
            margin: 15px 0;
            height : 250px;
        }
        .footer {
            text-align: center;
            padding: 10px 0;
            border-top: 1px solid #dddddd;
            font-size: 14px;
            color: #999999;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="image-container">
        <img src="cid:image" alt="Order Validation Failed Image" style="width: 100%; height: auto;">
    </div>
    <div class="content">
        <div class="header">
            <h1>Order Revalidation Failed</h1>
        </div>
        <p>$data.failureReason</p>
        <p>Order Details are given below : </p>
        <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
            <tr>
                <td style="padding:5pt;text-align:left;"><b>Customer Id : </b> #if($data.customerId)
                    $data.customerId
                    #else
                    N/A
                    #end</td>
                <td style="padding:5pt;text-align:left;"><b>Offer Code : </b>#if($data.offerCode)
                    $data.offerCode
                    #else
                    N/A
                    #end</td>
                <td style="padding:5pt;text-align:left;"><b>Unit Id : </b>#if($data.unitData)
                    $data.unitData
                    #else
                    N/A
                    #end</td>
            </tr>
        </table>
    </div>
</div>
</body>
</html>
