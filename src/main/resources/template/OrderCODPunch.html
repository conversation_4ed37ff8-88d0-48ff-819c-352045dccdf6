<#setting locale="en_US"><#setting date_format="dd/MM/yyyy HH:mm">
<style>
* {
	font-family: sans-serif;
}
</style>
<#if (orderSource)??>
	<center style="font-size: 18px"><b>${orderSource}</b></center>
</#if>
<hr />
<table style="font-size: 6px" cellpadding="0" cellspacing="4" width="8cm">
	<tr>
		<td width="3cm">Order No </td>
		<td>${order.generateOrderId}</td>
		<td width="1cm"></td>
	</tr>
	<tr>
		<td width="3cm">Order Time </td>
		<td>${order.billCreationTime?date}</td>
		<td width="1cm"></td>
	</tr>
	<#if (customer.id) gt 5 ><#if (customer.firstName)??>
	<tr>
		<td width="3cm">Name </td>
		<td>${customer.firstName}</td>
		<td width="1cm"></td>
	</tr>
	</#if>
	<tr>
		<td width="3cm">Contact </td>
		<td>${customer.countryCode}-${customer.contactNumber}</td>
		<td width="1cm"></td>
	</tr>
	</#if>
	<tr>
		<td width="3cm">Delivery Address </td>
		<td width="4cm" style="word-wrap: break-word; word-break: break-all; white-space: normal;">
			<#if (deliveryAddress)??>
				<#if (deliveryAddress.addressType)??>
					<span style="word-wrap: break-word; word-break: break-all; white-space: normal;">${deliveryAddress.addressType}, </span>
					<#if (deliveryAddress.company)??>
						<span style="word-wrap: break-word; word-break: break-all; white-space: normal;">${deliveryAddress.company}, </span>
					</#if>
				</#if>
				<#if (deliveryAddress.line1)??>
					<span style="word-wrap: break-word; word-break: break-all; white-space: normal;">${deliveryAddress.line1}, </span>
				</#if>
				<#if (deliveryAddress.line2)??>
					<span style="word-wrap: break-word; word-break: break-all; white-space: normal;">${deliveryAddress.line2 }, </span>
				</#if>
				<#if (deliveryAddress.line3)??>
					<span style="word-wrap: break-word; word-break: break-all; white-space: normal;">${deliveryAddress.line3}, </span>
				</#if>
				<#if (deliveryAddress.landmark)??>
					<span style="word-wrap: break-word; word-break: break-all; white-space: normal;">${deliveryAddress.landmark}, </span>
				</#if>
				<#if (deliveryAddress.locality)??>
					<span style="word-wrap: break-word; word-break: break-all; white-space: normal;">${deliveryAddress.locality}, </span>
				</#if>
					<span style="word-wrap: break-word; word-break: break-all; white-space: normal;">${deliveryAddress.city}</span>
			</#if>
		</td>
		<td width="1cm"></td>
	</tr>
	<#if (order.orderRemark)?? >
	<tr>
		<td width="3cm">Order Remark </td>
		<td><span>${order.orderRemark}</span></td>
		<td width="1cm"></td>
	</tr>
	</#if>
	<#if (order.offerCode)?? && order.offerCode != "">
	<tr>
		<td width="3cm">Coupon Applied :</td>
		<td><span>${order.offerCode}</span></td>
		<td width="1cm"></td>
	</tr>
	</#if>
</table>
<hr />
<table style="font-size: 6px" cellpadding="0" cellspacing="4" width="8cm">
	<tr>
		<td>Item</td>
		<td>Qty</td>
		<td>Price</td>
		<td>Amount</td>
	</tr>
	<#list order.orders> <#items as orderItem>
	<tr>
		<td width="5cm" colspan="2">
		${orderItem.code}??
		</td>
		<td></td>
		<td></td>
	</tr>
	<tr>
		<td width="3cm">
			<#if ((orderItem.complimentaryDetail)?? && (orderItem.complimentaryDetail.isComplimentary))>
			*
			<#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.discountCode)?? && (orderItem.discountDetail.discountCode > 0))>
			*
			<#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.promotionalOffer)?? && (orderItem.discountDetail.promotionalOffer > 0))>
			*
			</#if>
			
			${orderItem.productName}
			<#if orderItem.dimension != "None">
				${fillWhiteSpace(orderItem.dimension)}
			</#if>
		</td>
		<td>${orderItem.quantity}</td>
		<td>${orderItem.price}</td>
		<td>${orderItem.totalAmount}</td>
	</tr>
	
	<#if orderItem.productCategory.id == 8> <#list
	orderItem.composition.menuProducts><#items as menuItem>
	<tr>
		<td width="6cm" colspan="3">&nbsp;*(${menuItem.code}) ${menuItem.productName}
			<#if menuItem.dimension != "None"> ${fillWhiteSpace(menuItem.dimension)}</#if></td>
		<td></td>
	</tr>
	</#items>
	</#list> </#if>
	</#items></#list> 
</table>
<hr />
<table style="font-size: 6px" cellpadding="0" cellspacing="2" width="8cm">
	<tr>
		<td>Total</td>
		<td></td>
		<td></td>
		<td>${order.transactionDetail.totalAmount}</td>
	</tr>
	<#if promotionalDiscount gt 0>
	<tr>
		<td>Promotional Offer</td>
		<td></td>
		<td></td>
		<td>${promotionalDiscount}</td>
	</tr>
	</#if>
	<#if (discountPercent)?? && discountPercent gt 0>
	<tr>
		<td>Discount @ ${discountPercent}%</td>
		<td></td>
		<td></td>
		<td>${discountValue}</td>
	</tr>
	</#if>
	<#list order.transactionDetail.taxes> <#items as tax>
	<tr>
		<td>${tax.code} @ ${tax.percentage} %</td>
		<td></td>
		<td></td>
		<td>${tax.value}</td>
	</tr>
	</#items></#list> 
	<#if order.transactionDetail.roundOffValue != 0>
	<tr>
		<td>Round Off</td>
		<td></td>
		<td></td>
		<td>${order.transactionDetail.roundOffValue}</td>
	</tr>
	</#if>
	<tr>
		<td><b>Bill Total</b></td>
		<td></td>
		<td></td>
		<td><b>${order.transactionDetail.paidAmount}</b></td>
	</tr>
	<tr>
		<td colspan="4"><b>Rs. ${paidAmountInWords}</b></td>
	</tr>
	
</table>
<hr />
<table style="font-size: 6px" cellpadding="0" cellspacing="2" width="8cm">
	<#list settlements> 
		<#items as settlement>
		<tr>
			<#if settlement.modeDetail.category == "ONLINE">
				<td width="3cm"><b>Online</b></td>
			<#else>
				<td width="3cm"><b>${settlement.modeDetail.description}</b></td>
			</#if>		
			<td></td>
			<td></td>
			<td>${settlement.amount}</td>
		</tr>
		</#items>
	</#list>
</table>
<hr />
<#if (order.transactionDetail.savings)?? && order.transactionDetail.savings != 0>
<table style="font-size: 6px" cellpadding="0" cellspacing="2" width="8cm">
	<tr>
		<td width="3cm"><b>Net Savings</b></td>
		<td></td>
		<td></td>
		<td>${order.transactionDetail.savings}</td>
	</tr>
</table>
<hr />
</#if>
<!-- 	<img src="http://prod.kettle.chaayos.com:9797/kettle-service/images/offer-android.png"> -->