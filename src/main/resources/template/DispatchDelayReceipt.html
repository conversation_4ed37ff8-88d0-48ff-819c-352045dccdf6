<#setting locale="en_US"> <#setting date_format="MM/dd/yyyy HH:mm:ss">
<#setting number_format="0.##">
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<title>Dispatch Delay Email for ${unitName} - ${order.orderId}</title>
</head>
<body>

	<p>
		<strong>Hi Team,</strong>
	</p>
	<p>
		<strong>There is an order which has not been dispatched yet.
			Please find below the order detail.</strong>
	</p>
	<p>
		<strong>Please respond with the reason for delay</strong>
	</p>
	<p>Unit Name : ${unitName}</p>
	<p>Customer Name&nbsp;: ${customer.firstName}</p>
	<p>Contact Number&nbsp;: ${customer.contactNumber}</p>
	<p>Address&nbsp;: <#if (deliveryAddress)??> ${deliveryAddress}</#if></p>
	<p>Order : ${order.generateOrderId} - Status&nbsp;: ${order.status}</p>
	<p>Order Time: ${order.billingServerTime}</p>
	<#if (deliveryDetail)?? && (deliveryDetail.deliveryBoyName)??>
	<p>SDP Id: ${deliveryDetail.deliveryBoyId}</p>
	<p>SDP Name: ${deliveryDetail.deliveryBoyName}</p>
	<p>SDP Contact Number: ${deliveryDetail.deliveryBoyPhoneNum}</p>
	<#else>
	<p>
		<strong>No SDP Assigned</strong>
	</p>
	</#if>
	<table width="100%" border="1" cellpadding="12" cellspacing="0">
		<tbody>
			<tr>
				<td><b>Description </b></td>
				<td><b>Dimension</b></td>
				<td><b>Quantity</b></td>
			</tr>
			<#list order.orders> <#items as orderItem>
			<tr>
				<td style="text-align: left;">${orderItem.productName} <#if
					(orderItem.complimentaryDetail)?? &&
					(orderItem.complimentaryDetail.isComplimentary)> (Complimentary)
					</#if></td>
				<td><#if orderItem.dimension != "None">
					${orderItem.dimension}</#if></td>
				<td>${orderItem.quantity}</td>
			</tr>
			<#if orderItem.productCategory.id == 8> <#list
			orderItem.composition.menuProducts> <#items as menuItem>
			<tr>
				<td>${menuItem.productName}</td>
				<td><#if menuItem.dimension !=
					"None">*${menuItem.dimension}</#if></td>
				<td>${orderItem.quantity}</td>
			</tr>
			</#items> </#list> </#if> </#items> </#list>
		<tbody>
	</table>
	<p>&nbsp;</p>
	<p>Regards</p>
	<p>Delivery Support</p>


</body>
</html>