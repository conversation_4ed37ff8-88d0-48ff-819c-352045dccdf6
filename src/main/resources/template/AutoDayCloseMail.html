<#setting locale="en_US"> <#setting date_format="MM/dd/yyyy HH:mm:ss">
<#setting number_format="0.##">
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<title>Day Close Notification</title>
</head>
<body style="font-family: Verdana,sans-serif;">
<#if (incorrectDayCloses)?? || (dayCloseDefaulters)?? || (dayCloseFailures)??>
	<#if (incorrectDayCloses)??>
	<div style="box-shadow: 0 2px 4px 0 rgba(0,0,0,0.16),0 2px 10px 0 rgba(0,0,0,0.12); 
		margin: 20px 0; 
		background-color: #f1f1f1; 
		padding: 0.01em 16px; ">
	<div><b>Units with incorrect Day Close:</b></div><br/>
		<table border="1" style="background-color:ghostWhite">
			<tr>
			   <th style="color:white; background-color:red">Unit</th>
			   <th style="color:white; background-color:red">Employee</th>
			   <th>Business Date</th>
			   <th>Closure Start Time</th>
			   <th>Closure End Time</th>
			   <th>Closure Comment</th>
			</tr>
			<#list incorrectDayCloses>
			<#items as dayclose>
			<tr>
				<td style="color:white; background-color:red"><#if (dayclose.unitBasicDetail.name)?? >${dayclose.unitBasicDetail.name}</#if></td>
				<td style="color:white; background-color:red"><#if (dayclose.employee)?? >${dayclose.employee}</#if></td>
				<td><#if (dayclose.businessDate)?? >${dayclose.businessDate?date}</#if></td>
				<td><#if (dayclose.closureStartTime)?? >${dayclose.closureStartTime?datetime}</#if></td>
				<td><#if (dayclose.closureEndTime)?? >${dayclose.closureEndTime?datetime}</#if></td>
				<td><#if (dayclose.closureComment)?? >${dayclose.closureComment}</#if></td>
			</tr>
			</#items>
			</#list>
			</tr>
		</table>
	</#if>
	<#if (dayCloseDefaulters)??>
	<br>
	<div><b>Day auto closed for following units:</b></div><br/>
	<table border="1" style="background-color:ghostWhite">
		<tr>
		   <th>Unit</th>
		   <th>Business Date</th>
		</tr>
		<#list dayCloseDefaulters>
		<#items as defaulter>
		<tr>
			<td><#if (defaulter.unitBasicDetail.name)?? >${defaulter.unitBasicDetail.name}</#if></td>
			<td><#if (defaulter.businessDate)?? >${defaulter.businessDate?date}</#if></td>
		</tr>
		</#items>
		</#list>
		</tr>
	</table>
	</#if>
	<#if (dayCloseFailures)??>
	<br>
	<div><b>Encountered Error while closing day for following units:</b></div><br/>
	<table border="1" style="background-color:ghostWhite">
		<tr>
		   <th>Unit</th>
		   <th>Business Date</th>
		   <th>Error Message</th>
		</tr>
		<#list dayCloseFailures>
		<#items as failure>
		<tr>
			<td><#if (failure.unitBasicDetail.name)?? >${failure.unitBasicDetail.name}</#if></td>
			<td><#if (failure.businessDate)?? >${failure.businessDate?date}</#if></td>
			<td><#if (failure.errorMessage)?? >${failure.errorMessage}</#if></td>
		</tr>
		</#items>
		</#list>
		</tr>
	</table>
	</#if>
</div>

<#else>
<div style="box-shadow: 0 2px 4px 0 rgba(0,0,0,0.16),0 2px 10px 0 rgba(0,0,0,0.12); margin: 20px 0;
   background-color: #f1f1f1;
   padding: 0.01em 16px; ">Hurray! Day Close Processed Successfully.
   <br>
   <span style="color:green;">No Errors Encountered.</span>
</div>
</#if>
</body>
</html>


