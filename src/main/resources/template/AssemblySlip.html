<#setting locale="en_US"><#setting date_format="dd/MM/yyyy
		HH:mm">
	<#if (order.source=="TAKE_AWAY")>
		<p>
		<center><b>Chaayos Take Away</b></center>
		</p>
		<#else>
		<b>Assembly Slip</b>
	</#if>
<table cellpadding="0" cellspacing="4">
	<#if (unit.tableService) && (order.tableNumber)??>
		<tr>
			<td><b>Table No </b></td>
			<td><b>${order.tableNumber}</b></td>
		</tr>
	</#if>
	<#if (order.tokenNumber)?? && ( order.tokenNumber > 0 ) && (order.source != "COD")>
	<tr>
		<td>Token Number </td>
		<td>${order.tokenNumber}</td>
	</tr>
	</#if>
	<tr>
		<td>Order No </td>
		<td>${order.generateOrderId}</td>
	</tr>
	<tr>
		<td>Order Time </td>
		<td>${order.billCreationTime?date}</td>
	</tr>
	<#if (order.customerName)??>
	<tr>
		<td>Name </td>
		<td>${order.customerName}</td>
	</tr>
	</#if>
	<#if (customer.id) gt 5 > 
<!-- 	<tr>
		<td>Contact :</td>
		<td>${customer.countryCode}-${customer.contactNumber}</td>
	</tr> -->
	
<!--Add Delivery Partner only when it is not "none" which has Id = 1  -->
	<#if (deliveryPartner)?? && (deliveryPartner.name)?? && deliveryPartner.id gt 1>
	<tr>
		<td>Delivery Partner </td>
		<td>${deliveryPartner.name}</td>
	</tr>
	</#if>
	</#if>
</table>
<hr />
<table cellpadding="0" cellspacing="4" width="8cm">
	<tr>
		<td>Item</td>
		<td>Dim</td>
		<td>Qty</td>
	</tr>
	<#list order.orders> <#items as orderItem>
	<tr>
		<td>
			<span>
				<#if (orderItem.complimentaryDetail)?? && (orderItem.complimentaryDetail.isComplimentary)>*</#if>
				${orderItem.productName}
			</span>
		</td>
		<td>${orderItem.dimension?substring(0,1)}</td>
		<td>${orderItem.quantity}</td>
	</tr>
		<#if orderItem.composition.hasDefaultVariant()>
	<tr>
		<td colspan="3"><#list orderItem.composition.variants><#items as variant><#if
			variant.alias?? && !variant.defaultSetting><span>&nbsp;&nbsp;${variant.alias} -</span></#if></#items></#list></td>
	</tr>
	</#if>
	<#if orderItem.composition.products?? && orderItem.composition.products?has_content>
	<tr>
		<td colspan="3"><#list orderItem.composition.products><#items as product><#if
			product.product.name??><span>&nbsp;&nbsp;${product.product.name} -</span></#if></#items></#list></td>
	</tr>
	</#if>
	<#if orderItem.composition.addons?? && orderItem.composition.addons?has_content>
	<tr>
		<td colspan="3">&nbsp;&nbsp;<#list orderItem.composition.addons><#items as addon><#if
			addon.product.shortCode??><span>${addon.product.shortCode}, </span></#if></#items></#list></td>
	</tr>
	</#if>
	<#if orderItem.composition.menuProducts?? && orderItem.composition.menuProducts?has_content>
		<#list orderItem.composition.menuProducts><#items as menuProduct>
	<tr>
		<td><span>(C) ${menuProduct.productName}</span></td>
		<td>${menuProduct.dimension?substring(0,1)}</td>
		<td>${menuProduct.quantity}</td>
	</tr>
	<#if menuProduct.composition.hasDefaultVariant()>
	<tr>
		<td colspan="3"><#list menuProduct.composition.variants><#items as variant><#if
			variant.alias?? && !variant.defaultSetting><span>&nbsp;&nbsp;&nbsp;&nbsp;${variant.alias} -</span></#if></#items></#list></td>
	</tr>
	</#if>
	<#if menuProduct.composition.products?? && menuProduct.composition.products?has_content>
	<tr>
		<td colspan="3"><#list menuProduct.composition.products><#items as product><#if
			product.product.name??><span>&nbsp;&nbsp;&nbsp;&nbsp;${product.product.name} -</span></#if></#items></#list></td>
	</tr>
	</#if>
	<#if menuProduct.composition.addons?? && menuProduct.composition.addons?has_content>
	<tr>
		<td colspan="3">&nbsp;&nbsp;&nbsp;&nbsp;<#list menuProduct.composition.addons><#items as addon><#if
			addon.product.shortCode??><span>${addon.product.shortCode}, </span></#if></#items></#list></td>
	</tr>
	</#if>
		</#items></#list>
	</#if>
	
	</#items></#list>
</table>
