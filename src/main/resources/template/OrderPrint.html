<#setting locale="en_US"><#setting date_format="dd/MM/yyyy HH:mm">
<style>
* {
	font-family: sans-serif;
}
</style>
<#if (orderSource)??>
	<center><b style="font-size: 18px">${orderSource}</b></center>
</#if>
<center><i style="font-size: 13px">For blazing fast deliveries & LoyalTea benefits</i></center>
	 <center>Order Online</center>
	<center><b>www.chaayos.com</b></center>
	<#if (order.tokenNumber)?? && (order.tokenNumber > 0) && (order.source != "COD")>
		<center><b>Token : ${order.tokenNumber}</b></center>
	</#if>
<table style="font-size: 12px; width:100%;" cellpadding="0" cellspacing="2">
	<#if isNonProdEnvironment>
	<tr>
		<td><b>This Bill Is Not Valid</b></td>
	</tr>
	</#if>
	<#if isOrderReprint>
	<tr>
		<td><b>Duplicate Copy Of The Bill</b></td>
	</tr>
	</#if>
	<tr>
		<td>${unit.referenceName}</td>
	</tr>
	<tr>
		<td>${unit.address.line1}</td>
	</tr>
	<#if (unit.address.line2)?? && unit.address.line2 != "">
	<tr>
		<td>${unit.address.line2}</td>
	</tr>
	</#if>
	<#if (unit.address.line3)?? && unit.address.line3 != "">
	<tr>
		<td>${unit.address.line3}</td>
	</tr>
	</#if>
	<tr>
		<td>
			<table style="font-size: 12px; margin-left: -2;">
				<tr>
					<td>${unit.address.city}</td><td>, </td><td>${unit.address.state}</td>
				</tr>
			</table>
		</td>
	</tr>
</table>
<hr style="height:2px;border:none;color:#000;background-color:#000;" />
<table style="font-size: 12px; width:100%;" cellpadding="0" cellspacing="4">
	<#if isOrderReprint>
	<tr>
		<td><b>Duplicate Copy</b></td>
		<td></td>
	</tr>
	</#if>
	<#if (unit.tableService) && (order.tableNumber)??>
		<tr>
			<td style="font-size: 14px"><b>Table No </b></td>
			<td style="font-size: 14px"><b>${order.tableNumber}</b></td>
		</tr>
	</#if>
	<tr>
		<td>Order No </td>
		<td>${order.generateOrderId}</td>
	</tr>
	<tr>
		<td>Order Time </td>
		<td>${order.billCreationTime?date}</td>
	</tr>
	<#if (order.customerName)??>
	<tr>
		<td>Name </td>
		<td>${order.customerName}</td>
	</tr>
	</#if>
	<#if (customer.id) gt 5 >
	<tr>
		<td>Contact </td>
		<td>${customer.countryCode}-${customerContact}</td>
	</tr>
	<tr>
		<td>Loyalty Points </td>
		<td>${customer.loyaltyPoints}</td>
	</tr>
	<#if order.pointsRedeemed gt 0>
	<tr>
		<td>Points Redeemed </td>
		<td>${order.pointsRedeemed}</td>
	</tr>
	</#if>
	<#if (order.offerCode)?? && order.offerCode != "">
	<tr>
		<td>Coupon Applied </td>
		<td>${order.offerCode}</td>
	</tr>
	</#if>
	</#if>
	<#if (order.tempCode)?? && order.tempCode != "" && order.source != "COD">
	<tr>
		<td>Free Wifi Code </td>
		<td>${order.tempCode}</td>
	</tr>
	</#if>	
	<#if (order.tokenNumber)?? && (order.tokenNumber > 0) && (order.source != "COD")>
	<tr>
		<td>Token Number </td>
		<td>${order.tokenNumber}</td>
	</tr>
	</#if><#if isNonProdEnvironment>
	<tr>
		<td><b>This Bill Is Not Valid</b></td>
		<td></td>
	</tr>
	</#if><#if isOrderCancelled>
	<tr>
		<td><b>Cancelled Bill</b></td>
		<td></td>
	</tr>
	<tr>
		<td><b>This Bill is Not Valid</b></td>
		<td></td>
	</tr>
	</#if>
</table>
<hr style="height:2px;border:none;color:#000;background-color:#000;" />
<table style="font-size: 12px; width:100%;" cellpadding="0" cellspacing="4" >
	<tr>
		<td style="width:60%;">Item</td>
		<td style="width:15%;" align="center">Qty</td>
		<td style="width:15%;" align="center">Price</td>
		<td style="width:20%;" align="center">Amount</td>
	</tr>
	<#list order.orders> <#items as orderItem>
	<tr>
		<td colspan="2">
		${orderItem.code}
		</td>
		<td></td>
		<td></td>
	</tr>
	<tr>
		<td>
			<#if ((orderItem.complimentaryDetail)?? && (orderItem.complimentaryDetail.isComplimentary))>
			*
			<#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.discountCode)?? && (orderItem.discountDetail.discountCode > 0))>
			*
			<#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.promotionalOffer)?? && (orderItem.discountDetail.promotionalOffer > 0))>
			*
			</#if>
			
			${orderItem.productName}
			<#if orderItem.dimension != "None">
				${fillWhiteSpace(orderItem.dimension)}
			</#if>
		</td>
		<td align="center">${orderItem.quantity}</td>
		<td align="center">${orderItem.price}</td>
		<td align="center">${orderItem.amount}</td>
	</tr>
	
	<#if orderItem.productCategory.id == 8> <#list
	orderItem.composition.menuProducts><#items as menuItem>
	<tr>
		<td colspan="3">&nbsp;*(${menuItem.code}) ${menuItem.productName}
			<#if menuItem.dimension != "None"> ${fillWhiteSpace(menuItem.dimension)}</#if></td>
		<td></td>
	</tr>
	</#items>
	</#list> </#if>
	</#items></#list> 
</table>
<hr style="height:2px;border:none;color:#000;background-color:#000;" />
<#if isNonProdEnvironment>
<p style="font-size: 12px;">
	<b>This Bill Is Not Valid</b>
</p>
</#if>
<table style="font-size: 12px; width:100%;"  cellpadding="0" cellspacing="2" >
	<tr>
		<td style="width:80%;">Total</td>
		<td>${order.transactionDetail.totalAmount}</td>
	</tr>
	<#if promotionalDiscount gt 0>
	<tr>
		<td>Promotional Offer</td>
		<td>${promotionalDiscount}</td>
	</tr>
	</#if>
	<#if (discountPercent)?? && discountPercent gt 0>
	<tr>
		<td>Discount @ ${discountPercent}%</td>
		<td>${discountValue}</td>
	</tr>
	</#if>
	<#list order.transactionDetail.taxes> <#items as tax>
	<tr>
		<td>${tax.code} @ ${tax.percentage} %</td>
		<td>${tax.value}</td>
	</tr>
	</#items></#list> 
	<#if order.transactionDetail.roundOffValue != 0>
	<tr>
		<td>Round Off</td>
		<td>${order.transactionDetail.roundOffValue}</td>
	</tr>
	</#if>
	<tr>
		<td><b>Bill Total</b></td>
		<td><b>${order.transactionDetail.paidAmount}</b></td>
	</tr>
	<tr>
		<td colspan="4"><b>Rs. ${paidAmountInWords}</b></td>
	</tr>
</table>
<hr style="height:2px;border:none;color:#000;background-color:#000;" />
<table style="font-size: 12px; width:100%;" cellpadding="0" cellspacing="2">
	<#list settlements> 
		<#items as settlement>
		<tr>
			<#if settlement.modeDetail.category == "ONLINE">
				<td  style="width:80%;"><b>Online</b></td>
			<#else>
				<td  style="width:80%;"><b>${settlement.modeDetail.description}</b></td>
			</#if>
			<td>${settlement.amount}</td>
		</tr>
		</#items>
	</#list>
</table>
<hr style="height:2px;border:none;color:#000;background-color:#000;" />
<#if (order.transactionDetail.savings)?? && order.transactionDetail.savings != 0>
<table style="font-size: 12px; width:100%;" cellpadding="0" cellspacing="2">
	<tr>
		<td style="width:80%;"><b>Net Savings</b></td>
		<td>${order.transactionDetail.savings}</td>
	</tr>
</table>
<hr style="height:2px;border:none;color:#000;background-color:#000;" />
</#if>
<table style="font-size: 12px; width:100%;" cellpadding="0" cellspacing="2">
	<tr>
		<td style="width:20%;">CIN </td>
		<td>U55204DL2012PTC304447</td>
	</tr>
	<tr>
		<td style="width:20%;">GSTIN</td>
		<td>${unit.tin}</td>
	</tr>
</table>
<table style="font-size: 12px; width:100%;" cellpadding="0" cellspacing="2">
	<tr>
		<td>Sunshine Teahouse Private Limited</td>
	</tr>
	<tr>
		<td>1st Fl, #382 , 100' Rd, Ghitorni</td>
	</tr>
	<!--<tr>
		<td>100' Rd, Ghitorni</td>
	</tr>-->
	<tr>
		<td>New Delhi 30</td>
	</tr>
	<tr>
		<td></td>
	</tr>
	<#if billPromotion??>
	<tr>
		<td><i><b>${billPromotion}</b></i></td>
	</tr>
	</#if>
	<#if (order.tempCode)?? && order.tempCode != "" && order.source != "COD">
	<tr>
		<td>Free Wifi is subject to availability</td>
	</tr>
	</#if>
	<tr>
		<td>experiments with chai</td>
	</tr>
</table>
<!-- <img src="http://prod.kettle.chaayos.com:9797/kettle-service/images/offer-android.png"> -->
