spring.application.name=transaction-service
spring.config.import=optional:configserver:${config.url}


spring.cloud.config.label = ${spring.profiles.active}


management.endpoints.web.exposure.include=refresh
spring.cloud.config.username=pakoda
spring.cloud.config.password=CozyChaiMoment!

# GST Text Configuration
gst.text.description=GST is applicable as per government regulations. For any queries regarding GST, please contact our customer support.
gst.text.show=true






