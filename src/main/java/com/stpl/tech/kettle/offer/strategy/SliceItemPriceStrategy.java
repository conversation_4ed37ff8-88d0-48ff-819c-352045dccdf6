package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.TransactionConstants;
import com.stpl.tech.master.domain.model.*;
import lombok.extern.log4j.Log4j2;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Log4j2
public class SliceItemPriceStrategy extends FlatItemStrategy{
    StringBuffer offerMessage = new StringBuffer();
    String messageTemplate = "Applied discount of %.2f on %s <br/>";

    private static final Integer MULTIPLIER_FACTOR = 3;


    @Override
    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, ProductCache productCache
            , Map<String, OrderItem> foundItems) throws OfferValidationException {
        log.info("Applying percentage discount to the offer order");
        return applyDiscountStrategy(coupon, order, productCache);
    }

    @Override
    public OrderItem addDiscountDetails(OrderItem orderItem, BigDecimal discountInOffer, OfferDetail offer, int counter) {
        log.info("add DiscountDetails of percent item BOGO strategy");
        BigDecimal totalAmount = getTotalAmountOfItem(orderItem.getPrice(), orderItem.getQuantity());
        return getDiscountItem(orderItem, discountInOffer, totalAmount, orderItem.getQuantity());

    }

    private OrderItem getDiscountItem(OrderItem orderItem, BigDecimal discountInOffer, BigDecimal totalAmount, int quantity) {
        if (orderItem.getPrice().compareTo(discountInOffer) < 0 || orderItem.getBillType().equals(BillType.MRP)) {
            return orderItem;
        }
        BigDecimal discountOnPrice = orderItem.getPrice().multiply(BigDecimal.valueOf(quantity))
                .setScale(2, RoundingMode.HALF_UP);
        BigDecimal overallOfferValue = discountInOffer.multiply(BigDecimal.valueOf(quantity));
        BigDecimal valueDiscount = AppUtils.subtract(discountOnPrice, overallOfferValue).setScale(2, RoundingMode.HALF_UP);
        BigDecimal effectivePercentage = AppUtils.percentage(valueDiscount, totalAmount);
        offerMessage.append(String.format(messageTemplate, valueDiscount.floatValue(), orderItem.getProductName()));
        return getModifiedItem(orderItem, effectivePercentage, valueDiscount);
    }

    protected void updateOrderDiscount(OfferOrder offerOrder, String code, BigDecimal discountPercentage) {

        log.info("Updating order with coupon code:{}", code);
        DiscountDetail discountDetail = getDiscountDetail(code, discountPercentage);
        BigDecimal offerValue = AppUtils.percentOf(discountDetail.getDiscount().getPercentage(),
                offerOrder.getOrder().getTransactionDetail().getTotalAmount());
        discountDetail.setTotalDiscount(offerValue);
        if (offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer() != null) {
            discountDetail.setTotalDiscount(discountDetail.getTotalDiscount()
                    .add(offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer()));
        }
        discountDetail.getDiscount().setValue(offerValue);
        offerOrder.getOrder().getTransactionDetail().setDiscountDetail(discountDetail);
    }

    private DiscountDetail getDiscountDetail(String code, BigDecimal discountPercentage) {
        PercentageDetail percentageDetail = new PercentageDetail();
        percentageDetail.setPercentage(discountPercentage);
        DiscountDetail discountDetail = new DiscountDetail();
        discountDetail.setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
        discountDetail.setDiscountReason(code);
        discountDetail.setDiscount(percentageDetail);
        discountDetail.setPromotionalOffer(BigDecimal.ZERO);
        return discountDetail;
    }

    @Override
    public OfferOrder applyDiscountStrategy(CouponDetail coupon, OfferOrder order,
                                            ProductCache productCache) throws OfferValidationException {
        OfferDetail offer = coupon.getOffer();
        Map<OfferMetaDataType, Set<String>> mappings = makeMapOfMappings(offer.getMetaDataMappings());
        Map<Integer, OrderItem> productDiscountMap = new HashMap<>();
        Set<ProductBasicDetail> productDetails = new HashSet<>();
        for (OrderItem item : order.getOrder().getOrders()) {
            int productId = item.getProductId();
            productDiscountMap.put(productId, item);
            productDetails.add(productCache.getProductBasicDetailById(productId));
        }
        for (OfferMetaDataType mappingKey : mappings.keySet()) {
            Optional<List<Integer>> commonListProduct = Optional
                    .ofNullable(mappingKey.getCommonElements(mappings.get(mappingKey), productDetails));
            Set<Integer> commonList = new HashSet<>();
            if (commonListProduct.isPresent()) {
                for (Integer id : commonListProduct.get()) {
                    commonList.add(id);
                }
                if (commonList.size() > 0) {
                    List<Integer> commonElementList = new ArrayList<>();
                    for (Integer id : commonList) {
                        commonElementList.add(id);
                    }
                    commonElementList = sortCommonList(productDiscountMap, commonElementList);
                    for (Integer product : commonElementList) {
                        for (OrderItem item : order.getOrder().getOrders()) {
                            if (item.getProductId() == product) {
                                OrderItem modifiedItem = addDiscountDetails(item,
                                        BigDecimal.valueOf(offer.getOfferValue()), offer, 0);
                                addDiscountReason(modifiedItem, coupon);
                            }
                        }
                    }
                }
            }
        }
        order.setAppliedOfferMessage(getOfferMessage());
        updateOrderDiscount(order, coupon.getCode(), null);
        return order;
    }

}
