package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.master.domain.model.CouponDetail;

import java.math.BigDecimal;
import java.util.Map;

public interface OfferActionStrategy {

    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon,ProductCache productCache,
                                    Map<String, OrderItem> foundItems) throws OfferValidationException;

    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, ProductCache productCache
            , Map<String, OrderItem> foundItems, BigDecimal offerValue) throws OfferValidationException;

    DiscountDetail getDiscountDetail(String couponCode, BigDecimal discount, BigDecimal totalAmount,
                                     BigDecimal paidAmount, BigDecimal maxDiscountValue);
}
