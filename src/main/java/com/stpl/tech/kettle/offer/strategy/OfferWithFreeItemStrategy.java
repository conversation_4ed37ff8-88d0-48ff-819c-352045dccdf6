package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.model.*;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.kettle.exceptions.WebErrorCode;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferValueType;
import com.stpl.tech.master.domain.model.Product;
import org.apache.commons.lang3.NotImplementedException;

import java.math.BigDecimal;
import java.util.Map;

public class OfferWithFreeItemStrategy extends AbstractBillStrategy implements OfferActionStrategy{

    StringBuffer offerMessage = new StringBuffer();
    String discountTemplate = "Applied discount of %d percent.&nbsp;";
    String messageTemplate = "Added %d %s for <b>free</b> <br/>";

    @Override
    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, ProductCache productCache,
                                    Map<String, OrderItem> foundItems, BigDecimal offerValue) throws OfferValidationException {
        if (offerValue == null) {
            return applyStrategy(order, coupon, productCache, foundItems);
        } else {
            throw new NotImplementedException("Not Implemented the method for Offer WIth Free Item Strategy");
        }
    }


    /**
     * FLAT BILL = Apply flat value discount to this order.
     * <p>
     * Here we will update the discount details of the order. Rest of the
     * calculations will be done by the UI before punching of the order.
     *
     * @throws OfferValidationException
     *
     */
    @Override
    public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, ProductCache productCache,
                                    Map<String, OrderItem> foundItems)
            throws OfferValidationException {

        if (coupon.getOffer().getOfferWithFreeItem() == null) {
            throw new OfferValidationException("Offer With Free Item Values Not Defined For  " + coupon.getCode(),
                    WebErrorCode.INSUFFICIENT_DATA);
        }
        boolean hasProductInList = false;
        boolean hasProductDimension = false;
        BigDecimal offerItemAmount = null;
        BigDecimal offerItemTax = null;
        BigDecimal productTaxableAmount = BigDecimal.ZERO;
        boolean needSplit = false;
        int splitIntdex = -1;
        int count = 0;
        for (OrderItem item : offerOrder.getOrder().getOrders()) {
            if (item.getProductId() == coupon.getOffer().getOfferWithFreeItem().getProductId()) {
                hasProductInList = true;
                if (item.getDimension().equals(coupon.getOffer().getOfferWithFreeItem().getDimension())) {
                    hasProductDimension = true;
                }
                if (hasProductInList && hasProductDimension
                        && item.getQuantity() == coupon.getOffer().getOfferWithFreeItem().getQuantity()) {
                    needSplit = false;
                    splitIntdex = count;
                    break;
                } else if (hasProductInList && hasProductDimension
                        && item.getQuantity() > coupon.getOffer().getOfferWithFreeItem().getQuantity()) {
                    needSplit = true;
                    splitIntdex = count;
                }
            }
            count++;
        }

        if (splitIntdex != -1) {
            OrderItem item = offerOrder.getOrder().getOrders().get(splitIntdex);
            DiscountDetail comp = new DiscountDetail();
            comp.setDiscountReason(coupon.getCode());
            PercentageDetail percentage = new PercentageDetail();
            percentage.setPercentage(BigDecimal.ZERO);
            percentage.setValue(BigDecimal.ZERO);
            comp.setDiscount(percentage);
            item.setDiscountDetail(comp);
            if (needSplit) {
                offerItemAmount = AppUtils.multiply(item.getPrice(),
                        new BigDecimal(coupon.getOffer().getOfferWithFreeItem().getQuantity()));
                BigDecimal perUnitTax = AppUtils.divide(item.getTax(), new BigDecimal(item.getQuantity()));
                item.setTax(AppUtils.multiply(perUnitTax,
                        new BigDecimal(item.getQuantity() - coupon.getOffer().getOfferWithFreeItem().getQuantity())));
                item.setAmount(AppUtils.subtract(item.getAmount(), offerItemAmount));
                productTaxableAmount = productTaxableAmount.add(item.getPrice()
                        .multiply(new BigDecimal(coupon.getOffer().getOfferWithFreeItem().getQuantity())));
            } else {
                offerItemAmount = item.getTotalAmount();
                offerItemTax = item.getTax();
                item.setAmount(BigDecimal.ZERO);
                productTaxableAmount = productTaxableAmount
                        .add(item.getPrice().multiply(new BigDecimal(item.getQuantity())));

            }

            comp.setPromotionalOffer(offerItemAmount);
        }
        if (!hasProductInList) {
            Product product = productCache.getProductById(coupon.getOffer().getOfferWithFreeItem().getProductId());
            String dimension = coupon.getOffer().getOfferWithFreeItem().getDimension();
            throw new OfferValidationException(
                    "Missing Free Item In Order. Please add  " + product.getName() + ", " + dimension,
                    WebErrorCode.PRODUCT_NOT_FOUND);
        }

        if (!hasProductDimension) {
            throw new OfferValidationException(
                    "Offer Valid Only for " + coupon.getOffer().getOfferWithFreeItem().getDimension() + " Dimension",
                    WebErrorCode.PRODUCT_NOT_FOUND);
        }
        if (AppUtils
                .subtract(offerOrder.getOrder().getTransactionDetail().getPaidAmount(),
                        AppUtils.add(offerItemAmount, offerItemTax))
                .compareTo(new BigDecimal(coupon.getOffer().getMinValue())) < 0) {
            throw new OfferValidationException(
                    "Offer is only for order with minimum value  :  " + coupon.getOffer().getMinValue(),
                    WebErrorCode.MINIMUM_ORDER_VALUE);
        }

        offerMessage.append(String.format(messageTemplate, coupon.getOffer().getOfferWithFreeItem().getQuantity(),productCache
                .getProductBasicDetailById(coupon.getOffer().getOfferWithFreeItem().getProductId()).getDetail().getName()));

        BigDecimal discountPercentage = BigDecimal.ZERO;

        if (BigDecimal.ZERO.compareTo(coupon.getOffer().getOfferWithFreeItem().getValue()) != 0) {

            discountPercentage = coupon.getOffer().getOfferWithFreeItem().getValue();

            if (!OfferValueType.PERCENTAGE.equals(coupon.getOffer().getOfferWithFreeItem().getType())) {
                TransactionDetail trans = offerOrder.getOrder().getTransactionDetail();
                BigDecimal flatDiscount = coupon.getOffer().getOfferWithFreeItem().getValue();
                if (flatDiscount.compareTo(trans.getPaidAmount()) > 0) {
                    flatDiscount = trans.getPaidAmount();
                }

                BigDecimal taxRate = trans.getPaidAmount().divide(trans.getTaxableAmount(), 10,
                        BigDecimal.ROUND_HALF_UP);
                BigDecimal calculatedDiscount = flatDiscount.divide(taxRate, 10, BigDecimal.ROUND_HALF_UP);
                discountPercentage = AppUtils.percentage(calculatedDiscount,
                        trans.getTaxableAmount().subtract(productTaxableAmount), 10);
                offerMessage.append(String.format(" Applied Flat discount of Rs.%d on order", flatDiscount.intValue()));
            } else {
                discountPercentage = coupon.getOffer().getOfferWithFreeItem().getValue();
                offerMessage.append(String.format(" Applied discount of %.2f percent on order ", discountPercentage));
            }

            updateOrderItemDiscount(offerOrder, coupon.getCode(), discountPercentage);
        }

        offerOrder.setAppliedOfferMessage(getOfferMessage());
        offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getDiscount()
                .setPercentage(discountPercentage);
        return offerOrder;

    }


    private void copyAndReduce(OrderItem i, OrderItem n, int quantity) {
        BigDecimal perUnitTax = AppUtils.divide(i.getTax(), new BigDecimal(i.getQuantity()));
        n.setBillType(i.getBillType());
        n.setBookedWastage(i.getBookedWastage());
        n.setCardType(i.getCardType());
        n.setCode(i.getCode());
        n.setComplimentaryDetail(copy(i.getComplimentaryDetail()));
        n.setComposition(i.getComposition());
        n.setDimension(i.getDimension());
        n.setDiscountDetail(copy(i.getDiscountDetail()));
        n.setItemCode(i.getItemCode());
        n.setPrice(i.getPrice());
        n.setProductCategory(i.getProductCategory());
        n.setQuantity(quantity);
        n.setReasonId(i.getReasonId());
        n.setTakeAway(i.getTakeAway());
        n.setProductId(i.getProductId());
        n.setProductName(i.getProductName());
        n.setProductSubCategory(i.getProductSubCategory());
        n.setAmount(AppUtils.multiply(n.getPrice(), new BigDecimal(n.getQuantity())));
        n.setTotalAmount(AppUtils.multiply(n.getPrice(), new BigDecimal(n.getQuantity())));
        n.setTax(AppUtils.multiply(perUnitTax, new BigDecimal(n.getQuantity())));
        i.setQuantity(i.getQuantity() - quantity);
        i.setAmount(AppUtils.multiply(i.getPrice(), new BigDecimal(i.getQuantity())));
        i.setTotalAmount(AppUtils.multiply(i.getPrice(), new BigDecimal(i.getQuantity())));
        i.setTax(AppUtils.multiply(perUnitTax, new BigDecimal(i.getQuantity())));

    }



    private DiscountDetail copy(DiscountDetail i) {
        if (i == null) {
            return null;
        }
        DiscountDetail d = new DiscountDetail();
        d.setDiscount(i.getDiscount());
        d.setDiscountCode(i.getDiscountCode());
        d.setDiscountReason(i.getDiscountReason());
        d.setPromotionalOffer(i.getPromotionalOffer());
        d.setTotalDiscount(i.getTotalDiscount());
        return d;
    }


    private ComplimentaryDetail copy(ComplimentaryDetail i) {
        if (i == null) {
            return null;
        }
        ComplimentaryDetail c = new ComplimentaryDetail();
        c.setIsComplimentary(i.isIsComplimentary());
        c.setReason(i.getReason());
        c.setReasonCode(i.getReasonCode());
        return c;
    }

    @Override
    public String getOfferMessage() {
        return offerMessage.toString();
    }


    @Override
    public DiscountDetail getDiscountDetail(String couponCode, BigDecimal discount, BigDecimal totalAmount,
                                            BigDecimal paidAmount, BigDecimal maxDiscountValue) {
        return null;
    }

}
