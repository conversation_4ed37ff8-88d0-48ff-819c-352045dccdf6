package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.kettle.exceptions.WebErrorCode;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.TransactionConstants;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.OfferMetaDataType;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import lombok.extern.log4j.Log4j2;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;


@Log4j2
public class PercentageItemBogoStrategy extends PercentageItemStrategy{

    StringBuffer offerMessage = new StringBuffer();
    String messageTemplate = "Applied discount of %.2f on %s <br/>";

    private static final Integer MULTIPLIER_FACTOR=3;


    @Override
    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon,ProductCache productCache
            , Map<String, OrderItem> foundItems) throws OfferValidationException {
        log.info("Applying percentage discount to the offer order");
        return applyDiscountStrategy(coupon, order, productCache);
    }

    @Override
    public OrderItem addDiscountDetails(OrderItem orderItem, BigDecimal discountInOffer, OfferDetail offer, int counter) {
        log.info("add DiscountDetails of percent item BOGO strategy");
        BigDecimal totalAmount = getTotalAmountOfItem(orderItem.getPrice(), orderItem.getQuantity());
        if (counter >= orderItem.getQuantity()) {
            return getDiscountItem(orderItem, discountInOffer, totalAmount, orderItem.getQuantity());
        } else {
            return getDiscountItem(orderItem, discountInOffer, totalAmount, counter);
        }
    }

    private OrderItem getDiscountItem(OrderItem orderItem, BigDecimal discountInOffer, BigDecimal totalAmount, int quantity) {
        BigDecimal discountOnPrice = orderItem.getPrice().multiply(BigDecimal.valueOf(quantity))
                .setScale(2, RoundingMode.HALF_UP);
        BigDecimal valueDiscount = AppUtils.percentOf(discountInOffer, discountOnPrice);
        BigDecimal effectivePercentage = AppUtils.percentage(valueDiscount, totalAmount);
        offerMessage.append(String.format(messageTemplate, valueDiscount.floatValue(), orderItem.getProductName()));
        return getModifiedItem(orderItem, effectivePercentage, valueDiscount);
    }

    protected void updateOrderDiscount(OfferOrder offerOrder, String code, BigDecimal discountPercentage) {

        log.info("Updating order with coupon code:{}", code);
        DiscountDetail discountDetail = getDiscountDetail(code, discountPercentage);
        BigDecimal offerValue = AppUtils.percentOf(discountDetail.getDiscount().getPercentage(),
                offerOrder.getOrder().getTransactionDetail().getTotalAmount());
        discountDetail.setTotalDiscount(offerValue);
        if (offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer() != null) {
            discountDetail.setTotalDiscount(discountDetail.getTotalDiscount()
                    .add(offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer()));
        }
        discountDetail.getDiscount().setValue(offerValue);
        offerOrder.getOrder().getTransactionDetail().setDiscountDetail(discountDetail);
    }

    private DiscountDetail getDiscountDetail(String code, BigDecimal discountPercentage) {
        PercentageDetail percentageDetail = new PercentageDetail();
        percentageDetail.setPercentage(discountPercentage);
        DiscountDetail discountDetail = new DiscountDetail();
        discountDetail.setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
        discountDetail.setDiscountReason(code);
        discountDetail.setDiscount(percentageDetail);
        discountDetail.setPromotionalOffer(BigDecimal.ZERO);
        return discountDetail;
    }

    @Override
    public OfferOrder applyDiscountStrategy(CouponDetail coupon, OfferOrder order, ProductCache productCache) throws OfferValidationException {
        OfferDetail offer = coupon.getOffer();
        Map<OfferMetaDataType, Set<String>> mappings = makeMapOfMappings(offer.getMetaDataMappings());
        Map<Integer, OrderItem> productDiscountMap = new HashMap<>();
        Set<ProductBasicDetail> productDetails = new HashSet<>();
        for (OrderItem item : order.getOrder().getOrders()) {
            int productId = item.getProductId();
            productDiscountMap.put(productId, item);
            productDetails.add(productCache.getProductBasicDetailById(productId));
        }
        for (OfferMetaDataType mappingKey : mappings.keySet()) {
            Optional<List<Integer>> commonListProduct = Optional
                    .ofNullable(mappingKey.getCommonElements(mappings.get(mappingKey), productDetails));
            Set<Integer> commonList = new HashSet<>();
            if (commonListProduct.isPresent()) {
                for (Integer id : commonListProduct.get()){
                    commonList.add(id);
                }
                int applicableQuantity = 0;
                TreeMap<BigDecimal, List<OrderItem>> productMapCountMap = new TreeMap<>();
                for (OrderItem item : order.getOrder().getOrders()) {
                    if (commonList.contains(item.getProductId())) {
                        applicableQuantity = applicableQuantity + item.getQuantity();
                        if (!productMapCountMap.containsKey(item.getPrice())) {
                            productMapCountMap.put(item.getPrice(), new ArrayList<>(Arrays.asList(item)));
                        } else {
                            List<OrderItem> items = productMapCountMap.get(item.getPrice());
                            items.add(item);
                            productMapCountMap.put(item.getPrice(), items);
                        }
                    }
                }
                int minValue = offer.getMinQuantity() * 2;
                int maxValue = offer.getMinQuantity() * 2 * MULTIPLIER_FACTOR;//property
                if (applicableQuantity < minValue) {
                    throw new OfferValidationException("Minimum Item Count " + applicableQuantity + " Doesn't match required quantity " + minValue + " for the offer.",
                            WebErrorCode.MINIMUM_ORDER_VALUE);
                }
                if (commonList.size()>0) {
                    List<Integer> commonElementList = new ArrayList<>();
                    for(Integer id : commonList){
                        commonElementList.add(id);
                    }
                    commonElementList = sortCommonList(productDiscountMap, commonElementList);
                    int offerCount = Math.min(applicableQuantity, maxValue) / minValue;
                    int counter = offerCount * offer.getMinQuantity();
                    for (Integer product : commonElementList) {
                        for (Map.Entry<BigDecimal, List<OrderItem>> entry : productMapCountMap.entrySet()) {
                            if (counter <= 0) {
                                break;
                            }
                            for (OrderItem item : entry.getValue()) {
                                if (item.getProductId() == product) {
                                    OrderItem modifiedItem = addDiscountDetails(item,
                                            BigDecimal.valueOf(offer.getOfferValue()), offer, counter);
                                    if (counter >= item.getQuantity()) {
                                        counter = counter - item.getQuantity();
                                    } else {
                                        counter = 0;
                                    }
                                    addDiscountReason(modifiedItem, coupon);
                                }
                            }
                        }
                        if (counter <= 0) {
                            break;
                        }
                    }
                }
            }
        }
        order.setAppliedOfferMessage(getOfferMessage());
        updateOrderDiscount(order, coupon.getCode(), null);
        return order;
    }
}
