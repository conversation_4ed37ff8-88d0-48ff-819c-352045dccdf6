package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.master.domain.model.CouponDetail;

import java.math.BigDecimal;
import java.util.Map;

public class FlatBillStrategy extends AbstractBillStrategy implements OfferActionStrategy{
    StringBuffer offerMessage = new StringBuffer();
    String messageTemplate = "Applied Flat discount of Rs.%d on order <br/>";

    /**
     * FLAT BILL = Apply flat value discount to this order.
     * <p>
     * Here we will update the discount details of the order. Rest of the
     * calculations will be done by the UI before punching of the order.
     *
     * @throws OfferValidationException
     *
     */
    @Override
    public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, ProductCache productCache,
                                    Map<String, OrderItem> foundItems)
            throws OfferValidationException {

        BigDecimal flatDiscount = new BigDecimal(coupon.getOffer().getOfferValue());

        return applyStrategy(String.format(messageTemplate, flatDiscount.intValue()), flatDiscount, offerOrder, coupon,
                productCache);

    }

    @Override
    public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, ProductCache productCache,
                                    Map<String, OrderItem> foundItems, BigDecimal offerValue)
            throws OfferValidationException {

        return applyStrategy(String.format(messageTemplate, offerValue.intValue()), offerValue, offerOrder, coupon, productCache);

    }

    protected OfferOrder applyStrategy(String message, BigDecimal flatDiscount, OfferOrder offerOrder, CouponDetail coupon,
                                      ProductCache productCache) {
        TransactionDetail trans = offerOrder.getOrder().getTransactionDetail();

        if (flatDiscount.compareTo(trans.getPaidAmount()) > 0) {
            flatDiscount = trans.getPaidAmount();
        }

        BigDecimal taxRate = trans.getPaidAmount().divide(trans.getTaxableAmount(), 10, BigDecimal.ROUND_HALF_UP);
        BigDecimal calculatedDiscount = flatDiscount.divide(taxRate, 10, BigDecimal.ROUND_HALF_UP);
        BigDecimal discountPercentage = AppUtils.percentage(calculatedDiscount, trans.getTaxableAmount(), 10);
        offerMessage.append(message);
        updateOrderDiscount(offerOrder, coupon.getCode(), discountPercentage);
        updateOrderItemDiscount(offerOrder, coupon.getCode(), discountPercentage);
        offerOrder.setAppliedOfferMessage(getOfferMessage());
        return offerOrder;
    }

    @Override
    public String getOfferMessage() {
        return offerMessage.toString();
    }

    @Override
    public DiscountDetail getDiscountDetail(String couponCode, BigDecimal discount, BigDecimal totalAmount,
                                            BigDecimal paidAmount, BigDecimal maxDiscountValue){
        if (discount.compareTo(paidAmount) > 0) {
            discount = paidAmount;
        }

        BigDecimal taxRate = paidAmount.divide(totalAmount, 10, BigDecimal.ROUND_HALF_UP);
        BigDecimal calculatedDiscount = discount.divide(taxRate, 10, BigDecimal.ROUND_HALF_UP);
        BigDecimal discountPercentage = AppUtils.percentage(calculatedDiscount, totalAmount, 10);
        return getOrderDiscount(couponCode, discountPercentage, totalAmount);
    }
}
