package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.util.Constants.TransactionConstants;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

public class PercentageItemMaxCapStrategy extends PercentageItemStrategy implements OfferActionStrategy{
    @Autowired
    AbstractItemStrategy abstractItemStrategy;

    @Override
    public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, ProductCache productCache,
                                    Map<String, OrderItem> foundItems) {
        Optional<BigDecimal> maxCappedDiscount = Optional.ofNullable(coupon.getOffer().getMaxDiscountAmount());
        int offerValue = coupon.getOffer().getOfferValue();
        Map<Integer, BigDecimal> productDiscountMap = new HashMap<>();
        BigDecimal calculatedDiscount = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        if (maxCappedDiscount.isPresent()) {
            List<OrderItem> orderItems = offerOrder.getOrder().getOrders();
            List<IdCodeName> offerProducts = coupon.getOffer().getMetaDataMappings();
            if(Objects.nonNull(offerProducts) && Objects.nonNull(orderItems)) {
                for (IdCodeName idCodeName : offerProducts) {
                    for (OrderItem orderItem : orderItems) {
                        if (String.valueOf(orderItem.getProductId()).equals(idCodeName.getCode())) {
                            totalAmount = totalAmount.add(orderItem.getTotalAmount());
                            BigDecimal amount = BigDecimal.valueOf(offerValue).divide(BigDecimal.valueOf(100), 10, BigDecimal.ROUND_HALF_UP);
                            calculatedDiscount = calculatedDiscount.add(orderItem.getTotalAmount().multiply(amount));
                            productDiscountMap.put(orderItem.getProductId(), orderItem.getTotalAmount().multiply(amount));
                        }
                    }
                }
            }
            int flag = calculatedDiscount.setScale(0, RoundingMode.HALF_UP).compareTo(maxCappedDiscount.get());
            if (flag <= 0) {
                //apply the discount percentage to the products for this offer
                for (OrderItem orderItem : offerOrder.getOrder().getOrders()) {
                    getDiscountDetail(orderItem, productDiscountMap, offerValue,coupon);
                }
            } else {
                //change the discount according to the max cap and then apply to the desired products
                BigDecimal percentageOffered = maxCappedDiscount.get().multiply(BigDecimal.valueOf(100)).divide(totalAmount, 2, BigDecimal.ROUND_HALF_UP);
                if(Objects.nonNull(orderItems)) {
                    for (OrderItem orderItem : orderItems) {
                        if (productDiscountMap.containsKey(orderItem.getProductId())) {
                            orderItem.getDiscountDetail().getDiscount().setPercentage(percentageOffered);
                            BigDecimal value = orderItem.getTotalAmount().multiply(percentageOffered).divide(BigDecimal.valueOf(100));
                            orderItem.getDiscountDetail().getDiscount().setValue(value.setScale(0, RoundingMode.HALF_UP));
                            orderItem.getDiscountDetail().setTotalDiscount(value.setScale(0, RoundingMode.HALF_UP));
                            orderItem.getDiscountDetail().setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
                            orderItem.getDiscountDetail().setDiscountReason(coupon.getCode());
                        }
                    }
                }
            }
        }
        offerOrder.setAppliedOfferMessage(getOfferMessage());
        return offerOrder;
    }

    public void getDiscountDetail(OrderItem orderItem, Map<Integer, BigDecimal> productDiscountMap, int offerValue, CouponDetail coupon) {
        if (productDiscountMap.containsKey(orderItem.getProductId())) {
            BigDecimal calculatedDiscount = productDiscountMap.get(orderItem.getProductId());
            orderItem.getDiscountDetail().getDiscount().setValue(calculatedDiscount.setScale(0, RoundingMode.HALF_UP));
            orderItem.getDiscountDetail().getDiscount().setPercentage(BigDecimal.valueOf(offerValue));
            orderItem.getDiscountDetail().setTotalDiscount(calculatedDiscount.setScale(0, RoundingMode.HALF_UP));
            orderItem.getDiscountDetail().setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
            orderItem.getDiscountDetail().setDiscountReason(coupon.getCode());
        }
    }


}
