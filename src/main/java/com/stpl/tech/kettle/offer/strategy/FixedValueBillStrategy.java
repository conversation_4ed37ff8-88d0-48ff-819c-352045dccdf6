package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.master.domain.model.CouponDetail;

import java.math.BigDecimal;
import java.util.Map;

public class FixedValueBillStrategy extends  FlatBillStrategy implements OfferActionStrategy{
    @Override
    public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, ProductCache productCache,
                                    Map<String, OrderItem> foundItems) throws OfferValidationException {

        BigDecimal orderValue = offerOrder.getOrder().getTransactionDetail().getPaidAmount();
        BigDecimal flatDiscount = BigDecimal.ZERO;
        BigDecimal offerValue = new BigDecimal(coupon.getOffer().getOfferValue());

        if (orderValue.compareTo(offerValue) >= 0) {
            flatDiscount = orderValue.subtract(offerValue);
        } else {
            flatDiscount = orderValue;
        }

        return applyStrategy(String.format(messageTemplate, flatDiscount.intValue()), flatDiscount, offerOrder, coupon,
                productCache);

    }
}
