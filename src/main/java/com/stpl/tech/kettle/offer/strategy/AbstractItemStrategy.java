package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.kettle.util.Constants.TransactionConstants;
import com.stpl.tech.master.domain.model.*;

import java.math.BigDecimal;
import java.util.*;

public abstract class AbstractItemStrategy {
    public Map<OfferMetaDataType, Set<String>> makeMapOfMappings(List<IdCodeName> metaDataMappings) {

        Map<OfferMetaDataType, Set<String>> mappingMap = new HashMap<OfferMetaDataType, Set<String>>();
        for (IdCodeName mapping : metaDataMappings) {
            OfferMetaDataType mapKey = OfferMetaDataType.valueOf(mapping.getName());
            Set<String> listOfMappings = mappingMap.get(mapKey);
            if (listOfMappings == null) {
                listOfMappings = new HashSet<String>();
            }
            listOfMappings.add(mapping.getCode());
            mappingMap.put(mapKey, listOfMappings);
        }
        return mappingMap;

    }

    public OfferOrder applyDiscountStrategy(CouponDetail coupon, OfferOrder order, ProductCache productCache) throws OfferValidationException {

        OfferDetail offer = coupon.getOffer();

        Map<OfferMetaDataType, Set<String>> mappings = makeMapOfMappings(offer.getMetaDataMappings());
        Map<Integer, OrderItem> productDiscountMap = new HashMap<Integer, OrderItem>();

        //Set<OrderItem> orderItems = new HashSet<OrderItem>(order.getOrder().getOrders());
        Set<ProductBasicDetail> productDetails = new HashSet<ProductBasicDetail>();

        for (OrderItem item : order.getOrder().getOrders()) {
            int productId = item.getProductId();
            productDiscountMap.put(productId, item);
            productDetails.add(productCache.getProductBasicDetailById(productId));
        }

        for (OfferMetaDataType mappingKey : mappings.keySet()) {
            Optional<List<Integer>> commonList = Optional
                    .ofNullable(mappingKey.getCommonElements(mappings.get(mappingKey), productDetails));

            if (commonList.isPresent()) {
                List<Integer> commonElementList = commonList.get();
                commonElementList = sortCommonList(productDiscountMap, commonElementList);
				/*if (commonElementList.size() > offer.getMinQuantity()) {
					commonElementList = commonElementList.subList(0, offer.getMinQuantity());
				}*/

                int counter = offer.getMinQuantity();
                for(Integer product : commonElementList){
                    for (OrderItem item : order.getOrder().getOrders()) {
                        if (counter <= 0) {
                            break;
                        }
                        if (item.getProductId() == product) {
                            if (counter > 0) {
                                OrderItem modifiedItem = addDiscountDetails(item,
                                        BigDecimal.valueOf(offer.getOfferValue()), offer, counter);
                                counter = counter - modifiedItem.getQuantity();
                                addDiscountReason(modifiedItem, coupon);
                            }
                        }
                    }
                    if(counter <= 0) {
                        break;
                    }
                }

            }

        }
        order.setAppliedOfferMessage(getOfferMessage());
        return order;
    }

    void addDiscountReason(OrderItem modifiedItem, CouponDetail coupon){
        modifiedItem.getDiscountDetail().setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
        modifiedItem.getDiscountDetail().setDiscountReason(coupon.getCode());
    }

    List<Integer> sortCommonList(Map<Integer, OrderItem> productDiscountMap, List<Integer> commonElementList) {

        commonElementList.sort(new Comparator<Integer>() {
            @Override
            public int compare(Integer o1, Integer o2) {
                return productDiscountMap.get(o1).getPrice().subtract(productDiscountMap.get(o2).getPrice()).intValue();
            }
        });

        return commonElementList;
    }

    public BigDecimal getTotalAmountOfItem(BigDecimal itemPrice, int count) {
        BigDecimal itemCount = new BigDecimal(count);
        BigDecimal totalAmountofItem = itemPrice.multiply(itemCount).setScale(2, BigDecimal.ROUND_HALF_UP);
        return totalAmountofItem;
    }

    public OrderItem getModifiedItem(OrderItem orderItem, BigDecimal percentageDiscount, BigDecimal valueDiscount) {
        DiscountDetail discountDetail = new DiscountDetail();
        PercentageDetail percentageDetail = new PercentageDetail();

        percentageDetail.setPercentage(BigDecimal.ZERO);
        percentageDetail.setValue(BigDecimal.ZERO);

        discountDetail.setPromotionalOffer(valueDiscount);
        discountDetail.setDiscount(percentageDetail);
        orderItem.setDiscountDetail(discountDetail);

        return orderItem;
    }

    public abstract OrderItem addDiscountDetails(OrderItem orderItem, BigDecimal discountValue, OfferDetail offer, int counter);

    public abstract String getOfferMessage();

}
