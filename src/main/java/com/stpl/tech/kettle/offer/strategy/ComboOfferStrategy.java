package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferDetail;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

public class ComboOfferStrategy extends  PercentageItemStrategy implements  OfferActionStrategy{
    @Override
    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, ProductCache productCache,
                                    Map<String, OrderItem> foundItems) {

        OfferDetail offerDetail = coupon.getOffer();
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal prepaidAmount = offerDetail.getPrepaidAmount();
        if (offerDetail.isPrepaid() && prepaidAmount!=null){
            for(OrderItem item : foundItems.values()) {
                totalAmount = totalAmount.add(getTotalAmountOfItemWithTax(item.getPrice(),item.getQuantity(), item.getTaxes()));
            }
            if(prepaidAmount.compareTo(totalAmount)>=0){
                totalAmount = totalAmount.setScale(0, RoundingMode.HALF_UP);
                order.setPrepaidAmount(totalAmount);
                getMessageTemplate().append(String.format("Prepaid Mode added of value %.2f",totalAmount.floatValue()));
            }else{
                BigDecimal discountValue = AppUtils.subtract(totalAmount, prepaidAmount);
                BigDecimal percentDiscount = AppUtils.percentageWithScale10(discountValue, totalAmount);
                for(OrderItem item : foundItems.values()) {
                    addDiscountDetails(item,percentDiscount,offerDetail,item.getQuantity());
                }
                order.setPrepaidAmount(prepaidAmount);
            }
        }
        order.setAppliedOfferMessage(getOfferMessage());
        return order;
    }

    private BigDecimal getTotalAmountOfItemWithTax(BigDecimal price, int quantity, List<TaxDetail> taxes) {
        BigDecimal totalPrice = getTotalAmountOfItem(price,quantity);
        Double totalTax = taxes.stream().mapToDouble(value -> value.getValue().doubleValue()).sum();
        return totalPrice.add(BigDecimal.valueOf(totalTax));
    }
}
