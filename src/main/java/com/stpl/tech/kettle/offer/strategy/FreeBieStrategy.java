package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.kettle.exceptions.WebErrorCode;
import com.stpl.tech.kettle.util.Constants.TransactionConstants;
import com.stpl.tech.master.domain.model.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class FreeBieStrategy extends  PercentageItemStrategy implements OfferActionStrategy{
    @Override
    public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, ProductCache productCache,
                                    Map<String, OrderItem> foundItems) throws OfferValidationException {

        Map<Integer, IdCodeName> offerProducts = new HashMap<>();
        Map<Integer, IdCodeName> offerCategory = new HashMap<>();
        Map<Integer, IdCodeName> offerSubCategory = new HashMap<>();
        Map<Integer, OrderItem> orderProducts = new HashMap<>();
        Map<Integer, IdCodeName> freebieProduct = new HashMap<>();
        String freebieProductName = "";
        String freebieProductDimension = "";
        for (OrderItem orderItem : offerOrder.getOrder().getOrders()) {
            orderProducts.put(Integer.valueOf(orderItem.getProductId()), orderItem);
        }
        for (IdCodeName offerProd : coupon.getOffer().getMetaDataMappings()) {
            if (offerProd.getName().equalsIgnoreCase("FREEBIE_PRODUCT")) {
                freebieProduct.put(Integer.valueOf(offerProd.getCode()), offerProd);
                freebieProductName = offerProd.getName();
                Set<CouponMapping> couponMapping = coupon.getMappings().get("FREEBIE_PRODUCT");
                for (CouponMapping obj : couponMapping) {
                    freebieProductDimension = obj.getDimension();
                }
            }
            if (offerProd.getName().equalsIgnoreCase("PRODUCT_CATEGORY")) {
                offerCategory.put(Integer.valueOf(offerProd.getCode()), offerProd);
            }
            if (offerProd.getName().equalsIgnoreCase("PRODUCT_SUB_CATEGORY")) {
                offerSubCategory.put(Integer.valueOf(offerProd.getCode()), offerProd);
            }
            if (offerProd.getName().equalsIgnoreCase("PRODUCT")) {
                offerProducts.put(Integer.valueOf(offerProd.getCode()), offerProd);
            }
        }
        validateFreeBieProduct(orderProducts, freebieProduct, freebieProductName, freebieProductDimension);
        validateOrderProducts(orderProducts, offerProducts, offerCategory, offerSubCategory, productCache, coupon, freebieProduct);

        offerOrder.setAppliedOfferMessage(getOfferMessage());
        return offerOrder;
    }

    public void validateFreeBieProduct(Map<Integer, OrderItem> orderProducts, Map<Integer, IdCodeName> freebieProduct,
                                       String freebieProductName, String freebieProductDimension) throws OfferValidationException {
        boolean isFreebieProductAvailable = false;
        boolean isFreebieProductDimensionAvailable = false;
        for (Map.Entry<Integer, IdCodeName> freebie : freebieProduct.entrySet()) {
            if (orderProducts.containsKey(freebie.getKey())) {
                isFreebieProductAvailable = true;
                OrderItem orderItem = orderProducts.get(freebie.getKey());
                if (orderItem.getDimension().replaceAll(" ", "").equalsIgnoreCase(freebieProductDimension.replaceAll(" ", ""))) {
                    isFreebieProductDimensionAvailable = true;
                }
            }
        }
        if (!isFreebieProductAvailable) {
            throw new OfferValidationException("Freebie product : " + freebieProductName + " doesn't exist in this order ",
                    WebErrorCode.FREEBIE_PRODUCT_NOT_FOUND);
        }
        if (!isFreebieProductDimensionAvailable) {
            throw new OfferValidationException("Freebie product : " + freebieProductName + " available but with different dimension. Required Dimension is : "
                    + freebieProductDimension,
                    WebErrorCode.FREEBIE_PRODUCT_NOT_FOUND);
        }
    }

    public void validateOrderProducts(Map<Integer, OrderItem> orderProducts, Map<Integer, IdCodeName> offerProducts, Map<Integer, IdCodeName> offerCategory,
                                      Map<Integer, IdCodeName> offerSubCategory, ProductCache productCache,
                                      CouponDetail coupon, Map<Integer, IdCodeName> freebieProduct) throws OfferValidationException {
        boolean flag = true;
        for (Map.Entry<Integer, IdCodeName> freebie : freebieProduct.entrySet()) {
            for (Map.Entry<Integer, OrderItem> map : orderProducts.entrySet()) {
                OrderItem orderItem = map.getValue();
                OrderItem freeProd = orderProducts.get(freebie.getKey());
                if (offerProducts.containsKey(map.getKey())) {
                    Product product = productCache.getProductById(map.getKey());
                    ListData dimensionProfile = productCache.getDimensionProfile(product.getDimensionProfileId());
                    if (dimensionProfile.getDetail().getName().equalsIgnoreCase(orderItem.getDimension())) {
                        addDiscountDetails(freebie.getKey(), freeProd, coupon);
                        flag = false;
                    }
                }
                Map<Integer, ListData> cat = productCache.getListCategoryData();
                for (Map.Entry<Integer, IdCodeName> offerCat : offerCategory.entrySet()) {
                    if (offerCat.getValue().getCode().equals(String.valueOf(orderItem.getProductType()))) {
                        addDiscountDetails(freebie.getKey(), freeProd, coupon);
                        flag = false;
                    }
                }
                for (Map.Entry<Integer, IdCodeName> offerSubCat : offerSubCategory.entrySet()) {
                    ListData catData = cat.get(offerSubCat.getKey());
                    if (offerSubCat.getValue().getCode().equals(String.valueOf(catData.getDetail().getCode()))) {
                        addDiscountDetails(freebie.getKey(), freeProd, coupon);
                        flag = false;
                    }
                }
            }
        }
        if (flag) {
            throw new OfferValidationException("Offer product not found ",
                    WebErrorCode.OFFER_PRODUCT_NOT_FOUND);
        }
    }

    public void addDiscountDetails(Integer productId, OrderItem orderItem, CouponDetail coupon) {
        orderItem.getDiscountDetail().getDiscount().setValue(orderItem.getPrice().setScale(0, RoundingMode.HALF_UP));
        orderItem.getDiscountDetail().getDiscount().setPercentage(BigDecimal.valueOf(100));
        orderItem.getDiscountDetail().setTotalDiscount(orderItem.getPrice().setScale(0, RoundingMode.HALF_UP));
        orderItem.getDiscountDetail().setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
        orderItem.getDiscountDetail().setDiscountReason(coupon.getCode());
    }
}
