package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferDetail;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.NotImplementedException;

import java.math.BigDecimal;
import java.util.Map;

@Log4j2
public class FlatItemStrategy extends AbstractItemStrategy implements OfferActionStrategy{
    StringBuffer offerMessage = new StringBuffer();
    String messageTemplate = "Applied Flat discount of Rs.%d on %s <br/>";

    @Override
    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon,  ProductCache productCache
            , Map<String, OrderItem> foundItems) throws OfferValidationException {
        return applyDiscountStrategy(coupon, order, productCache);
    }

    @Override
    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, ProductCache productCache,
                                    Map<String, OrderItem> foundItems, BigDecimal offerValue) throws OfferValidationException {
        if (offerValue == null) {
            return applyStrategy(order, coupon, productCache, foundItems);
        } else {
            throw new NotImplementedException("Not Implemented the method for Flat Item Strategy");
        }
    }

    @Override
    public OrderItem addDiscountDetails(OrderItem orderItem, BigDecimal discountInOffer, OfferDetail offer, int counter) {

        log.info("addDiscountDetails of flat item strategy");

        BigDecimal totalAmount = getTotalAmountOfItem(orderItem.getPrice(), orderItem.getQuantity());
        int discountQuantity = counter < orderItem.getQuantity()
                ? counter
                : orderItem.getQuantity();
        BigDecimal valueDiscount = discountInOffer.multiply(BigDecimal.valueOf(discountQuantity));
        if (valueDiscount.compareTo(totalAmount) > 0) {
            valueDiscount = totalAmount;
        }
        BigDecimal percentageDiscount = AppUtils.percentage(valueDiscount, totalAmount);

        offerMessage.append(String.format(messageTemplate, discountInOffer.intValueExact(), orderItem.getProductName()));
        return getModifiedItem(orderItem, percentageDiscount, valueDiscount);

    }

    @Override
    public String getOfferMessage() {
        return offerMessage.toString();
    }

    @Override
    public DiscountDetail getDiscountDetail(String couponCode, BigDecimal discount, BigDecimal totalAmount,
                                            BigDecimal paidAmount, BigDecimal maxDiscountValue){
        return null;
    }
}
