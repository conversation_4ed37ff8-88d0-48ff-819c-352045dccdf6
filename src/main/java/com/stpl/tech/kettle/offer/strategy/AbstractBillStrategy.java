package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.TransactionConstants;
import lombok.extern.log4j.Log4j2;

import java.math.BigDecimal;


@Log4j2
public abstract class AbstractBillStrategy {
    protected void updateOrderDiscount(OfferOrder offerOrder, String code, BigDecimal discountPercentage) {

        log.info("Updating order with coupon code:{}", code);
        DiscountDetail discountDetail = getDiscountDetail(code, discountPercentage);
        BigDecimal offerValue = AppUtils.percentOf(discountDetail.getDiscount().getPercentage(),
                offerOrder.getOrder().getTransactionDetail().getTotalAmount());
        discountDetail.setTotalDiscount(offerValue);
        if (offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer() != null) {
            discountDetail.setTotalDiscount(discountDetail.getTotalDiscount()
                    .add(offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer()));
        }
        discountDetail.getDiscount().setValue(offerValue);
        offerOrder.getOrder().getTransactionDetail().setDiscountDetail(discountDetail);
    }


    protected DiscountDetail getOrderDiscount(String code, BigDecimal discountPercentage, BigDecimal totalAmount) {
        log.info("Getting order with coupon code:{}", code);
        DiscountDetail discountDetail = getDiscountDetail(code, discountPercentage);
        BigDecimal offerValue = AppUtils.percentOf(discountDetail.getDiscount().getPercentage(),
                totalAmount);
        discountDetail.setTotalDiscount(offerValue);
        discountDetail.getDiscount().setValue(offerValue);
        return discountDetail;
    }

    protected void updateOrderItemDiscount(OfferOrder offerOrder, String code, BigDecimal discountPercentage) {

        log.info("Updating order item with coupon code:{}", code);
        for (OrderItem item : offerOrder.getOrder().getOrders()) {
            if(item.getAmount().compareTo(BigDecimal.ZERO) <= 0){
                continue;
            }
            DiscountDetail discountDetail = getDiscountDetail(code, discountPercentage);
            BigDecimal offerValue = AppUtils.percentOf(discountDetail.getDiscount().getPercentage(),
                    item.getTotalAmount());
            discountDetail.setTotalDiscount(offerValue);
            if (item.getDiscountDetail().getPromotionalOffer() != null) {
                discountDetail.setTotalDiscount(
                        discountDetail.getTotalDiscount().add(item.getDiscountDetail().getPromotionalOffer()));
            }
            discountDetail.getDiscount().setValue(offerValue);
            item.setDiscountDetail(discountDetail);
        }
    }

    private DiscountDetail getDiscountDetail(String code, BigDecimal discountPercentage) {
        PercentageDetail percentageDetail = new PercentageDetail();
        percentageDetail.setPercentage(discountPercentage);
        DiscountDetail discountDetail = new DiscountDetail();
        discountDetail.setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
        discountDetail.setDiscountReason(code);
        discountDetail.setDiscount(percentageDetail);
        discountDetail.setPromotionalOffer(BigDecimal.ZERO);
        return discountDetail;
    }

    public abstract String getOfferMessage();
}
