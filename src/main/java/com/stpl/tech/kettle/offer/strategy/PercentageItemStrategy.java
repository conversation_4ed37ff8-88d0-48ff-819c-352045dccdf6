package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferDetail;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.NotImplementedException;

import java.math.BigDecimal;
import java.util.Map;


@Log4j2
public class PercentageItemStrategy extends AbstractItemStrategy implements OfferActionStrategy{
    StringBuffer offerMessage = new StringBuffer();
    String messageTemplate = "Applied discount of %.2f on %s <br/>";

    @Override
    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon,  ProductCache productCache, Map<String, OrderItem> foundItems) throws OfferValidationException {
        log.info("Applying percentage discount to the offer order");
        return applyDiscountStrategy(coupon, order, productCache);
    }

    @Override
    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, ProductCache productCache,
                                    Map<String, OrderItem> foundItems, BigDecimal offerValue) throws OfferValidationException {
        if (offerValue == null) {
            return applyStrategy(order, coupon,productCache,foundItems);
        } else {
            throw new NotImplementedException("Not Implemented the method for Percentage Item Strategy");
        }
    }

    @Override
    public OrderItem addDiscountDetails(OrderItem orderItem, BigDecimal discountInOffer, OfferDetail offer, int counter) {
        log.info("add DiscountDetails of percent item strategy");
        BigDecimal totalAmount = getTotalAmountOfItem(orderItem.getPrice(), orderItem.getQuantity());
        int discountQuantity = counter < orderItem.getQuantity()
                ? counter
                : orderItem.getQuantity();
        BigDecimal discountOnPrice = orderItem.getPrice().multiply(BigDecimal.valueOf(discountQuantity))
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal valueDiscount = AppUtils.percentOf(discountInOffer, discountOnPrice);

        BigDecimal effectivePercentage = AppUtils.percentage(valueDiscount, totalAmount);
        offerMessage.append(String.format(messageTemplate, valueDiscount.floatValue(), orderItem.getProductName()));
        return getModifiedItem(orderItem, effectivePercentage, valueDiscount);
    }

    public StringBuffer getMessageTemplate(){
        return offerMessage;
    }

    @Override
    public String getOfferMessage() {
        return offerMessage.toString();
    }

    @Override
    public DiscountDetail getDiscountDetail(String couponCode, BigDecimal discount, BigDecimal totalAmount,
                                            BigDecimal paidAmount, BigDecimal maxDiscountValue) {
        return null;
    }
}
