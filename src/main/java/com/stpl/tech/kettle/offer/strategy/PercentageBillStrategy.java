package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.master.domain.model.CouponDetail;

import java.math.BigDecimal;
import java.util.Map;

public class PercentageBillStrategy extends AbstractBillStrategy implements  OfferActionStrategy {
    StringBuffer offerMessage = new StringBuffer();
    String messageTemplate = "Applied discount of %.2f on order <br/>";

    /**
     * PERCENTAGE BILL = Apply % value discount to this order.
     * <p>
     * Here we will update the discount details of the order. Rest of the
     * calculations will be done by the UI before punching of the order.
     *
     */
    @Override
    public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, ProductCache productCache
            , Map<String, OrderItem> foundItems) throws OfferValidationException {

        BigDecimal discountPercentage = new BigDecimal(coupon.getOffer().getOfferValue());
        return apply(offerOrder, coupon, discountPercentage);
    }

    @Override
    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, ProductCache productCache,
                                    Map<String, OrderItem> foundItems, BigDecimal offerValue) throws OfferValidationException {
        if (offerValue == null) {
            return applyStrategy(order, coupon, productCache,foundItems);
        } else {
            return apply(order,coupon,offerValue);
        }
    }

    @Override
    public String getOfferMessage() {
        return offerMessage.toString();
    }

    private OfferOrder apply(OfferOrder offerOrder, CouponDetail coupon, BigDecimal offerValue){
        BigDecimal discountPercentage = offerValue;
        updateOrderDiscount(offerOrder, coupon.getCode(), discountPercentage);
        offerMessage.append(String.format(messageTemplate,
                offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getDiscount().getValue()));
        updateOrderItemDiscount(offerOrder, coupon.getCode(), discountPercentage);
        offerOrder.setAppliedOfferMessage(getOfferMessage());
        return offerOrder;
    }

    @Override
    public DiscountDetail getDiscountDetail(String couponCode, BigDecimal discount, BigDecimal totalAmount,
                                            BigDecimal paidAmount, BigDecimal maxDiscountValue) {
        return getOrderDiscount(couponCode, discount, totalAmount);
    }

}
