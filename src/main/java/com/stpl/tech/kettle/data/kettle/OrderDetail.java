/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.stpl.tech.master.domain.model.OfferLastRedemptionView;

import jakarta.persistence.Column;
import jakarta.persistence.ColumnResult;
import jakarta.persistence.ConstructorResult;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SqlResultSetMapping;
import jakarta.persistence.SqlResultSetMappings;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Getter
@Setter
@SqlResultSetMappings(value = {
		@SqlResultSetMapping(name = "OfferLastRedemptionView", classes = @ConstructorResult(targetClass = OfferLastRedemptionView.class, columns = {
				@ColumnResult(name = "customerId", type = Integer.class),
				@ColumnResult(name = "lastOrderTime", type = Date.class),
				@ColumnResult(name = "orderToday", type = Integer.class),
				@ColumnResult(name = "orderInLastHour", type = Integer.class), }))

})
@Table(name = "ORDER_DETAIL", uniqueConstraints = @UniqueConstraint(columnNames = "GENERATED_ORDER_ID"))
public class OrderDetail implements java.io.Serializable {
	private static final long serialVersionUID = 5441681397850434848L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_ID", unique = true, nullable = false)
	private Integer orderId;
	@Column(name = "GENERATED_ORDER_ID", unique = true, nullable = false, length = 30)
	private String generatedOrderId;
	@Column(name = "UNIT_ORDER_ID", nullable = false)
	private Integer unitOrderId;
	@Column(name = "TERMINAL_ID", nullable = true)
	private Integer terminalId;
	@Column(name = "TABLE_NUMBER", nullable = true)
	private Integer tableNumber;
	@Column(name = "CUSTOMER_ID")
	private Integer customerId;
	@Column(name = "CAMPAIGN_ID")
	private String campaignId;
	@Column(name = "EMP_ID", nullable = false)
	private int empId;
	@Column(name = "ORDER_STATUS", nullable = false, length = 30)
	private String orderStatus;
	@Column(name = "CANCELATION_REASON", length = 100)
	private String cancelationReason;
	@Column(name = "CANCELATION_REASON_ID")
	private Integer cancellationReasonId;
	@Column(name = "WASTAGE_TYPE", length = 20)
	private String wastageType;
	@Column(name = "WASTAGE_KETTLE_ID", nullable = true)
	private Integer wastageKettleId;
	@Column(name = "SETTLEMENT_TYPE", length = 10)
	private String settlementType;
	@Column(name = "UNIT_ID", nullable = false)
	private int unitId;
	@Column(name = "HAS_PARCEL", nullable = false, length = 1)
	private String hasParcel;
	@Temporal(TemporalType.DATE)
	@Column(name = "BUSINESS_DATE", nullable = true, length = 10)
	private Date businessDate;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "BILL_START_TIME", nullable = false, length = 19)
	private Date billStartTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "BILL_GENERATION_TIME", length = 19)
	private Date billGenerationTime;
	@Column(name = "BILL_CREATION_SECONDS", nullable = false, length = 19)
	private int billCreationSeconds;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "BILLING_SERVER_TIME", nullable = false, length = 19)
	private Date billingServerTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "BILL_CANCELLATION_TIME", nullable = true, length = 19)
	private Date billCancellationTime;
	@Column(name = "CHANNEL_PARTNER_ID", nullable = false)
	private int channelPartnerId;
	@Column(name = "DELIVERY_PARTNER_ID", nullable = true)
	private int deliveryPartnerId;
	@Column(name = "DELIVERY_ADDRESS", nullable = true)
	private Integer deliveryAddress;
	@Column(name = "ORDER_REMARK", nullable = true)
	private String orderRemark;
	@Column(name = "TOTAL_AMOUNT", precision = 10)
	private BigDecimal totalAmount;
	@Column(name = "SALE_AMOUNT", precision = 10)
	private BigDecimal saleAmount;
	@Column(name = "PROMOTIONAL_DISCOUNT", precision = 10)
	private BigDecimal promotionalDiscount;
	@Column(name = "TOTAL_DISCOUNT", precision = 10)
	private BigDecimal totalDiscount;
	@Column(name = "TAXABLE_AMOUNT", precision = 10)
	private BigDecimal taxableAmount;
	@Column(name = "DISCOUNT_PERCENT", precision = 10)
	private BigDecimal discountPercent;
	@Column(name = "DISCOUNT_AMOUNT", precision = 10)
	private BigDecimal discountAmount;
	@Column(name = "DISCOUNT_REASON_ID")
	private Integer discountReasonId;
	@Column(name = "DISCOUNT_REASON")
	private String discountReason;
	@Column(name = "ORDER_SOURCE", nullable = false, length = 10)
	private String orderSource;
	@Column(name = "ORDER_TYPE", nullable = true)
	private String orderType;
	@Column(name = "ORDER_SOURCE_ID", nullable = true)
	private String orderSourceId;
	@Column(name = "TOTAL_TAX", precision = 10)
	private BigDecimal taxAmount;
	@Column(name = "ROUND_OFF_AMOUNT", precision = 10)
	private BigDecimal roundOffAmount;
	@Column(name = "SETTLED_AMOUNT", precision = 10)
	private BigDecimal settledAmount;
	@Column(name = "PRINT_COUNT", nullable = false)
	private Integer printCount;
	@Column(name = "CANCELLED_BY", nullable = true)
	private Integer cancelledBy;
	@Column(name = "CANCEL_APPROVED_BY", nullable = true)
	private Integer cancelApprovedBy;
	@Column(name = "POINTS_REDEEMED")
	private Integer pointsRedeemed;
	@Column(name = "OFFER_CODE", nullable = true, length = 30)
	private String offerCode;
	@Column(name = "TEMP_CODE", nullable = true, length = 10)
	private String tempCode;
	@Column(name = "SAVING_AMOUNT", precision = 10)
	private BigDecimal savingAmount;
	@Column(name = "CUSTOMER_NAME", nullable = true)
	private String customerName;
	@Column(name = "TOKEN_NUMBER", nullable = true)
	private Integer tokenNumber;
	@Column(name = "LINKED_ORDER_ID", nullable = true)
	private Integer linkedOrderId;
	@Column(name = "MANUAL_BILL_BOOK_NO", nullable = true)
	private Integer manualBillBookNo;
	@Column(name = "OUT_OF_DELIVERY", nullable = true)
	private String outOfDelivery = "N";
	@Column(name = "TABLE_REQUEST_ID", nullable = true)
	private Integer tableRequestId;
	@Column(name = "IS_GIFT_CARD_ORDER", nullable = true)
	private String giftCardOrder;
	@Column(name = "QR_LINK", nullable = true)
	private String qrLink;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBSCRIPTION_ID", nullable = true)
	private SubscriptionDetail subscriptionDetail;
	@Transient
	private OrderInvoiceDetail invoiceDetail;
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	private List<OrderItem> orderItems = new ArrayList<OrderItem>(0);
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	private List<EmployeeMealData> employeeMealData = new ArrayList<EmployeeMealData>(0);
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	private List<OrderSettlement> orderSettlements = new ArrayList<OrderSettlement>(0);
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	private List<OrderTaxDetail> orderTaxes = new ArrayList<OrderTaxDetail>(0);
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	private List<OrderRePrintDetail> orderReprints = new ArrayList<OrderRePrintDetail>(0);
	@Column(name = "ORDER_ATTRIBUTE", nullable = true)
	private String orderAttribute;
	@Column(name = "BRAND_ID")
	private Integer brandId;
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	private List<PartnerOrderDiscountMapping> partnerOrderDiscountMapping = new ArrayList<PartnerOrderDiscountMapping>(
			0);
	@Column(name = "PARTNER_CUSTOMER_ID")
	private String partnerCustomerId;
	@Column(name = "INVOICE_ID")
	private String invoiceId;
	@Column(name = "IS_INVOICE")
	private String isInvoice;
	@Column(name = "COLLECTION_AMOUNT", precision = 10)
	private BigDecimal collectionAmount;

	@Column(name = "REF_ORDER_ID")
	private Integer refOrderId;

	@Column(name = "SOURCE_VERSION", length = 10)
	private String sourceVersion;


	@Column(name = "NO_OF_PAX")
	private Integer noOfPax;

	@Transient
	private List<Integer> orderItemIdOnHold = new ArrayList<>();

	@Column(name = "SERVICE_CHARGE_AMOUNT", precision = 10)
	private BigDecimal serviceCharge;

	@Column(name = "SERVICE_CHARGE_PERCENT", precision = 10)
	private BigDecimal serviceChargePercent;

	@Column(name = "SERVICE_TAX_AMOUNT", precision = 10)
	private BigDecimal serviceTaxAmount;


	public OrderDetail() {
	}

	public OrderDetail(int unitOrderId, String generatedOrderId, int empId, String orderStatus, int unitId,
			String hasParcel, Date billStartTime, int channelPartnerId, String orderSource) {
		this.unitOrderId = unitOrderId;
		this.generatedOrderId = generatedOrderId;
		this.empId = empId;
		this.orderStatus = orderStatus;
		this.unitId = unitId;
		this.hasParcel = hasParcel;
		this.billStartTime = billStartTime;
		this.channelPartnerId = channelPartnerId;
		this.orderSource = orderSource;
	}

}
