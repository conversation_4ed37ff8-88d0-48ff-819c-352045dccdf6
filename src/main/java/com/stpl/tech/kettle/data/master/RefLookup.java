package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "REF_LOOKUP", uniqueConstraints = @UniqueConstraint(columnNames = { "RTL_ID", "RL_CODE" }))
@Getter
@Setter
@NoArgsConstructor
public class RefLookup implements java.io.Serializable {

	private static final long serialVersionUID = -7232245665726322782L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "RL_ID", unique = true, nullable = false)
	private Integer rlId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "RTL_ID", nullable = false)
	private RefLookupType refLookupType;

	@Column(name = "RL_CODE", nullable = false, length = 20)
	private String rlCode;

	@Column(name = "RL_SHORT_CODE", nullable = true, length = 3)
	private String rlShortCode;

	@Column(name = "RL_NAME", nullable = false, length = 30)
	private String rlName;

	@Column(name = "RL_STATUS", nullable = false, length = 10)
	private String rlStatus;

	public RefLookup(RefLookupType refLookupType, String rlCode, String rlName, String rlStatus) {
		this.refLookupType = refLookupType;
		this.rlCode = rlCode;
		this.rlName = rlName;
		this.rlStatus = rlStatus;
	}

}
