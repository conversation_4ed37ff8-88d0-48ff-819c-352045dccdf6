package com.stpl.tech.kettle.data.kettle;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "DELIVERY_DETAIL", uniqueConstraints = @UniqueConstraint(columnNames = "DELIVERY_TASK_ID"))
@Getter
@Setter
public class DeliveryDetail implements java.io.Serializable {

	private static final long serialVersionUID = 8309737834688866595L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID", unique = true, nullable = false)
	private int id;
	@Column(name = "DELIVERY_PARTNER_ID", nullable = false)
	private int deliveryPartnerId;
	@Column(name = "ORDER_ID", nullable = false)
	private int orderId;
	@Column(name = "DELIVERY_TASK_ID", nullable = false)
	private String deliveryTaskId;
	@Column(name = "DELIVERY_STATUS", nullable = false)
	private String deliveryStatus;
	@Column(name = "GENERATED_ORDER_ID", nullable = true)
	private String generatedOrderId;
	@Column(name = "STATUS_UPDATE_TMSTMP", nullable = false)
	private Date statusUpdateTime;
	@Column(name = "DELIVERY_BOY_ID", nullable = true)
	private Integer deliveryBoyId;
	@Column(name = "DELIVERY_BOY_NAME", nullable = true)
	private String deliveryBoyName;
	@Column(name = "DELIVERY_BOY_PHONE_NUM", nullable = true)
	private String deliveryBoyPhoneNum;
	@Column(name = "FEEDBACK_STATUS", nullable = true)
	private String feedbackStatus = "N";
	@Column(name = "FEEDBACK_CODE_RECEIVED", nullable = true)
	private String feedbackCodeReceived;
	@Column(name = "POSITIVE_FEEDBACK_CODE", nullable = true)
	private String positiveFeedbackCode;
	@Column(name = "NEGATIVE_FEEDBACK_CODE", nullable = true)
	private String negativeFeedbackCode;
	@Column(name = "ALLOTED_NO", nullable = true)
	private String allotedNo;
	@Column(name = "DELIVERY_SOURCE", nullable = true)
	private String deliverySource;

}
