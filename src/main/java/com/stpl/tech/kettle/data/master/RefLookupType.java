package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "REF_LOOKUP_TYPE", uniqueConstraints = @UniqueConstraint(columnNames = { "RTL_GROUP", "RTL_CODE" }))
@Getter
@Setter
@NoArgsConstructor
public class RefLookupType implements java.io.Serializable {

	private static final long serialVersionUID = 5854808906184139220L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "RTL_ID", unique = true, nullable = false)
	private Integer rtlId;

	@Column(name = "RTL_GROUP", nullable = false, length = 15)
	private String rtlGroup;

	@Column(name = "RTL_CODE", nullable = false, length = 20)
	private String rtlCode;

	@Column(name = "RTL_NAME", nullable = false, length = 30)
	private String rtlName;

	@Column(name = "STATUS", nullable = false, length = 10)
	private String status;

	@OneToMany(fetch = FetchType.EAGER, mappedBy = "refLookupType")
	@OrderBy("rlId asc")
	private List<RefLookup> refLookups = new ArrayList<RefLookup>(0);

	public RefLookupType(String rtlGroup, String rtlCode, String rtlName) {
		this.rtlGroup = rtlGroup;
		this.rtlCode = rtlCode;
		this.rtlName = rtlName;
	}

	public RefLookupType(String rtlGroup, String rtlCode, String rtlName, List<RefLookup> refLookups) {
		this.rtlGroup = rtlGroup;
		this.rtlCode = rtlCode;
		this.rtlName = rtlName;
		this.refLookups = refLookups;
	}

}
