package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.util.Constants.AppConstants;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "OFFER_DETAIL_DATA")
@Getter
@Setter
@NoArgsConstructor
public class OfferDetailData implements java.io.Serializable {

	private static final long serialVersionUID = -6850311105562111784L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "OFFER_DETAIL_ID", unique = true, nullable = false)
	private Integer offerDetailId;
	// OfferCategory = BILL, ITEM, LOYALITY, BEHAVIOUR, CHEAPER-ONE, BOGO

	@Column(name = "OFFER_CATEGORY", nullable = false, length = 30)
	private String offerCategory;
	// OfferType = PERCENTAGE_BILL_STRATEGY, FLAT_BILL_STRATEGY etc.

	@Column(name = "OFFER_TYPE", nullable = false, length = 30)
	private String offerType;

	@Column(name = "OFFER_TEXT", nullable = false, length = 500)
	private String offerText;

	@Column(name = "OFFER_DESCRIPTION", nullable = false, length = 1000)
	private String offerDescription;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "START_DATE", nullable = false, length = 19)
	private Date startDate;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "END_DATE", nullable = false, length = 19)
	private Date endDate;

	@Temporal(TemporalType.DATE)
	@Column(name = "OFFER_LAUNCH_START_DATE", nullable = true, length = 10)
	private Date launchStartDate;

	@Temporal(TemporalType.DATE)
	@Column(name = "OFFER_LAUNCH_END_DATE", nullable = true, length = 10)
	private Date launchEndDate;

	@Column(name = "OFFER_LAUNCH_STRATEGY", nullable = true)
	private String launchStrategy;

	@Column(name = "OFFER_LAUNCH_MESSAGE", nullable = true)
	private String launchMessage;

	@Column(name = "MIN_VALUE", nullable = false)
	private int minValue;

	@Column(name = "INCLUDE_TAXES", nullable = false)
	private String includeTaxes;

	@Column(name = "OFFER_STATUS", nullable = false, length = 15)
	private String offerStatus;

	@Column(name = "VALIDATE_CUSTOMER", nullable = false)
	private String validateCustomer;

	@Column(name = "QUANTITY_LIMIT", nullable = false)
	private Integer minQuantity;

	@Column(name = "LOYALTY_LIMIT", nullable = false)
	private int minLoyalty;
	// minItemCount is a check for Minimum Number of items present in order.

	@Column(name = "MIN_ITEM_COUNT", nullable = false)
	private int minItemCount;

	@Column(name = "OFFER_VALUE", nullable = false)
	private int value;
	// offerScope = scope of offer i.e MASS, INTERNAL, CUSTOMER, CORPORATE

	@Column(name = "OFFER_SCOPE", nullable = false)
	private String offerScope;
	// offerScope = scope of offer i.e MASS, INTERNAL, CUSTOMER, CORPORATE

	@Column(name = "EMAIL_DOMAIN", nullable = true)
	private String emailDomain;

	@Column(name = "REMOVE_LOYALTY_REWARD", nullable = true)
	private String removeLoyaltyReward;

	@Column(name = "PRIORITY", nullable = false)
	private Integer priority;

	@Column(name = "FREE_ITEM_PRODUCT_ID", nullable = true)
	private Integer freeItemProductId;

	@Column(name = "FREE_ITEM_DIMENSION", nullable = true)
	private String freeItemDimension;

	@Column(name = "FREE_ITEM_QUANTITY", nullable = true)
	private Integer freeItemQuantity;

	@Column(name = "FREE_ITEM_OFFER_VALUE", nullable = true, precision = 10)
	private BigDecimal freeItemOfferValue;

	@Column(name = "FREE_ITEM_OFFER_TYPE", nullable = true)
	private String freeItemOfferType;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ACCOUNTS_CATEGORY", nullable = true)
	private OfferAccountCategory accountsCategory;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "offer")
	private List<OfferPartner> partners = new ArrayList<OfferPartner>(0);

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "offerId")
	private List<OfferMetadata> metaDataMappings = new ArrayList<OfferMetadata>(0);

	@Column(name = "PREPAID")
	private String prepaid = AppConstants.NO;

	@Column(name = "PREPAID_AMOUNT")
	private BigDecimal prepaidAmount;

	@Column(name = "MAX_BILL_VALUE")
	private BigDecimal maxBillValue;

	@Column(name = "OTP_REQUIRED")
	private String otpRequired;

	@Column(name = "MAX_DISCOUNT_AMOUNT")
	private BigDecimal maxDiscountAmount;

	@OneToMany(mappedBy = "offerDetail")
	private List<OfferDetailMappingData> mappings;

	@Column(name = "FREQUENCY_APPLICABLE")
	private String frequencyApplicable;

	@Column(name = "FREQUENCY_STRATEGY")
	private String frequencyStrategy;

	@Column(name = "FREQUENCY_COUNT")
	private Integer frequencyCount;

	@Column(name = "DAILY_FREQUENCY_COUNT")
	private Integer dailyFrequencyCount;

	@Column(name = "APPLICABLE_HOUR")
	private Integer applicableHour;

	@Column(name = "UNIT_AUTO_APPLICABLE")
	private String autoApplicableforUnit;

	@Column(name = "TERMS_AND_CONDITIONS")
	private String termsAndConditions;

	@Column(name = "MAX_QUANTITY")
	private Integer maxQuantity;

	public OfferDetailData(String offerCategory, String offerType, String offerText, String offerDescription,
			Date startDate, Date endDate, int minValue, String includeTaxes, String offerStatus,
			String validateCustomer, Integer minQuantity, List<OfferPartner> partners, Integer priority) {
		super();
		this.offerCategory = offerCategory;
		this.offerType = offerType;
		this.offerText = offerText;
		this.offerDescription = offerDescription;
		this.startDate = startDate;
		this.endDate = endDate;
		this.minValue = minValue;
		this.includeTaxes = includeTaxes;
		this.offerStatus = offerStatus;
		this.validateCustomer = validateCustomer;
		this.minQuantity = minQuantity;
		this.partners = partners;
		this.priority = priority;
	}
}
