package com.stpl.tech.kettle.data.kettle;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "CUSTOMER_BRAND_MAPPING")
@NoArgsConstructor
@Getter
@Setter
@AllArgsConstructor
public class CustomerBrandMapping {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID", unique = true, nullable = false)
	private int id;

	@Column(name = "CUSTOMER_ID")
	private int customerId;

	@Column(name = "BRAND_ID")
	private int brandId;

	@Column(name = "LAST_ORDER_ID")
	private int lastOrderId;

	@Column(name = "TOTAL_ORDER")
	private int totalOrder;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_ORDER_TIME")
	private Date lastOrderTime;

	@Column(name = "LAST_SPECIAL_ORDER_ID")
	private Integer lastSpecialOrderId;

	@Column(name = "TOTAL_SPECIAL_ORDER")
	private Integer totalSpecialOrder;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_SPECIAL_ORDER_TIME")
	private Date lastSpecialOrderTime;

	public CustomerBrandMapping(int customerId, int brandId, int totalOrder) {
		this.customerId = customerId;
		this.brandId = brandId;
		this.totalOrder = totalOrder;
	}

	public CustomerBrandMapping(int customerId, int brandId, int lastOrderId, int totalOrder, Date lastOrderTime) {
		this.customerId = customerId;
		this.brandId = brandId;
		this.lastOrderId = lastOrderId;
		this.totalOrder = totalOrder;
		this.lastOrderTime = lastOrderTime;
	}

	public CustomerBrandMapping(int customerId, int brandId, int totalOrder,Integer totalSpecialOrder) {
		this.customerId = customerId;
		this.brandId = brandId;
		this.totalOrder = totalOrder;
		this.totalSpecialOrder = totalSpecialOrder;
	}

}
