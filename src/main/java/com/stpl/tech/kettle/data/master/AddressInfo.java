package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "ADDRESS_INFO")
@Getter
@Setter
@NoArgsConstructor
public class AddressInfo implements Serializable {

	private static final long serialVersionUID = -3649514780512868273L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ADDRESS_ID", unique = true, nullable = false)
	private Integer addressId;
	@Column(name = "ADDRESS_LINE_1", nullable = false)
	private String addressLine1;
	@Column(name = "ADDRESS_LINE_2")
	private String addressLine2;
	@Column(name = "ADDRESS_LINE_3")
	private String addressLine3;
	@Column(name = "CITY", nullable = false, length = 128)
	private String city;
	@Column(name = "STATE", nullable = false, length = 128)
	private String state;
	@Column(name = "COUNTRY", nullable = false, length = 128)
	private String country;
	@Column(name = "ZIPCODE", nullable = false, length = 40)
	private String zipcode;
	@Column(name = "CONTACT_NUM_1", length = 32)
	private String contactNum1;
	@Column(name = "CONTACT_NUM_2", length = 32)
	private String contactNum2;
	@Column(name = "ADDRESS_TYPE", nullable = false, length = 50)
	private String addressType;
	@Column(name = "LATITUDE", nullable = true, length = 15)
	private String latitude;
	@Column(name = "LONGITUDE", nullable = true, length = 15)
	private String longitude;

	public AddressInfo(String addressLine1, String city, String state, String country, String zipcode,
			String contactNum1, String addressType) {
		this.addressLine1 = addressLine1;
		this.city = city;
		this.state = state;
		this.country = country;
		this.zipcode = zipcode;
		this.contactNum1 = contactNum1;
		this.addressType = addressType;
	}

	public AddressInfo(String addressLine1, String addressLine2, String addressLine3, String city, String state,
			String country, String zipcode, String contactNum1, String contactNum2, String addressType, String latitude,
			String longitude) {
		this.addressLine1 = addressLine1;
		this.addressLine2 = addressLine2;
		this.addressLine3 = addressLine3;
		this.city = city;
		this.state = state;
		this.country = country;
		this.zipcode = zipcode;
		this.contactNum1 = contactNum1;
		this.contactNum2 = contactNum2;
		this.addressType = addressType;
		this.latitude = latitude;
		this.longitude = longitude;
	}

}
