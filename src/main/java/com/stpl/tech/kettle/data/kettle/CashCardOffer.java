package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "CASH_CARD_OFFER")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CashCardOffer implements Serializable {

	private static final long serialVersionUID = -8341842765639416792L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CASH_CARD_OFFER_ID", unique = true, nullable = false)
	private Integer cashCardOfferId;
	@Column(name = "CARD_DESCRIPTION", nullable = true)
	private String description;
	@Column(name = "SUGGEST_WALLET_CARD_DESCRIPTION", nullable = true)
	private String suggestWalletDescription;
	@Column(name = "OFFER_STATUS", nullable = false, length = 15)
	private String offerStatus;
	@Temporal(TemporalType.DATE)
	@Column(name = "START_DATE", nullable = false, length = 10)
	private Date startDate;
	@Temporal(TemporalType.DATE)
	@Column(name = "END_DATE", length = 10)
	private Date endDate;
	@Column(name = "CREATED_BY")
	private String createdBy;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATION_TIME", nullable = false, length = 19)
	private Date creationTime;
	@Column(name = "CANCELLED_BY")
	private String cancelledBy;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CANCELLATION_TIME", nullable = true, length = 19)
	private Date cancellationTime;
	@Column(name = "UNIT_ID")
	private Integer unitId;
	@Column(name = "CARD_DENOMINATION", nullable = false, precision = 10)
	private BigDecimal denomination;
	@Column(name = "OFFER_PERCENTAGE", nullable = true, precision = 10)
	private BigDecimal percentage;
	@Column(name = "SUGGEST_WALLET_OFFER_PERCENTAGE", nullable = true, precision = 10)
	private BigDecimal suggestWalletPercentage;
	@Transient
	private  String walletType;
	@Column(name = "PARTNER_ID", nullable = false)
	private Integer partnerId;
}
