package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "WEB_ORDER_METRIC_DATA")
@Getter
@Setter
public class WebOrderMetricDetailData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "METRIC_ID", unique = true, nullable = false)
    private Integer metricId;
    @Column(name = "CUSTOMER_ID", nullable = false)
    private Integer customerId;
    @Column(name ="CUSTOMER_NAME")
    private String customerName;
    @Column(name ="UNIT_ID",nullable = false)
    private Integer unitId;
    @Column(name ="UNIT_NAME",nullable = false)
    private String unitName;
    @Column(name = "ORDER_ID", nullable = false)
    private Integer orderId;
    @Column(name = "CART_ID", nullable = false)
    private String cartId;
    @Column(name = "ORDER_TIME", nullable = false)
    private Date orderTime;
    @Column(name ="OFFER_CODE",nullable = false)
    private String offerCode;
    @Column(name = "DISCOUNT_AMOUNT", nullable = false)
    private BigDecimal discountAmount;
    @Column(name = "TOTAL_AMOUNT", nullable = false)
    private BigDecimal totalAmount;
    @Column(name ="TABLE_NUMBER")
    private Integer tableNumber;
    @Column(name ="SCAN_TYPE")
    private String scanType;

}
