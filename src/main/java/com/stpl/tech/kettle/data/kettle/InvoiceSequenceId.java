package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "INVOICE_SEQUENCE_ID")
@Getter
@Setter
public class InvoiceSequenceId implements Serializable {

	private static final long serialVersionUID = 5483131831490692364L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SEQUENCE_ID", unique = true, nullable = false)
	private Integer sequenceId;
	@Column(name = "STATE_CODE", nullable = false)
	private String stateCode;
	@Column(name = "FINANCIAL_YEAR", nullable = false)
	private String financialYear;
	@Column(name = "NEXT_VALUE", nullable = false)
	private int nextValue;

	public InvoiceSequenceId(String stateCode, String financialYear, int nextValue) {
		this.stateCode = stateCode;
		this.financialYear = financialYear;
		this.nextValue = nextValue;
	}

}
