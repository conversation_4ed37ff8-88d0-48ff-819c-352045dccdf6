package com.stpl.tech.kettle.data.kettle;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

@Entity
@Table(name = "CUSTOMER_INFO")
public class CustomerInfo {

	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Id
	@Column(name = "CUSTOMER_ID", nullable = false)
	private Integer customerId;

	@Column(name = "FIRST_NAME", nullable = true, length = 50)
	private String firstName;

	@Column(name = "MIDDLE_NAME", nullable = true, length = 50)
	private String middleName;

	@Column(name = "LAST_NAME", nullable = true, length = 50)
	private String lastName;

	@Column(name = "COUNTRY_CODE", nullable = false, length = 5)
	private String countryCode;

	@Column(name = "CONTACT_NUMBER", nullable = false, length = 12)
	private String contactNumber;

	@Column(name = "EMAIL_ID", nullable = true, length = 100)
	private String emailId;

	@Column(name = "IS_NUMBER_VERIFIED", nullable = true, length = 1)
	private String isNumberVerified;

	@Column(name = "IS_EMAIL_VERIFIED", nullable = true, length = 1)
	private String isEmailVerified;

	@Column(name = "ADD_TIME", nullable = false)
	private Timestamp addTime;

	@Column(name = "NUMBER_VERIFICATION_TIME", nullable = true)
	private Timestamp numberVerificationTime;

	@Column(name = "EMAIL_VERIFICATION_TIME", nullable = true)
	private Timestamp emailVerificationTime;

	@Column(name = "ACQUISITION_SOURCE", nullable = false, length = 100)
	private String acquisitionSource;

	@Column(name = "ACQUISITION_TOKEN", nullable = false, length = 50)
	private String acquisitionToken;

	@Column(name = "REGISTRATION_UNIT_ID", nullable = true)
	private Integer registrationUnitId;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "customerInfo")
	private List<CustomerAddressInfo> customerAddressInfos = new ArrayList<CustomerAddressInfo>(0);

	@Column(name = "IS_SMS_SUBSCRIBER", nullable = true, length = 1)
	private String isSmsSubscriber;

	@Column(name = "IS_EMAIL_SUBSCRIBER", nullable = true, length = 1)
	private String isEmailSubscriber;

	@Column(name = "IS_LOYALTY_SUBSCRIBER", nullable = true, length = 1)
	private String isLoyaltySubscriber;

	@Column(name = "IS_BLACKLISTED", nullable = false, length = 1)
	private String isBlacklisted;

	@Column(name = "TRUE_CALLER_PROFILE_ID", nullable = true)
	private Integer trueCallerProfileId;

	@Column(name = "IS_DND", nullable = true, length = 1)
	private String isDnd;

	@Column(name = "IS_INTERNAL", nullable = true, length = 1)
	private String isInternal;

	@Column(name = "REF_CODE", nullable = true, length = 15)
	private String refCode;

	@Column(name = "IS_REF_SUBSCRIBER", nullable = true, length = 1)
	private String isRefSubscriber;

	@Column(name = "REF_ACQUISITION_SOURCE", nullable = true, length = 20)
	private String refAcquisitionSource;

	@Column(name = "REFERRED_ON", nullable = true)
	private Timestamp referredOn;

	@Column(name = "REFERRAL_DATA_ID", nullable = true)
	private Integer referralDataId;

	@Column(name = "REFERRER_ID", nullable = true)
	private Integer referrerId;

	@Column(name = "IS_REFERRER_AWARDED", nullable = true, length = 1)
	private String isReferrerAwarded;

	@Column(name = "FACE_ID", nullable = true, length = 100)
	private String faceId;

	@Column(name = "OPT_OUT_FACE_IT", nullable = true, length = 1)
	private String optOutFaceIt;

	@Column(name = "OPT_OUT_TIME", nullable = true)
	private Timestamp optOutTime;

	@Column(name = "ACQUISITION_BRAND_ID", nullable = false)
	private Integer acquisitionBrandId;

	@Column(name = "IS_CHAAYOS_CUSTOMER", nullable = false, length = 1)
	private String isChaayosCustomer;

	@Column(name = "GENDER", nullable = true, length = 10)
	private String gender;

	@Column(name = "DATE_OF_BIRTH", nullable = true)
	private Timestamp dateOfBirth;

	@Column(name = "ANNIVERSARY", nullable = true)
	private Timestamp anniversary;

	@Column(name = "CUSTOMER_APP_ID", nullable = true, length = 55)
	private String customerAppId;

	@Column(name = "OPT_IN_WHATSAPP", nullable = true, length = 10)
	private String optInWhatsapp;

	@Column(name = "IS_DELETED", nullable = true, length = 1)
	private String isDeleted;

	@Column(name = "IS_RENEWED", nullable = true, length = 1)
	private String isRenewed;

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getMiddleName() {
		return middleName;
	}

	public void setMiddleName(String middleName) {
		this.middleName = middleName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public String getIsNumberVerified() {
		return isNumberVerified;
	}

	public void setIsNumberVerified(String isNumberVerified) {
		this.isNumberVerified = isNumberVerified;
	}

	public String getIsEmailVerified() {
		return isEmailVerified;
	}

	public void setIsEmailVerified(String isEmailVerified) {
		this.isEmailVerified = isEmailVerified;
	}

	public Timestamp getAddTime() {
		return addTime;
	}

	public void setAddTime(Timestamp addTime) {
		this.addTime = addTime;
	}

	public Timestamp getNumberVerificationTime() {
		return numberVerificationTime;
	}

	public void setNumberVerificationTime(Timestamp numberVerificationTime) {
		this.numberVerificationTime = numberVerificationTime;
	}

	public Timestamp getEmailVerificationTime() {
		return emailVerificationTime;
	}

	public void setEmailVerificationTime(Timestamp emailVerificationTime) {
		this.emailVerificationTime = emailVerificationTime;
	}

	public String getAcquisitionSource() {
		return acquisitionSource;
	}

	public void setAcquisitionSource(String acquisitionSource) {
		this.acquisitionSource = acquisitionSource;
	}

	public String getAcquisitionToken() {
		return acquisitionToken;
	}

	public void setAcquisitionToken(String acquisitionToken) {
		this.acquisitionToken = acquisitionToken;
	}

	public Integer getRegistrationUnitId() {
		return registrationUnitId;
	}

	public void setRegistrationUnitId(Integer registrationUnitId) {
		this.registrationUnitId = registrationUnitId;
	}

	public String getIsSmsSubscriber() {
		return isSmsSubscriber;
	}

	public void setIsSmsSubscriber(String isSmsSubscriber) {
		this.isSmsSubscriber = isSmsSubscriber;
	}

	public String getIsEmailSubscriber() {
		return isEmailSubscriber;
	}

	public void setIsEmailSubscriber(String isEmailSubscriber) {
		this.isEmailSubscriber = isEmailSubscriber;
	}

	public String getIsLoyaltySubscriber() {
		return isLoyaltySubscriber;
	}

	public void setIsLoyaltySubscriber(String isLoyaltySubscriber) {
		this.isLoyaltySubscriber = isLoyaltySubscriber;
	}

	public String getIsBlacklisted() {
		return isBlacklisted;
	}

	public void setIsBlacklisted(String isBlacklisted) {
		this.isBlacklisted = isBlacklisted;
	}

	public Integer getTrueCallerProfileId() {
		return trueCallerProfileId;
	}

	public void setTrueCallerProfileId(Integer trueCallerProfileId) {
		this.trueCallerProfileId = trueCallerProfileId;
	}

	public String getIsDnd() {
		return isDnd;
	}

	public void setIsDnd(String isDnd) {
		this.isDnd = isDnd;
	}

	public String getIsInternal() {
		return isInternal;
	}

	public void setIsInternal(String isInternal) {
		this.isInternal = isInternal;
	}

	public String getRefCode() {
		return refCode;
	}

	public void setRefCode(String refCode) {
		this.refCode = refCode;
	}

	public String getIsRefSubscriber() {
		return isRefSubscriber;
	}

	public void setIsRefSubscriber(String isRefSubscriber) {
		this.isRefSubscriber = isRefSubscriber;
	}

	public String getRefAcquisitionSource() {
		return refAcquisitionSource;
	}

	public void setRefAcquisitionSource(String refAcquisitionSource) {
		this.refAcquisitionSource = refAcquisitionSource;
	}

	public Timestamp getReferredOn() {
		return referredOn;
	}

	public void setReferredOn(Timestamp referredOn) {
		this.referredOn = referredOn;
	}

	public Integer getReferralDataId() {
		return referralDataId;
	}

	public void setReferralDataId(Integer referralDataId) {
		this.referralDataId = referralDataId;
	}

	public Integer getReferrerId() {
		return referrerId;
	}

	public void setReferrerId(Integer referrerId) {
		this.referrerId = referrerId;
	}

	public String getIsReferrerAwarded() {
		return isReferrerAwarded;
	}

	public void setIsReferrerAwarded(String isReferrerAwarded) {
		this.isReferrerAwarded = isReferrerAwarded;
	}

	public String getFaceId() {
		return faceId;
	}

	public void setFaceId(String faceId) {
		this.faceId = faceId;
	}

	public String getOptOutFaceIt() {
		return optOutFaceIt;
	}

	public void setOptOutFaceIt(String optOutFaceIt) {
		this.optOutFaceIt = optOutFaceIt;
	}

	public Timestamp getOptOutTime() {
		return optOutTime;
	}

	public void setOptOutTime(Timestamp optOutTime) {
		this.optOutTime = optOutTime;
	}

	public Integer getAcquisitionBrandId() {
		return acquisitionBrandId;
	}

	public void setAcquisitionBrandId(Integer acquisitionBrandId) {
		this.acquisitionBrandId = acquisitionBrandId;
	}

	public String getIsChaayosCustomer() {
		return isChaayosCustomer;
	}

	public void setIsChaayosCustomer(String isChaayosCustomer) {
		this.isChaayosCustomer = isChaayosCustomer;
	}

	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public Timestamp getDateOfBirth() {
		return dateOfBirth;
	}

	public void setDateOfBirth(Timestamp dateOfBirth) {
		this.dateOfBirth = dateOfBirth;
	}

	public Timestamp getAnniversary() {
		return anniversary;
	}

	public void setAnniversary(Timestamp anniversary) {
		this.anniversary = anniversary;
	}

	public String getCustomerAppId() {
		return customerAppId;
	}

	public void setCustomerAppId(String customerAppId) {
		this.customerAppId = customerAppId;
	}

	public String getOptInWhatsapp() {
		return optInWhatsapp;
	}

	public void setOptInWhatsapp(String optInWhatsapp) {
		this.optInWhatsapp = optInWhatsapp;
	}

	public String getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}

	public String getIsRenewed() {
		return isRenewed;
	}

	public void setIsRenewed(String isRenewed) {
		this.isRenewed = isRenewed;
	}

	public List<CustomerAddressInfo> getCustomerAddressInfos() {
		return customerAddressInfos;
	}

	public void setCustomerAddressInfos(List<CustomerAddressInfo> customerAddressInfos) {
		this.customerAddressInfos = customerAddressInfos;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		CustomerInfo that = (CustomerInfo) o;
		return Objects.equals(customerId, that.customerId) && Objects.equals(firstName, that.firstName)
				&& Objects.equals(middleName, that.middleName) && Objects.equals(lastName, that.lastName)
				&& Objects.equals(countryCode, that.countryCode) && Objects.equals(contactNumber, that.contactNumber)
				&& Objects.equals(emailId, that.emailId) && Objects.equals(isNumberVerified, that.isNumberVerified)
				&& Objects.equals(isEmailVerified, that.isEmailVerified) && Objects.equals(addTime, that.addTime)
				&& Objects.equals(numberVerificationTime, that.numberVerificationTime)
				&& Objects.equals(emailVerificationTime, that.emailVerificationTime)
				&& Objects.equals(acquisitionSource, that.acquisitionSource)
				&& Objects.equals(acquisitionToken, that.acquisitionToken)
				&& Objects.equals(registrationUnitId, that.registrationUnitId)
				&& Objects.equals(isSmsSubscriber, that.isSmsSubscriber)
				&& Objects.equals(isEmailSubscriber, that.isEmailSubscriber)
				&& Objects.equals(isLoyaltySubscriber, that.isLoyaltySubscriber)
				&& Objects.equals(isBlacklisted, that.isBlacklisted)
				&& Objects.equals(trueCallerProfileId, that.trueCallerProfileId) && Objects.equals(isDnd, that.isDnd)
				&& Objects.equals(isInternal, that.isInternal) && Objects.equals(refCode, that.refCode)
				&& Objects.equals(isRefSubscriber, that.isRefSubscriber)
				&& Objects.equals(refAcquisitionSource, that.refAcquisitionSource)
				&& Objects.equals(referredOn, that.referredOn) && Objects.equals(referralDataId, that.referralDataId)
				&& Objects.equals(referrerId, that.referrerId)
				&& Objects.equals(isReferrerAwarded, that.isReferrerAwarded) && Objects.equals(faceId, that.faceId)
				&& Objects.equals(optOutFaceIt, that.optOutFaceIt) && Objects.equals(optOutTime, that.optOutTime)
				&& Objects.equals(acquisitionBrandId, that.acquisitionBrandId)
				&& Objects.equals(isChaayosCustomer, that.isChaayosCustomer) && Objects.equals(gender, that.gender)
				&& Objects.equals(dateOfBirth, that.dateOfBirth) && Objects.equals(anniversary, that.anniversary)
				&& Objects.equals(optInWhatsapp, that.optInWhatsapp) && Objects.equals(isDeleted, that.isDeleted);
	}

	@Override
	public int hashCode() {
		return Objects.hash(customerId, firstName, middleName, lastName, countryCode, contactNumber, emailId,
				isNumberVerified, isEmailVerified, addTime, numberVerificationTime, emailVerificationTime,
				acquisitionSource, acquisitionToken, registrationUnitId, isSmsSubscriber, isEmailSubscriber,
				isLoyaltySubscriber, isBlacklisted, trueCallerProfileId, isDnd, isInternal, refCode, isRefSubscriber,
				refAcquisitionSource, referredOn, referralDataId, referrerId, isReferrerAwarded, faceId, optOutFaceIt,
				optOutTime, acquisitionBrandId, isChaayosCustomer, gender, dateOfBirth, anniversary, customerAppId,
				optInWhatsapp, isDeleted, isRenewed);
	}
}
