package com.stpl.tech.kettle.data.master;

import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "STATE_DETAIL")
@Getter
@Setter
public class StateDetail {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "STATE_DETAIL_ID", nullable = false, unique = true)
	private Integer id;

	@Column(name = "STATE", nullable = false, length = 100)
	private String state;

	@Column(name = "STATE_CODE", nullable = false, length = 10)
	private String stateCode;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "COUNTRY_DETAIL_ID", nullable = false)
	private CountryDetail country;

	@Column(name = "STATE_SHORT_CODE", nullable = false, length = 10)
	private String stateShortCode;

	@Column(name = "IS_UT", nullable = false, length = 1)
	private String isUt;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "state")
	private List<LocationDetail> locations = new ArrayList<LocationDetail>(0);

	@Column(name = "FUNCTIONAL_FLAG", nullable = false, length = 1)
	private String functionalFlag;

}
