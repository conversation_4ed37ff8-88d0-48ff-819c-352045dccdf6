package com.stpl.tech.kettle.data.master;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "DELIVERY_COUPON_ALLOCATION_DETAIL_DATA")
public class DeliveryCouponAllocationDetailData implements Serializable {

	private static final long serialVersionUID = -4918964006475742867L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ALLOCATION_ID", unique = true, nullable = false)
	private Integer allocationId;
	@Column(name = "DELIVERY_COUPON_ID")
	private Integer deliveryCouponId;
	@Column(name = "CUSTOMER_ID")
	private Integer customerId;
	@Column(name = "CONTACT_NUMBER")
	private String contactNumber;
	@Column(name = "CAMPAIGN_ID")
	private Integer campaignId;
	@Column(name = "ALLOTMENT_TIME")
	private Date allotmentTime;
	@Column(name = "ALLOTMENT_ORDER_ID")
	private Integer allotmentOrderId;

}
