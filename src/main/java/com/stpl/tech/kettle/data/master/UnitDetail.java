package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "UNIT_DETAIL")
@NoArgsConstructor
public class UnitDetail implements Serializable {

	private static final long serialVersionUID = 2964468400272409403L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_ID", unique = true, nullable = false)
	private Integer unitId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "BUSINESS_DIV_ID", nullable = false)
	private BusinessDivision businessDivision;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_ADDR_ID", nullable = false)
	private AddressInfo addressInfo;

	@Column(name = "UNIT_NAME", nullable = false)
	private String unitName;

	@Column(name = "UNIT_REGION", nullable = false, length = 25)
	private String unitRegion;

	@Column(name = "UNIT_EMAIL", nullable = false, length = 50)
	private String unitEmail;

	@Column(name = "UNIT_CATEGORY", nullable = false, length = 30)
	private String unitCategory;

	@Column(name = "UNIT_SUB_CATEGORY", nullable = false)
	private String unitSubCategory;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "START_DATE", nullable = true, length = 19)
	private Date startDate;

	@Column(name = "UNIT_STATUS", nullable = false, length = 15)
	private String unitStatus;

	@Column(name = "TIN", nullable = true, length = 15)
	private String tin;

	@Column(name = "FSSAI")
	private String fssai;

	@Column(name = "GSTIN", nullable = true, length = 20)
	private String gstin;

	@Column(name = "NO_OF_TERMINALS", nullable = false)
	private Integer noOfTerminals;

	@Column(name = "NO_OF_TA_TERMINALS", nullable = true)
	private int noOfTakeawayTerminals;

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_MANAGER", nullable = true)
	private EmployeeDetail unitManager;

	@Column(name = "UNIT_REFERENCE_NAME", nullable = true)
	private String referenceName;

	@Column(name = "COMMUNICATION_CHANNEL", nullable = true)
	private String communicationChannel;

	@Column(name = "CREDIT_ACCOUNT_ID", nullable = true)
	private Integer creditAccountId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LOCATION_DETAIL_ID", nullable = false)
	private LocationDetail location;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitDetail")
	private List<UnitProductMapping> unitProductMappings = new ArrayList<UnitProductMapping>(0);

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitDetail")
	private List<UnitAttributeMapping> unitAttributeMappings = new ArrayList<UnitAttributeMapping>(0);

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitDetail")
	private List<UnitTaxMapping> unitTaxMappings = new ArrayList<UnitTaxMapping>(0);

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitDetail")
	private List<UnitPaymentModeMapping> unitPaymentMappings = new ArrayList<UnitPaymentModeMapping>(0);

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitDetail")
	private List<BusinessHours> businessHours = new ArrayList<BusinessHours>(0);

	@Column(name = "BUSINESS_TYPE", nullable = true)
	private String businessType;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "COMPANY_ID", nullable = false)
	private CompanyDetail companyDetail;

	@Column(name = "CAFE_MANAGER")
	private Integer cafeManager;

	@Column(name = "IS_LIVE")
	private String isLive;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "HANDOVER_DATE")
	private Date handoverDate;

	@Column(name = "CAFE_APP_STATUS")
	private String cafeAppStatus;

	@Column(name = "CAFE_NEO_STATUS")
	private String cafeNeoStatus;

	@Column(name = "SHORT_NAME")
	private String shortName;

	@Column(name = "COST_CENTER")
	private String costCenter;

	@Column(name = "CONSIDERED_FOR_ACCOUNTING")
	private String consideredForAccounting;

	@Column(name = "SHORT_CODE")
	private String shortCode;

	@Column(name = "CLONED_FROM")
	private Integer clonedFrom;

	@Column(name = "SALES_CLONED_FROM")
	private Integer salesClonedFrom;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "PROBABLE_OPENING_DATE")
	private Date probableOpeningDate;

	@Column(name = "PRICING_PROFILE")
	private Integer pricingProfile;

	@Column(name = "UNIT_ZONE")
	private String unitZone;

	@Column(name = "F9_ENABLED")
	private String f9Enabled;

	@Column(name = "IS_CLOSED")
	private String isClosed;

	@Column(name = "CUSTOMER_LOGIN")
	private String customerLogin;

	public UnitDetail(BusinessDivision businessDivision, AddressInfo addressInfo, String unitName, String unitRegion,
			String unitEmail, String unitCategory, Date startDate, String unitStatus, String tin, int noOfTerminals,
			int noOfTakeawayTerminals, String unitSubCategory, String shortName) {
		this.businessDivision = businessDivision;
		this.addressInfo = addressInfo;
		this.unitName = unitName;
		this.unitRegion = unitRegion;
		this.unitEmail = unitEmail;
		this.unitCategory = unitCategory;
		this.unitSubCategory = unitSubCategory;
		this.startDate = startDate;
		this.unitStatus = unitStatus;
		this.gstin = tin;
		this.noOfTerminals = noOfTerminals;
		this.noOfTakeawayTerminals = noOfTakeawayTerminals;
		this.unitSubCategory = unitSubCategory;
		this.shortName = shortName;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public BusinessDivision getBusinessDivision() {
		return businessDivision;
	}

	public void setBusinessDivision(BusinessDivision businessDivision) {
		this.businessDivision = businessDivision;
	}

	public AddressInfo getAddressInfo() {
		return addressInfo;
	}

	public void setAddressInfo(AddressInfo addressInfo) {
		this.addressInfo = addressInfo;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getUnitRegion() {
		return unitRegion;
	}

	public void setUnitRegion(String unitRegion) {
		this.unitRegion = unitRegion;
	}

	public String getUnitEmail() {
		return unitEmail;
	}

	public void setUnitEmail(String unitEmail) {
		this.unitEmail = unitEmail;
	}

	public String getUnitCategory() {
		return unitCategory;
	}

	public void setUnitCategory(String unitCategory) {
		this.unitCategory = unitCategory;
	}

	public String getUnitSubCategory() {
		return unitSubCategory;
	}

	public void setUnitSubCategory(String unitSubCategory) {
		this.unitSubCategory = unitSubCategory;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public String getUnitStatus() {
		return unitStatus;
	}

	public void setUnitStatus(String unitStatus) {
		this.unitStatus = unitStatus;
	}

	public String getTin() {
		return tin;
	}

	public void setTin(String tin) {
		this.tin = tin;
	}

	public String getFssai() {
		return fssai;
	}

	public void setFssai(String fssai) {
		this.fssai = fssai;
	}

	public String getGstin() {
		return gstin;
	}

	public void setGstin(String gstin) {
		this.gstin = gstin;
	}

	public Integer getNoOfTerminals() {
		return noOfTerminals;
	}

	public void setNoOfTerminals(Integer noOfTerminals) {
		this.noOfTerminals = noOfTerminals;
	}

	public int getNoOfTakeawayTerminals() {
		return noOfTakeawayTerminals;
	}

	public void setNoOfTakeawayTerminals(int noOfTakeawayTerminals) {
		this.noOfTakeawayTerminals = noOfTakeawayTerminals;
	}

	public EmployeeDetail getUnitManager() {
		return unitManager;
	}

	public void setUnitManager(EmployeeDetail unitManager) {
		this.unitManager = unitManager;
	}

	public String getReferenceName() {
		return referenceName;
	}

	public void setReferenceName(String referenceName) {
		this.referenceName = referenceName;
	}

	public String getCommunicationChannel() {
		return communicationChannel;
	}

	public void setCommunicationChannel(String communicationChannel) {
		this.communicationChannel = communicationChannel;
	}

	public Integer getCreditAccountId() {
		return creditAccountId;
	}

	public void setCreditAccountId(Integer creditAccountId) {
		this.creditAccountId = creditAccountId;
	}

	public LocationDetail getLocation() {
		return location;
	}

	public void setLocation(LocationDetail location) {
		this.location = location;
	}

	@OrderBy("productDetail.productId ASC")
	public List<UnitProductMapping> getUnitProductMappings() {
		return unitProductMappings;
	}

	public void setUnitProductMappings(List<UnitProductMapping> unitProductMappings) {
		this.unitProductMappings = unitProductMappings;
	}

	public List<UnitAttributeMapping> getUnitAttributeMappings() {
		return unitAttributeMappings;
	}

	public void setUnitAttributeMappings(List<UnitAttributeMapping> unitAttributeMappings) {
		this.unitAttributeMappings = unitAttributeMappings;
	}

	@OrderBy("taxProfile.taxProfileId ASC")
	public List<UnitTaxMapping> getUnitTaxMappings() {
		return unitTaxMappings;
	}

	public void setUnitTaxMappings(List<UnitTaxMapping> unitTaxMappings) {
		this.unitTaxMappings = unitTaxMappings;
	}

	@OrderBy("paymentMode.paymentModeId ASC")
	public List<UnitPaymentModeMapping> getUnitPaymentMappings() {
		return unitPaymentMappings;
	}

	public void setUnitPaymentMappings(List<UnitPaymentModeMapping> unitPaymentMappings) {
		this.unitPaymentMappings = unitPaymentMappings;
	}

	@OrderBy("dayOfTheWeekNumber ASC")
	public List<BusinessHours> getBusinessHours() {
		return businessHours;
	}

	public void setBusinessHours(List<BusinessHours> businessHours) {
		this.businessHours = businessHours;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public CompanyDetail getCompanyDetail() {
		return companyDetail;
	}

	public void setCompanyDetail(CompanyDetail companyDetail) {
		this.companyDetail = companyDetail;
	}

	public Integer getCafeManager() {
		return cafeManager;
	}

	public void setCafeManager(Integer cafeManager) {
		this.cafeManager = cafeManager;
	}

	public String getIsLive() {
		return isLive;
	}

	public void setIsLive(String isLive) {
		this.isLive = isLive;
	}

	public Date getHandoverDate() {
		return handoverDate;
	}

	public void setHandoverDate(Date handoverDate) {
		this.handoverDate = handoverDate;
	}

	public String getCafeAppStatus() {
		return cafeAppStatus;
	}

	public void setCafeAppStatus(String cafeAppStatus) {
		this.cafeAppStatus = cafeAppStatus;
	}

	public String getCafeNeoStatus() {
		return cafeNeoStatus;
	}

	public void setCafeNeoStatus(String cafeNeoStatus) {
		this.cafeNeoStatus = cafeNeoStatus;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public String getCostCenter() {
		return costCenter;
	}

	public void setCostCenter(String costCenter) {
		this.costCenter = costCenter;
	}

	public String getConsideredForAccounting() {
		return consideredForAccounting;
	}

	public void setConsideredForAccounting(String consideredForAccounting) {
		this.consideredForAccounting = consideredForAccounting;
	}

	public String getShortCode() {
		return shortCode;
	}

	public void setShortCode(String shortCode) {
		this.shortCode = shortCode;
	}

	public Integer getClonedFrom() {
		return clonedFrom;
	}

	public void setClonedFrom(Integer clonedFrom) {
		this.clonedFrom = clonedFrom;
	}

	public Integer getSalesClonedFrom() {
		return salesClonedFrom;
	}

	public void setSalesClonedFrom(Integer salesClonedFrom) {
		this.salesClonedFrom = salesClonedFrom;
	}

	public Date getProbableOpeningDate() {
		return probableOpeningDate;
	}

	public void setProbableOpeningDate(Date probableOpeningDate) {
		this.probableOpeningDate = probableOpeningDate;
	}

	public Integer getPricingProfile() {
		return pricingProfile;
	}

	public void setPricingProfile(Integer pricingProfile) {
		this.pricingProfile = pricingProfile;
	}

	public String getUnitZone() {
		return unitZone;
	}

	public void setUnitZone(String unitZone) {
		this.unitZone = unitZone;
	}

	public String getF9Enabled() {
		return f9Enabled;
	}

	public void setF9Enabled(String f9Enabled) {
		this.f9Enabled = f9Enabled;
	}

	public String getIsClosed() {
		return isClosed;
	}

	public void setIsClosed(String isClosed) {
		this.isClosed = isClosed;
	}

	public String getCustomerLogin() {
		return customerLogin;
	}

	public void setCustomerLogin(String customerLogin) {
		this.customerLogin = customerLogin;
	}
}
