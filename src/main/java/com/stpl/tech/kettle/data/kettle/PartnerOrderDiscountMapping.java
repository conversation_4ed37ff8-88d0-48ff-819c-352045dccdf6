
package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "PARTNER_ORDER_DISCOUNT_MAPPING")
@Getter
@Setter
public class PartnerOrderDiscountMapping implements java.io.Serializable {

	private static final long serialVersionUID = -1107745531565329522L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_DISCOUNT_MAPPING_ID", unique = true, nullable = false)
	private Integer orderDiscountMappingId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ID", nullable = false)
	private OrderDetail orderDetail;
	@Column(name = "BRAND_ID", nullable = true)
	private Integer brandId;
	@Column(name = "PARTNER_NAME", nullable = true)
	private String partnerName;
	@Column(name = "DISCOUNT_NAME", nullable = true)
	private String discountName;
	@Column(name = "DISCOUNT_TYPE", nullable = true)
	private String discountType;
	@Column(name = "DISCOUNT_CATEGORY", nullable = true)
	private String discountCategory;
	@Column(name = "DISCOUNT_VALUE", nullable = true)
	private BigDecimal discountValue;
	@Column(name = "DISCOUNT_AMOUNT", nullable = true)
	private BigDecimal discountAmount;
	@Column(name = "DISCOUNT_TAXED", nullable = true)
	private String discountIsTaxed;
	@Column(name = "DISCOUNT_APPLIEDON", nullable = true)
	private BigDecimal discountAppliedOn;
	@Column(name = "VOUCHER_CODE", nullable = true)
	private String voucherCode;
	@Column(name = "IS_PARTNER_DISCOUNT", nullable = true)
	private String isPartnerDiscount;

}
