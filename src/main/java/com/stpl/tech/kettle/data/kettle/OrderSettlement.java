
package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "ORDER_SETTLEMENT")
@NoArgsConstructor
@Getter
@Setter
public class OrderSettlement implements java.io.Serializable {

	private static final long serialVersionUID = -4339378331276670854L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SETTLEMENT_ID", unique = true, nullable = false)
	private Integer settlementId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ID", nullable = false)
	private OrderDetail orderDetail;
	@Column(name = "PAYMENT_MODE_ID", nullable = false)
	private int paymentModeId;
	@Column(name = "AMOUNT_PAID", precision = 10)
	private BigDecimal amountPaid;
	@Column(name = "ROUND_OFF_AMOUNT", precision = 10)
	private BigDecimal roundOffAmount;
	@Column(name = "EXTRA_VOUCHERS", precision = 10)
	private BigDecimal extraVouchers;
	@Column(name = "IS_EDITED", nullable = true)
	private String edited;
	@Column(name = "EDITED_BY", nullable = true)
	private Integer editedBy;
	@Column(name = "PREVIOUS_PAYMENT_MODE_ID", nullable = true)
	private Integer previousPaymentMode;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EDIT_TIME", nullable = true, length = 19)
	private Date editTime;
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderSettlement")
	private List<OrderPaymentDenominationDetail> denominations = new ArrayList<OrderPaymentDenominationDetail>();
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderSettlement")
	private List<OrderExternalSettlementData> externalTransactions = new ArrayList<OrderExternalSettlementData>();

	public OrderSettlement(OrderDetail orderDetail, int paymentModeId) {
		this.orderDetail = orderDetail;
		this.paymentModeId = paymentModeId;
	}

	public OrderSettlement(OrderDetail orderDetail, int paymentModeId, BigDecimal amountPaid,
			BigDecimal roundOffAmount) {
		this.orderDetail = orderDetail;
		this.paymentModeId = paymentModeId;
		this.amountPaid = amountPaid;
		this.roundOffAmount = roundOffAmount;
	}

}
