package com.stpl.tech.kettle.data.master;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "DENOMINATION")
@Getter
@Setter
public class Denomination implements Serializable {

	private static final long serialVersionUID = -6377755537542568058L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "DENOMINATION_ID", unique = true, nullable = false)
	private Integer id;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PAYMENT_MODE", nullable = false)
	private PaymentMode paymentMode;

	@Column(name = "DENOMINATION_TEXT", nullable = false)
	private String denominationText;

	@Column(name = "DENOMINATION_CODE", nullable = false)
	private String denominationCode;

	@Column(name = "DENOMINATION_VALUE", nullable = false)
	private Integer denominationValue;

	@Column(name = "DISPLAY_ORDER", nullable = false)
	private Integer displayOrder;

	@Column(name = "STATUS", nullable = false)
	private String status;

	@Column(name = "BUNDLE_SIZE", nullable = false)
	private Integer bundleSize;

}
