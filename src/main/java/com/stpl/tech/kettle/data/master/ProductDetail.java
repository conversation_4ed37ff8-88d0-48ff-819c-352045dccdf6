package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "PRODUCT_DETAIL")
@Getter
@Setter
@NoArgsConstructor
public class ProductDetail implements java.io.Serializable {

	private static final long serialVersionUID = 5714792789155313488L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "PRODUCT_ID", unique = true, nullable = false)
	private Integer productId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "DIMENSION_CODE", nullable = false)
	private RefLookupType dimensionCode;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ADDITIONAL_ITEM_TYPES")
	private RefLookupType addonTypes;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PRODUCT_SUB_TYPE", nullable = false)
	private RefLookup productSubType;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PRODUCT_TYPE", nullable = false)
	private RefLookupType productType;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "WEB_CATEGORY_TYPE", nullable = true)
	private RefLookup webType;

	@Column(name = "PRODUCT_NAME", nullable = false)
	private String productName;

	@Column(name = "PRODUCT_DESCRIPTION", nullable = false, length = 5000)
	private String productDescription;

	@Column(name = "PRODUCT_STATUS", nullable = false, length = 20)
	private String productStatus;

	@Column(name = "PRODUCT_CLASSIFICATION", nullable = false)
	private String classification;

	@Column(name = "SHORT_CODE", nullable = false, length = 6)
	private String shortCode;

	@Column(name = "IS_INVENTORY_TRACKED", nullable = false, length = 1)
	private String isInventoryTracked;

	@Column(name = "EMPLOYEE_MEAL_COMPONENT", nullable = false, length = 1)
	private String employeeMealComponent;

	@Column(name = "ATTRIBUTE", length = 20)
	private String attribute;

	@Temporal(TemporalType.DATE)
	@Column(name = "PRODUCT_START_DATE", nullable = false, length = 10)
	private Date productStartDate;

	@Temporal(TemporalType.DATE)
	@Column(name = "PRODUCT_END_DATE", nullable = false, length = 10)
	private Date productEndDate;

	@Column(name = "PRODUCT_SKU_CODE", nullable = false, length = 30)
	private String productSkuCode;

	@Column(name = "PRICE_TYPE", nullable = false, length = 10)
	private String priceType;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "IN_TMSTMP", nullable = false, length = 19)
	private Date inTmstmp;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "OUT_TMSTMP", nullable = false, length = 19)
	private Date outTmstmp;

	@Column(name = "TAX_CODE", nullable = true, length = 20)
	private String taxCode;

	@Column(name = "PREPARATION_MODE", nullable = true, length = 15)
	private String preparationMode;

	@Column(name = "SUPPORTS_VARIANT_LEVEL_ORDERING")
	private String supportsVariantLevelOrdering;

	@Column(name = "BRAND_ID")
	private Integer brandId;

	@Column(name = "TAXABLE_COGS")
	private String taxableCogs;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "productDetailByConstituentProductId")
	private Set<ProductRelationship> productRelationshipsForConstituentProductId = new HashSet<>(0);

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "productDetailByProductId")
	private Set<ProductRelationship> productRelationshipsForProductId = new HashSet<>(0);

	@Column(name = "PREP_TIME")
	private BigDecimal prepTime;

	public ProductDetail(RefLookupType dimensionCode, RefLookup productSubType, RefLookupType productType,
			String productName, String productDescription, String productStatus, Date productStartDate,
			Date productEndDate, String productSkuCode, String priceType, Date inTmstmp, Date outTmstmp) {
		this.dimensionCode = dimensionCode;
		this.productSubType = productSubType;
		this.productType = productType;
		this.productName = productName;
		this.productDescription = productDescription;
		this.productStatus = productStatus;
		this.productStartDate = productStartDate;
		this.productEndDate = productEndDate;
		this.productSkuCode = productSkuCode;
		this.priceType = priceType;
		this.inTmstmp = inTmstmp;
		this.outTmstmp = outTmstmp;
	}

	public ProductDetail(RefLookupType dimensionCode, RefLookupType addonTypes, RefLookup productSubType,
			RefLookupType productType, String productName, String productDescription, String productStatus,
			String attribute, Date productStartDate, Date productEndDate, String productSkuCode, String priceType,
			Date inTmstmp, Date outTmstmp, Set<ProductRelationship> productRelationshipsForConstituentProductId,
			Set<ProductRelationship> productRelationshipsForProductId) {
		this.dimensionCode = dimensionCode;
		this.addonTypes = addonTypes;
		this.productSubType = productSubType;
		this.productType = productType;
		this.productName = productName;
		this.productDescription = productDescription;
		this.productStatus = productStatus;
		this.attribute = attribute;
		this.productStartDate = productStartDate;
		this.productEndDate = productEndDate;
		this.productSkuCode = productSkuCode;
		this.priceType = priceType;
		this.inTmstmp = inTmstmp;
		this.outTmstmp = outTmstmp;
		this.productRelationshipsForConstituentProductId = productRelationshipsForConstituentProductId;
		this.productRelationshipsForProductId = productRelationshipsForProductId;
	}

}
