package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "INVENTORY_LOG_DATA")
public class InventoryLogData implements Serializable {

	private static final long serialVersionUID = 7984843889108905724L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "INVENTORY_LOG_ID")
	private int id;

	@Column(name = "UNIT_ID")
	private int unitId;

	@Column(name = "PRODUCT_ID")
	private int productId;

	@Column(name = "EMPLOYEE_ID")
	private int employeeId;

	@Column(name = "EVENT_TYPE")
	private String eventType;

	@Column(name = "REASON_CODE")
	private String reasonCode;

	@Column(name = "UPDATE_TIME")
	public Date updateTime;

	@Column(name = "BUSINESS_DATE")
	public Date businessDate;

	@Column(name = "ORDER_ID", nullable = true)
	public Integer orderId;

	@Override
	public String toString() {
		return "InventoryLogData{" + "productId=" + id + ", unitId=" + unitId + ", productId=" + productId
				+ ", employeeId=" + employeeId + ", eventType='" + eventType + '\'' + ", updateTime=" + updateTime
				+ ", reasonCode='" + reasonCode + '\'' + '}';
	}
}
