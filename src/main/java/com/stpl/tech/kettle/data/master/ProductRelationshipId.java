package com.stpl.tech.kettle.data.master;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Embeddable
@Getter
@Setter
@NoArgsConstructor
public class ProductRelationshipId implements java.io.Serializable {

	private static final long serialVersionUID = -4197263878555739911L;

	@Column(name = "PRODUCT_ID", nullable = false)
	private int productId;

	@Column(name = "CONSTITUENT_PRODUCT_ID", nullable = false)
	private int constituentProductId;

	@Column(name = "RELATIONSHIP_TYPE", nullable = false, length = 10)
	private String relationshipType;

	@Column(name = "QUANTITY", nullable = false)
	private int quantity;

	@Column(name = "PRICE_MULTIPLIER", nullable = false, precision = 4, scale = 4)
	private BigDecimal priceMultiplier;

	public ProductRelationshipId(int productId, int constituentProductId, String relationshipType, int quantity,
			BigDecimal priceMultiplier) {
		this.productId = productId;
		this.constituentProductId = constituentProductId;
		this.relationshipType = relationshipType;
		this.quantity = quantity;
		this.priceMultiplier = priceMultiplier;
	}

	public boolean equals(Object other) {
		if ((this == other)) {
			return true;
		}
		if ((other == null)) {
			return false;
		}
		if (!(other instanceof ProductRelationshipId)) {
			return false;
		}
		ProductRelationshipId castOther = (ProductRelationshipId) other;

		return (this.getProductId() == castOther.getProductId())
				&& (this.getConstituentProductId() == castOther.getConstituentProductId())
				&& ((this.getRelationshipType() == castOther.getRelationshipType())
						|| (this.getRelationshipType() != null && castOther.getRelationshipType() != null
								&& this.getRelationshipType().equals(castOther.getRelationshipType())))
				&& (this.getQuantity() == castOther.getQuantity())
				&& ((this.getPriceMultiplier() == castOther.getPriceMultiplier())
						|| (this.getPriceMultiplier() != null && castOther.getPriceMultiplier() != null
								&& this.getPriceMultiplier().equals(castOther.getPriceMultiplier())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result + this.getProductId();
		result = 37 * result + this.getConstituentProductId();
		result = 37 * result + (getRelationshipType() == null ? 0 : this.getRelationshipType().hashCode());
		result = 37 * result + this.getQuantity();
		result = 37 * result + (getPriceMultiplier() == null ? 0 : this.getPriceMultiplier().hashCode());
		return result;
	}

}
