/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.kettle;


import jakarta.persistence.*;
import lombok.*;

import java.io.Serial;

import static jakarta.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "COMPLIMENTARY_CODE")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ComplimentaryCode implements java.io.Serializable {


	@Serial
	private static final long serialVersionUID = 540712523445629563L;
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "COMP_ID", unique = true, nullable = false)
	private Integer id;
	@Column(name = "COMP_CODE", nullable = false, length = 50)
	private String code;
	@Column(name = "CATEGORY", nullable = false, length = 15)
	private String category;
	@Column(name = "NAME", nullable = false, length = 100)
	private String name;
	@Column(name = "DESCRIPTION", length = 100)
	private String description;
	@Column(name = "STATUS", nullable = false, length = 10)
	private String status;
	@Column(name = "IS_ACCOUNTABLE", nullable = false, length = 1)
	private String isAccountable;
}
