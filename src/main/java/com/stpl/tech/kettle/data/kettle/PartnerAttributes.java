package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.*;
import lombok.ToString;


@ToString
@Entity
@Table(name = "PARTNER_ATTRIBUTES")
public class PartnerAttributes {
    private int id;
    private int partnerId;
    private String mappingType;
    private String mappingValue;
    private String partnerType;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Column(name = "PARTNER_ID", nullable = false)
    public int getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(int partnerId) {
        this.partnerId = partnerId;
    }

    @Column(name = "MAPPING_TYPE", nullable = false)
    public String getMappingType() {
        return mappingType;
    }

    public void setMappingType(String mappingType) {
        this.mappingType = mappingType;
    }

    @Column(name = "MAPPING_VALUE", nullable = false)
    public String getMappingValue() {
        return mappingValue;
    }

    public void setMappingValue(String mappingValue) {
        this.mappingValue = mappingValue;
    }

    @Column(name = "PARTNER_TYPE", nullable = false)
    public String getPartnerType() {
        return partnerType;
    }

    public void setPartnerType(String partnerType) {
        this.partnerType = partnerType;
    }
}
