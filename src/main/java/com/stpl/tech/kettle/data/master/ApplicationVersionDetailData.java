package com.stpl.tech.kettle.data.master;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Entity
@Table(name = "APPLICATION_VERSION_DETAIL_DATA")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplicationVersionDetailData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    private Integer id;
    @Column(name = "APPLICATION_NAME")
    private String applicationName;
    @Column(name = "UNIT_ID")
    private Integer unitId;
    @Column(name = "UNIT_REGION")
    private String unitRegion;
    @Column(name = "APPLICATION_VERSION")
    private String applicationVersion;
    @Column(name = "VERSION_STATUS")
    private String versionStatus;
    @Column(name = "LAST_UPDATE_TIME")
    private Date lastUpdateTime;
    @Column(name = "TERMINAL")
    private Integer terminal;

}
