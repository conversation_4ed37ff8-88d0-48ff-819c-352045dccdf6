/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.master;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

import static jakarta.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "EMPLOYEE_UNIT_MAPPING")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeUnitMapping implements java.io.Serializable {

	private static final long serialVersionUID = 6056330838231520834L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EMP_UNIT_KEY_ID", unique = true, nullable = false)
	private Integer empUnitKeyId;
	@Column(name = "EMP_ID", nullable = false)
	private Integer employeeId;
	@Column(name = "UNIT_ID", nullable = false)
	private Integer unitId;
	@Column(name = "MAPPING_STATUS", nullable = false, length = 15)
	private String mappingStatus;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = false, length = 19)
	private Date lastUpdateTime;
}
