package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "BUSINESS_DIVISION")
@Getter
@Setter
@NoArgsConstructor
public class BusinessDivision implements Serializable {

	private static final long serialVersionUID = -9214657938318398469L;
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "BUSINESS_DIV_ID", unique = true, nullable = false)
	private Integer businessDivId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "COMPANY_ID", nullable = false)
	private CompanyDetail companyDetail;
	@Column(name = "BUSINESS_DIV_NAME", nullable = false)
	private String businessDivName;
	@Column(name = "BUSIENSS_DIV_DESC", nullable = false, length = 5000)
	private String busienssDivDesc;
	@Column(name = "BUSIENSS_DIV_CATEGORY", nullable = false, length = 30)
	private String busienssDivCategory;
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "businessDivision")
	private Set<UnitDetail> unitDetails = new HashSet<UnitDetail>(0);
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "businessDivision")
	private Set<Department> departments = new HashSet<Department>(0);

	public BusinessDivision(CompanyDetail companyDetail, String businessDivName, String busienssDivDesc,
			String busienssDivCategory) {
		this.companyDetail = companyDetail;
		this.businessDivName = businessDivName;
		this.busienssDivDesc = busienssDivDesc;
		this.busienssDivCategory = busienssDivCategory;
	}

	public BusinessDivision(CompanyDetail companyDetail, String businessDivName, String busienssDivDesc,
			String busienssDivCategory, Set<UnitDetail> unitDetails, Set<Department> departments) {
		this.companyDetail = companyDetail;
		this.businessDivName = businessDivName;
		this.busienssDivDesc = busienssDivDesc;
		this.busienssDivCategory = busienssDivCategory;
		this.unitDetails = unitDetails;
		this.departments = departments;
	}
}
