package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "INVENTORY_UPDATE_EVENT")
public class InventoryUpdateEvent implements Serializable {

	private static final long serialVersionUID = -1786806393243549704L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "INVENTORY_UPDATE_EVENT_ID", unique = true, nullable = false)
	private Integer inventoryUpdateEventId;
	@Column(name = "UNIT_ID", nullable = false)
	private int unitId;
	@Column(name = "RECORDS_COUNT", nullable = false)
	private int recordsCount;
	@Column(name = "EVENT_TYPE", nullable = false, length = 15)
	private String eventType;
	@Column(name = "UPDATE_COMMENT", nullable = true, length = 300)
	private String updateComment;
	@Column(name = "UPDATED_BY", nullable = true)
	private int updatedBy;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATE_TIME", nullable = false, length = 19)
	private Date updateTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "BUSINESS_DATE", nullable = false, length = 19)
	private Date businessDate;
	@Column(name = "UPDATE_STATUS", nullable = false, length = 15)
	private String updateStatus;
}
