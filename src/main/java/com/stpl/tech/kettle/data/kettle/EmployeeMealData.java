/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "EMPLOYEE_MEAL_DATA")
@Getter
@Setter
public class EmployeeMealData implements java.io.Serializable {

	private static final long serialVersionUID = 5765178073397127426L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EMPLOYEE_MEAL_DATA_ID", unique = true, nullable = false)
	private Integer employeeMealDataId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ID", nullable = false)
	private OrderDetail orderDetail;
	@Column(name = "PRODUCT_ID", nullable = false)
	private int productId;
	@Column(name = "UNIT_ID", nullable = false)
	private int unitId;
	@Column(name = "EMPLOYEE_ID", nullable = false)
	private int employeeId;
	@Column(name = "PRODUCT_TYPE_ID", nullable = false)
	private int productTypeId;
	@Column(name = "QUANTITY", nullable = false)
	private int quantity;
	@Temporal(TemporalType.DATE)
	@Column(name = "BUSINESS_DATE", nullable = false, length = 10)
	private Date businessDate;
	@Column(name = "DIMENSION_CODE", length = 10)
	private String dimension;

}
