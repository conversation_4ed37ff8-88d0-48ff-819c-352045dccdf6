package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import com.stpl.tech.kettle.domain.model.CustomerEmailData;

import jakarta.persistence.Column;
import jakarta.persistence.ColumnResult;
import jakarta.persistence.ConstructorResult;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.NamedNativeQuery;
import jakarta.persistence.SqlResultSetMapping;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "CUSTOMER_OFFER_DETAIL")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@SqlResultSetMapping(name = "CustomerEmailData", classes = @ConstructorResult(targetClass = CustomerEmailData.class, columns = {
		@ColumnResult(name = "customerId", type = Integer.class),
		@ColumnResult(name = "overallOrders", type = Integer.class),
		@ColumnResult(name = "overallVisits", type = Integer.class),
		@ColumnResult(name = "overallSavings", type = BigDecimal.class),
		@ColumnResult(name = "availedSignupOffer", type = String.class),
		@ColumnResult(name = "signupOfferExpiryTime", type = Date.class),
		@ColumnResult(name = "acquiredLoyaltyPoints", type = Integer.class),
		@ColumnResult(name = "walletBalance", type = BigDecimal.class),
		@ColumnResult(name = "chaayosCashBalance", type = BigDecimal.class),
		@ColumnResult(name = "membershipAvailable", type = String.class),
		@ColumnResult(name = "membershipPlan", type = String.class),
		@ColumnResult(name = "membershipEndDate", type = Date.class) }))
@NamedNativeQuery(name = "getCustomerEmailData", query = """
				SELECT a.CUSTOMER_ID customerId, a.overallOrders overallOrders,
		    a.overallVisits, a.overallSavings,a.availedSignupOffer,
		    a.signupOfferExpiryTime,a.acquiredLoyaltyPoints,
		    COALESCE(b.CARD_PENDING_AMOUNT, 0) walletBalance,
		    COALESCE(c.CURRENT_AMOUNT, 0) chaayosCashBalance,
		    a.membershipAvailable, a.membershipPlan, a.membershipEndDate
		FROM (SELECT ci.CUSTOMER_ID CUSTOMER_ID, COALESCE(SUM(CASE WHEN
		 od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL OR IS_GIFT_CARD_ORDER = 'N')
		THEN 1 ELSE 0 END), 0) overallOrders, COALESCE(SUM(CASE WHEN od.ORDER_ID IS NOT NULL
		AND (IS_GIFT_CARD_ORDER IS NULL OR IS_GIFT_CARD_ORDER = 'N')
		THEN  od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT ELSE 0 END), 0) overallSavings,
		COUNT(DISTINCT CASE WHEN od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL
		OR IS_GIFT_CARD_ORDER = 'N') THEN od.BUSINESS_DATE  ELSE 0 END) overallVisits,
		COALESCE(MAX(ls.AVAILED_SIGNUP_OFFER), 'Y') availedSignupOffer,
		MAX(ls.SIGNUP_OFFER_EXPIRY_TIME) signupOfferExpiryTime,
		COALESCE(MAX(ls.ACQUIRED_POINTS), 0) acquiredLoyaltyPoints,
		COALESCE(MAX(ls.CUMULATIVE_POINTS), 0) cumulativeLoyaltyPoints,
		(case when sp.SUBSCRIPTION_PLAN_ID IS NULL THEN 'N' ELSE 'Y' END) membershipAvailable,
		(case when sp.SUBSCRIPTION_PLAN_ID IS NULL THEN NULL ELSE sp.SUBSCRIPTION_PLAN_CODE END) membershipPlan,
		(case when sp.SUBSCRIPTION_PLAN_ID IS NULL THEN NULL ELSE sp.PLAN_END_DATE END) membershipEndDate
		FROM CUSTOMER_INFO ci
		INNER JOIN LOYALTY_SCORE ls ON ls.CUSTOMER_ID = ci.CUSTOMER_ID
		LEFT OUTER JOIN ORDER_DETAIL od ON od.CUSTOMER_ID = ci.CUSTOMER_ID
		AND od.ORDER_STATUS <> 'CANCELLED'
		AND od.BRAND_ID = :brandId         AND od.ORDER_TYPE = 'order'
		LEFT OUTER JOIN SUBSCRIPTION_PLAN sp ON ci.CUSTOMER_ID = sp.CUSTOMER_ID
		AND sp.PLAN_STATUS  = 'ACTIVE'
		AND sp.PLAN_START_DATE <= :currentDate         AND sp.PLAN_END_DATE >= :currentDate      WHERE
		ci.CUSTOMER_ID = :customerId     GROUP BY ci.CUSTOMER_ID, sp.SUBSCRIPTION_PLAN_ID) a
		LEFT OUTER JOIN (SELECT  ls.CUSTOMER_ID, SUM(ls.CARD_PENDING_AMOUNT) CARD_PENDING_AMOUNT
		FROM CASH_CARD_DETAIL ls WHERE ls.CUSTOMER_ID = :customerId 
	    AND ls.CARD_STATUS = 'ACTIVE' GROUP BY ls.CUSTOMER_ID) b ON a.CUSTOMER_ID = b.CUSTOMER_ID
		LEFT OUTER JOIN (SELECT ls.CUSTOMER_ID, ls.CURRENT_AMOUNT FROM CASH_DATA ls
		WHERE ls.CUSTOMER_ID = :customerId) c ON a.CUSTOMER_ID = c.CUSTOMER_ID LIMIT 1
		 """, resultClass = CustomerEmailData.class, resultSetMapping = "CustomerEmailData")
public class CustomerOfferDetail implements java.io.Serializable {

	private static final long serialVersionUID = 7585433405843098379L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "OFFER_DETAIL_ID", unique = true, nullable = false)
	private Integer offerDetailId;

	@Column(name = "CUSTOMER_ID", nullable = false)
	private Integer customerId;

	@Column(name = "OFFER_CODE", nullable = false, length = 100)
	private String offerCode;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "AVAIL_TIME", length = 19)
	private Date availTime;

	@Column(name = "ORDER_ID", nullable = false)
	private Integer orderId;

	@Column(name = "IS_AVAILED", nullable = false, length = 1)
	private String availed;

}