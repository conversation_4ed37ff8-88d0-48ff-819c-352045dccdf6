package com.stpl.tech.kettle.data.master;

import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "COUNTRY_DETAIL")
@Getter
@Setter
public class CountryDetail {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "COUNTRY_DETAIL_ID", nullable = false, unique = true)
	private Integer id;
	@Column(name = "COUNTRY", nullable = false, length = 100)
	private String country;
	@Column(name = "COUNTRY_CODE", nullable = false, length = 10)
	private String countryCode;
	@Column(name = "COUNTRY_ISD_CODE", nullable = false, length = 10)
	private String isdCode;
	@Column(name = "COUNTRY_STATUS", nullable = false, length = 15)
	private String status;
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "country")
	private List<StateDetail> states = new ArrayList<StateDetail>(0);

}
