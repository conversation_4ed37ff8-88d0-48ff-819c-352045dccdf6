package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "CUSTOMER_ADDRESS_INFO")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CustomerAddressInfo {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ADDRESS_ID", unique = true, nullable = false)
	private Integer addressId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CUSTOMER_ID", nullable = false)
	private CustomerInfo customerInfo;

	@Column(name = "LANDMARK")
	private String landmark;

	@Column(name = "ADDRESS_LINE_1", nullable = false)
	private String addressLine1;

	@Column(name = "ADDRESS_LINE_2")
	private String addressLine2;

	@Column(name = "ADDRESS_LINE_3")
	private String addressLine3;

	@Column(name = "LOCALITY")
	private String locality;

	@Column(name = "SUB_LOCALITY")
	private String subLocality;

	@Column(name = "CITY", nullable = false, length = 128)
	private String city;

	@Column(name = "STATE", nullable = false, length = 128)
	private String state;
	@Column(name = "COUNTRY", nullable = false, length = 128)
	private String country;

	@Column(name = "ZIPCODE", length = 40)
	private String zipcode;

	@Column(name = "ADDRESS_TYPE", nullable = false, length = 50)
	private String addressType;

	@Column(name = "COMPANY")
	private String company;

	@Column(name = "PREFERRED_ADDRESS")
	private Boolean preferredAddress;

	@Column(name = "NAME")
	private String name;

	@Column(name = "CONTACT")
	private String contact;

	@Column(name = "EMAIL")
	private String email;

	@Column(name = "STATUS")
	private String status;

	@Column(name = "LATITUDE")
	private String latitude;

	@Column(name = "LONGITUDE")
	private String longitude;

	@Column(name = "SOURCE")
	private String source;

	@Column(name = "SOURCE_ID", unique = true)
	private String sourceId;

	public CustomerAddressInfo(CustomerInfo customerInfo, String addressLine1, String city, String state,
			String country, String zipcode, String addressType, String name, String source, String sourceId) {
		this.customerInfo = customerInfo;
		this.addressLine1 = addressLine1;
		this.city = city;
		this.state = state;
		this.country = country;
		this.zipcode = zipcode;
		this.addressType = addressType;
		this.name = name;
		this.source = source;
		this.sourceId = sourceId;
	}

	public CustomerAddressInfo(CustomerInfo customerInfo, String addressLine1, String addressLine2, String addressLine3,
			String city, String state, String country, String zipcode, String addressType, String source,
			String sourceId) {
		this.customerInfo = customerInfo;
		this.addressLine1 = addressLine1;
		this.addressLine2 = addressLine2;
		this.addressLine3 = addressLine3;
		this.city = city;
		this.state = state;
		this.country = country;
		this.zipcode = zipcode;
		this.addressType = addressType;
		this.source = source;
		this.sourceId = sourceId;

	}

}
