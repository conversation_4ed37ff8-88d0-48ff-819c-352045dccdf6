package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "TAX_PROFILE")
@Getter
@Setter
@NoArgsConstructor
public class TaxProfile implements java.io.Serializable {

	private static final long serialVersionUID = 4858950382833107202L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "TAX_PROFILE_ID", unique = true, nullable = false)
	private Integer taxProfileId;

	@Column(name = "TAX_TYPE", nullable = false, length = 20)
	private String taxType;

	@Column(name = "TAX_NAME", nullable = false, length = 40)
	private String taxName;

	@Column(name = "TAX_PROFILE_STATUS", nullable = false, length = 15)
	private String taxProfileStatus;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "taxProfile")
	private List<UnitTaxMapping> unitTaxMappings = new ArrayList<UnitTaxMapping>(0);

	public TaxProfile(String taxType, String taxName) {
		this.taxType = taxType;
		this.taxName = taxName;
	}

	public TaxProfile(String taxType, String taxName, String state, List<UnitTaxMapping> unitTaxMappings) {
		this.taxType = taxType;
		this.taxName = taxName;
		this.unitTaxMappings = unitTaxMappings;
	}

}
