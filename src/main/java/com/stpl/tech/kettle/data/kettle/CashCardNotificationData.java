package com.stpl.tech.kettle.data.kettle;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CashCardNotificationData {


    private String cardNumber;

    private String unitName;

    private BigDecimal refundAmount;

    private BigDecimal pendingAmount;

    private String  customerName;

    private BigDecimal usedAmount;

    private BigDecimal purchaseAmount;

    private BigDecimal cashBackAmount;

    private String  startDate;

    private String endDate;

}
