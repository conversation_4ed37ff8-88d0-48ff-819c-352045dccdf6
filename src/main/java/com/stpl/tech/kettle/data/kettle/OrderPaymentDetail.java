package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "ORDER_PAYMENT_DETAIL")
public class OrderPaymentDetail implements Serializable {

	private static final long serialVersionUID = -7540092716780707940L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ORDER_PAYMENT_DETAIL_ID", unique = true, nullable = false)
	private Integer orderPaymentDetailId;
	@Column(name = "ORDER_SETTLEMENT_ID", nullable = true)
	private Integer orderSettlementId;
	@Column(name = "ORDER_ID", nullable = true)
	private Integer orderId;
	@Column(name = "EXTERNAL_ORDER_ID", nullable = true)
	private String externalOrderId;
	@Column(name = "PAYMENT_MODE_ID", nullable = false)
	private int paymentModeId;
	@Column(name = "PAYMENT_SOURCE", nullable = true)
	private String paymentSource;
	@Column(name = "PAYMENT_MODE_NAME", nullable = true)
	private String paymentModeName;
	@Column(name = "REQUEST_STATUS", nullable = true)
	private String requestStatus;
	@Column(name = "PAYMENT_STATUS", nullable = true)
	private String paymentStatus;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REQUEST_TIME", nullable = true, length = 19)
	private Date requestTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATE_TIME", nullable = true, length = 19)
	private Date updateTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "RESPONSE_TIME", nullable = true, length = 19)
	private Date responseTime;
	@Column(name = "REFUND_ID", nullable = true, length = 45)
	private String refundId;
	@Column(name = "REFUND_REQUESTED", nullable = true)
	private String refundRequested;
	@Column(name = "REFUND_STATUS", nullable = true)
	private String refundStatus;
	@Column(name = "REFUND_REASON", nullable = true)
	private String refundReason;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REFUND_REQUEST_TIME", nullable = true, length = 19)
	private Date refundRequestTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REFUND_PROCESS_TIME", nullable = true, length = 19)
	private Date refundProcessTime;
	@Column(name = "PARTNER_ORDER_ID", nullable = true)
	private String partnerOrderId;
	@Column(name = "PARTNER_TRANSACTION_ID", nullable = true)
	private String partnerTransactionId;
	@Column(name = "PARTNER_PAYMENT_STATUS", nullable = true)
	private String partnerPaymentStatus;
	@Column(name = "REDIRECT_URL", nullable = true)
	private String redirectUrl;
	@Column(name = "CANCELLED_BY", nullable = true)
	private String cancelledBy;
	@Column(name = "CANCELLATION_REASON", nullable = true)
	private String cancellationReason;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CANCELLATION_TIME", nullable = true, length = 19)
	private Date cancellationTime;
	@Column(name = "FAILURE_REASON", nullable = true)
	private String failureReason;
	@Column(name = "CART_ID")
	private String cartId;
	@Column(name = "CONTACT_NUMBER")
	private String contactNumber;
	@Column(name = "CUSTOMER_NAME")
	private String customerName;
	@Column(name = "CUSTOMER_ID")
	private Integer customerId;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "FAILURE_TIME", nullable = true, length = 19)
	private Date failureTime;
	@Column(name = "TRANSACTION_AMOUNT", precision = 10)
	private BigDecimal transactionAmount;
	@Column(name = "MERCHANT_ID")
	private String merchantId;
}
