/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.master;


import jakarta.persistence.*;
import lombok.*;

import static jakarta.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "CHANNEL_PARTNER")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChannelPartner implements java.io.Serializable {

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "PARTNER_ID", unique = true, nullable = false)
	private Integer partnerId;
	@Column(name = "PARTNER_CODE", nullable = false, length = 50)
	private String partnerCode;
	@Column(name = "PARTNER_DISPLAY_NAME", nullable = false, length = 100)
	private String partnerDisplayName;
	@Column(name = "SERVICE_TYPE", nullable = false, length = 15)
	private String serviceType;
	@Column(name = "CREDIT_ACCOUNT_ID", nullable = true)
	private Integer creditAccountId;
	@Column(name = "API_INTEGRATED", length = 1)
	private String apiIntegrated = "N";
	@Column(name = "PARTNER_STATUS", length = 10)
	private String status;

}
