package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "CASH_CARD_DETAIL")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CashCardDetail implements Serializable {

	private static final long serialVersionUID = 8873885246918847493L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CASH_CARD_ID", unique = true, nullable = false)
	private Integer cashCardId;
	@Column(name = "CARD_NUMBER", nullable = false, length = 8, unique = true)
	private String cardNumber;
	@Column(name = "CARD_SERIAL", unique = true)
	private String cardSerial;
	@Column(name = "CARD_STATUS", nullable = false, length = 30)
	private String cardStatus;
	@Temporal(TemporalType.DATE)
	@Column(name = "START_DATE", nullable = false, length = 10)
	private Date startDate;
	@Temporal(TemporalType.DATE)
	@Column(name = "END_DATE", length = 10)
	private Date endDate;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATION_TIME", nullable = false, length = 19)
	private Date creationTime;
	@Column(name = "BUYER_ID")
	private Integer buyerId;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "PURCHASE_DATE", length = 19)
	private Date purchaseTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ACTIVATION_DATE", length = 10)
	private Date activationTime;
	@Column(name = "CUSTOMER_ID", nullable = true)
	private Integer customerId;
	@Column(name = "CASH_CARD_OFFER_ID", nullable = true)
	private Integer offerId;
	@Column(name = "CARD_INITIAL_AMOUNT", nullable = false, precision = 10)
	private BigDecimal cashInitialAmount;
	@Column(name = "CARD_INITIAL_OFFER", nullable = true, precision = 10)
	private BigDecimal initialOffer;
	@Column(name = "CARD_PENDING_AMOUNT", nullable = false, precision = 10)
	private BigDecimal cashPendingAmount;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_MODIFIED", length = 19)
	private Date lastModified;
	@Column(name = "CARD_TYPE", nullable = true, length = 30)
	private String cardType;
	@Column(name = "SERIAL_NUMBER", nullable = true, length = 20)
	private String serialNumber;
	@Column(name = "PURCHASE_ORDER_ID", nullable = true)
	private Integer purchaseOrderId;

}
