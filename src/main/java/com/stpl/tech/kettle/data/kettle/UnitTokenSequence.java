package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "UNIT_TOKEN_SEQUENCE")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UnitTokenSequence implements Serializable {


	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SEQUENCE_ID", unique = true, nullable = false)
	private Integer sequenceId;
	@Column(name = "UNIT_ID", nullable = false)
	private int unitId;
	@Column(name = "NEXT_VALUE", nullable = false)
	private int nextValue;
	
	public UnitTokenSequence(int unitId, int nextValue) {
		this.unitId = unitId;
		this.nextValue = nextValue;
	}
}
