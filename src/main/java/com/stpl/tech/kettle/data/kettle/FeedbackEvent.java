package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "ORDER_FEEDBACK_EVENT")
@Getter
@Setter
public class FeedbackEvent implements Serializable {

	private static final long serialVersionUID = -629578548217676307L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "FEEDBACK_EVENT_ID", unique = true, nullable = false)
	private Integer feedbackEventId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "FEEDBACK_ID", nullable = false)
	private FeedbackDetail feedbackDetail;
	@Column(name = "EVENT_STATUS", nullable = true, length = 30)
	private String eventStatus;
	@Column(name = "EVENT_SOURCE", nullable = false, length = 15)
	private String eventSource;
	@Column(name = "EVENT_TYPE", nullable = true, length = 15)
	private String eventType;
	@Column(name = "FEEDBACK_RATING", nullable = true)
	private Integer rating;
	@Column(name = "EVENT_LONG_URL", nullable = true, length = 500)
	private String eventLongUrl;
	@Column(name = "EVENT_SHORT_URL", nullable = true, length = 150)
	private String eventShortUrl;
	@Column(name = "EVENT_SHORT_URL_ID", nullable = true, length = 50)
	private String eventShortUrlId;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_GENERATION_TIME", nullable = false, length = 19)
	private Date eventGenerationTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_NOTIFICATION_TIME", nullable = true, length = 19)
	private Date eventNotificationTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_TRIGGER_TIME", nullable = false, length = 19)
	private Date eventTriggerTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_COMPLETION_TIME", nullable = true, length = 19)
	private Date eventCompletionTime;
	@Column(name = "LATEST_FEEDBACK_INFO_ID", nullable = true)
	private Integer latestFeedbackInfoId;
	@Column(name = "BRAND_ID")
	private Integer brandId;
	@Column(name = "RATING_TYPE")
	private String ratingType;
	@Column(name = "MAX_RATING")
	private Integer maxRating;
}
