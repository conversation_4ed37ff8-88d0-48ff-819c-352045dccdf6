package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import com.stpl.tech.master.domain.model.UnitProductPriceSheetDetail;

import jakarta.persistence.Column;
import jakarta.persistence.ColumnResult;
import jakarta.persistence.ConstructorResult;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SqlResultSetMapping;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "UNIT_PRODUCT_PRICING", uniqueConstraints = @UniqueConstraint(columnNames = { "UNIT_PROD_REF_ID",
		"DIMENSION_CODE" }))
@SqlResultSetMapping(name = "UnitProductPriceSheetDetail", classes = @ConstructorResult(targetClass = UnitProductPriceSheetDetail.class, columns = {
		@ColumnResult(name = "pricingProfileName", type = String.class),
		@ColumnResult(name = "unitId", type = Integer.class), @ColumnResult(name = "unitName", type = String.class),
		@ColumnResult(name = "productId", type = Integer.class),
		@ColumnResult(name = "productName", type = String.class),
		@ColumnResult(name = "dimensionCode", type = Integer.class),
		@ColumnResult(name = "dimension", type = String.class),
		@ColumnResult(name = "unitCategory", type = String.class),
		@ColumnResult(name = "pricingProfile", type = Integer.class),
		@ColumnResult(name = "brandId", type = Integer.class), @ColumnResult(name = "unitRegion", type = String.class),
		@ColumnResult(name = "rtlCode", type = String.class), @ColumnResult(name = "rlCode", type = String.class),
		@ColumnResult(name = "productStatus", type = String.class),
		@ColumnResult(name = "unitProductMappingId", type = Integer.class),
		@ColumnResult(name = "unitProductMappingStatus", type = String.class),
		@ColumnResult(name = "unitProductPriceId", type = Integer.class),
		@ColumnResult(name = "unitProductPricingStatus", type = String.class),
		@ColumnResult(name = "price", type = BigDecimal.class), }))
@Getter
@Setter
@NoArgsConstructor
public class UnitProductPricing implements java.io.Serializable {

	private static final long serialVersionUID = 4186075417232707549L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_PROD_PRICE_ID", unique = true, nullable = false)
	private Integer unitProdPriceId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "DIMENSION_CODE", nullable = false)
	private RefLookup refLookup;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_PROD_REF_ID", nullable = false)
	private UnitProductMapping unitProductMapping;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TMSTMP", nullable = false, length = 19)
	private Date lastUpdateTmstmp;

	@Column(name = "PRICE", precision = 10)
	private BigDecimal price;

	@Column(name = "COST", precision = 10)
	private BigDecimal cost;

	@Column(name = "COD_COST", precision = 10)
	private BigDecimal codCost;

	@Column(name = "BUFFER_QUANTITY")
	private Integer buffer;

	@Column(name = "THRESHOLD_QUANTITY")
	private Integer threshold;

	@Column(name = "RECIPE_PROFILE", nullable = false)
	private String recipeProfile;

	@Column(name = "PRICING_STATUS", nullable = false)
	private String status = "ACTIVE";

	@Column(name = "UPDATED_BY")
	private Integer updatedBy;

	@Column(name = "ALIAS_PRODUCT_NAME")
	private String aliasProductName;

	@Column(name = "DIMENSION_DESCRIPTOR")
	private String dimensionDescriptor;

	@Column(name = "IS_DELIVERY_ONLY_PRODUCT")
	private String isDeliveryOnlyProduct;

	public UnitProductPricing(RefLookup refLookup, UnitProductMapping unitProductMapping, Date lastUpdateTmstmp) {
		this.refLookup = refLookup;
		this.unitProductMapping = unitProductMapping;
		this.lastUpdateTmstmp = lastUpdateTmstmp;
	}

	public UnitProductPricing(RefLookup refLookup, UnitProductMapping unitProductMapping, Date lastUpdateTmstmp,
			BigDecimal price, String recipeProfile) {
		this.refLookup = refLookup;
		this.unitProductMapping = unitProductMapping;
		this.lastUpdateTmstmp = lastUpdateTmstmp;
		this.price = price;
		this.recipeProfile = recipeProfile;
	}

	public UnitProductPricing(RefLookup refLookup, UnitProductMapping unitProductMapping, Date lastUpdateTmstmp,
			BigDecimal price, String recipeProfile, String status) {
		this.refLookup = refLookup;
		this.unitProductMapping = unitProductMapping;
		this.lastUpdateTmstmp = lastUpdateTmstmp;
		this.price = price;
		this.recipeProfile = recipeProfile;
		this.status = status;
	}

}
