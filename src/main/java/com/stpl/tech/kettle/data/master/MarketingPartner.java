package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "MARKETING_PARTNER")
@Getter
@Setter
public class MarketingPartner implements Serializable {

	private static final long serialVersionUID = -5676410221020083431L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "PARTNER_ID", unique = true, nullable = false)
	private int partnerId;

	@Column(name = "PARTNER_NAME", nullable = false)
	private String partnerName; // default value is CHAAYOS

	@Column(name = "PARTNER_TYPE", nullable = false)
	private String partnerType; // default value is INTERNAL

	@Column(name = "STATUS", nullable = false)
	private String status; // default in ACTIVE

	@Column(name = "AUTHORIZATION_KEY", nullable = true)
	private String authorizationKey;

}
