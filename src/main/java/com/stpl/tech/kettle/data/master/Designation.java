package com.stpl.tech.kettle.data.master;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "DESIGNATION")
@Getter
@Setter
@NoArgsConstructor
public class Designation implements Serializable {

	private static final long serialVersionUID = -2071011863809454761L;

	@Id
	@Column(name = "DESIGNATION_ID", unique = true, nullable = false)
	private int designationId;

	@Column(name = "DESIGNATION_NAME", nullable = false)
	private String designationName;

	@Column(name = "DESIGNATION_DESC", nullable = false, length = 500)
	private String designationDesc;

	@Column(name = "TRANSACTION_SYSTEM_ACCESS", nullable = false, length = 1)
	protected String transactionSystemAccess;

	@Column(name = "SCM_SYSTEM_ACCESS", nullable = false, length = 1)
	protected String scmSystemAccess;

	@Column(name = "ADMIN_SYSTEM_ACCESS", nullable = false, length = 1)
	protected String adminSystemAccess;

	@Column(name = "CLM_SYSTEM_ACCESS", nullable = false, length = 1)
	protected String clmSystemAccess;

	@Column(name = "ANALYTICS_SYSTEM_ACCESS", nullable = false, length = 1)
	protected String analyticsSystemAccess;

	@Column(name = "CRM_SYSTEM_ACCESS", nullable = false, length = 1)
	protected String crmSystemAccess;

	@Column(name = "FORMS_SYSTEM_ACCESS", nullable = false, length = 1)
	protected String formsSystemAccess;

	@Column(name = "APP_INSTALLER_ACCESS", nullable = false, length = 1)
	protected String appInstallerAccess;

	@Column(name = "CHANNEL_PARTNER_ACCESS", nullable = false, length = 1)
	protected String channelPartnerAccess;

	@Column(name = "MAX_ALLOCATED_UNIT", nullable = true)
	protected Integer maxAllocatedUnits = -1;

	@Column(name = "ATTENDANCE_ACCESS", nullable = false)
	protected String attendanceAccess;

	@Column(name = "KNOCK_APPLICATION_ACCESS", nullable = false)
	protected String knockApplicationAccess;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "designation")
	private Set<EmployeeDetail> employeeDetails = new HashSet<EmployeeDetail>(0);

	@ManyToMany(fetch = FetchType.LAZY, mappedBy = "designations")
	private Set<Department> departments = new HashSet<Department>(0);

	public Designation(int designationId, String designationName, String designationDesc) {
		this.designationId = designationId;
		this.designationName = designationName;
		this.designationDesc = designationDesc;
	}

	public Designation(int designationId, String designationName, String designationDesc,
			Set<EmployeeDetail> employeeDetails, Set<Department> departments) {
		this.designationId = designationId;
		this.designationName = designationName;
		this.designationDesc = designationDesc;
		this.employeeDetails = employeeDetails;
		this.departments = departments;
	}

}
