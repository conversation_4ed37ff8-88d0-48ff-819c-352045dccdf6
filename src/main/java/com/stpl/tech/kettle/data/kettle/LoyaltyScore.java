package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.kettle.util.Constants.AppConstants;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "LOYALTY_SCORE")
@Getter
@Setter
@NoArgsConstructor
public class LoyaltyScore implements java.io.Serializable {

	private static final long serialVersionUID = 2128033053713361072L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "LOYALTY_POINTS_ID", unique = true, nullable = false)
	private Integer loyaltyPointsId;
	@Column(name = "CUSTOMER_ID", nullable = false)
	private Integer customerId;
	@Column(name = "ACQUIRED_POINTS", nullable = true)
	private Integer acquiredPoints;
	@Column(name = "CUMULATIVE_POINTS", nullable = true)
	private Integer cumulativePoints;
	@Column(name = "BLOCKED_POINTS", nullable = true)
	private Integer blockedPoints;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_ORDER_TIME", nullable = true, length = 19)
	private Date lastOrderTime;
	@Column(name = "LAST_ORDER_ID", nullable = true)
	private Integer lastOrderId;
	@Column(name = "ORDER_COUNT", nullable = true)
	private Integer orderCount;
	@Column(name = "AVAILED_SIGNUP_OFFER", nullable = true, length = 1)
	private String availedSignupOffer;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_NPS_TIME", nullable = true)
	private Date lastNPSTime;
	@Column(name = "SIGNUP_OFFER_EXPIRED", nullable = true, length = 1)
	private String signupOfferExpired;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "SIGNUP_OFFER_EXPIRY_TIME", nullable = true)
	private Date signupOfferExpiryTime;
	@Column(name = "SIGNUP_OFFER_STATUS")
	private String signupOfferStatus;
	@Column(name = "REDEMPTION_ORDER_ID")
	private Integer redemptionOrderId;
	@Column(name = "TOTAL_REDEEMED_POINTS")
	private Integer totalRedeemedPoints = 0;
	@Column(name = "TOTAL_EXPIRED_POINTS")
	private Integer totalExpiredPoints = 0;

	public LoyaltyScore(Integer customerId, Integer acquiredPoints) {
		this.customerId = customerId;
		this.acquiredPoints = acquiredPoints;
		this.cumulativePoints = acquiredPoints;
		this.availedSignupOffer = AppConstants.NO;
		this.signupOfferStatus = SignupOfferStatus.AVAILABLE.name();
	}
}
