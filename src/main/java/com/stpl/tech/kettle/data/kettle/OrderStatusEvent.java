package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "ORDER_STATUS_EVENT")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrderStatusEvent implements Serializable {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ORDER_STATUS_ID", unique = true, nullable = false)
	private Integer orderStatusId;
	@Column(name = "ORDER_ID", nullable = false)
	private int orderId;
	@Column(name = "FROM_STATUS", nullable = false, length = 30)
	private String fromStatus;
	@Column(name = "TO_STATUS", nullable = false, length = 30)
	private String toStatus;
	@Column(name = "REASON_TEXT", nullable = false, length = 100)
	private String reasonText;
	@Column(name = "GENERATED_BY", nullable = false)
	private int generatedBy;
	@Column(name = "APPROVED_BY", nullable = false)
	private int approvedBy;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "START_TIME", nullable = false, length = 19)
	private Date startTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATE_TIME", nullable = false, length = 19)
	private Date updateTime;
	@Column(name = "TRANSITION_STATUS", nullable = false, length = 30)
	private String transitionStatus;
	@Column(name = "ERROR_TRACE", nullable = true, length = 5000)
	private String errorTrace;

	public OrderStatusEvent(int orderId, String fromStatus, String toStatus, String reasonText, int generatedBy,
			int approvedBy, Date startTime, Date updateTime, String transitionStatus, String errorTrace) {
		super();
		this.orderId = orderId;
		this.fromStatus = fromStatus;
		this.toStatus = toStatus;
		this.reasonText = reasonText;
		this.generatedBy = generatedBy;
		this.approvedBy = approvedBy;
		this.startTime = startTime;
		this.updateTime = updateTime;
		this.transitionStatus = transitionStatus;
		this.errorTrace = errorTrace;
	}
	
	@Override
	public String toString() {
		return "OrderStatusEvent [orderStatusId=" + orderStatusId + ", orderId=" + orderId + ", fromStatus="
				+ fromStatus + ", toStatus=" + toStatus + ", reasonText=" + reasonText + ", generatedBy=" + generatedBy
				+ ", approvedBy=" + approvedBy + ", updateTime=" + updateTime + ", transitionStatus=" + transitionStatus
				+ ", errorTrace=" + errorTrace + "]";
	}

}
