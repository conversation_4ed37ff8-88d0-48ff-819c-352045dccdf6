package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "OFFER_DETAIL_MAPPING_DATA")
@Getter
@Setter
public class OfferDetailMappingData implements java.io.Serializable {

	private static final long serialVersionUID = -821504026019701035L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "OFFER_DETAIL_MAPPING_DATA_ID", unique = true, nullable = false)
	private Integer offerDetailMappingId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "OFFER_DETAIL_ID", nullable = false)
	private OfferDetailData offerDetail;

	@Column(name = "MAPPING_TYPE", nullable = false, length = 50)
	private String mappingType;

	@Column(name = "MAPPING_VALUE", nullable = false, length = 100)
	private String mappingValue;

	@Column(name = "DIMENSION", nullable = true)
	private String dimension;

	@Column(name = "MAPPING_DATA_TYPE", nullable = false, length = 50)
	private String dataType;

	@Column(name = "MIN_VALUE", nullable = false, length = 10)
	private String minValue;

	@Column(name = "MAPPING_GROUP", nullable = false)
	private int mappingGroup;

	@Column(name = "STATUS", nullable = false)
	private String status;

}
