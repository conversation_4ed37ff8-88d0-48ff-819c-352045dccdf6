package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "UNIT_TAX_MAPPING")
@Getter
@Setter
@NoArgsConstructor
public class UnitTaxMapping implements java.io.Serializable {

	private static final long serialVersionUID = -3444480521835203265L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_TAX_MAPPING_ID", unique = true, nullable = false)
	private Integer unitTaxMappingId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "TAX_PROFILE_ID", nullable = false)
	private TaxProfile taxProfile;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_ID", nullable = false)
	private UnitDetail unitDetail;

	@Column(name = "TAX_PERCENTAGE", nullable = false, precision = 10)
	private BigDecimal taxPercentage;

	@Column(name = "PROFILE_STATUS", nullable = false, length = 20)
	private String profileStatus;

	@Column(name = "STATE", nullable = false, length = 20)
	private String state;

	public UnitTaxMapping(TaxProfile taxProfile, UnitDetail unitDetail, BigDecimal taxPercentage, String profileStatus,
			String state) {
		this.taxProfile = taxProfile;
		this.unitDetail = unitDetail;
		this.taxPercentage = taxPercentage;
		this.profileStatus = profileStatus;
		this.state = state;
	}

}
