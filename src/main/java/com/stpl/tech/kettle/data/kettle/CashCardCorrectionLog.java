package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "CASH_CARD_CORRECTION_LOG")
@NoArgsConstructor
@Getter
@Setter
public class CashCardCorrectionLog implements Serializable {

    private static final long serialVersionUID = 2905452666197515792L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CASH_CARD_CORRECTION_LOG_ID", unique = true, nullable = false)
    private Integer cashCardCorrectionLogId;
    @Column(name = "ORDER_ID")
    private Integer orderId;
    @Column(name = "AMOUNT_TO_BE_DEDUCTED")
    private BigDecimal amountToBeDeducted;
    @Column(name = "AMOUNT_DEDUCTED")
    private BigDecimal amountDeducted;
    @Column(name = "AMOUNT_LEFT_FOR_DEDUCTION")
    private BigDecimal amountLeftForDeduction;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME")
    private Date creationTime;
}
