package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "TABLE_ORDER_MAPPING")
@Getter
@Setter
@NoArgsConstructor
public class TableOrderMappingDetail {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "MAPPING_ID", unique = true, nullable = false)
	private int mappingId;
	@Column(name = "TABLE_REQUEST_ID", nullable = false)
	private int tableRequestId;
	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ID", nullable = false)
	private OrderDetail order;

	public TableOrderMappingDetail(int tableRequestId, OrderDetail order) {
		super();
		this.tableRequestId = tableRequestId;
		this.order = order;
	}

}
