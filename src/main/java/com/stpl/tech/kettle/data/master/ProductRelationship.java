package com.stpl.tech.kettle.data.master;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "PRODUCT_RELATIONSHIP")
@Getter
@Setter
@NoArgsConstructor
public class ProductRelationship implements java.io.Serializable {

	private static final long serialVersionUID = 1838044990445544915L;

	@EmbeddedId
	@AttributeOverrides({
			@AttributeOverride(name = "productId", column = @Column(name = "PRODUCT_ID", nullable = false)),
			@AttributeOverride(name = "constituentProductId", column = @Column(name = "CONSTITUENT_PRODUCT_ID", nullable = false)),
			@AttributeOverride(name = "relationshipType", column = @Column(name = "RELATIONSHIP_TYPE", nullable = false, length = 10)),
			@AttributeOverride(name = "quantity", column = @Column(name = "QUANTITY", nullable = false)),
			@AttributeOverride(name = "priceMultiplier", column = @Column(name = "PRICE_MULTIPLIER", nullable = false, precision = 4, scale = 4)) })
	private ProductRelationshipId id;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CONSTITUENT_PRODUCT_ID", nullable = false, insertable = false, updatable = false)
	private ProductDetail productDetailByConstituentProductId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PRODUCT_ID", nullable = false, insertable = false, updatable = false)
	private ProductDetail productDetailByProductId;

	public ProductRelationship(ProductRelationshipId id, ProductDetail productDetailByConstituentProductId,
			ProductDetail productDetailByProductId) {
		this.id = id;
		this.productDetailByConstituentProductId = productDetailByConstituentProductId;
		this.productDetailByProductId = productDetailByProductId;
	}

}
