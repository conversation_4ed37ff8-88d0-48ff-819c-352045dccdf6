package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
@Entity
@Table(name = "ORDER_ITEM_STATUS")
public class OrderItemStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ORDER_ITEM_STATUS_ID")
    private Integer orderItemStatusId;

    @Column(name = "TABLE_REQUEST_ID")
    private Integer tableRequestId;

    @Column(name = "ORDER_ID")
    private Integer orderId;
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ORDER_ITEM_ID", nullable = false)
    private OrderItem orderItemId;
    

    @Column(name = "STATUS")
    private String status;

    @Column(name = "UPDATED_BY")
    private Integer updatedBy;

    @Column(name = "SERVED_BY")
    private Integer servedBy;



    @Column(name = "UPDATION_TIME")
    private Date updationTime;

    @Column(name = "ITEM_CREATION_TIME")
    private Date itemCreationTime;
    @Column(name = "ITEM_IN_PROCESS_TIME")
    private Date itemInProcessTime;

    @Column(name = "ITEM_COMPLETION_TIME")
    private Date itemCompletionTime;

    @Column(name = "ITEM_SERVE_TIME")
    private Date itemServeTime;

    @Column(name = "TOTAL_PROCESSING_TIME")
    private Integer totalProcessingTime;

    @Column(name = "SETTLEMENT_ORDER_ID")
    private Integer settlementOrderId;

    @Column(name = "TOTAL_HOLD_TIME")
    private Integer totalHoldTime;


}

