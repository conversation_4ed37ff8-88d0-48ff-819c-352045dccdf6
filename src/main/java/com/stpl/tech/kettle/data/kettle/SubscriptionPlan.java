package com.stpl.tech.kettle.data.kettle;

import java.math.BigDecimal;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "SUBSCRIPTION_PLAN")
@Getter
@Setter
public class SubscriptionPlan {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SUBSCRIPTION_PLAN_ID", unique = true, nullable = false)
	private Integer subscriptionPlanId;

	@Column(name = "SUBSCRIPTION_PLAN_CODE", nullable = false, length = 200)
	private String subscriptionPlanCode;

	@Column(name = "CUSTOMER_ID", nullable = false)
	private Integer customerId;

	@Column(name = "PLAN_STATUS", nullable = false, length = 15)
	private String status;

	@Temporal(TemporalType.DATE)
	@Column(name = "PLAN_START_DATE", nullable = true, length = 10)
	private Date planStartDate;

	@Temporal(TemporalType.DATE)
	@Column(name = "PLAN_END_DATE", nullable = true, length = 10)
	private Date planEndDate;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "RENEWAL_TIME", nullable = false, length = 19)
	private Date renewalTime;

	@Column(name = "EVENT_TYPE", nullable = false, length = 50)
	private String eventType;

	@Column(name = "LAST_RENEWAL_EVENT_ID", nullable = false)
	private Integer lastRenewalEventId;

	@Column(name = "OVERALL_SAVING", nullable = true)
	private BigDecimal overAllSaving;

	@Column(name = "OFFER_DESCRIPTION", nullable = true)
	private String offerDescription;

	@Column(name = "FREQUENCY_STRATEGY", nullable = true)
	private String frequencyStrategy;

	@Column(name = "OVERALL_FREQUENCY")
	private BigDecimal overAllFrequency;

	@Column(name = "FREQUENCY_LIMIT", nullable = true)
	private BigDecimal frequencyLimit;
}
