
package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "SUBSCRIPTION_DETAIL")
@Getter
@Setter
public class SubscriptionDetail implements java.io.Serializable {

	private static final long serialVersionUID = 5441681397850434848L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SUBSCRIPTION_ID", unique = true, nullable = false)
	private Integer subscriptionId;
	@Column(name = "GENERATED_SUBSCRIPTION_ID", nullable = false, length = 30)
	private String generatedSubscriptionId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CUSTOMER_ID", nullable = false)
	private CustomerInfo customerInfo;
	@Column(name = "EMP_ID", nullable = false)
	private int empId;
	@Column(name = "SUBSCRIPTION_STATUS", nullable = false, length = 30)
	private String subscriptionStatus;
	@Column(name = "CANCELLATION_REASON", length = 100)
	private String cancellationReason;
	@Column(name = "SETTLEMENT_TYPE", length = 10)
	private String settlementType;
	@Column(name = "UNIT_ID", nullable = false)
	private int unitId;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "SUBSCRIPTION_CREATION_TIME", nullable = false, length = 19)
	private Date subscriptionCreationTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CANCELLATION_TIME", nullable = true, length = 19)
	private Date cancellationTime;
	@Column(name = "CHANNEL_PARTNER_ID", nullable = false)
	private int channelPartnerId;
	@Column(name = "DELIVERY_ADDRESS", nullable = true)
	private Integer deliveryAddress;
	@Column(name = "ORDER_REMARK", nullable = true)
	private String orderRemark;
	@Column(name = "TOTAL_AMOUNT", precision = 10)
	private BigDecimal totalAmount;
	@Column(name = "SALE_AMOUNT", precision = 10)
	private BigDecimal saleAmount;
	@Column(name = "PROMOTIONAL_DISCOUNT", precision = 10)
	private BigDecimal promotionalDiscount;
	@Column(name = "TOTAL_DISCOUNT", precision = 10)
	private BigDecimal totalDiscount;
	@Column(name = "TAXABLE_AMOUNT", precision = 10)
	private BigDecimal taxableAmount;
	@Column(name = "DISCOUNT_PERCENT", precision = 10)
	private BigDecimal discountPercent;
	@Column(name = "DISCOUNT_AMOUNT", precision = 10)
	private BigDecimal discountAmount;
	@Column(name = "DISCOUNT_REASON_ID")
	private Integer discountReasonId;
	@Column(name = "DISCOUNT_REASON")
	private String discountReason;
	@Column(name = "ORDER_SOURCE_ID", nullable = true)
	private String orderSourceId;
	@Column(name = "OFFER_CODE", nullable = true, length = 15)
	private String offerCode;
	@Column(name = "EMAIL_NOTIFICATION", nullable = false, length = 1)
	private String emailNotification;
	@Column(name = "SMS_NOTIFICATION", nullable = false, length = 1)
	private String smsNotification;
	@Column(name = "TOTAL_TAX", precision = 10)
	private BigDecimal taxAmount;
	@Column(name = "ROUND_OFF_AMOUNT", precision = 10)
	private BigDecimal roundOffAmount;
	@Column(name = "SETTLED_AMOUNT", precision = 10)
	private BigDecimal settledAmount;
	@Column(name = "AUTOMATED_DELIVERY", nullable = false, length = 1)
	private String automatedDelivery;
	@Column(name = "CANCELLED_BY", nullable = true)
	private Integer cancelledBy;
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "subscriptionDetail")
	private List<SubscriptionItem> subscriptionItems = new ArrayList<SubscriptionItem>(0);
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "subscriptionDetail")
	private List<SubscriptionSettlement> subscriptionSettlements = new ArrayList<SubscriptionSettlement>(0);
	@Temporal(TemporalType.DATE)
	@Column(name = "START_DATE", nullable = false, length = 10)
	private Date startDate;
	@Temporal(TemporalType.DATE)
	@Column(name = "END_DATE", nullable = false, length = 10)
	private Date endDate;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = true, length = 19)
	private Date lastUpdateTime;
	@Column(name = "SUBSCRIPTION_TYPE", nullable = false, length = 10)
	private String subscriptionType;// MONTHLY, WEEKLY
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "subscriptionDetail")
	@OrderBy("eventItemValue asc")
	private List<SubscriptionEventItem> eventItems = new ArrayList<SubscriptionEventItem>(0);
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "subscriptionDetail")
	private List<SubscriptionTaxDetail> subscriptionTaxes = new ArrayList<SubscriptionTaxDetail>(0);

}
