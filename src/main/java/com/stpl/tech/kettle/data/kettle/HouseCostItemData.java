package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;
import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

@Entity
@Table(name = "HOUSE_COST_ITEM_DATA")
public class HouseCostItemData implements Serializable {

	private static final long serialVersionUID = -9213023945300545286L;

	private Integer orderItemId;
	private HouseCostEvent orderDetail;
	private int productId;
	private String productName;
	private BigDecimal quantity;
	private BigDecimal price;
	private BigDecimal costPrice;
	private BigDecimal totalAmount;
	private BigDecimal totalCost;
	private String dimension;
	private Integer recipeId;
	private Integer linkedOrderItemId;

	public HouseCostItemData() {
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "HOUSE_COST_ITEM_DATA_ID", unique = true, nullable = false)
	public Integer getOrderItemId() {
		return this.orderItemId;
	}

	public void setOrderItemId(Integer orderItemId) {
		this.orderItemId = orderItemId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "HOUSE_COST_EVENT_ID", nullable = false)
	public HouseCostEvent getOrderDetail() {
		return this.orderDetail;
	}

	public void setOrderDetail(HouseCostEvent orderDetail) {
		this.orderDetail = orderDetail;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return this.productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	@Column(name = "PRODUCT_NAME", nullable = false)
	public String getProductName() {
		return this.productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	@Column(name = "QUANTITY", nullable = false)
	public BigDecimal getQuantity() {
		return this.quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	@Column(name = "PRICE", nullable = true)
	public BigDecimal getPrice() {
		return this.price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	@Column(name = "COST_PRICE", nullable = true)
	public BigDecimal getCostPrice() {
		return costPrice;
	}

	public void setCostPrice(BigDecimal costPrice) {
		this.costPrice = costPrice;
	}

	@Column(name = "TOTAL_COST", nullable = true)
	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = true)
	public BigDecimal getTotalAmount() {
		return this.totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	@Column(name = "DIMENSION", length = 10)
	public String getDimension() {
		return this.dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	@Column(name = "RECIPE_ID", nullable = true)
	public Integer getRecipeId() {
		return recipeId;
	}

	public void setRecipeId(Integer recipeId) {
		this.recipeId = recipeId;
	}

	@Column(name = "LINKED_ORDER_ITEM_ID", nullable = true)
	public Integer getLinkedOrderItemId() {
		return linkedOrderItemId;
	}

	public void setLinkedOrderItemId(Integer linkedOrderItemId) {
		this.linkedOrderItemId = linkedOrderItemId;
	}
}
