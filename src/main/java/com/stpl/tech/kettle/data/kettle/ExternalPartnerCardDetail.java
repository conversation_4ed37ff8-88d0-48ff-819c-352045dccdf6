package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "EXTERNAL_PARTNER_CARD_DETAIL")
@Getter
@Setter
public class ExternalPartnerCardDetail implements Serializable {

	private static final long serialVersionUID = 4723969042214306323L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "CARD_DETAIL_ID", unique = true, nullable = false)
	private Integer cardDetailId;
	@Column(name = "EXTERNAL_ORDER_ID", nullable = false)
	private String externalOrderId;
	@Column(name = "PARTNER_CARD_NUMBER", nullable = false)
	private String partnerCardNumber;
	@Column(name = "PARTNER_CODE", nullable = false)
	private String partnerCode;
	@Column(name = "REQUEST_SOURCE", nullable = true)
	private String requestSource;
	@Column(name = "REQUEST_STATUS", nullable = true)
	private String requestStatus;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REQUEST_TIME", nullable = true, length = 19)
	private Date requestTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "RESPONSE_TIME", nullable = true, length = 19)
	private Date responseTime;
	@Column(name = "PARTNER_TRANSACTION_ID", nullable = true)
	private String partnerTransactionId;
	@Column(name = "REDIRECT_URL", nullable = true)
	private String redirectUrl;
	@Column(name = "CANCELLED_BY", nullable = true)
	private String cancelledBy;
	@Column(name = "CANCELLATION_REASON", nullable = true)
	private String cancellationReason;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CANCELLATION_TIME", nullable = true, length = 19)
	private Date cancellationTime;
	@Column(name = "CUSTOMER_NAME", nullable = false)
	private String customerName;
	@Column(name = "CUSTOMER_ID", nullable = false)
	private Integer customerId;
	@Column(name = "TRANSACTION_AMOUNT", precision = 10, nullable = true)
	private BigDecimal transactionAmount;
	@Column(name = "CARD_NUMBER", nullable = true)
	private String cardNumber;
	@Column(name = "RESPONSE_RESULT", nullable = true, columnDefinition = "blob")
	private byte[] response;

}
