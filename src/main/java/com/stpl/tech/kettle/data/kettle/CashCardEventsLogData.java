package com.stpl.tech.kettle.data.kettle;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "CASH_CARD_EVENT_LOG")
@Getter
@Setter
public class CashCardEventsLogData {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CASH_CARD_EVENT_LOG_ID", nullable = false, unique = true)
	private Integer id;
	@Column(name = "CARD_NUMBER")
	private String cardNumber;
	@Column(name = "CARD_SERIAL")
	private String cardSerial;
	@Column(name = "EVENT_NAME", nullable = false, length = 100)
	private String event;
	@Column(name = "EVENT_DETAIL", length = 200, nullable = false)
	private String eventDetail;
	@Column(name = "EVENT_TIME", nullable = false)
	private Date eventTime;
	@Column(name = "EMP_ID", nullable = false)
	private Integer empId;
	@Column(name = "UNIT_ID")
	private Integer unitId;

}
