package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "DEPARTMENT")
@Getter
@Setter
@NoArgsConstructor
public class Department implements java.io.Serializable {

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "DEPT_ID", unique = true, nullable = false)
	private Integer deptId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "BUSINESS_DIV_ID", nullable = false)
	private BusinessDivision businessDivision;

	@Column(name = "DEPT_NAME", nullable = false)
	private String deptName;

	@Column(name = "DEPT_DESC", nullable = false, length = 500)
	private String deptDesc;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "department")
	private Set<EmployeeDetail> employeeDetails = new HashSet<EmployeeDetail>(0);

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "DEPARTMENT_DESIGNATION_MAPPING", joinColumns = {
			@JoinColumn(name = "DEPT_ID", nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = "DESIGNATION_ID", nullable = false, updatable = false) })
	private Set<Designation> designations = new HashSet<Designation>(0);

	public Department(BusinessDivision businessDivision, String deptName, String deptDesc) {
		this.businessDivision = businessDivision;
		this.deptName = deptName;
		this.deptDesc = deptDesc;
	}

	public Department(BusinessDivision businessDivision, String deptName, String deptDesc,
			Set<EmployeeDetail> employeeDetails, Set<Designation> designations) {
		this.businessDivision = businessDivision;
		this.deptName = deptName;
		this.deptDesc = deptDesc;
		this.employeeDetails = employeeDetails;
		this.designations = designations;
	}

}
