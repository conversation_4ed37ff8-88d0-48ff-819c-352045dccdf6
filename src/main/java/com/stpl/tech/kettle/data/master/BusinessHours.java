package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "BUSINESS_HOURS")
@Getter
@Setter
public class BusinessHours implements Serializable {

	private static final long serialVersionUID = -2356417016421088155L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "BUSINESS_HOURS_ID", unique = true, nullable = false)
	protected int businessHoursId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_ID", nullable = false)
	protected UnitDetail unitDetail;
	@Column(name = "DAY_OF_WEEK_NUMBER", nullable = false)
	protected int dayOfTheWeekNumber;
	@Column(name = "DAY_OF_WEEK_TEXT", nullable = false, length = 10)
	protected String dayOfTheWeek;
	@Column(name = "NO_OF_SHIFTS", nullable = false)
	protected int noOfShifts;
	@Column(name = "IS_OPERATIONAL", nullable = false, length = 1)
	protected String isOperational;
	@Column(name = "HAS_DELIVERY", nullable = false, length = 1)
	protected String hasDelivery;
	@Column(name = "HAS_DINE_IN", nullable = false, length = 1)
	protected String hasDineIn;
	@Column(name = "HAS_TAKE_AWAY", nullable = false, length = 1)
	protected String hasTakeAway;
	@Temporal(TemporalType.TIME)
	@Column(name = "DINE_IN_OPEN_TIME", length = 8)
	protected Date dineInOpeningTime;
	@Temporal(TemporalType.TIME)
	@Column(name = "DINE_IN_CLOSE_TIME", length = 8)
	protected Date dineInClosingTime;
	@Temporal(TemporalType.TIME)
	@Column(name = "DELIVERY_OPEN_TIME", length = 8)
	protected Date deliveryOpeningTime;
	@Temporal(TemporalType.TIME)
	@Column(name = "DELIVERY_CLOSE_TIME", length = 8)
	protected Date deliveryClosingTime;
	@Temporal(TemporalType.TIME)
	@Column(name = "TAKE_AWAY_OPEN_TIME", length = 8)
	protected Date takeAwayOpeningTime;
	@Temporal(TemporalType.TIME)
	@Column(name = "TAKE_AWAY_CLOSE_TIME", length = 8)
	protected Date takeAwayClosingTime;
	@Temporal(TemporalType.TIME)
	@Column(name = "SHIFT_ONE_HANDOVER_TIME", length = 8)
	protected Date shiftOneHandoverTime;
	@Temporal(TemporalType.TIME)
	@Column(name = "SHIFT_TWO_HANDOVER_TIME", length = 8)
	protected Date shiftTwoHandoverTime;
	@Column(name = "BUSINESS_HOURS_STATUS", nullable = false, length = 15)
	protected String status;
	@Column(name = "BRAND_ID", nullable = false)
	protected int brandId;
}
