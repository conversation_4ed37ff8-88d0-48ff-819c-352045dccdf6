
package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

@Entity
@Table(name = "SUBSCRIPTION_ITEM_ADDON")
public class SubscriptionItemAddon implements java.io.Serializable {


	private static final long serialVersionUID = -8180898028850062564L;
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SUBSCRIPTION_ITEM_ADDON_ID", unique = true, nullable = false)
	private Integer subscriptionItemAddonId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBSCRIPTION_ITEM_ID", nullable = false)
	private SubscriptionItem subscriptionItem;
	@Column(name = "ADDON_ID", nullable = false)
	private int productId;
	@Column(name = "PRODUCT_SOURCE_SYSTEM", nullable = true)
	private String source;
	@Column(name = "DIMENSION", nullable = true)
	private String dimension;
	@Column(name = "UNIT_OF_MEASURE", nullable = true)
	private String uom;
	@Column(name = "QUANTITY", nullable = true)
	private BigDecimal quantity;
	@Column(name = "ADDON_NAME", nullable = true)
	private String name;
	@Column(name = "DEFAULT_SETTING", nullable = true)
	private String defaultSetting;
	@Column(name = "ADDON_TYPE", nullable = true)
	private String type;

	public SubscriptionItemAddon(SubscriptionItem subscriptionItem, int productId, String name, String type,
			String source, String dimension, String uom, BigDecimal quantity, String defaultSetting) {
		this.subscriptionItem = subscriptionItem;
		this.productId = productId;
		this.source = source;
		this.dimension = dimension;
		this.quantity = quantity;
		this.name = name;
		this.type = type;
		this.uom = uom;
		this.defaultSetting = defaultSetting;
	}

}
