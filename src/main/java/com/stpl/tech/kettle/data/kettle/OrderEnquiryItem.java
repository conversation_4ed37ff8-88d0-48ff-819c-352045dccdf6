package com.stpl.tech.kettle.data.kettle;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "ORDER_ENQUIRY_ITEM")
public class OrderEnquiryItem {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ORDER_ENQUIRY_ITEM_ID", unique = true, nullable = false)
	private int id;

	@Column(name = "PRODUCT_ID", nullable = false)
	private int productId;

	@Column(name = "ORDERED_QUANTITY", nullable = false)
	private int orderderedQuantity;

	@Column(name = "AVAILABLE_QUANTITY", nullable = false)
	private int availableQuantity;

	@Column(name = "IS_REPLACEMENT_SERVED", nullable = false)
	private boolean isReplacementServed;

	@Column(name = "ORDER_ID", nullable = true)
	private Integer orderId;

	@Column(name = "ENQUIRY_ORDER_ID", nullable = true)
	private String enquiryOrderId;

	@Column(name = "CUSTOMER_ID", nullable = true)
	private Integer customerId;

	@Column(name = "UNIT_ID", nullable = true)
	private int unitId;

	@Column(name = "ENQUIRY_TIME", nullable = false)
	private Date enquiryTime;

}
