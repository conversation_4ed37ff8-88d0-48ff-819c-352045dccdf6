package com.stpl.tech.kettle.data.kettle;

import java.util.Date;

import com.stpl.tech.kettle.util.AppUtils;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "EVENT_PUSH_TRACK")
@NoArgsConstructor
@Getter
@Setter
public class EventPushTrack {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID", unique = true, nullable = false)
	private Integer id;
	@Column(name = "EVENT_NAME", nullable = false)
	private String eventName;
	@Column(name = "ORDER_ID", nullable = false)
	private Integer orderId;
	@Column(name = "STATUS", nullable = false)
	private String status;
	@Column(name = "UPDATE_TYPE", nullable = false)
	private String updateType;
	@Column(name = "UPDATED_AT", nullable = false)
	private Date updatedAt;
	@Column(name = "TRACKER_TYPE", nullable = false)
	private String trackerType;
	@Column(name = "PUBLISH_TIME",nullable = true)
	private Date publishTime;
	@Column(name = "PROCESS_START_TIME",nullable = true)
	private Date processStartTime;
	@Column(name = "PROCESS_END_TIME",nullable = true)
	private Date processEndTime;
	@Column(name = "TOTAL_PROCESS_TIME",nullable = true)
	private Long totalProcessSec;
	@Column(name = "CLEVERTAP_RESPONSE_TIME",nullable = true)
	private Long clevertapResponseTime;

	public EventPushTrack(String eventName, Integer orderId, String status, String updateType, String trackerType) {
		this.orderId = orderId;
		this.eventName = eventName;
		this.status = status;
		this.updateType = updateType;
		this.updatedAt = AppUtils.getCurrentTimestamp();
		this.trackerType = trackerType;
	}
}
