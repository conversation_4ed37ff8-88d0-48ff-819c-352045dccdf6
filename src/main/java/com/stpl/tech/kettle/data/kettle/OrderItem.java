
package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "ORDER_ITEM")
@Getter
@Setter
@NoArgsConstructor
public class OrderItem implements java.io.Serializable {

	private static final long serialVersionUID = -2894784227756403110L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_ITEM_ID", unique = true, nullable = false)
	private Integer orderItemId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ID", nullable = false)
	private OrderDetail orderDetail;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ITEM_INVOICE_ID", nullable = true)
	private OrderItemInvoice orderItemInvoice;
	@Column(name = "PRODUCT_ID", nullable = false)
	private int productId;
	@Column(name = "PRODUCT_NAME", nullable = false)
	private String productName;
	@Column(name = "PRODUCT_ALIAS_NAME")
	private String productAliasName;
	@Column(name = "QUANTITY", nullable = false)
	private int quantity;
	@Column(name = "PRICE", nullable = false, precision = 10)
	private BigDecimal price;
	@Column(name = "ORIGINAL_PRICE")
	private BigDecimal originalPrice;
	@Column(name = "HAS_ADDON", nullable = false, length = 1)
	private String hasAddon;
	@Column(name = "TOTAL_AMOUNT", nullable = false, precision = 10)
	private BigDecimal totalAmount;
	@Column(name = "AMOUNT_PAID", nullable = false, precision = 10)
	private BigDecimal paidAmount;
	@Column(name = "DISCOUNT_PERCENT", precision = 10)
	private BigDecimal discountPercent;
	@Column(name = "DISCOUNT_AMOUNT", precision = 10)
	private BigDecimal discountAmount;
	@Column(name = "DISCOUNT_REASON_ID")
	private Integer discountReasonId;
	@Column(name = "PARENT_ITEM_ID")
	private Integer parentItemId;
	@Column(name = "DISCOUNT_REASON")
	private String discountReason;
	@Column(name = "IS_COMPLIMENTARY", length = 1)
	private String isComplimentary;
	@Column(name = "COMBO_CONSTITUENT", length = 1)
	private String comboConstituent;
	@Column(name = "COMPLIMENTARY_TYPE_ID")
	private Integer complimentaryTypeId;
	@Column(name = "COMPLIMENTARY_REASON", length = 150)
	private String complimentaryReason;
	@Column(name = "DIMENSION", length = 10)
	private String dimension;
	@Column(name = "RECIPE_PROFILE", nullable = true)
	private String recipeProfile;
	@Column(name = "BILL_TYPE", nullable = false, length = 10)
	private String billType;
	@Column(name = "TAX_CODE", nullable = true, length = 40)
	private String taxCode;
	@Column(name = "PROMOTIONAL_DISCOUNT", nullable = true, length = 10)
	private BigDecimal promotionalDiscount;
	@Column(name = "TOTAL_TAX", precision = 10)
	private BigDecimal taxAmount;
	@Column(name = "RECIPE_ID", nullable = true)
	private Integer recipeId;
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderItem")
	private List<OrderItemAddon> orderItemAddons = new ArrayList<OrderItemAddon>(0);
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderItem")
	private List<OrderItemTaxDetail> orderItemTaxes = new ArrayList<OrderItemTaxDetail>(0);
	@Column(name = "CANCELATION_REASON_ID")
	private Integer cancellationReasonId;
	@Column(name = "WASTAGE_BOOKED", length = 1)
	private String wastageBooked;
	@Column(name = "TAKE_AWAY", length = 1)
	private String takeAway;
	@Column(name = "RECOM_CATEGORY", nullable = true)
	private String recomCategory;
	@Column(name = "TAX_DEDUCTED_BY_PARTNER")
	private String taxDeductedByPartner;
	@Column(name = "SOURCE_CATEGORY" ,nullable=true)
	private String sourceCategory;
	@Column(name = "SOURCE_SUB_CATEGORY" ,nullable=true)
	private String sourceSubCategory;
	@Column(name = "ORDER_ITEM_REMARK", nullable = true)
	private String orderItemRemark;
	@Transient
	private String isHoldOn = "N";


	public OrderItem(OrderDetail orderDetail, int productId, String productName, int quantity, BigDecimal price,
			String hasAddon, BigDecimal totalAmount, BigDecimal paidAmount, String billType, String taxCode) {
		this.orderDetail = orderDetail;
		this.productId = productId;
		this.productName = productName;
		this.quantity = quantity;
		this.price = price;
		this.hasAddon = hasAddon;
		this.totalAmount = totalAmount;
		this.paidAmount = paidAmount;
		this.billType = billType;
		this.taxCode = taxCode;
	}

	public OrderItem(OrderDetail orderDetail, int productId, String productName, int quantity, BigDecimal price,
			String hasAddon, BigDecimal totalAmount, BigDecimal paidAmount, BigDecimal discountPercent,
			BigDecimal discountAmount, Integer discountReasonId, String discountReason, String isComplimentary,
			Integer complimentaryTypeId, String complimentaryReason, String dimension, String billType, String taxCode,
			List<OrderItemAddon> orderItemAddons) {
		this.orderDetail = orderDetail;
		this.productId = productId;
		this.productName = productName;
		this.quantity = quantity;
		this.price = price;
		this.hasAddon = hasAddon;
		this.totalAmount = totalAmount;
		this.paidAmount = paidAmount;
		this.discountPercent = discountPercent;
		this.discountAmount = discountAmount;
		this.discountReasonId = discountReasonId;
		this.discountReason = discountReason;
		this.isComplimentary = isComplimentary;
		this.complimentaryTypeId = complimentaryTypeId;
		this.complimentaryReason = complimentaryReason;
		this.dimension = dimension;
		this.billType = billType;
		this.taxCode = taxCode;
		this.orderItemAddons = orderItemAddons;
	}

}
