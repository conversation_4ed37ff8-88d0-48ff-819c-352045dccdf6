package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;
import java.util.Date;

import com.stpl.tech.kettle.domain.model.CustomerDineInView;

import jakarta.persistence.Column;
import jakarta.persistence.ColumnResult;
import jakarta.persistence.ConstructorResult;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedNativeQuery;
import jakarta.persistence.SqlResultSetMapping;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "CUSTOMER_PRODUCT_FEEDBACK")
@SqlResultSetMapping(name = "CustomerDineInViewData", classes = @ConstructorResult(targetClass = CustomerDineInView.class, columns = {
		@ColumnResult(name = "customerId", type = Integer.class),
		@ColumnResult(name = "activeDineInOrders", type = Integer.class),
		@ColumnResult(name = "dineInOrders", type = Integer.class),
		@ColumnResult(name = "availedSignupOffer", type = String.class),
		@ColumnResult(name = "signupOfferExpiryTime", type = Date.class),
		@ColumnResult(name = "lastOrderTime", type = Date.class) }))
@NamedNativeQuery(name = "getCustomerDineInView", query = """
		SELECT ci.CUSTOMER_ID customerId,COALESCE(SUM(CASE WHEN
		od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL
		OR IS_GIFT_CARD_ORDER = 'N') AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),INTERVAL - 90 DAY)
		AND od.ORDER_SOURCE <> 'COD' THEN 1 ELSE 0 END),0) activeDineInOrders,
		COALESCE(SUM(CASE WHEN  od.ORDER_ID IS NOT NULL AND (IS_GIFT_CARD_ORDER IS NULL
		OR IS_GIFT_CARD_ORDER = 'N')AND od.ORDER_SOURCE <> 'COD' THEN  1 ELSE 0 END), 0) dineInOrders,
		COALESCE(MAX(ls.AVAILED_SIGNUP_OFFER), 'Y') availedSignupOffer,
		MAX(ls.SIGNUP_OFFER_EXPIRY_TIME) signupOfferExpiryTime, (ls.LAST_ORDER_TIME) lastOrderTime
		FROM   CUSTOMER_INFO ci INNER JOIN     LOYALTY_SCORE ls ON ls.CUSTOMER_ID = ci.CUSTOMER_ID
		LEFT OUTER JOIN   ORDER_DETAIL od ON od.CUSTOMER_ID = ci.CUSTOMER_ID
		AND od.ORDER_STATUS <> 'CANCELLED' AND od.BRAND_ID = ?2 AND od.ORDER_TYPE = 'order'
		AND od.ORDER_ID NOT IN (?3)   WHERE ci.CUSTOMER_ID = ?1
		group by ci.CUSTOMER_ID """, resultClass = CustomerDineInView.class, resultSetMapping = "CustomerDineInViewData")
public class CustomerProductFeedback implements Serializable {

	private static final long serialVersionUID = 3890775449396971884L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CUSTOMER_PRODUCT_FEEDBACK_ID", unique = true, nullable = false)
	private int id;
	@Column(name = "CUSTOMER_ID", nullable = false)
	private Integer customerId;
	@Column(name = "PRODUCT_ID", nullable = false)
	private Integer productId;
	@Column(name = "RATING", nullable = false)
	private Integer rating;
	@Column(name = "RATING_SOURCE", nullable = false)
	private String source;
	@Column(name = "RATING_SOURCE_ID", nullable = true)
	private Integer sourceId;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ADD_TIME", nullable = false, length = 19)
	private Date addTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATE_TIME", nullable = true, length = 19)
	private Date updateTime;
	@Column(name = "RATING_STATUS", nullable = false)
	private String status;

	@Override
	public String toString() {
		return "CustomerProductFeedback [id=" + id + ", customerId=" + customerId + ", productId=" + productId
				+ ", rating=" + rating + ", source=" + source + ", sourceId=" + sourceId + ", addTime=" + addTime
				+ ", updateTime=" + updateTime + ", status=" + status + "]";
	}
}
