/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "DELIVERY_PARTNER")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryPartner implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "PARTNER_ID", unique = true, nullable = false)
	private Integer partnerId;
	@Column(name = "PARTNER_CODE", nullable = false, length = 50)
	private String partnerCode;
	@Column(name = "PARTNER_DISPLAY_NAME", nullable = false, length = 100)
	private String partnerDisplayName;
	@Column(name = "PARTNER_STATUS", nullable = false, length = 15)
	private String partnerStatus;
	@Column(name = "PARTNER_TYPE", nullable = false, length = 15)
	private String partnerType;
	@Column(name = "AUTOMATED", nullable = false, length = 1)
	private String automated;
	@Column(name = "PER_DELIVERY_COST", nullable = false)
	private BigDecimal deliveryCost;
	@Column(name = "CASH_ELIGIBILITY", nullable = true)
	private String eligibleForCash;

	public DeliveryPartner(String partnerCode, String partnerDisplayName) {
		this.partnerCode = partnerCode;
		this.partnerDisplayName = partnerDisplayName;
	}
}
