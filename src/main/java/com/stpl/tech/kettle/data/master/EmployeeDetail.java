package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "EMPLOYEE_DETAIL")
@Getter
@Setter
@NoArgsConstructor
public class EmployeeDetail implements java.io.Serializable {

	private static final long serialVersionUID = 6138733235192593724L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EMP_ID", unique = true, nullable = false)
	private Integer empId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EMP_PERMANENT_ADDR", nullable = false)
	private AddressInfo addressInfoByEmpPermanentAddr;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "DEPTARTMENT_ID", nullable = false)
	private Department department;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EMP_CURRENT_ADDR", nullable = false)
	private AddressInfo addressInfoByEmpCurrentAddr;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "REPORTING_MANAGER_ID")
	private EmployeeDetail reportingManager;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "DESIGNATION_ID", nullable = false)
	private Designation designation;

	@Column(name = "EMP_NAME", nullable = false)
	private String empName;

	@Column(name = "EMP_GENDER", nullable = true, length = 1)
	private String empGender;

	@Column(name = "EMP_CONTACT_NUM_1", length = 32)
	private String empContactNum1;

	@Column(name = "EMP_CONTACT_NUM_2", length = 32)
	private String empContactNum2;

	@Column(name = "EMPLOYMENT_TYPE", nullable = false, length = 10)
	private String employmentType;

	@Column(name = "EMPLOYMENT_STATUS", nullable = false, length = 10)
	private String employmentStatus;

	@Column(name = "BIOMETRIC_IDENTIFIER")
	private String biometricIdentifier;

	@Temporal(TemporalType.DATE)
	@Column(name = "JOINING_DATE", nullable = false, length = 10)
	private Date joiningDate;

	@Temporal(TemporalType.DATE)
	@Column(name = "TERMINATION_DATE", length = 10)
	private Date terminationDate;

	@Column(name = "EMP_EMAIL", nullable = true, length = 10)
	private String employeeEmail;

	@Column(name = "EMPLOYEE_CODE", nullable = true, length = 15)
	private String employeeCode;

	@Column(name = "COMMUNICATION_CHANNEL", nullable = true, length = 50)
	private String communicationChannel;

	@Column(name = "SDP_CONTACT", length = 20, unique = true)
	private String sdpContact;

	@Column(name = "EMPLOYEE_MEAL_ELIGIBLE", length = 1)
	private String employeeMealEligible;

	@Column(name = "REASON_FOR_TERMINATION")
	private String reasonForTermination;

	@Column(name = "HR_EXECUTIVE")
	private String hrExecutive;

	@Column(name = "LEAVE_APPROVAL_AUTHORITY")
	private String leaveApprovalAuthority;

	@Column(name = "DOB")
	private Date dob;

	@Column(name = "LOCATION_CODE")
	private Integer locCode;

	public EmployeeDetail(Integer empId, AddressInfo addressInfoByEmpPermanentAddr, Department department,
			AddressInfo addressInfoByEmpCurrentAddr, EmployeeDetail reportingManager, Designation designation,
			String empName, String empGender, String empContactNum1, String empContactNum2, String employmentType,
			String employmentStatus, String biometricIdentifier, Date joiningDate, Date terminationDate,
			String employeeEmail, String employeeCode, String communicationChannel, String sdpContact,
			String employeeMealEligible, String reasonForTermination, String hrExecutive, String leaveApprovalAuthority,
			Date dob) {
		this.empId = empId;
		this.addressInfoByEmpPermanentAddr = addressInfoByEmpPermanentAddr;
		this.department = department;
		this.addressInfoByEmpCurrentAddr = addressInfoByEmpCurrentAddr;
		this.reportingManager = reportingManager;
		this.designation = designation;
		this.empName = empName;
		this.empGender = empGender;
		this.empContactNum1 = empContactNum1;
		this.empContactNum2 = empContactNum2;
		this.employmentType = employmentType;
		this.employmentStatus = employmentStatus;
		this.biometricIdentifier = biometricIdentifier;
		this.joiningDate = joiningDate;
		this.terminationDate = terminationDate;
		this.employeeEmail = employeeEmail;
		this.employeeCode = employeeCode;
		this.communicationChannel = communicationChannel;
		this.sdpContact = sdpContact;
		this.employeeMealEligible = employeeMealEligible;
		this.reasonForTermination = reasonForTermination;
		this.hrExecutive = hrExecutive;
		this.leaveApprovalAuthority = leaveApprovalAuthority;
		this.dob = dob;
	}

}
