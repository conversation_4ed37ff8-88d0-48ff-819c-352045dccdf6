package com.stpl.tech.kettle.data.master;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "SIGNUP_OFFER_COUPON_DETAILS")
@Getter
@Setter
public class SignupOffersCouponDetails {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "KEY_ID", unique = true, nullable = false)
	Integer keyId;
	@Column(name = "OFFER_DETAIL_ID")
	Integer offerDetailId;
	@Column(name = "COUPON_CODE")
	String couponCode;
	@Column(name = "COUPON_PREFIX")
	String couponPrefix;
	@Column(name = "COUPON_STATUS")
	String couponStatus;
	@Column(name = "COUPON_VALIDITY")
	String couponValidity;

}
