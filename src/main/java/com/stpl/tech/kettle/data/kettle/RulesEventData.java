package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "RULES_EVENT_DATA")
@Getter
@Setter
public class RulesEventData implements Serializable {

	private static final long serialVersionUID = -2012089696822974172L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "RULES_EVENT_DATA_ID", unique = true, nullable = false)
	private Integer rulesEventDataId;

	@Column(name = "OPTION_RESULT_DATA_ID", nullable = false)
	private int optionResultDataId;

	@Column(name = "PRODUCT_LIST", nullable = true, length = 1000)
	private String productList;

	@Column(name = "STOCK_OUT_LIST", nullable = true, length = 1000)
	private String stockOutProductList;

	@Column(name = "AVAILED", nullable = true, length = 1)
	private String availed;

	@Column(name = "COUPON_CODE", nullable = true, length = 20)
	private String couponCode;

	@Column(name = "TRIGGERED_BY", nullable = true, length = 20)
	private String triggeredBy;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_TIME", nullable = true, length = 19)
	private Date eventTime;

	@Column(name = "ORDER_ID", nullable = true)
	private Integer orderId;

	@Column(name = "QUANTITY", nullable = true)
	private Integer quantity;

	@Column(name = "NEW_CUSTOMER", nullable = true, length = 1)
	private String newCustomer;
}
