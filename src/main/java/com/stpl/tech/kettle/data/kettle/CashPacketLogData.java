package com.stpl.tech.kettle.data.kettle;

import java.math.BigDecimal;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "CASH_PACKET_LOG_DATA")
@Getter
@Setter
public class CashPacketLogData {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CASH_PACKET_LOG_DATA_ID", unique = true, nullable = false)
	private Integer cashPacketLogId;
	@Column(name = "CASH_PACKET_ID", nullable = false)
	private Integer cashPacketId;
	@Column(name = "TRANSACTION_TYPE", nullable = false)
	private String transactionType;
	@Column(name = "TRANSACTION_CODE", nullable = false)
	private String transactionCode; /* SignUpRefrral, Cash Bonus 5th SignUp, Cash Bonus 10th Sign Up */
	@Column(name = "TRANSACTION_CODE_TYPE", nullable = false)
	private String transactionCodeType; /* Referral,Cash Bonus, Initial Load */
	@Column(name = "TRANSACTION_AMOUNT", nullable = false)
	private BigDecimal transactionAmount;
	@Column(name = "TRANSACTION_REASON", nullable = false)
	private String transactionReason; /* Sign Up, Initial Load */
	@Column(name = "TRANSACTION_TIME", nullable = false)
	private Date transactionTime;
	@Column(name = "CASH_LOG_DATA_ID", nullable = true)
	private Integer cashLogDataId;/* Reference for chaayos transaction Log (Many to one) */
	@Column(name = "ORDER_ID", nullable = true)
	private Integer orderId;
}
