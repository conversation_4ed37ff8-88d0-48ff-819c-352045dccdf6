package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "PAYMENT_MODE")
@Getter
@Setter
@NoArgsConstructor
public class PaymentMode implements java.io.Serializable {

	private static final long serialVersionUID = -4511110376679712211L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "PAYMENT_MODE_ID", unique = true, nullable = false)
	private Integer paymentModeId;

	@Column(name = "MODE_NAME", nullable = false, length = 100)
	private String modeName;

	@Column(name = "MODE_TYPE", nullable = false, length = 15)
	private String modeType;

	@Column(name = "MODE_DESCRIPTION", nullable = false)
	private String modeDescription;

	@Column(name = "SETTLEMENT_TYPE", nullable = false, length = 20)
	private String settlementType;

	@Column(name = "GENERATE_PULL", nullable = false, columnDefinition = "TINYINT(1)")
	private boolean generatePull;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "paymentMode")
	private List<Denomination> denominations;

	@Column(name = "COMMISSION_RATE", nullable = false)
	private BigDecimal commissionRate;

	@Column(name = "MODE_STATUS", nullable = false)
	private String modeStatus;

	@Column(name = "MODE_CATEGORY", nullable = false)
	private String modeCategory;

	@Column(name = "IS_EDITABLE", nullable = false)
	private String editable;

	@Column(name = "APPLICABLE_ON_DISCOUNTED_ORDERS", nullable = true)
	private String applicableOnDiscountedOrders;

	@Column(name = "AUTOMATIC_PULL_VALIDATE", nullable = false)
	private String automaticPullValidate;

	@Column(name = "AUTOMATIC_TRANSFER", nullable = false)
	private String automaticTransfer;

	@Column(name = "AUTOMATIC_CLOSE_TRANSFER", nullable = false)
	private String automaticCloseTransfer;

	@Column(name = "NEEDS_SETTLEMENT_SLIP_NUMBER", nullable = true)
	private String needsSettlementSlip;

	@Column(name = "VALIDATION_SOURCE", nullable = true)
	private String validationSource;

	@Column(name = "LEDGER_NAME")
	private String ledgerName;

	public PaymentMode(String modeName, String modeType, String modeDescription, String settlementType) {
		this.modeName = modeName;
		this.modeType = modeType;
		this.modeDescription = modeDescription;
		this.settlementType = settlementType;
	}

}
