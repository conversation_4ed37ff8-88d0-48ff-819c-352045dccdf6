package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "ORDER_FEEDBACK_DETAIL")
public class FeedbackDetail implements Serializable {

	private static final long serialVersionUID = -6635154596546609799L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "FEEDBACK_ID", unique = true, nullable = false)
	private Integer feedbackId;
	@Column(name = "ORDER_ID", nullable = true)
	private Integer orderId;
	@Column(name = "FEEDBACK_STATUS", nullable = false, length = 30)
	private String feedbackStatus;
	@Column(name = "ORDER_SOURCE", nullable = true, length = 20)
	private String orderSource;
	@Column(name = "PRODUCT_IDS", nullable = true, length = 60)
	private String productIds;
	@Column(name = "CUSTOMER_NAME", nullable = true, length = 100)
	private String customerName;
	@Column(name = "UNIT_ID", nullable = true)
	private Integer unitId;
	@Column(name = "CUSTOMER_ID")
	private int customerId;
	@Column(name = "CONTACT_NUMBER", nullable = false, length = 15)
	private String contactNumber;
	@Column(name = "EMAIL_ID", nullable = true, length = 100)
	private String emailId;
	@Column(name = "FEEDBACK_SOURCE", nullable = true, length = 15)
	private String source;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "FEEDBACK_CREATION_TIME", nullable = false, length = 19)
	private Date feedbackCreationTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "FEEDBACK_TIME", nullable = true, length = 19)
	private Date feedbackTime;
	@Column(name = "FEEDBACK_UNIT_ID", length = 19)
	private Integer feedbackUnitId;
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "feedbackDetail")
	private List<FeedbackEvent> feedbackEvents = new ArrayList<FeedbackEvent>(0);
	@Column(name = "LATEST_FEEDBACK_INFO_ID", nullable = true)
	private Integer latestFeedbackInfoId;
	@Column(name = "FEEDBACK_RATING", nullable = true)
	private Integer rating;
	@Column(name = "EVENT_TYPE", nullable = true, length = 15)
	private String eventType;
	@Column(name = "RATING_TYPE")
	private String ratingType;
	@Column(name = "MAX_RATING")
	private Integer maxRating;
}
