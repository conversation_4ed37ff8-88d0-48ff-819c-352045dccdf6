package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "COMPANY_DETAIL")
@Getter
@Setter
@NoArgsConstructor
public class CompanyDetail implements Serializable {

	private static final long serialVersionUID = -3989589809131205966L;
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "COMPANY_ID", unique = true, nullable = false)
	private Integer companyId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "REGD_ADDR_ID", nullable = false)
	private AddressInfo addressInfo;
	@Column(name = "COMPANY_NAME", nullable = false)
	private String companyName;
	@Column(name = "COMPANY_DESCRIPTION", nullable = false, length = 5000)
	private String companyDescription;
	@Column(name = "CIN", nullable = false, length = 21)
	private String cin;
	@Column(name = "SERVICE_TAX_NO", nullable = false, length = 15)
	private String serviceTaxNo;
	@Column(name = "WEBSITE_ADDR", nullable = false)
	private String websiteAddr;
	@Column(name = "SHORT_CODE", nullable = false)
	private String shortCode;
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "companyDetail")
	private Set<BusinessDivision> businessDivisions = new HashSet<BusinessDivision>(0);

	public CompanyDetail(AddressInfo addressInfo, String companyName, String companyDescription, String cin,
			String serviceTaxNo, String websiteAddr, String shortCode) {
		this.addressInfo = addressInfo;
		this.companyName = companyName;
		this.companyDescription = companyDescription;
		this.cin = cin;
		this.serviceTaxNo = serviceTaxNo;
		this.websiteAddr = websiteAddr;
		this.shortCode = shortCode;
	}

	public CompanyDetail(AddressInfo addressInfo, String companyName, String companyDescription, String cin,
			String serviceTaxNo, String websiteAddr, String shortCode, Set<BusinessDivision> businessDivisions) {
		this.addressInfo = addressInfo;
		this.companyName = companyName;
		this.companyDescription = companyDescription;
		this.cin = cin;
		this.serviceTaxNo = serviceTaxNo;
		this.websiteAddr = websiteAddr;
		this.shortCode = shortCode;
		this.businessDivisions = businessDivisions;
	}

}
