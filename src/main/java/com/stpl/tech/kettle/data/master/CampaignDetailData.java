package com.stpl.tech.kettle.data.master;

import java.util.Date;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "CAMPAIGN_DETAIL_DATA")
@Getter
@Setter
public class CampaignDetailData {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CAMPAIGN_ID", unique = true, nullable = false)
	private Integer campaignId;
	@Column(name = "PRIMARY_URL")
	private String primaryUrl;
	@Column(name = "CAMPAIGN_STRATEGY")
	private String campaignStrategy;
	@Column(name = "CAMPAIGN_SOURCE")
	private String campaignSource;
	@Column(name = "CAMPAIGN_MEDIUM")
	private String campaignMedium;
	@Column(name = "CAMPAIGN_NAME")
	private String campaignName;
	@Column(name = "CAMPAIGN_CATEGORY")
	private String campaignCategory;
	@Column(name = "CAMPAIGN_DESC")
	private String campaignDesc;
	@Column(name = "COUPON_CODE")
	private String couponCode;
	@Column(name = "COUPON_CODE_DESC")
	private String couponCodeDesc;
	@Column(name = "IS_COUPON_CLONE")
	private String isCouponClone;
	@Column(name = "REGION")
	private String region;
	@Column(name = "CITY", columnDefinition = "TEXT")
	private String city;
	@Column(name = "UNIT_IDS", columnDefinition = "TEXT")
	private String unitIds;
	@Column(name = "USAGE_LIMIT")
	private Integer usageLimit;
	@Column(name = "START_DATE")
	@Temporal(TemporalType.TIMESTAMP)
	private Date startDate;
	@Column(name = "END_DATE")
	@Temporal(TemporalType.TIMESTAMP)
	private Date endDate;
	@Column(name = "VALIDITY")
	private Integer validity;
	@Column(name = "HERO_BANNER_MOBILE")
	private String heroBannerMobile;
	@Column(name = "HERO_BANNER_DESKTOP")
	private String heroBannerDesktop;
	@Column(name = "LANDING_PAGE_DESC")
	private String landingPageDesc;
	@Column(name = "SMS_TEMPLATE")
	private String smsTemplate;
	@Column(name = "SMS_REMINDER")
	private String smsReminder;
	@Column(name = "WHATSAPP_TEMPLATE")
	private String whatsappTemplate;
	@Column(name = "WHATSAPP_REMINDER")
	private String whatsappReminder;
	@Column(name = "REMINDER_DAY_GAP")
	private Integer reminderDayGap;
	@Column(name = "UTM_HEADING")
	private String utmHeading;
	@Column(name = "UTM_DESC")
	private String utmDesc;
	@Column(name = "UTM_IMAGE_URL")
	private String utmImageUrl;
	@Column(name = "REDIRECTION_URL")
	private String redirectionUrl;
	@Column(name = "CAMPAIGN_STATUS")
	private String campaignStatus;
	@Column(name = "CAMPAIGN_TOKEN")
	private String campaignToken;
	@Column(name = "IMAGE1")
	private String image1;
	@Column(name = "IMAGE2")
	private String image2;
	@Column(name = "IMAGE3")
	private String image3;
	@Column(name = "LONG_URL")
	private String longUrl;
	@Column(name = "SHORT_URL")
	private String shortUrl;
	@Column(name = "NEW_CUSTOMER_ONLY")
	private String newCustomerOnly;
	@Column(name = "CAMPAIGN_REACH")
	private String campaignReach;
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "campaignDetailData")
	private List<CampaignCouponMapping> couponMapping;
	@Column(name = "COUPON_PREFIX")
	private String couponPrefix;
	@Column(name = "LINKED_CAMPAIGN_ID")
	private Integer linkedCampaignId;
	@Column(name = "COUPON_APPLICABLE_AFTER")
	private Integer couponApplicableAfter;
	@Column(name = "BRAND_ID")
	private Integer brandId;
	@Column(name = "APPLICABLE_FOR_ORDER")
	private String applicableForOrder;
	@Column(name = "PARENT_CAMPAIGN_STRATEGY")
	private String parentCampaignStrategy;
	@Column(name = "LAUNCH_UNIT_ID")
	private Integer launchUnitId;
	@Column(name = "CAFE_LAUNCH_DATE")
	private Date cafeLaunchDate;

}
