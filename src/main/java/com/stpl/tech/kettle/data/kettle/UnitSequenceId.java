package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "UNIT_SEQUENCE_ID")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UnitSequenceId implements Serializable {

	private static final long serialVersionUID = 6289405289226700832L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SEQUENCE_ID", unique = true, nullable = false)
	private Integer sequenceId;
	@Column(name = "UNIT_ID", nullable = false)
	private int unitId;
	@Column(name = "DATA_SOURCE", nullable = false)
	private String dataSource;
	@Column(name = "NEXT_VALUE", nullable = false)
	private int nextValue;

	public UnitSequenceId(int unitId, String dataSource, int nextValue) {
		this.unitId = unitId;
		this.dataSource = dataSource;
		this.nextValue = nextValue;
	}
}
