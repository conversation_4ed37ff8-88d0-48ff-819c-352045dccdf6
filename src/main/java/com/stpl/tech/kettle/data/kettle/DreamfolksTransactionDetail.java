package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "DREAMFOLKS_TRANSACTION_DETAIL")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DreamfolksTransactionDetail implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID", unique = true, nullable = false)
	private Integer id;

	@Column(name = "VOUCHER_CODE", length = 10)
	private String voucherCode;

	@Column(name = "TRANSACTION_ID")
	private String transactionId;

	@Column(name = "QUANTITY_REDEEMED")
	private Integer quantityRedeemed;

	@Column(name = "ORDER_ID")
	private Integer orderId;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "TRANSACTION_TIME")
	private Date transactionTime;

} 