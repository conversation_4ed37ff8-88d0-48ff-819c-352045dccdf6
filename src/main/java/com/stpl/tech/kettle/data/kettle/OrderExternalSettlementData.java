/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.kettle;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "ORDER_EXTERNAL_SETTLEMENT_DATA")
@Getter
@Setter
public class OrderExternalSettlementData implements java.io.Serializable {

	private static final long serialVersionUID = 662279153143569601L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_EXTERNAL_SETTLEMENT_ID", unique = true, nullable = false)
	private Integer externalSettlementId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SETTLEMENT_ID", nullable = false)
	private OrderSettlement orderSettlement;
	@Column(name = "AMOUNT_PAID", precision = 10)
	private BigDecimal amountPaid;
	@Column(name = "EXTERNAL_TRANSACTION_ID")
	private String externalTransactionId;

	public OrderExternalSettlementData() {
	}

	public OrderExternalSettlementData(OrderSettlement orderSettlement, BigDecimal amountPaid,
			String externalTransactionId) {
		this.orderSettlement = orderSettlement;
		this.amountPaid = amountPaid;
		this.externalTransactionId = externalTransactionId;
	}

}
