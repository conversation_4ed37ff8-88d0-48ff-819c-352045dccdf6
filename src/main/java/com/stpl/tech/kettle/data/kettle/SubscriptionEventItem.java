
package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "SUBSCRIPTION_EVENT_ITEM")
@Getter
@Setter
public class SubscriptionEventItem implements java.io.Serializable {

	private static final long serialVersionUID = -2864462558303094819L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SUBSCRIPTION_EVENT_ITEM_ID", unique = true, nullable = false)
	private Integer subscriptionEventItemId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBSCRIPTION_ID", nullable = false)
	private SubscriptionDetail subscriptionDetail;
	@Column(name = "EVENT_ITEM_TYPE", nullable = false, length = 30)
	private String eventItemType;
	@Column(name = "EVENT_ITEM_VALUE", nullable = false)
	private int eventItemValue;
	@Column(name = "EVENT_ITEM_STATUS", nullable = false, length = 15)
	private String eventItemStatus;

}
