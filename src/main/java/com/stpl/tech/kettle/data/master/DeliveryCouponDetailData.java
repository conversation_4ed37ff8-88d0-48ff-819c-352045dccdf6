package com.stpl.tech.kettle.data.master;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "DELIVERY_COUPON_DETAIL_DATA")
@Getter
@Setter
public class DeliveryCouponDetailData implements Serializable {

	private static final long serialVersionUID = -3210007238081118742L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "DELIVERY_COUPON_ID", unique = true, nullable = false)
	private Integer deliveryCouponId;

	@Column(name = "BRAND_ID")
	private Integer brandId;

	@Column(name = "CHANNEL_PARTNER_ID")
	private Integer channelPartnerId;

	@Column(name = "COUPON_STRATEGY")
	private String couponStrategy;

	@Column(name = "MASTER_COUPON")
	private String masterCoupon;

	@Column(name = "COUPON_CODE")
	private String couponCode;

	@Column(name = "START_DATE")
	private Date startDate;

	@Column(name = "END_DATE")
	private Date endDate;

	@Column(name = "VALIDITY_IN_DAYS")
	private Integer validityInDays;

	@Column(name = "MAX_NO_OF_DISTRIBUTIONS")
	private Integer maxNoOfDistributions;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATION_TIME")
	private Date creationTime;

	@Column(name = "NO_OF_ALLOCATIONS")
	private Integer noOfAllocations;

	@Column(name = "IS_EXHAUSTED", nullable = false)
	private String isExhausted;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_ALLOCATION_TIME")
	private Date lastAllocationTime;

	@Column(name = "DELIVERY_COUPON_STATUS")
	private String deliveryCouponStatus;

	@Column(name = "MAX_USAGE")
	private Integer maxUsage;

}
