package com.stpl.tech.kettle.data.kettle;

import java.util.Date;

import com.stpl.tech.kettle.util.AppUtils;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "CLEVERTAP_PROFILE_PUSH_TRACK")
@NoArgsConstructor
@Getter
@Setter
public class CleverTapProfilePushTrack {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID", unique = true, nullable = false)
	private Integer id;
	@Column(name = "CUSTOMER_ID", nullable = false)
	private Integer customerId;
	@Column(name = "STATUS", nullable = false)
	private String status;
	@Column(name = "UPDATE_TYPE", nullable = false)
	private String updateType;
	@Column(name = "UPDATED_AT", nullable = false)
	private Date updateAt;
	@Column(name = "PUBLISH_TIME",nullable = true)
	private Date publishTime;
	@Column(name = "PROCESS_START_TIME",nullable = true)
	private Date processStartTime;
	@Column(name = "PROCESS_END_TIME",nullable = true)
	private Date processEndTime;
	@Column(name = "TOTAL_PROCESS_TIME",nullable = true)
	private Long totalProcessSec;
	@Column(name = "CLEVERTAP_RESPONSE_TIME",nullable = true)
	private Long clevertapResponseTime;

	public CleverTapProfilePushTrack(Integer customerId, String status, String updateType) {
		this.customerId = customerId;
		this.status = status;
		this.updateType = updateType;
		this.updateAt = AppUtils.getCurrentTimestamp();
	}

}
