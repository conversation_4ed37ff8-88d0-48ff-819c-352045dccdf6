package com.stpl.tech.kettle.data.kettle;

import java.math.BigDecimal;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "CASH_PACKET_DATA")
@Getter
@Setter
public class CashPacketData {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CASH_PACKET_ID", unique = true, nullable = false)
	private Integer cashPacketId;
	@Column(name = "CUSTOMER_ID", nullable = false)
	private Integer customerId;
	@Column(name = "CASH_DATA_ID", nullable = false)
	private Integer cashDataId;
	@Column(name = "INITIAL_AMOUNT", nullable = false)
	private BigDecimal initialAmount;
	@Column(name = "REDEEMED_AMOUNT", nullable = false)
	private BigDecimal redeemedAmount;
	@Column(name = "RETAINED_AMOUNT", nullable = false)
	private BigDecimal retainedAmount;
	@Column(name = "EXPIRED_AMOUNT", nullable = false)
	private BigDecimal expiredAmount;
	@Column(name = "CURRENT_AMOUNT")
	private BigDecimal currentAmount;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EXPIRATION_DATE")
	private Date expirationDate;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "INITIAL_EXPIRATION_DATE")
	private Date initialExpirationDate; // ( Default = Expiration Date)
	@Column(name = "TRANSACTION_CODE")
	private String transactionCode; /* SignUpRefrral, Cash Bonus 5th SignUp, Cash Bonus 10th Sign Up */
	@Column(name = "TRANSACTION_CODE_TYPE")
	private String transactionCodeType; /* Referral,Cash Bonus, Initial Load */
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME")
	private Date lastUpdateTime;
	@Column(name = "REFERENT_ID")
	private Integer referentId; // ( Customer Id nullable)
	@Column(name = "REFERRAL_DATA_ID")
	private Integer referralDataId;// ( Record Id on sign up )
	@Temporal(TemporalType.DATE)
	@Column(name = "CREATION_DATE")
	private Date creationDate;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATION_TIME")
	private Date creationTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ACTIVATION_TIME")
	private Date activationTime;
	@Column(name = "EVENT_STATUS")
	private String eventStatus; // Initiated,Active,Expired,Retained

}
