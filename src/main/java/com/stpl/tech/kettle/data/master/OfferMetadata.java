package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "OFFER_METADATA")
@Getter
@Setter
public class OfferMetadata implements java.io.Serializable {

	private static final long serialVersionUID = 9079270054663581954L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ID", unique = true, nullable = false)
	private int id;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "OFFER_ID", nullable = false)
	private OfferDetailData offerId;

	@Column(name = "MAPPING_TYPE", nullable = false)
	private String mappingType;

	@Column(name = "MAPPING_VALUE", nullable = false)
	private String mappingValue;

	@Column(name = "STATUS", nullable = false)
	private String status;

	@Column(name = "MAPPING_CLASS", nullable = true)
	private String mappingClass;

}
