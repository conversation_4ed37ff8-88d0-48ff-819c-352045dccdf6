package com.stpl.tech.kettle.data.kettle;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

@Entity
@Table(name = "HOUSE_COST_CONSUMABLE_DATA")
public class HouseCostConsumableData implements Serializable {

	@Serial
	private static final long serialVersionUID = -3835874676549361807L;

	private Integer orderItemId;
	private HouseCostEvent orderDetail;
	private int productId;
	private String productName;
	private BigDecimal quantity;
	private String uom;

	public HouseCostConsumableData() {
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "HOUSE_COST_CONSUMABLE_DATA_ID", unique = true, nullable = false)
	public Integer getOrderItemId() {
		return this.orderItemId;
	}

	public void setOrderItemId(Integer orderItemId) {
		this.orderItemId = orderItemId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "HOUSE_COST_EVENT_ID", nullable = false)
	public HouseCostEvent getOrderDetail() {
		return this.orderDetail;
	}

	public void setOrderDetail(HouseCostEvent orderDetail) {
		this.orderDetail = orderDetail;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return this.productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	@Column(name = "PRODUCT_NAME", nullable = false)
	public String getProductName() {
		return this.productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	@Column(name = "QUANTITY", nullable = false)
	public BigDecimal getQuantity() {
		return this.quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	@Column(name = "DIMENSION", length = 10)
	public String getUom() {
		return this.uom;
	}

	public void setUom(String dimension) {
		this.uom = dimension;
	}
}
