package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "ORDER_METADATA_DETAIL")
@Getter
@Setter
public class OrderMetadataDetail {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "METADATA_ID", unique = true, nullable = false)
	private int metadataId;
	@Column(name = "ORDER_ID", nullable = false)
	private int orderId;
	@Column(name = "ATTRIBUTE_NAME", nullable = false)
	private String attributeName;
	@Column(name = "ATTRIBUTE_VALUE", nullable = true)
	private String attributeValue;

}
