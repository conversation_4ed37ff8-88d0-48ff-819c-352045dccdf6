
package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "ORDER_RE_PRINT_DETAIL")
@Getter
@Setter
@NoArgsConstructor
public class OrderRePrintDetail implements java.io.Serializable {

	private static final long serialVersionUID = -1160609111659112936L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_PRINT_ID", unique = true, nullable = false)
	private Integer orderPrintId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ID", nullable = false)
	private OrderDetail orderDetail;
	@Column(name = "PRINT_REASON", nullable = false)
	private String printReason;
	@Column(name = "GENERATED_BY", nullable = false)
	private int generatedBy;
	@Column(name = "APPROVED_BY", nullable = false)
	private int approvedBy;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REPRINT_TIME", nullable = false, length = 19)
	private Date reprintTime;

	public OrderRePrintDetail(OrderDetail orderDetail, String printReason) {
		this.orderDetail = orderDetail;
		this.printReason = printReason;
	}

	public OrderRePrintDetail(OrderDetail orderDetail, String printReason, int generatedBy, int approvedBy,
			Date reprintTime) {
		this.orderDetail = orderDetail;
		this.printReason = printReason;
		this.generatedBy = generatedBy;
		this.approvedBy = approvedBy;
		this.reprintTime = reprintTime;
	}

}
