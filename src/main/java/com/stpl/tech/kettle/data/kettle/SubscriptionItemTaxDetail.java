
package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "SUBSCRIPTION_ITEM_TAX_DETAIL")
@Getter
@Setter
public class SubscriptionItemTaxDetail implements java.io.Serializable {

	private static final long serialVersionUID = 6909018841091451616L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SUBSCRIPTION_ITEM_TAX_DETAIL_ID", unique = true, nullable = false)
	private Integer subscriptionItemTaxDetailId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBSCRIPTION_ITEM_ID", nullable = false)
	private SubscriptionItem subscriptionItem;
	@Column(name = "TAX_TYPE", nullable = false, length = 20)
	private String taxType;
	@Column(name = "TAX_CODE", nullable = false, length = 20)
	private String taxCode;
	@Column(name = "TAX_PERCENTAGE", precision = 10)
	private BigDecimal taxPercentage;
	@Column(name = "TOTAL_TAX", precision = 10)
	private BigDecimal totalTax;
	@Column(name = "TOTAL_AMOUNT", precision = 10)
	private BigDecimal totalAmount;
	@Column(name = "TAXABLE_AMOUNT", precision = 10)
	private BigDecimal taxableAmount;

}