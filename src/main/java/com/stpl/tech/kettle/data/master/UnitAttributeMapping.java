package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "UNIT_ATTRIBUTE_MAPPING")
@Getter
@Setter
@NoArgsConstructor
public class UnitAttributeMapping implements java.io.Serializable {

	private static final long serialVersionUID = -7726578415700938848L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_ATTRIBUTE_MAPPING_ID", unique = true, nullable = false)
	private Integer attributeMappingId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_ID", nullable = false)
	private UnitDetail unitDetail;

	@Column(name = "ATTRIBUTE_CODE", nullable = false)
	private String attributeCode;

	@Column(name = "ATTRIBUTE_ID", nullable = false, length = 15)
	private Integer attributeId;

	@Column(name = "ATTRIBUTE_VALUE", nullable = false, length = 500)
	private String attributeValue;

	@Column(name = "ATTRIBUTE_TYPE", nullable = false, length = 20)
	private String attributeType;

	@Column(name = "MAPPING_STATUS", nullable = false, length = 15)
	private String mappingStatus;

}
