package com.stpl.tech.kettle.data.kettle;

import java.math.BigDecimal;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "EMPLOYEE_MEAL_ALLOWANCE_DATA")
@Getter
@Setter
public class EmployeeMealAllowanceData {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "MEAL_ID", unique = true, nullable = false)
	private Integer mealId;
	@Column(name = "EMPLOYEE_ID", nullable = false)
	private Integer employeeId;
	@Column(name = "ORDER_ID", nullable = false)
	private Integer orderId;
	@Column(name = "AMOUNT_AVAILED", precision = 10)
	private BigDecimal amount;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ORDER_TIME", nullable = false)
	private Date ordertime;

}
