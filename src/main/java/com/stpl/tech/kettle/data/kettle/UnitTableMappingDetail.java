package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "UNIT_TABLE_MAPPING")
public class UnitTableMappingDetail implements Serializable {

	private static final long serialVersionUID = -7263950073243161899L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "TABLE_REQUEST_ID", unique = true, nullable = false)
	private int tableRequestId;
	@Column(name = "UNIT_ID", nullable = false)
	private int unitId;
	@Column(name = "TABLE_NUMBER", nullable = false)
	private int tableNumber;
	@Column(name = "CUSTOMER_ID", nullable = true)
	private Integer customerId;
	@Column(name = "CONTACT", nullable = true)
	private String contact;
	@Column(name = "CUSTOMER_NAME", nullable = true)
	private String customerName;
	@Column(name = "TOTAL_ORDERS", nullable = false)
	private int totalOrders;
	@Column(name = "TOTAL_AMOUNT", nullable = false)
	private int totalAmount;
	@Column(name = "TABLE_STATUS", nullable = false)
	private String tableStatus;

	@Column(name = "NO_OF_PAX" , nullable = true)
	private Integer noOfPax;

	@Column(name = "CUSTOMER_TYPE" , nullable = true)
	private String customerType;
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "tableRequestId")
	private List<TableOrderMappingDetail> orders;

	@Column(name = "SETTLEMENT_ORDER_ID",nullable = true)
	private Integer settledOrderId;

	@Column(name = "GENERATED_ORDER_ID", unique = true, nullable = false, length = 30)
	private String generatedOrderId;

    @Column(name = "LOYALTEA_TO_BE_REDEEMED")
	private String loyalteaToBeRedeemed;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "TABLE_START_TIME")
	private Date tableStartTime;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ORDER_START_TIME")
	private Date orderStartTime;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "TABLE_SETTLEMENT_TIME")
	private Date tableSettlementTime;

	@Temporal(TemporalType.DATE)
	@Column(name = "BUSINESS_DATE")
	private Date businessDate;

	@Column(name = "TOTAL_ORDER_TIME_IN_MIN")
	private Integer totalOrderTimeInMin;

	@Column(name = "SERVICE_CHARGE_APPLIED")
	private String serviceChargeApplied;

	@Column(name = "BILL_PRINT_COUNT", nullable = false)
	private int billPrintCount;

	@Column(name = "BILL_PRINT_ALLOWED")
	private String billPrintAllowed;

	@Column(name = "OFFER_CODE")
	private String offerCode;

	@Column(name = "LOYALTEA_POINTS_REDEEMED")
	private int pointsRedeemed;

}
