package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "COUPON_DETAIL_DATA")
@Getter
@Setter
public class CouponDetailData implements java.io.Serializable {

	private static final long serialVersionUID = 1232092149957614402L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "COUPON_DETAIL_ID", unique = true, nullable = false)
	private Integer couponDetailId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "OFFER_DETAIL_ID", nullable = false)
	private OfferDetailData offerDetail;
	@Column(name = "COUPON_CODE", nullable = false, length = 8)
	private String couponCode;
	@Column(name = "COUPON_REUSE", nullable = false, length = 1)
	private String couponReuse; // Y/N if reusable or not, default 'N'
	@Column(name = "CUSTOMER_REUSE", nullable = false, length = 1)
	private String customerReuse; // Y/N if reusable or not, default 'N'
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "START_DATE", nullable = false, length = 19)
	private Date startDate;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "END_DATE", nullable = false, length = 19)
	private Date endDate;
	@Column(name = "COUPON_STATUS", nullable = false, length = 15)
	private String couponStatus; // active or not
	@Column(name = "MAX_USAGE", nullable = true)
	private Integer maxUsage; // maximum usage count of the coupon
	@Column(name = "MAX_CUSTOMER_USAGE", nullable = true)
	private Integer maxCustomerUsage; // maximum usage count of the coupon
	@Column(name = "USAGE_COUNT", nullable = false)
	private int usageCount; // usage count till date
	@Column(name = "MANUAL_OVERRIDE", nullable = false)
	private String manualOverride;

	// validation mappings of any kind of data regarding order manipulations
	@OneToMany(mappedBy = "couponDetail")
	private List<CouponDetailMappingData> mappings;

}
