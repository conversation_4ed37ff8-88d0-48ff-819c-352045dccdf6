package com.stpl.tech.kettle.data.kettle;

import com.stpl.tech.kettle.domain.enums.SuperUEventStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;


import java.util.Date;

@Entity
@Table(name = "SUPERU_EVENT_TRACK")
@Getter
@Setter
public class SuperUEventTrack {

     @Id
     @GeneratedValue(strategy = GenerationType.IDENTITY)
     @Column(name = "EVENT_ID")
     Integer eventId;
     @Column(name = "ORDER_ID")
    Integer orderId;
    @Column(name = "EVENT_QPUSH_TIME")
    Date eventQPushTime;
    @Column(name = "EVENT_API_PUSH_TIME")
    Date eventApiPushTime;
    @Column(name = "METADATA" , columnDefinition = "TEXT")
    String metadata;
    @Column(name = "STATUS")
    @Enumerated(EnumType.STRING)
    SuperUEventStatus status;
    @Column(name="API_RESPONSE")
    String apiResponse;
}
