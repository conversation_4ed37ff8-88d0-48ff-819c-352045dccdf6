package com.stpl.tech.kettle.data.master;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "LOCATION_DETAIL")
@Getter
@Setter
public class LocationDetail {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "LOCATION_ID", nullable = false, unique = true)
	private Integer id;

	@Column(name = "CITY", nullable = false, length = 100)
	private String city;

	@Column(name = "CITY_CODE", nullable = false, length = 10)
	private String cityCode;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "STATE_DETAIL_ID", nullable = false)
	private StateDetail state;

	@Column(name = "COUNTRY_CODE", nullable = false, length = 10)
	private String countryCode;

	@Column(name = "LOCATION_STATUS", nullable = false, length = 15)
	private String status;

	@Column(name = "FUNCTIONAL_FLAG", nullable = false, length = 1)
	private String functionalFlag;

}
