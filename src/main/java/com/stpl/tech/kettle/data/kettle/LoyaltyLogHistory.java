package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


import java.util.Date;

import static jakarta.persistence.GenerationType.IDENTITY;

@Table(name = "LOYALTY_LOG_HISTORY")
@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class LoyaltyLogHistory {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "LOYALTY_LOG_HISTORY_ID", unique = true, nullable = false)
    private Integer loyaltyLogId;
    @Column(name = "LOYALTY_EVENT_ID")
    private Integer loyaltyEventId;
    @Column(name = "TRANSACTION_TYPE", nullable = false, length = 10)
    private String transactionType;
    @Column(name = "TRANSACTION_POINTS", nullable = false)
    private Integer transactionPoints;
    @Column(name = "TRANSACTION_STATUS", nullable = false, length = 10)
    private String transactionStatus;
    @Column(name = "TRANSACTION_CODE_TYPE", nullable = false, length = 100)
    private String transactionCodeType;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "TRANSACTION_TIME", nullable = false, length = 19)
    private Date transactionTime;
    @Column(name = "ORDER_ID", nullable = true)
    private Integer orderId;
    @Column(name = "TRANSACTION_EVENT_ID")
    private Integer transactionEventId;



}
