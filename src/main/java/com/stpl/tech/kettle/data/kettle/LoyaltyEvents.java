package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "LOYALTY_EVENTS")
@NoArgsConstructor
@Getter
@Setter
public class LoyaltyEvents implements Serializable {

	private static final long serialVersionUID = -4843141040070866709L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "LOYALTY_EVENTS_ID", unique = true, nullable = false)
	private Integer loyaltyEventsId;
	@Column(name = "CUSTOMER_ID", nullable = false, length = 50)
	private Integer customerId;
	@Column(name = "TRANSACTION_TYPE", nullable = false, length = 10)
	private String transactionType;
	@Column(name = "TRANSACTION_CODE_TYPE", nullable = false, length = 100)
	private String transactionCodeType;
	@Column(name = "TRANSACTION_CODE", nullable = false, length = 500)
	private String transactionCode;
	@Column(name = "TRANSACTION_STATUS", nullable = false, length = 10)
	private String transactionStatus;
	@Column(name = "TRANSACTION_POINTS", nullable = false)
	private Integer transactionPoints;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "TRANSACTION_TIME", nullable = false, length = 19)
	private Date transactionTime;
	@Column(name = "ORDER_ID", nullable = true)
	private Integer orderId;
	@Column(name = "EVENT_ID", nullable = true)
	private Integer eventId;
	@Column(name = "OPENING_BALANCE", nullable = true)
	private Integer openingBalance;
	@Column(name = "CLOSING_BALANCE", nullable = true)
	private Integer closingBalance;
	@Column(name="REASON",nullable = true)
	private String reason;
	@Column(name = "LOYALTY_EVENT_STATUS")
	private String loyaltyEventStatus;
	@Column(name = "REDEEMED_POINTS")
	private Integer redeemedPoints=0;
	@Column(name = "EXPIRED_POINTS")
	private Integer expiredPoints=0;
	@Temporal(TemporalType.DATE)
	@Column(name = "EXPIRATION_TIME")
	private Date expirationTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REDEMPTION_TIME" ,length = 19)
	private Date redemptionTime;

	public LoyaltyEvents(Integer customerId, String transactionType) {
		this.customerId = customerId;
		this.transactionType = transactionType;
	}

	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		result.append("Loyalty Events Id: " + this.loyaltyEventsId);
		result.append(", Customer Id: " + this.customerId);
		result.append(", Transaction Type: " + transactionType);

		result.append(", Transaction Code Type: " + this.transactionCodeType);
		result.append(", Transaction Code: " + this.transactionCode);
		result.append(", Transaction Status: " + this.transactionStatus);
		result.append(", Transaction Points: " + this.transactionPoints);
		result.append(", Transaction Time: " + this.transactionTime);
		result.append(", Order Id: " + this.orderId);
		result.append(" , Reason : " + this.reason);
		result.append(" , LoyaltyEventStatus : "+this.loyaltyEventStatus);
		result.append(" , RedeemedPoints : "+this.redeemedPoints);
		result.append(" , ExpiredPoints : "+this.expiredPoints);
		result.append(" , ExpirationTime : "+this.expirationTime);
		return result.toString();
	}

}
