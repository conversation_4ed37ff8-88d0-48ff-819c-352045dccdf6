
package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "ORDER_ITEM_INVOICE")
@Getter
@Setter
public class OrderItemInvoice implements java.io.Serializable {

	private static final long serialVersionUID = 6328575415693548837L;
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_ITEM_INVOICE_ID", unique = true, nullable = false)
	private Integer orderItemInvoiceId;
	@Column(name = "ORDER_ID", nullable = false)
	private int orderId;
	@Column(name = "STATE_ID", nullable = false)
	private int stateId;
	@Column(name = "STATE_INVOICE_ID", nullable = false)
	private int stateInvoiceId;
	@Column(name = "TOTAL_AMOUNT", nullable = false, precision = 10)
	private BigDecimal totalAmount;
	@Column(name = "TAXABLE_AMOUNT", nullable = false, precision = 10)
	private BigDecimal taxableAmount;
	@Column(name = "TAX_CATEGORY", nullable = true, length = 40)
	private String taxCategory;
	@Column(name = "TOTAL_TAX", precision = 10)
	private BigDecimal taxAmount;
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderItemInvoice")
	private List<OrderItemInvoiceTaxDetail> orderItemInvoiceTaxes = new ArrayList<OrderItemInvoiceTaxDetail>(0);

}
