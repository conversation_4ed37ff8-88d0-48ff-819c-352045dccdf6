package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "OFFER_PARTNERS")
@Getter
@Setter
public class OfferPartner implements java.io.Serializable {

	private static final long serialVersionUID = 4794380425785597396L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "MAPPING_ID", unique = true, nullable = false)
	private int mappingId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PARTNER_ID", nullable = false)
	private MarketingPartner partner;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "OFFER_ID", nullable = false)
	private OfferDetailData offer;

	@Column(name = "STATUS", nullable = false)
	private String status;

}
