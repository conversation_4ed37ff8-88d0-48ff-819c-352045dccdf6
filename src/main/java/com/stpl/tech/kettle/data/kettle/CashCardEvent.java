package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "CASH_CARD_EVENT")
@NoArgsConstructor
@Getter
@Setter
public class CashCardEvent implements Serializable {

	private static final long serialVersionUID = 2905452666197515792L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CASH_CARD_EVENT_ID", unique = true, nullable = false)
	private Integer cashCardEventId;
	@Column(name = "CASH_CARD_ID", nullable = false)
	private int cashCardId;
	@Column(name = "ORDER_ID", nullable = false)
	private int orderId;
	@Column(name = "ORDER_SETTLEMENT_ID", nullable = false)
	private int orderSettlementId;
	@Column(name = "SETTLEMENT_AMOUNT", nullable = false, precision = 10)
	private BigDecimal settlementAmount;
	@Column(name = "SETTLEMENT_STATUS", nullable = false, length = 20)
	private String settlementStatus;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "SETTLEMENT_TIME", nullable = false, length = 19)
	private Date settlementTime;
}
