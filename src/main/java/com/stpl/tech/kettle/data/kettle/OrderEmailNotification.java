package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "ORDER_EMAIL_NOTIFICATION")
@Getter
@Setter
@NoArgsConstructor
public class OrderEmailNotification implements Serializable {

	private static final long serialVersionUID = -7157361822550700720L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ORDER_EMAIL_ID", unique = true, nullable = false)
	private Integer orderEmailId;
	@Column(name = "ENTRY_TYPE", nullable = false, length = 30)
	private String entryType;
	@Column(name = "ORDER_ID", nullable = false)
	private int orderId;
	@Column(name = "RETRY_COUNT", nullable = false)
	private int retryCount;
	@Column(name = "CONTACT", nullable = true, length = 15)
	private String contact;
	@Column(name = "EMAIL_ADDRESS", length = 200)
	private String emailAddress;
	@Column(name = "USER_REQUESTED", nullable = false, length = 1)
	private String userRequested;
	@Column(name = "IS_EMAIL_DELIVERED", length = 1)
	private String isEmailDelivered;
	@Column(name = "IS_EMAIL_VERIFIED", length = 1)
	private String isEmailVerified;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REQUEST_TIME", nullable = false, length = 19)
	private Date requestTime;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EXECUTION_TIME", length = 19)
	private Date executionTime;
	@Column(name = "ERROR_MESSAGE", length = 5000)
	private String errorMessage;
	@Column(name = "CUSTOMER_NAME", nullable = true, length = 45)
	private String customerName;

	public OrderEmailNotification(String entryType, int orderId, int retryCount, String userRequested,
			Date requestTime) {
		this.entryType = entryType;
		this.orderId = orderId;
		this.retryCount = retryCount;
		this.userRequested = userRequested;
		this.requestTime = requestTime;
	}

	public OrderEmailNotification(String entryType, int orderId, int retryCount, String emailAddress,
			String userRequested, String isEmailDeivered, String isEmailVerified, Date requestTime, Date executionTime,
			String errorMessage) {
		this.entryType = entryType;
		this.orderId = orderId;
		this.retryCount = retryCount;
		this.emailAddress = emailAddress;
		this.userRequested = userRequested;
		this.isEmailDelivered = isEmailDeivered;
		this.isEmailVerified = isEmailVerified;
		this.requestTime = requestTime;
		this.executionTime = executionTime;
		this.errorMessage = errorMessage;
	}
}
