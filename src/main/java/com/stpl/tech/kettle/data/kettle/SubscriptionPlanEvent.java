package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "SUBSCRIPTION_PLAN_EVENT")
@Getter
@Setter
public class SubscriptionPlanEvent implements Serializable {

	private static final long serialVersionUID = 8920071463443188445L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SUBSCRIPTION_PLAN_EVENT_ID", unique = true, nullable = false)
	private Integer subscriptionPlanEventId;
	@Column(name = "SUBSCRIPTION_PLAN_ID", nullable = false)
	private Integer subscriptionPlanId;
	@Column(name = "SUBSCRIPTION_PLAN_CODE", nullable = false, length = 200)
	private String subscriptionPlanCode;
	@Column(name = "CUSTOMER_ID", nullable = false)
	private Integer customerId;
	@Column(name = "PLAN_STATUS", nullable = false, length = 15)
	private String status;
	@Temporal(TemporalType.DATE)
	@Column(name = "PLAN_START_DATE", nullable = true, length = 10)
	private Date planStartDate;
	@Temporal(TemporalType.DATE)
	@Column(name = "PLAN_END_DATE", nullable = true, length = 10)
	private Date planEndDate;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "RENEWAL_TIME", nullable = false, length = 19)
	private Date renewalTime;
	@Column(name = "EVENT_TYPE", nullable = false, length = 50)
	private String eventType;
	@Column(name = "PRODUCT_ID", nullable = false)
	private Integer productId;
	@Column(name = "DIMENSION_CODE", nullable = false, length = 100)
	private String dimensionCode;
	@Column(name = "ORDER_ID", nullable = false)
	private Integer orderId;
	@Column(name = "ORDER_ITEM_ID", nullable = false)
	private Integer orderItemId;
	@Column(name = "PRICE", precision = 10)
	private BigDecimal price;
	@Column(name = "VALIDITY_IN_DAYS", nullable = false)
	private Integer validityInDays;
	@Column(name = "CAMPAIGN_ID")
	private Integer campaignId;
	@Column(name = "SUBSCRIPTION_SOURCE")
	private String subscriptionSource;
	@Column(name = "SUBSCRIPTION_SAVINGS")
	private BigDecimal subscriptionSavings;
}
