package com.stpl.tech.kettle.data.master;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "CAMPAIGN_COUPON_MAPPING")
@Getter
@Setter
public class CampaignCouponMapping implements Serializable {

	private static final long serialVersionUID = 5607306977988316577L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CAMPAIGN_COUPON_MAPPING_ID", unique = true, nullable = false)
	private Integer campaignCouponMappingId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CAMPAIGN_ID", nullable = false)
	private CampaignDetailData campaignDetailData;
	@Column(name = "CUSTOMER_TYPE")
	private String customerType;
	@Column(name = "JOURNEY_NUMBER")
	private Integer journeyNumber;
	@Column(name = "CLONE_CODE")
	private String cloneCode;
	@Column(name = "CLONE_CODE_DESC")
	private String cloneCodeDesc;
	@Column(name = "VALIDITY_IN_DAYS")
	private Integer validityInDays;
	@Column(name = "REMINDER_DAYS")
	private Integer reminderDays;

}
