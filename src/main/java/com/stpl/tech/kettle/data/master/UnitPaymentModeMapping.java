package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "UNIT_PAYMENT_MODE_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = { "UNIT_ID",
		"PAYMENT_MODE_ID" }))
@Getter
@Setter
@NoArgsConstructor
public class UnitPaymentModeMapping implements java.io.Serializable {

	private static final long serialVersionUID = 2945074202196636013L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_PAYMENT_MODE_MAPPING_ID", unique = true, nullable = false)
	private Integer unitPaymentModeMappingId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PAYMENT_MODE_ID", nullable = false)
	private PaymentMode paymentMode;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_ID", nullable = false)
	private UnitDetail unitDetail;

	@Column(name = "MAPPING_STATUS", nullable = false, length = 15)
	private String mappingStatus;

	public UnitPaymentModeMapping(PaymentMode paymentMode, UnitDetail unitDetail, String mappingStatus) {
		this.paymentMode = paymentMode;
		this.unitDetail = unitDetail;
		this.mappingStatus = mappingStatus;
	}

}
