package com.stpl.tech.kettle.data.kettle;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "STATE_SEQUENCE_ID")
@AllArgsConstructor
@NoArgsConstructor
public class StateSequenceId implements Serializable {

	private static final long serialVersionUID = 8365083759632810521L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SEQUENCE_ID", unique = true, nullable = false)
	private Integer sequenceId;
	@Column(name = "STATE_ID", nullable = false)
	private int stateId;
	@Column(name = "NEXT_VALUE", nullable = false)
	private int nextValue;

	public StateSequenceId(int stateId, int nextValue) {
		this.stateId = stateId;
		this.nextValue = nextValue;
	}
}
