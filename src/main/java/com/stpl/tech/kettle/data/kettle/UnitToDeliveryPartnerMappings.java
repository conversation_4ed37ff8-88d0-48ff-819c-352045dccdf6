package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.*;

import java.io.Serial;
import java.io.Serializable;

@Entity
@Table(name = "UNIT_TO_DELIVERY_MAPPINGS")
public class UnitToDeliveryPartnerMappings implements Serializable {

    @Serial
    private static final long serialVersionUID = 6889033734357752990L;
    private int id;
    private int unitId;
    private DeliveryPartner deliveryPartner;
    private int priority;

    public UnitToDeliveryPartnerMappings() {
    }

    public UnitToDeliveryPartnerMappings(int unitDetail, DeliveryPartner deliveryPartner, int priority) {
        super();
        this.unitId = unitDetail;
        this.deliveryPartner = deliveryPartner;
        this.priority = priority;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public int getUnitId() {
        return this.unitId;
    }

    public void setUnitId(int unitDetail) {
        this.unitId = unitDetail;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "DELIVERY_PARTNER_ID", nullable = false)
    public DeliveryPartner getDeliveryPartner() {
        return deliveryPartner;
    }

    public void setDeliveryPartner(DeliveryPartner deliveryPartner) {
        this.deliveryPartner = deliveryPartner;
    }

    @Column(name = "PRIORITY")
    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    @Override
    public String toString() {
        return "UnitToDeliveryPartnerMappings [productId=" + id + ", unitId=" + unitId + ", deliveryPartnerId="
                + deliveryPartner.getPartnerId() + ", priority=" + priority + "]";
    }
}
