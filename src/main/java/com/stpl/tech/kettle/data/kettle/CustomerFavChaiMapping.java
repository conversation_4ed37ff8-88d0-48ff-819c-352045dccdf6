package com.stpl.tech.kettle.data.kettle;



import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.xml.bind.annotation.XmlType;

import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CustomerFavChaiMapping", propOrder = {"customerId", "productId", "productName", "status", "dimension", "creationTime", "lastUpdatedTime", "consumeType", "tagType"})
@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "CUSTOMER_FAV_CHAI_MAPPING")
public class CustomerFavChaiMapping {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CUSTOMIZATION_ID", unique = true, nullable = false)
    private Integer customizationId;
    @Column(name = "CUSTOMER_ID", nullable = false)
    private int customerId;
    @Column(name = "PRODUCT_ID", nullable = false)
    private int productId;
    @Column(name = "PRODUCT_NAME", nullable = false)
    private String productName;
    @Column(name = "STATUS", nullable = false)
    private String status;
    @Column(name = "DIMENSION", length = 10)
    private String dimension;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME", nullable = false, length = 19)
    private Date creationTime;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATED_TIME", nullable = true, length = 19)
    private Date lastUpdatedTime;
    @Temporal(TemporalType.DATE)
    @Column(name = "CREATED_AT", nullable = false)
    private Date createdAt;
    @Column(name = "CONSUME_TYPE")
    private String consumeType;
    @Column(name = "TAG_TYPE")
    private String tagType;
    @Column(name = "IS_UPDATED")
    private String isUpdated;
    @Column(name = "SOURCE_ID")// pos- 1
    private Integer sourceId;
    @Column(name = "SOURCE_NAME") //For now CAFE
    private String sourceName;
    @Column(name = "RECIPE_ID", nullable = true)
    private Integer recipeId;
    @Column(name = "RECIPE_PROFILE", nullable = true)
    private String recipeProfile;
    @Column(name = "QUANTITY", nullable = false)
    private int quantity;
    @Column(name = "PRODUCT_SHORT_CODE")
    private String shortCode;
    @Column(name = "PRICE")
    private BigDecimal price;
    @Column(name = "TOTAL_ORDER_COUNT")
    private Integer totalOrderCount;

}
