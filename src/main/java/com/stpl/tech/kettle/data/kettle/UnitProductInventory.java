package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "UNIT_PRODUCT_INVENTORY")
@Getter
@Setter
public class UnitProductInventory implements Serializable {

	private static final long serialVersionUID = 3113411875056633730L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_PRODUCT_INVENTORY_ID", unique = true, nullable = false)
	private Integer unitProductInventoryId;
	@Column(name = "UNIT_ID", nullable = false)
	private int unitId;
	@Column(name = "PRODUCT_ID", nullable = false)
	private int productId;
	@Column(name = "NO_OF_UNIT", nullable = false)
	private int noOfUnits;
	@Column(name = "PRODUCT_INVENTORY_STATUS", nullable = false, length = 15)
	private String productInventoryStatus;
	@Column(name = "LAST_UPDATE_TMSTMP", nullable = false, length = 19)
	private Date lastUpdateTmstmp;
	@Column(name = "LAST_STOCK_OUT_TIME", nullable = true, length = 19)
	private Date lastStockOutTime;
	@Column(name = "EXPIRE_QUANTITY", nullable = false, length = 15)
	private int expireQuantity;

}
