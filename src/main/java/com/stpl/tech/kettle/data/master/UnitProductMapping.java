package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "UNIT_PRODUCT_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = { "UNIT_ID", "PRODUCT_ID" }))
@NoArgsConstructor
public class UnitProductMapping implements java.io.Serializable {

	
	private static final long serialVersionUID = -2312608502651655936L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_PROD_REF_ID", unique = true, nullable = false)
	private Integer unitProdRefId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PRODUCT_ID", nullable = false)
	private ProductDetail productDetail;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_ID", nullable = false)
	private UnitDetail unitDetail;

	@Column(name = "PRODUCT_STATUS", nullable = false, length = 15)
	private String productStatus;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TMSTMP", nullable = false, length = 19)
	private Date lastUpdateTmstmp;

	@Temporal(TemporalType.DATE)
	@Column(name = "PRODUCT_START_DATE", nullable = false, length = 10)
	private Date productStartDate;

	@Temporal(TemporalType.DATE)
	@Column(name = "PRODUCT_END_DATE", nullable = false, length = 10)
	private Date productEndDate;

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitProductMapping")
	private List<UnitProductPricing> unitProductPricings = new ArrayList<>(0);

	public UnitProductMapping(ProductDetail productDetail, UnitDetail unitDetail, String productStatus,
			Date lastUpdateTmstmp, Date productStartDate, Date productEndDate) {
		this.productDetail = productDetail;
		this.unitDetail = unitDetail;
		this.productStatus = productStatus;
		this.lastUpdateTmstmp = lastUpdateTmstmp;
		this.productStartDate = productStartDate;
		this.productEndDate = productEndDate;
	}

	public UnitProductMapping(ProductDetail productDetail, UnitDetail unitDetail, String productStatus,
			Date lastUpdateTmstmp, Date productStartDate, Date productEndDate,
			List<UnitProductPricing> unitProductPricings) {
		this.productDetail = productDetail;
		this.unitDetail = unitDetail;
		this.productStatus = productStatus;
		this.lastUpdateTmstmp = lastUpdateTmstmp;
		this.productStartDate = productStartDate;
		this.productEndDate = productEndDate;
		this.unitProductPricings = unitProductPricings;
	}

	public Integer getUnitProdRefId() {
		return unitProdRefId;
	}

	public void setUnitProdRefId(Integer unitProdRefId) {
		this.unitProdRefId = unitProdRefId;
	}

	public ProductDetail getProductDetail() {
		return productDetail;
	}

	public void setProductDetail(ProductDetail productDetail) {
		this.productDetail = productDetail;
	}

	public UnitDetail getUnitDetail() {
		return unitDetail;
	}

	public void setUnitDetail(UnitDetail unitDetail) {
		this.unitDetail = unitDetail;
	}

	public String getProductStatus() {
		return productStatus;
	}

	public void setProductStatus(String productStatus) {
		this.productStatus = productStatus;
	}

	public Date getLastUpdateTmstmp() {
		return lastUpdateTmstmp;
	}

	public void setLastUpdateTmstmp(Date lastUpdateTmstmp) {
		this.lastUpdateTmstmp = lastUpdateTmstmp;
	}

	public Date getProductStartDate() {
		return productStartDate;
	}

	public void setProductStartDate(Date productStartDate) {
		this.productStartDate = productStartDate;
	}

	public Date getProductEndDate() {
		return productEndDate;
	}

	public void setProductEndDate(Date productEndDate) {
		this.productEndDate = productEndDate;
	}
	@OrderBy("refLookup.rlId")
	public List<UnitProductPricing> getUnitProductPricings() {
		return unitProductPricings;
	}

	public void setUnitProductPricings(List<UnitProductPricing> unitProductPricings) {
		this.unitProductPricings = unitProductPricings;
	}

	
}
