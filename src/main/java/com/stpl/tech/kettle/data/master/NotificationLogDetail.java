package com.stpl.tech.kettle.data.master;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "NOTIFICATION_LOG_DETAIL")
public class NotificationLogDetail implements Serializable {

	private static final long serialVersionUID = 8392148642053770010L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "NOTIFICATION_ID", unique = true, nullable = false)
	public int notificationId;

	@Column(name = "CONTACT", nullable = true)
	public String contact;

	@Column(name = "MESSAGE", nullable = true)
	public String message;

	@Column(name = "SERVICE_CLIENT", nullable = true)
	public String serviceClient;

	@Column(name = "TYPE", nullable = true)
	public String type;

	@Column(name = "NOTIFICATION_SENT", nullable = true)
	public String notificationSent;

	@Column(name = "NOTIFICATION_TIME", nullable = true)
	public Date notificationTime;
}