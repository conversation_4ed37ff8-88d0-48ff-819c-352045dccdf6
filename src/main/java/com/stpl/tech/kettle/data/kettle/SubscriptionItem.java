
package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

@Entity
@Table(name = "SUBSCRIPTION_ITEM")
public class SubscriptionItem implements java.io.Serializable {

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SUBSCRIPTION_ITEM_ID", unique = true, nullable = false)
	private Integer subscriptionItemId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBSCRIPTION_ID", nullable = false)
	private SubscriptionDetail subscriptionDetail;
	@Column(name = "PRODUCT_ID", nullable = false)
	private int productId;
	@Column(name = "PRODUCT_NAME", nullable = false)
	private String productName;
	@Column(name = "QUANTITY", nullable = false)
	private int quantity;
	@Column(name = "PRICE", nullable = false, precision = 10)
	private BigDecimal price;
	@Column(name = "HAS_ADDON", nullable = false, length = 1)
	private String hasAddon;
	@Column(name = "PARENT_ITEM_ID")
	private Integer parentItemId;
	@Column(name = "COMBO_CONSTITUENT", length = 1)
	private String comboConstituent;
	@Column(name = "TOTAL_AMOUNT", nullable = false, precision = 10)
	private BigDecimal totalAmount;
	@Column(name = "AMOUNT_PAID", nullable = false, precision = 10)
	private BigDecimal paidAmount;
	@Column(name = "DISCOUNT_PERCENT", precision = 10)
	private BigDecimal discountPercent;
	@Column(name = "DISCOUNT_AMOUNT", precision = 10)
	private BigDecimal discountAmount;
	@Column(name = "TOTAL_TAX", precision = 10)
	private BigDecimal taxAmount;
	@Column(name = "DISCOUNT_REASON_ID")
	private Integer discountReasonId;
	@Column(name = "DISCOUNT_REASON")
	private String discountReason;
	@Column(name = "DIMENSION", length = 10)
	private String dimension;
	@Column(name = "BILL_TYPE", nullable = false, length = 10)
	private String billType;
	@Column(name = "TAX_CODE", nullable = true, length = 40)
	private String taxCode;
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "subscriptionItem")
	private List<SubscriptionItemAddon> subscriptionItemAddons = new ArrayList<SubscriptionItemAddon>(0);
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "subscriptionItem")
	private List<SubscriptionItemTaxDetail> subscriptionItemTaxes = new ArrayList<SubscriptionItemTaxDetail>(0);
	@Column(name = "TAKE_AWAY", length = 1)
	private String takeAway;
}
