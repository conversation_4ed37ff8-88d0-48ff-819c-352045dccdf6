package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "ORDER_METRIC_DETAIL_DATA")
@Getter
@Setter
public class OrderMetricDetailData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "METRIC_ID", unique = true, nullable = false)
    private Integer metricId;
    @Column(name = "ORDER_ID", nullable = false)
    private Integer orderId;
    @Column(name = "BILLING_SERVER_TIME", nullable = false)
    private Date billingServerTime;
    @Column(name = "POS_VERSION")
    private String posVersion;
    @Column(name = "CAFE_APP_VERSION")
    private String cafeAppVersion;
    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;
    @Column(name = "TERMINAL_ID", nullable = false)
    private Integer terminalId;
    @Column(name = "TAXABLE_AMOUNT", nullable = false)
    private BigDecimal taxableAmount;
    @Column(name = "TOTAL_AMOUNT", nullable = false)
    private BigDecimal totalAmount;
    @Column(name = "SETTLED_AMOUNT",nullable = false)
    private BigDecimal settledAmount;
    @Column(name = "ORDER_START_TIME")
    private Date orderStartTime;
    @Column(name = "ORDER_END_TIME")
    private Date orderEndTime;
    @Column(name = "LOGIN_START_TIME")
    private Date loginStartTime;
    @Column(name = "LOGIN_END_TIME")
    private Date loginEndTime;
    @Column(name = "FACE_IT_LOGIN", nullable = false)
    private String loginViaFaceIt;
    @Column(name = "OTP_REQUESTED", nullable = false)
    private String otpRequested;
    @Column(name = "OTP_DELIVERED_TIME")
    private Integer otpDeliveredTime;
    @Column(name = "CUSTOMER_ID", nullable = false)
    private Integer customerId;
    @Column(name = "NEW_CUSTOMER")
    private String newCustomer;
    @Column(name = "VALID_CUSTOMER", nullable = false)
    private String validCustomer;
    @Column(name = "CUSTOMER_ORDER_NUMBER")
    private Integer customerOrderNumber;
    @Column(name = "RECOMMENDED_PRODUCT_ADDED")
    private String recommendedProductAdded;
    @Column(name = "CUSTOMIZATION_DONE", nullable = false)
    private String customizationDone;
    @Column(name = "CUSTOMIZATION_REQUIRED", nullable = false)
    private String customizationRequired;
    @Column(name = "CUSTOMIZED_PRODUCT_ADDED", nullable = false)
    private String customizedProductAdded;
    @Column(name = "ADDED_VIA_CUSTOMIZATION", nullable = false)
    private String addedViaCustomization;
    @Column(name = "CUSTOMIZATION_START_TIME")
    private Date customizationStartTime;
    @Column(name = "CUSTOMIZATION_END_TIME")
    private Date customizationEndTime;
    @Column(name = "WALLET_SUGGESTED", nullable = false)
    private String walletSuggested;
    @Column(name = "WALLET_PURCHASED", nullable = false)
    private String walletPurchased;
    @Column(name = "WALLET_REDEEMED", nullable = false)
    private String walletRedeemed;
    @Column(name = "WALLET_START_TIME")
    private Date walletSuggestionStartTime;
    @Column(name = "WALLET_END_TIME")
    private Date walletSuggestionEndTime;
    @Column(name = "LOYALTY_REDEEMED", nullable = false)
    private String loyaltyRedeemed;
    @Column(name = "LOYALTY_REDEMPTION_DISPLAYED", nullable = false)
    private String loyaltyRedemptionDisplayed;
    @Column(name = "AWARDED_LOYALTY_POINTS",columnDefinition = "integer default 0")
    private Integer awardedLoyaltyPoints;
    @Column(name = "REDEEMED_LOYALTY_POINTS",columnDefinition = "integer default 0")
    private Integer redeemedLoyaltyPoints;
    @Column(name = "SECOND_FREE_CHAI_REDEEMED", nullable = false)
    private String secondFreeChaiRedeemed;
    @Column(name = "SECOND_FREE_CHAI_AVAILABLE", nullable = false)
    private String secondFreeChaiAvailable;
    @Column(name = "MEMBERSHIP_SUGGESTED", nullable = false)
    private String membershipSuggested;
    @Column(name = "MEMBERSHIP_PURCHASED", nullable = false)
    private String membershipPurchased;
    @Column(name = "MEMBERSHIP_USED", nullable = false)
    private String membershipUsed;
    @Column(name = "MEMBERSHIP_TYPE")
    private String membershipType;
    @Column(name = "PAYMENT_START_TIME")
    private Date paymentStartTime;
    @Column(name = "PAYMENT_END_TIME")
    private Date paymentEndTime;
    @Column(name = "EDC_PAYMENT", nullable = false)
    private String isEdcPayment;
    @Column(name = "PREVIOUS_ORDER_ID")
    private Integer previousOrderId;
    @Column(name = "PREVIOUS_ORDER_BUSINESS_DATE")
    private Date previousOrderBusinessDate;
    @Column(name = "PREVIOUS_ORDER_UNIT_ID")
    private Integer previousOrderUnitId;
    @Column(name = "SAVED_CHAI_ADDED", nullable = false)
    private String savedChaiAdded;
    @Column(name = "PREVIOUS_ORDER_ADDED", nullable = false)
    private String previousOrderProductAdded;
    @Column(name = "OFFER_AVAILABLE", nullable = false)
    private String offerAvailable;
    @Column(name = "OFFER_AVAILED", nullable = false)
    private String offerAvailed;
    @Column(name = "ORDER_SOURCE", nullable = false)
    private String orderSource;
    @Column(name = "EXPLORE_MORE_ADDED", nullable = true)
    private String exploreMoreAdded;
    @Column(name = "NEW_CHAI_SAVED", nullable = true)
    private String newChaiSaved;
    @Column(name = "FREE_PRODUCT_INFORMED", nullable = true)
    private String freeProductProgramInformed;
    @Column(name = "RECORDING_ID", nullable = true)
    private String recordingId;
}
