package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

import static jakarta.persistence.GenerationType.IDENTITY;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "INVENTORY_UPDATE_DATA")
public class InventoryUpdateData implements java.io.Serializable {
    @Id
    @GeneratedValue(strategy = IDENTITY)

    @Column(name = "INVENTORY_UPDATE_DATA_ID", unique = true, nullable = false)
    public Integer inventoryUpdateDataId;

    @Column(name = "UNIT_ID", nullable = false)
    public int unitId;

    @Column(name = "QUANTITY", nullable = false)
    private int quantity;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ADD_TIME", nullable = false, length = 19)
    private Date addTime;

    @Column(name = "PRODUCT_ID", nullable = false)
    private int productId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "BUSINESS_DATE", nullable = false, length = 19)
    private Date businessDate;

    @Column(name = "INVENTORY_UPDATE_EVENT_ID", nullable = false)
    private int  inventoryUpdateEventId;

    @Column(name = "THRESHOLD_QUANTITY", nullable = false)
    private int thresholdQuantity;

    @Column(name = "EXPIRE_QUANTITY", nullable = false)
    private int expireQuantity;

}
