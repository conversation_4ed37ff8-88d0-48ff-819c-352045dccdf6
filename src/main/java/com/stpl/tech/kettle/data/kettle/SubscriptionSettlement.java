
package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "SUBSCRIPTION_SETTLEMENT")
@Getter
@Setter
@NoArgsConstructor
public class SubscriptionSettlement implements java.io.Serializable {

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SUBSCRIPTION_SETTLEMENT_ID", unique = true, nullable = false)
	private Integer subscriptionSettlementId;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBSCRIPTION_ID", nullable = false)
	private SubscriptionDetail subscriptionDetail;
	@Column(name = "PAYMENT_MODE_ID", nullable = false)
	private int paymentModeId;
	@Column(name = "AMOUNT_PAID", precision = 10)
	private BigDecimal amountPaid;
	@Column(name = "ROUND_OFF_AMOUNT", precision = 10)
	private BigDecimal roundOffAmount;

	public SubscriptionSettlement(SubscriptionDetail orderDetail, int paymentModeId) {
		this.subscriptionDetail = orderDetail;
		this.paymentModeId = paymentModeId;
	}

	public SubscriptionSettlement(SubscriptionDetail orderDetail, int paymentModeId, BigDecimal amountPaid,
			BigDecimal roundOffAmount) {
		this.subscriptionDetail = orderDetail;
		this.paymentModeId = paymentModeId;
		this.amountPaid = amountPaid;
		this.roundOffAmount = roundOffAmount;
	}

}
