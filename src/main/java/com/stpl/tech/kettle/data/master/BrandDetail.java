/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.master;


import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import static jakarta.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "BRAND_DETAIL")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BrandDetail implements java.io.Serializable {

    private static final long serialVersionUID = -9133952863692186923L;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "BRAND_ID", unique = true, nullable = false)
    private Integer brandId;
    @Column(name = "BRAND_NAME", nullable = false)
    private String brandName;
    @Column(name = "BRAND_CODE", nullable = false)
    private String brandCode;
    @Column(name = "BRAND_TAGLINE", nullable = false)
    private String tagLine;
    @Column(name = "BRAND_DOMAIN", nullable = false)
    private String domain;
    @Column(name = "BRAND_BILL_TAGLINE", nullable = false)
    private String billTag;
    @Column(name = "BRAND_WEBSITE_LINK", nullable = false)
    private String websiteLink;
    @Column(name = "BRAND_STATUS", nullable = false)
    private String status;
    @Column(name = "BRAND_SUPPORT_CONTACT", nullable = false)
    private String supportContact;
    @Column(name = "BRAND_SUPPORT_EMAIL", nullable = false)
    private String supportEmail;
    @Column(name = "BRAND_VERBIAGE", nullable = false)
    private String verbiage;
    @Column(name = "BRAND_CONTACT_CODE")
    private String brandContactCode;


}