package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


@Entity
@Table(name = "ORDER_ITEM_METADATA_DETAIL")
@Getter
@Setter
public class OrderItemMetaDataDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "METADATA_ID", unique = true, nullable = false)
    private int metadataId;

    @Column(name = "ORDER_ITEM_ID", nullable = false)
    private int orderItemId;

    @Column(name = "ORDER_ID", nullable = false)
    private  Integer orderId;
    @Column(name = "CUSTOMER_ID", nullable = true)
    private Integer customerId;
    @Column(name = "ADDED_BY", nullable = false)
    private String addedBy;
    @Column(name = "IS_RECOMMENDED", nullable = true)
    private String isRecommended;

    @Column(name = "ITEM_NAME" , nullable = true)
    private String itemName;

    @Column(name = "IS_SAVED_CHAI")
    private String isSavedChai;

    @Column(name = "SAVED_CHAI_ID")
    private Integer savedChaiId;

    @Column(name = "IS_EXPLORE_MORE", nullable = true)
    private String exploreMore;

    @Column(name = "SPECIAL_MILK_VARIANT")
    private Integer specialMilkVariant;

    @Column(name = "CART_RULE_ID")
    private Integer recomRuleId;
}
