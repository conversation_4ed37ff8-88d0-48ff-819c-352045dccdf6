package com.stpl.tech.kettle.data.kettle;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "CASH_DATA")
@Getter
@Setter
@NoArgsConstructor
public class CashData {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CASH_DATA_ID", unique = true, nullable = false)
	private Integer cashDataId;

	@Column(name = "CUSTOMER_ID", nullable = false)
	private Integer customerId;

	@Column(name = "ACCUMULATED_AMOUNT", nullable = false)
	private BigDecimal accumulatedAmount;

	@Column(name = "CURRENT_AMOUNT", nullable = false)
	private BigDecimal currentAmount;

	@Column(name = "REDEEMED_AMOUNT", nullable = false)
	private BigDecimal redeemedAmount;

	@Column(name = "EXPIRED_AMOUNT", nullable = false)
	private BigDecimal expiredAmount;

	@Column(name = "RETAINED_AMOUNT", nullable = false)
	private BigDecimal retainedAmount;

	@Column(name = "REFERRAL_CODE")
	private String referralCode;

	@Column(name = "CREATION_TIME", nullable = false)
	private Date creationTime;

	@Column(name = "LAST_UPDATE_TIME", nullable = false)
	private Date lastUpdateTime;

}