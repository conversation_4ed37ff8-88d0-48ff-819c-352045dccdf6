package com.stpl.tech.kettle.data.master;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "OFFER_ACCOUNT_CATEGORY")
@Getter
@Setter
public class OfferAccountCategory {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CATEGORY_ID", unique = true, nullable = false)
	private int id;

	@Column(name = "CATEGORY_NAME", unique = true, nullable = false)
	private String name;

	@Column(name = "CATEGORY_DESCRIPTION", nullable = false)
	private String description;

	@Column(name = "CATEGORY_STATUS", nullable = false)
	private String status;

	@Column(name = "BUDGET_CATEGORY", nullable = true)
	private String budgetCategory;

}
