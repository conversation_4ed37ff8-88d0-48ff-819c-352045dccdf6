package com.stpl.tech.kettle.data.kettle;

import static jakarta.persistence.GenerationType.IDENTITY;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "ORDER_INVOICE_DETAIL")
@Getter
@Setter
public class OrderInvoiceDetail {

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_INVOICE_DETAIL_ID", unique = true, nullable = false)
	private Integer orderInvoiceDetailId;
	@Column(name = "ORDER_ID", unique = true, nullable = false, length = 100)
	private String orderId;
	@Column(name = "GST_IN", unique = true, nullable = false, length = 50)
	private String gstIn;
	@Column(name = "COMPANY_NAME", unique = true, nullable = false, length = 255)
	private String companyName;
	@Column(name = "COMPANY_ADDRESS", unique = true, nullable = false, length = 255)
	private String companyAddress;
	@Column(name = "GENERATED_BY", unique = true, nullable = false, length = 50)
	private String generatedBy;
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "GENERATION_TIME", nullable = false)
	private Date generationTime;
	@Column(name = "STATE_CODE", nullable = false)
	private String stateCode;
	@Column(name = "TAX_TYPE", nullable = false)
	private String taxType;

}
