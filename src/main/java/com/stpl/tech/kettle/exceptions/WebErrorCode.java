package com.stpl.tech.kettle.exceptions;

public enum WebErrorCode {

	CUSTOMER_NOT_FOUND(101, "Customer Login Required"), PRODUCT_NOT_FOUND(102, "Mising Required Product"),
	INVALID_UNIT(103, "Not Available at Unit"), INVALID_REGION(104, "Not Available in Region"),
	INVALID_SOURCE(105, "Not Available for current order source"), NOT_AVAILABLE(106, "Not Available"),
	INVALID_COUPON(107, "Coupon Not Found"), EXPIRED(108, "Expired"), INSUFFICIENT_DATA(109, "Insufficient Data"),
	COUPON_APPLICABLE_FIRST_ORDER(110, "Offer applicable on first order only."),
	MISMATCH_QUANTITY(111, "Mismatch Quantity"), MINIMUM_ORDER_VALUE(112, "Required Minimum Order Value For Offer"),
	DATA_NOT_FOUND(113, "Data Not Found Exception"), NO_CHAAYOS_CASH_AVAILABLE(113, "No Chaayos Cash Available"),
	MAX_LIMIT_REACHED(114, "Maximum Limit Reached"),
	UNAVAILABLE_MEMNBERSHIP(115, "Unavailable Membership"),

	FREEBIE_PRODUCT_NOT_FOUND(116,"Freebie product not Found"),
    COUPON_REUSABILITY_FOR_CUSTOMER_FAILED(123,"Customer has already availed the Coupon, This Coupon cannot be used again."),
    COUPON_REUSABILITY_FAILED(122,"This Coupon cannot be used again."),


    OFFER_PRODUCT_NOT_FOUND(117,"Offer product not found"),
	WALLET_RECOMMENDATION_FAILED(118,"Wallet recommendation failed");

	private int code;
	private String reason;

	WebErrorCode(int code, String reason) {
		this.code = code;
		this.reason = reason;
	}

	public int getCode() {
		return code;
	}

	public String getReason() {
		return reason;
	}
}
