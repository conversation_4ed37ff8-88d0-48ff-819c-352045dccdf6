package com.stpl.tech.kettle.exceptions;


import com.stpl.tech.kettle.exceptions.models.BaseError;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Getter
@NoArgsConstructor
@Builder
@ResponseStatus(value = HttpStatus.UNAUTHORIZED, reason = "Unauthorised user")
public class AuthException extends RuntimeException implements Serializable {

    @Serial
    private static final long serialVersionUID = 3048224066690651041L;

    private BaseError error;

    private final String baseTitle = "Unauthorised user";

    private final Integer baseCode = 401;

    public AuthException(String message) {
        super(message);
        this.error = new BaseError(baseCode, baseTitle, message);
    }

    public AuthException(String title, String message) {
        super(message);
        this.error = new BaseError(401, title, message);
    }

    public AuthException(BaseError error) {
        super(error.getMessage());
        if (Objects.isNull(error.getCode())) {
            error.setCode(baseCode);
        }
        if (Objects.isNull(error.getTitle())) {
            error.setTitle(baseTitle);
        }
        this.error = error;
    }

    public AuthException(Throwable cause) {
        super(cause);
    }

    public AuthException(String message, Throwable cause) {
        super(message, cause);
    }

    public AuthException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
