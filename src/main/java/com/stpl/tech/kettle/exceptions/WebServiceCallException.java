package com.stpl.tech.kettle.exceptions;


import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serial;

@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR, reason = "Web service call exception")
public class WebServiceCallException extends Exception {


    @Serial
    private static final long serialVersionUID = 7652617609980262850L;

    public WebServiceCallException() {
    }

    public WebServiceCallException(String message) {
        super(message);
    }

    public WebServiceCallException(Throwable cause) {
        super(cause);
    }

    public WebServiceCallException(String message, Throwable cause) {
        super(message, cause);
    }

    public WebServiceCallException(String message, Throwable cause, boolean enableSuppression,
                                   boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
