package com.stpl.tech.kettle.exceptions;

public class OfferValidationException extends Exception{

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private WebErrorCode errorCode;

    public OfferValidationException() {
    }

    public OfferValidationException(String message, WebErrorCode errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public OfferValidationException(String message) {
        super(message);
    }

    public OfferValidationException(Throwable cause) {
        super(cause);
    }

    public OfferValidationException(String message, Throwable cause) {
        super(message, cause);
    }

    public OfferValidationException(String message, Throwable cause, boolean enableSuppression,
                                    boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public WebErrorCode getErrorCode() {
        return errorCode;
    }
}
