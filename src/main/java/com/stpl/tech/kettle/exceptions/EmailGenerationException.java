package com.stpl.tech.kettle.exceptions;

public class EmailGenerationException extends Exception {

	public EmailGenerationException() {
	}

	public EmailGenerationException(String message) {
		super(message);
	}

	public EmailGenerationException(Throwable cause) {
		super(cause);
	}

	public EmailGenerationException(String message, Throwable cause) {
		super(message, cause);
	}

	public EmailGenerationException(String message, Throwable cause, boolean enableSuppression,
			boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}

}
