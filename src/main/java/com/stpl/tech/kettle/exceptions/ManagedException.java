/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * 
 */
package com.stpl.tech.kettle.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 *
 */
@ResponseStatus(value = HttpStatus.OK, reason = "Managed Exception")
public class ManagedException extends Exception {

	/**
	 *
	 */
	private static final long serialVersionUID = 616315625637405710L;

	private MasterError code;

	public ManagedException() {
	}

	public ManagedException(String message) {
		super(message);
		this.code = new MasterError("", message, WebErrorCode.NOT_AVAILABLE.getCode());
	}

	public ManagedException(String title, String message) {
		super(message);
		this.code = new MasterError(title, message, WebErrorCode.NOT_AVAILABLE.getCode());
	}

	public MasterError getCode() {
		return code;
	}

	public ManagedException(Throwable cause) {
		super(cause);
	}

	public ManagedException(String message, Throwable cause) {
		super(message, cause);
	}

	public ManagedException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}
}
