package com.stpl.tech.kettle.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.NOT_ACCEPTABLE, reason = "SMS Client Allocation Exception")
public class SMSClientAllocationException extends Throwable {
    public SMSClientAllocationException() {
    }

    public SMSClientAllocationException(String message) {
        super(message);
    }

    public SMSClientAllocationException(Throwable cause) {
        super(cause);
    }

    public SMSClientAllocationException(String message, Throwable cause) {
        super(message, cause);
    }

    public SMSClientAllocationException(String message, Throwable cause, boolean enableSuppression,
                                        boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }


}
