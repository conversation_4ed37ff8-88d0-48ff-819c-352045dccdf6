package com.stpl.tech.kettle.exceptions;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class DataNotFoundInHazelCastException extends RuntimeException{

    private String message;
    public DataNotFoundInHazelCastException(String message){
        super(message+" (datasource : HazelCast)");
        this.message=message+" (datasource : HazelCast)";
    }
}
