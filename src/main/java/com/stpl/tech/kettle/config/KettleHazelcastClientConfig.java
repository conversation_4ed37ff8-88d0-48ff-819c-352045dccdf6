package com.stpl.tech.kettle.config;

import com.hazelcast.client.HazelcastClient;
import com.hazelcast.client.config.ClientConfig;
import com.hazelcast.config.NearCacheConfig;
import com.hazelcast.core.HazelcastInstance;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@ConditionalOnProperty(value = "is.kettle.client.node", havingValue = "true", matchIfMissing = false)
@Configuration
public class KettleHazelcastClientConfig {

    @Value("${kettle.client.node.ip.details}")
    private String kettleServerNodeIps;

    @Bean(name = "KettleHazelCastSessionConfig")
    public ClientConfig clientKettleConfig() {
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.setClusterName("KettleHazelCastCacheCluster");
        NearCacheConfig nearCacheConfig = new NearCacheConfig("NearKettleHazelCastCache");
        nearCacheConfig.setMaxIdleSeconds(60);
        nearCacheConfig.setInvalidateOnChange(true);
        clientConfig.addNearCacheConfig(nearCacheConfig);
        clientConfig.setInstanceName("KettleHazelCastInstance");
        String ips[] = kettleServerNodeIps.split(",");
        clientConfig.getNetworkConfig().addAddress(ips);
        return clientConfig;
    }

    @Bean(name = "KettleHazelCastInstance")
    public HazelcastInstance hazelcastKettleInstance() {
        return HazelcastClient.newHazelcastClient(clientKettleConfig());
    }
}
