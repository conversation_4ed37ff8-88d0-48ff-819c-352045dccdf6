package com.stpl.tech.kettle.config;

import com.hazelcast.config.Config;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@ConditionalOnProperty(value = "is.kettle.client.node", havingValue = "false", matchIfMissing = false)
@Configuration
public class KettleHazelcastClusterConfig {
    @Value("${kettle.cluster.node.ip.details}")
    private String KettleClusterIps;

    @Bean(name = "KettleHazelCastSessionConfig")
    public Config config(String instanceName) {
        Config config = new Config();
        config.setClusterName("KettleHazelCastCacheCluster");
        config.getNetworkConfig().getJoin().getMulticastConfig().setEnabled(false);
        config.setProperty("hazelcast.logging.type", "log4j2");
        config.setProperty("hazelcast.health.monitoring.level", "OFF");
        config.setInstanceName(instanceName);
        String ips[] = KettleClusterIps.split(",");
        for(String ip : ips){
            String address = ip.split(":")[0];
            Integer port = Integer.valueOf(ip.split(":")[1]);
            config.getNetworkConfig().setPort(port);
            config.getNetworkConfig().getJoin().getTcpIpConfig().setEnabled(true).addMember(address);
        }
        return config;
    }

    @Bean(name = "KettleHazelCastInstance")
    public HazelcastInstance hazelcastInstance() {
        return Hazelcast.getOrCreateHazelcastInstance(config("KettleHazelCastInstance"));
    }
}
