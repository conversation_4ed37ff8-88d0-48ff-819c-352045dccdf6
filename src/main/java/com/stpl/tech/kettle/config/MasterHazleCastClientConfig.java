package com.stpl.tech.kettle.config;

import com.hazelcast.client.HazelcastClient;
import com.hazelcast.client.config.ClientConfig;
import com.hazelcast.config.NearCacheConfig;
import com.hazelcast.core.HazelcastInstance;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@ConditionalOnProperty(value = "is.client.node", havingValue = "true", matchIfMissing = false)
@Configuration
public class MasterHazleCastClientConfig {

	@Value("${client.node.ip.details}")
	private String serverNodeIps;

	public MasterHazleCastClientConfig(){super();}


	@Bean(name = "MasterHazelCastClientSessionConfig")
	public ClientConfig clientMasterConfig() {
		ClientConfig clientConfig = new ClientConfig();
		clientConfig.getNetworkConfig().setSmartRouting(true);
		clientConfig.setClusterName("MasterHazelCastCacheCluster");
		NearCacheConfig nearCacheConfig = new NearCacheConfig("NearHazelCastCache");
		nearCacheConfig.setMaxIdleSeconds(60);
		nearCacheConfig.setInvalidateOnChange(true);
		clientConfig.addNearCacheConfig(nearCacheConfig);
		clientConfig.setInstanceName("MasterDataCache");
		String ips[] = serverNodeIps.split(",");
		clientConfig.getNetworkConfig().addAddress(ips);
		return clientConfig;
	}


	@Bean(name = "MasterHazelCastInstance")
	public HazelcastInstance hazelcastMasterInstance() {
		return HazelcastClient.newHazelcastClient(clientMasterConfig());
	}





}
