package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.CustomJsonDateDeserializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MetricDefinition {

    private boolean suggested;
    private boolean redeemed;
    private boolean purchased;
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date startTime;
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date endTime;
    private boolean required;
    private boolean customized;
    private boolean available;
    private String type;

}