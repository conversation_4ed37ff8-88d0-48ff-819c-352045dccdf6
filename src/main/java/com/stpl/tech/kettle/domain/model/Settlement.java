package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;
import com.stpl.tech.master.domain.model.PaymentMode;

public class Settlement implements Serializable {

	private static final long serialVersionUID = 5068507138606482424L;
	@Id
	private String _id;

	protected int settlementId;

	protected int mode;
	protected PaymentMode modeDetail;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal amount;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal extraVouchers;

	protected boolean edited;

	protected String editedBy;

	protected PaymentMode oldMode;
	@Field
	protected List<OrderPaymentDenomination> denominations;
	@Field
	protected List<ExternalSettlement> externalSettlements;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public int getSettlementId() {
		return settlementId;
	}

	public void setSettlementId(int value) {
		this.settlementId = value;
	}

	public int getMode() {
		return mode;
	}

	public void setMode(int value) {
		this.mode = value;
	}

	public PaymentMode getModeDetail() {
		return modeDetail;
	}

	public void setModeDetail(PaymentMode value) {
		this.modeDetail = value;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal value) {
		this.amount = value;
	}

	public BigDecimal getExtraVouchers() {
		return extraVouchers;
	}

	public void setExtraVouchers(BigDecimal value) {
		this.extraVouchers = value;
	}

	public List<OrderPaymentDenomination> getDenominations() {
		if (Objects.isNull(denominations)) {
			denominations = new ArrayList<OrderPaymentDenomination>();
		}
		return this.denominations;
	}

	public List<ExternalSettlement> getExternalSettlements() {
		if (Objects.isNull(externalSettlements)) {
			externalSettlements = new ArrayList<ExternalSettlement>();
		}
		return this.externalSettlements;
	}

	public void setExternalSettlements(List<ExternalSettlement> externalSettlements) {
		this.externalSettlements = externalSettlements;
	}

	public boolean isEdited() {
		return edited;
	}

	public void setEdited(boolean edited) {
		this.edited = edited;
	}

	public String getEditedBy() {
		return editedBy;
	}

	public void setEditedBy(String editedBy) {
		this.editedBy = editedBy;
	}

	public PaymentMode getOldMode() {
		return oldMode;
	}

	public void setOldMode(PaymentMode oldMode) {
		this.oldMode = oldMode;
	}
}
