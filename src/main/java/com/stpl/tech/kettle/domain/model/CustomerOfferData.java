package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomerOfferData {
	private String contactNumber;
	private String customerName;
	private int customerId;
	private int unitId;
	private String unitName;
	private BigDecimal amount;
	private String region;
	private String couponCode;
	private String message;
	private String validUntil;
	private String url;
	private int couponDetailId;
	private int offerDetailId;
	private Date numberVerificationTime;

	public CustomerOfferData(String contactNumber, String customerName, int customerId, int unitId, String unitName,
			BigDecimal amount, String region, Date numberVerificationTime) {
		super();
		this.contactNumber = contactNumber;
		this.customerName = customerName;
		this.customerId = customerId;
		this.unitId = unitId;
		this.unitName = unitName;
		this.amount = amount;
		this.region = region;
		this.numberVerificationTime = numberVerificationTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((contactNumber == null) ? 0 : contactNumber.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CustomerOfferData other = (CustomerOfferData) obj;
		if (contactNumber == null) {
			if (other.contactNumber != null)
				return false;
		} else if (!contactNumber.equals(other.contactNumber))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "CustomerOfferData [contactNumber=" + contactNumber + ", customerName=" + customerName + ", customerId="
				+ customerId + ", unitId=" + unitId + ", unitName=" + unitName + ", amount=" + amount + ", region="
				+ region + ", couponCode=" + couponCode + ", message=" + message + ", url=" + url + ", couponDetailId="
				+ couponDetailId + ", offerDetailId=" + offerDetailId + "]";
	}

}
