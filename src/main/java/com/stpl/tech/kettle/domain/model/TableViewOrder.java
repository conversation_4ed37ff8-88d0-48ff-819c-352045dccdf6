package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TableViewOrder {
    Order order;

    OrderInfo orderInfo;
    List<Integer> tableOrderIds;
    Boolean loyalteaToBeRedeemed;

}
