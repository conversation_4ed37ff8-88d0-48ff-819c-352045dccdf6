package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.CustomJsonDateDeserializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrderMetricDomain {

    private OrderMetricData orderMetricData;
    private Integer customerId;
    private boolean newCustomer;
    private boolean validCustomer;
    private boolean recommendedProductAdded;
    private String posVersion;
    private String cafeAppVersion;
    private Integer previousOrderId;
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date previousOrderBusinessDate;
    private Integer previousOrderUnitId;
    private Integer customerOrderNumber;
    private PaymentMetricData paymentMetricData;
    private MetricDefinition customizationData;
    private MetricDefinition walletData;
    private MetricDefinition secondFreeChaiData;
    private MetricDefinition offerData;
    private MetricDefinition membershipData;
    private boolean savedChaiAdded;
    private Integer awardedLoyaltyPoints;
    private Integer redeemedLoyaltyPoints;
    private boolean loyaltyOfferRedemptionShown;
    private boolean previousSuggestedProductAdded;
    private Integer otpDeliveredTime;
    private boolean exploreMoreAdded;
    private boolean newChaiSaved;
    private boolean freeProductProgramInformed;
    private String recordingId;

}
