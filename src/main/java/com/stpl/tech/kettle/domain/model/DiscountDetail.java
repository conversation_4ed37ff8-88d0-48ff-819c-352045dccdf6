package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DiscountDetail implements Serializable {

	private static final long serialVersionUID = -3624088464502553964L;

	@Id
	private String _id;
	protected Integer discountCode;
	protected String discountReason;
	protected PercentageDetail discount;

	protected BigDecimal promotionalOffer;

	protected BigDecimal totalDiscount;

}
