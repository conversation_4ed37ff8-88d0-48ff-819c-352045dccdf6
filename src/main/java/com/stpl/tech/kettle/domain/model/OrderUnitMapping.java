package com.stpl.tech.kettle.domain.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class OrderUnitMapping {

    private int unitId;
    private Integer terminalId;
    private String orderSource;
    private int employeeId;

    public OrderUnitMapping(int unitId, Integer terminalId, String orderSource, int employeeId) {
        super();
        this.unitId = unitId;
        this.terminalId = terminalId;
        this.orderSource = orderSource;
        this.employeeId = employeeId;
    }



    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + employeeId;
        result = prime * result + ((orderSource == null) ? 0 : orderSource.hashCode());
        result = prime * result + ((terminalId == null) ? 0 : terminalId.hashCode());
        result = prime * result + unitId;
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        OrderUnitMapping other = (OrderUnitMapping) obj;
        if (employeeId != other.employeeId) {
            return false;
        }
        if (orderSource == null) {
            if (other.orderSource != null) {
                return false;
            }
        } else if (!orderSource.equals(other.orderSource)) {
            return false;
        }
        if (terminalId == null) {
            if (other.terminalId != null) {
                return false;
            }
        } else if (!terminalId.equals(other.terminalId)) {
            return false;
        }
        if (unitId != other.unitId) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "OrderUnitMapping [unitId=" + unitId + ", terminalId=" + terminalId + ", orderSource=" + orderSource
                + ", employeeId=" + employeeId + "]";
    }

}
