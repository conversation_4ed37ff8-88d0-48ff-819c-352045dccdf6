package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.List;

import com.stpl.tech.master.domain.model.Pair;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class TransactionData {
    private int quantity;
    private BigDecimal price;
    private BigDecimal amount;
    private BigDecimal tax;
    private List<Pair<String, BigDecimal>> taxes;

    public TransactionData(int quantity, BigDecimal price, BigDecimal amount, BigDecimal tax,
                           List<Pair<String, BigDecimal>> taxes) {
        super();
        this.quantity = quantity;
        this.price = price;
        this.amount = amount;
        this.tax = tax;
        this.taxes = taxes;
    }

}
