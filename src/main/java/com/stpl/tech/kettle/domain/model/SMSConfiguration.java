package com.stpl.tech.kettle.domain.model;

import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Component
public class SMSConfiguration {
	private String userId;

	private String passCode;

	private String url;

	public SMSConfiguration(String passCode, String url) {
		this.passCode = passCode;
		this.url = url;
	}
}
