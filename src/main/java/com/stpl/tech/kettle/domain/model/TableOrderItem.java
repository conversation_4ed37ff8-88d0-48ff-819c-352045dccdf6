package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TableOrderItem", propOrder = { "productId", "productName", "quantity", "price", "totalAmount",
        "amount", "dimension", "itemCode", })
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class TableOrderItem {


    private Integer orderItemId;

    private int productId;
    @XmlElement(required = true, nillable = true)
    private String productName;
    private int quantity;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
//    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    private BigDecimal price;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
//    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    private BigDecimal totalAmount;
    @XmlElement(required = true, nillable = true)
    private String dimension;
    @XmlElement(required = true, nillable = true)
    private String itemCode;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
//    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    private BigDecimal taxAmount;

    private String itemStatus;
    private String isHoldOn = "N";

    private TableOrderItemComposition orderItemComposition;
}

