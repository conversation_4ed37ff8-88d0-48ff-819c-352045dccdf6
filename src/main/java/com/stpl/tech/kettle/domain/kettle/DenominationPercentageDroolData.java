package com.stpl.tech.kettle.domain.kettle;

import lombok.*;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DenominationPercentageDroolData implements Serializable {

    @Serial
    private static final long serialVersionUID = -9165289818647569255L;
    private String isDefault;
    private Integer denominationValue;
    private String availableDenomination;
    private Integer extraValue;
    private Integer baseValue;
    private  Integer stepperValue;
}
