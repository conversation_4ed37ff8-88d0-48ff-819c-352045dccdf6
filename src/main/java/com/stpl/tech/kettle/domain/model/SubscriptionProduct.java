package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SubscriptionProduct {
	private Integer productId;
	private String productName;
	private String subscriptionCode;
	private String dimensionCode;
	private BigDecimal price;
	private Integer validityInDays;

	@Override
	public String toString() {
		return "SubscriptionProduct [productId=" + productId + ", productName=" + productName + ", subscriptionCode="
				+ subscriptionCode + ", dimensionCode=" + dimensionCode + ", price=" + price + ", validityInDays="
				+ validityInDays + "]";
	}

}
