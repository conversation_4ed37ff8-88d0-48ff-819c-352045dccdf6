package com.stpl.tech.kettle.domain.model;

import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DenominationValueData implements Serializable {

    @Serial
    private static final long serialVersionUID = -2058118297774012130L;
    private Map<Integer,DenominationOfferPercentage> denominationOfferMap;
    private List<String> activeDenomination;
    private Integer baseValue;
    private Integer stepperValue;
}
