package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;


@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class SuperUEvent{

    public SuperUEvent() {
    }

    private Integer eventId;
    private int unitId;
    private int customerId;
    private int empId;
    private int terminalNumber;
    private int orderId;
    private String orderStartTime;
    private String billingServerTime;
    private String businessDate;
    private float revenue;
    private String productsAndQuantity;
    private String unitName;
    private String customerName;
    private String orderSource;
    private String daypart;
    private String customerType;
    @Override
    public String toString() {
        return "{" +
                "\"eventId\": " + (eventId != null ? eventId : "null") + ", " +
                "\"unitId\": " + unitId + ", " +
                "\"customerId\": " + customerId + ", " +
                "\"empId\": " + empId + ", " +
                "\"terminalNumber\": " + terminalNumber + ", " +
                "\"orderId\": " + orderId + ", " +
                "\"orderStartTime\": \"" + (orderStartTime != null ? orderStartTime : "null") + "\", " +
                "\"billingServerTime\": \"" + (billingServerTime != null ? billingServerTime : "null") + "\", " +
                "\"businessDate\": \"" + (businessDate != null ? businessDate : "null") + "\", " +
                "\"revenue\": " + revenue + ", " +
                "\"productsAndQuantity\": \"" + (productsAndQuantity != null ? productsAndQuantity : "null") + "\", " +
                "\"unitName\": \"" + (unitName != null ? unitName : "null") + "\", " +
                "\"customerName\": \"" + (customerName != null ? customerName : "null") + "\", " +
                "\"orderSource\": \"" + (orderSource != null ? orderSource : "null") + "\", " +
                "\"daypart\": \"" + (daypart != null ? daypart : "null") + "\", " +
                "\"customerType\": \"" + (customerType != null ? customerType : "null") + "\"" +
                "}";
    }



}
