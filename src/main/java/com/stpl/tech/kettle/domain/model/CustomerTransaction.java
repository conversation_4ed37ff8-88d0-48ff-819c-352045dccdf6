package com.stpl.tech.kettle.domain.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CustomerTransaction implements Serializable {

	private static final long serialVersionUID = 8136028942496458656L;

	private Integer id;

    private Integer customerId;

    private Integer brandId;

    private Integer firstOrderId;

    private String firstOrderSource;

    private Integer firstOrderChannelPartnerId;

    private Date firstOrderBusinessDate;

    private Integer firstOrderUnitId;

    private Integer lastOrderId;

    private String lastOrderSource;

    private Integer lastOrderChannelPartnerId;

    private Date lastOrderBusinessDate;

    private Integer lastOrderUnitId;

    private Integer totalDineInOrders;

    private Integer totalDeliveryOrders;

    private Integer totalOrders;

}
