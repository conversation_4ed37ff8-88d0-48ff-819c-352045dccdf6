package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CustomerEmailData {
    private Integer customerId;
    private Integer overallOrders;
    private Integer overallVisits;
    private BigDecimal overallSavings;
    private String availedSignupOffer;
    private Date signupOfferExpiryTime;
    private Integer acquiredLoyaltyPoints;
    private BigDecimal walletBalance;
    private BigDecimal chaayosCashBalance;
    private String membershipAvailable;
    private String membershipPlan;
    private Date membershipEndDate;
}
