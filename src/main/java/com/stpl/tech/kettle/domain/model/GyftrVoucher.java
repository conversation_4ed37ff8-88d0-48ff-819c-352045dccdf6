package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GyftrVoucher implements Serializable {

	private static final long serialVersionUID = -7146060054050176362L;

	List<GyftrVBatchConsumeResult> vBatchConsumeResult = new ArrayList<>();

	@Override
	public String toString() {
		return "GytrVoucher [vBatchConsumeResult=" + vBatchConsumeResult + "]";
	}

}
