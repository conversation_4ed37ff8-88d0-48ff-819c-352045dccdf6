package com.stpl.tech.kettle.domain.model;

public enum SubscriptionStatus {

    INITIATED("INITIATED"),
    CREATED("CREATED"),
    ON_HOLD("ON_HOLD"),
    CANCELLED("CANCELLED");


    private final String value;

    SubscriptionStatus(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static SubscriptionStatus fromValue(String v) {
        for (SubscriptionStatus c : SubscriptionStatus.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}

