package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.CustomJsonDateDeserializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrderMetricData {

    private Integer orderId;
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date billingServerTime;
    private Integer unitId;
    private Integer terminalId;
    private BigDecimal taxableAmount;
    private BigDecimal totalAmount;
    private BigDecimal settledAmount;
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date orderStartTime;
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date orderEndTime;
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date loginStartTime;
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date loginEndTime;
    private boolean loginViaFaceIt;
    private boolean otpRequested;
    private String orderSource;
}
