package com.stpl.tech.kettle.domain.model;

import lombok.*;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WalletRecommendationDetail implements Serializable {

    @Serial
    private static final long serialVersionUID = 605534242441310974L;

    private  Integer walletSuggestion;
    private Integer incrementStepper;
    private  Integer extraAmount;
}
