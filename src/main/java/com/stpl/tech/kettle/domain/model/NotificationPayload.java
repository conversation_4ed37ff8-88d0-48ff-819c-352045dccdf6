package com.stpl.tech.kettle.domain.model;

import java.util.Date;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class NotificationPayload {

	private String messageType;
	private Integer orderId;
	private Integer customerId;
	private String contactNumber;
	private boolean whatsappOptIn;
	private boolean sendWhatsapp;
	private Date requestTime;
	private Map<String, String> payload;

	public NotificationPayload(String messageType, Integer orderId, Integer customerId, boolean whatsappOptIn,
			boolean sendWhatsapp, Date requestTime) {
		this.messageType = messageType;
		this.orderId = orderId;
		this.customerId = customerId;
		this.whatsappOptIn = whatsappOptIn;
		this.sendWhatsapp = sendWhatsapp;
		this.requestTime = requestTime;
	}
}
