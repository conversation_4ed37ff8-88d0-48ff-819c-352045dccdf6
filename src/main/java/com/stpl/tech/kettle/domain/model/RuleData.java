package com.stpl.tech.kettle.domain.model;


import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public enum RuleData {


    M1("Morning : 1 or N Beverages only", DaySlot.MORNING, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),
    M2("Morning : N Beverages & M Food Items (Breakfast, Nashta, Feasts & Sandwiches) where M = N", DaySlot.MORNING, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),
    M3("Morning : N Beverages & M Food Items (Breakfast, Nashta, Feasts & Sandwiches) where M < N", DaySlot.MORNING, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),
    M4("Morning : N Beverages & M Food Items (Breakfast, Nashta, Feasts & Sandwiches) where M > N", DaySlot.MORNING, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),

    E1("Evening : 1 or N Beverages only", DaySlot.EVENING, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),
    E2("Evening : N Beverages & M Food Items (Breakfast, Nashta, Feasts & Sandwiches) where M = N", DaySlot.EVENING, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),
    E3("Evening : N Beverages & M Food Items (Breakfast, Nashta, Feasts & Sandwiches) where M < N", DaySlot.EVENING, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),
    E4("Evening : N Beverages & M Food Items (Breakfast, Nashta, Feasts & Sandwiches) where M > N", DaySlot.EVENING, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),

    L1("Lunch : 1 or N Beverages only", DaySlot.LUNCH, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),
    L2("Lunch : N Beverages & M Food Items (Breakfast, Nashta, Feasts & Sandwiches) where M = N", DaySlot.LUNCH, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),
    L3("Lunch : N Beverages & M Food Items (Breakfast, Nashta, Feasts & Sandwiches) where M < N", DaySlot.LUNCH, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),
    L4("Lunch : N Beverages & M Food Items (Breakfast, Nashta, Feasts & Sandwiches) where M > N", DaySlot.LUNCH, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),

    D1("Lunch : 1 or N Beverages only", DaySlot.DINNER, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),
    D2("Lunch : N Beverages & M Food Items (Breakfast, Nashta, Feasts & Sandwiches) where M = N", DaySlot.DINNER, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),
    D3("Lunch : N Beverages & M Food Items (Breakfast, Nashta, Feasts & Sandwiches) where M < N", DaySlot.DINNER, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),
    D4("Lunch : N Beverages & M Food Items (Breakfast, Nashta, Feasts & Sandwiches) where M > N", DaySlot.DINNER, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)}),
    //D5("Dinner : New Customer",DaySlot.DINNER, new Option[]{}),
    CATCH_ALL("Catch All", DaySlot.NONE, new Option[]{new Option(25, 710), new Option(26, 730), new Option(27, 1057)});

    private final String desc;

    public static final Set<Integer> PRODUCT_IDS = new HashSet<>(Arrays.asList(710, 730, 1057));

    private final DaySlot slot;

    private final Option[] options;

    public Option[] getOptions() {
        return options;
    }

    public String getDesc() {
        return desc;
    }


    public DaySlot getSlot() {
        return slot;
    }

    public String getCoupon() {
        return "DISCR" + this.name();
    }

    public String getCouponDescription() {
        return "Discount for Rule " + this.name();
    }

    private RuleData(String desc, DaySlot slot, Option[] options) {
        this.desc = desc;
        this.slot = slot;
        this.options = options;
    }

}
