package com.stpl.tech.kettle.domain.model;


import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import org.springframework.data.annotation.Id;
import com.stpl.tech.kettle.util.adapter.CustomJsonDateDeserializer;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


public class Subscription implements Serializable {

    private static final long serialVersionUID = 7695229577630112318L;
    @Id
    private String _id;

    protected int subscriptionId;

    protected SubscriptionStatus subscriptionStatus;

    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    protected Date startDate;

    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    protected Date endDate;

    protected SubscriptionType type;

    protected boolean emailNotification;

    protected boolean smsNotification;

    protected boolean automatedDelivery;

    protected List<Integer> daysOfTheMonth;

    protected List<Integer> daysOfTheWeek;

    protected List<Integer> timeOfTheDay;
    protected BigDecimal overAllSavings;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public int getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(int value) {
        this.subscriptionId = value;
    }

    public SubscriptionStatus getSubscriptionStatus() {
        return subscriptionStatus;
    }

    public void setSubscriptionStatus(SubscriptionStatus value) {
        this.subscriptionStatus = value;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date value) {
        this.startDate = value;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date value) {
        this.endDate = value;
    }

    public SubscriptionType getType() {
        return type;
    }

    public void setType(SubscriptionType value) {
        this.type = value;
    }

    public boolean isEmailNotification() {
        return emailNotification;
    }

    public void setEmailNotification(boolean value) {
        this.emailNotification = value;
    }

    public boolean isSmsNotification() {
        return smsNotification;
    }

    public void setSmsNotification(boolean value) {
        this.smsNotification = value;
    }

    public boolean isAutomatedDelivery() {
        return automatedDelivery;
    }

    public void setAutomatedDelivery(boolean value) {
        this.automatedDelivery = value;
    }

    public List<Integer> getDaysOfTheMonth() {
        if (Objects.isNull(daysOfTheMonth)) {
            daysOfTheMonth = new ArrayList<Integer>();
        }
        return this.daysOfTheMonth;
    }


    public List<Integer> getDaysOfTheWeek() {
        if (Objects.isNull(daysOfTheWeek)) {
            daysOfTheWeek = new ArrayList<Integer>();
        }
        return this.daysOfTheWeek;
    }

    public List<Integer> getTimeOfTheDay() {
        if (Objects.isNull(timeOfTheDay)) {
            timeOfTheDay = new ArrayList<Integer>();
        }
        return this.timeOfTheDay;
    }

    public void setDaysOfTheMonth(List<Integer> daysOfTheMonth) {
        this.daysOfTheMonth = daysOfTheMonth;
    }

    public void setDaysOfTheWeek(List<Integer> daysOfTheWeek) {
        this.daysOfTheWeek = daysOfTheWeek;
    }

    public void setTimeOfTheDay(List<Integer> timeOfTheDay) {
        this.timeOfTheDay = timeOfTheDay;
    }

    public BigDecimal getOverAllSavings() {
        return overAllSavings;
    }

    public void setOverAllSavings(BigDecimal overAllSavings) {
        this.overAllSavings = overAllSavings;
    }
}
