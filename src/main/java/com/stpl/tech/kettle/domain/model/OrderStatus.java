package com.stpl.tech.kettle.domain.model;

public enum OrderStatus {

    INITIATED,
    CREATED,
    PROCESSING,
    READY_TO_PARTIALLY_DISPATCH,
    READY_TO_DISPATCH,
    SETTLED,
    CANCELLED_REQUESTED,
    <PERSON><PERSON><PERSON>LED,
    CLOSED,
    <PERSON><PERSON>IVERE<PERSON>,
    ON_HOLD;

    public String value() {
        return name();
    }

    public static OrderStatus fromValue(String v) {
        return valueOf(v);
    }

}
