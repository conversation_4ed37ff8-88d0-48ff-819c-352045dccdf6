package com.stpl.tech.kettle.domain.model;

public enum CashCardEventStatus {
	CARD___VALIDATION___FAILED("CARD_VALIDATION_FAILED"), CARD___ACTIVATION___FAILED("CARD_ACTIVATION_FAILED"),
	CARD___ACTIVATION___SUCCESS("CARD_ACTIVATION_SUCCESS");

	private final String value;

	CashCardEventStatus(String v) {
		value = v;
	}

	public String value() {
		return value;
	}

	public static CashCardEventStatus fromValue(String v) {
		for (CashCardEventStatus c : CashCardEventStatus.values()) {
			if (c.value.equals(v)) {
				return c;
			}
		}
		throw new IllegalArgumentException(v);
	}

}
