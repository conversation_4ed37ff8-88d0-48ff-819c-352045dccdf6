package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UnitSessionDetail implements Serializable {

	private static final long serialVersionUID = -5365668891489162125L;

	private int unitId;

	private Integer terminalId;

	private String generatedOrderId;

	private Customer customer;

	private boolean newCustomer = false;

	private boolean sentOtp = false;

	private boolean hasRedemption = false;

	private int loyaltyPoints;

	private String loyaltyReason;

	private boolean pointsRedeemedSuccessfully;

	private int redeemedProductId;

	public UnitSessionDetail(int unitId, Integer terminalId, String generatedOrderId) {
		super();
		this.unitId = unitId;
		this.generatedOrderId = generatedOrderId;
		this.terminalId = terminalId;
	}

}
