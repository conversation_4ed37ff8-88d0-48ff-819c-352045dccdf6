package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SubscriptionRequest {

	private SubscriptionEventType type;
	private String offerDescription;
	private Integer productId;
	private String dimension;
	private BigDecimal price;
	private Integer validityInDays;
	private Date startDate;
	private Date endDate;
	private Integer orderId;
	private Integer orderItemId;
	private Integer customerId;
	private Integer planId;
	private String frequencyStrategy;// TIME_BASED OR QAUNTITY_BASED
	private BigDecimal overAllFrequency;
	private BigDecimal frequencyLimit;

}
