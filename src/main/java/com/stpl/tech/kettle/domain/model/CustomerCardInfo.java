package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomerCardInfo {
    private String  customerId;
    private String firstName;
    private String middleName;
    private String lastName;
    private String countryCode;
    private String contactNumber;
    private String emailId;
    private String isNumberVerified;
    private Date numberVerificationTime;

    private String subscriptionPlanCode;
    private Date planStartDate;
    private Date planEndDate;
    private String cardCode;

    private BigDecimal walletBalance;
    private Integer loyaltyBalance;

}
