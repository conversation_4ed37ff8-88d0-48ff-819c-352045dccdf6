package com.stpl.tech.kettle.domain;

import java.io.Serializable;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CustomerMappingTypes implements Serializable {

	private static final long serialVersionUID = 3516006995763769468L;

	private String customerTypes;
	private Integer journeyCount;
	private Integer validityInDays;
	private String defaultCloneCode;
	private Integer reminderDays;

	public CustomerMappingTypes(String customerTypes, Integer journeyCount, Integer validityInDays,
			String defaultCloneCode, Integer reminderDays) {
		this.customerTypes = customerTypes;
		this.journeyCount = journeyCount;
		this.validityInDays = validityInDays;
		this.defaultCloneCode = defaultCloneCode;
		this.reminderDays = reminderDays;
	}

}
