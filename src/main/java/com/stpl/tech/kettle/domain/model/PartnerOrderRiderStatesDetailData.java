package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PartnerOrderRiderStatesDetailData implements Serializable {

    private static final long serialVersionUID = 2257035730054893375L;
    private Integer id;

    private String partnerOrderId;

    private Integer kettleOrderId;

    private String partnerName;


    private Date billingServerTime;


    private String riderName ;

    private String riderContact;
    private String riderDelayStatus;

    private Date riderAssignedAt;

    private Date riderArrivedAt;

    private Date orderPickupTime;

    private Date orderDeliveryTime;

    private Date riderReachedCustomerAt;

    private String delayReason;

}
