package com.stpl.tech.kettle.domain.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.stpl.tech.master.domain.model.ProductInventory;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class InventoryUpdateEvent {

	protected List<ProductInventory> currentInventory;
	protected List<InventoryEventData> updatedInventory;
	protected InventoryEventType type;
	protected String comment;
	protected Date updateTime;
	protected Date businessDate;
	protected int updatedBy;
	protected int unitId;

	public List<ProductInventory> getCurrentInventory() {
		if (Objects.isNull(currentInventory)) {
			currentInventory = new ArrayList<ProductInventory>();
		}
		return this.currentInventory;
	}

	public List<InventoryEventData> getUpdatedInventory() {
		if (Objects.isNull(updatedInventory)) {
			updatedInventory = new ArrayList<InventoryEventData>();
		}
		return this.updatedInventory;
	}

}
