package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.CustomJsonDateDeserializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PaymentMetricData {

    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date paymentStartTime;
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date paymentEndTime;
    private boolean edcPayment;

}
