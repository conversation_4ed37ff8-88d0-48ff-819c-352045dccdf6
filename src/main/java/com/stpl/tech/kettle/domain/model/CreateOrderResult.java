package com.stpl.tech.kettle.domain.model;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.stpl.tech.kettle.data.kettle.SubscriptionPlan;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateOrderResult {
	private int orderId;
	private List<String> giftCard;
	private boolean generateQRCode;
	private int feedbackId;
	private boolean generateInAppFeedback;
	private SubscriptionPlan subscriptionPlan;

	private Set<Integer> orderItemIdsToBeOnHold = new HashSet<>();

}
