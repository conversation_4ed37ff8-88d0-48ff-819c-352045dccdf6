package com.stpl.tech.kettle.domain.model;


import java.io.Serializable;
import java.util.Date;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActionDetail implements Serializable {

    private static final long serialVersionUID = -7782659644886973589L;
    @Id
    private String _id;

    protected String reason;

    protected Integer reasonId;

    protected String bookedWastage;

    protected Integer approvedBy;

    protected Integer generatedBy;
    protected Date actionTime;

}

