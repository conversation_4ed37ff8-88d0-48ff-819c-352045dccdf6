package com.stpl.tech.kettle.domain.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CreateNextOfferRequest {
	private Integer customerId;
	private String contactNumber;
	private Integer brandId;
	private Integer campaignId;
	private String utmSource;
	private String utmMedium;

	private String campaignToken;

	private String authToken;

	public CreateNextOfferRequest(Integer customerId, String contactNumber, Integer brandId, Integer campaignId,
			String utmSource, String utmMedium) {
		this.customerId = customerId;
		this.contactNumber = contactNumber;
		this.brandId = brandId;
		this.campaignId = campaignId;
		this.utmSource = utmSource;
		this.utmMedium = utmMedium;
	}
}
