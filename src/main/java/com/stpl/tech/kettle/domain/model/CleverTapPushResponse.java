package com.stpl.tech.kettle.domain.model;

import java.util.List;

import com.stpl.tech.kettle.data.kettle.CleverTapProfilePushTrack;
import com.stpl.tech.kettle.data.kettle.EventPushTrack;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CleverTapPushResponse {
	private String status;
	private int processed;
	private List<Object> unprocessed;
	private List<CleverTapProfilePushTrack> profiles;
	private List<EventPushTrack> events;
}
