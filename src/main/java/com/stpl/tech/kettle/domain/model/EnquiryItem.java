package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EnquiryItem implements Serializable {

	private static final long serialVersionUID = -2348267485937822977L;

	@Id
	private String _id;

	protected int id;

	protected String name;

	protected int orderedQuantity;

	protected int availableQuantity;

	protected boolean replacementServed;

	protected int linkedOrderId;

	protected int linkedCustomerId;

	protected int linkedUnitId;

}
