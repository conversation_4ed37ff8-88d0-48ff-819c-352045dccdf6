package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrderPaymentDenomination implements Serializable {

	private static final long serialVersionUID = 1917517604444772745L;
	@Id
	private String _id;

	protected int id;
	protected int orderId;
	protected int settlementId;
	protected int denominationDetailId;
	protected int count;

	protected int totalAmount;

}
