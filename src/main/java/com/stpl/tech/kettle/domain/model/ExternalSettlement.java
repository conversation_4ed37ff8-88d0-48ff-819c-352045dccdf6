package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

import org.springframework.data.annotation.Id;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ExternalSettlement implements Serializable {

	private static final long serialVersionUID = 7597118806842459583L;

	@Id
	private String _id;

	protected int externalSettlementId;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal amount;

	protected String externalTransactionId;

	public ExternalSettlement(int externalSettlementId, BigDecimal amount, String externalTransactionId) {
		super();
		this.externalSettlementId = externalSettlementId;
		this.amount = amount;
		this.externalTransactionId = externalTransactionId;
	}

}
