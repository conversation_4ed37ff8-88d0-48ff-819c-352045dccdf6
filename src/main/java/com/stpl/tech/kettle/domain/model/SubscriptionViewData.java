package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionViewData {
	private Integer customerId;
	private String customerName;
	private String customerNumber;
	private Integer subscriptionId;
	private Date planStartDate;
	private Date planEndDate;
	private String subscriptionPlanCode;
	private String subscriptionName;
	private String offerDescription;
	private Integer validityDays;
	private Integer nThDay;
	private Integer totalSaving;
	private BigDecimal price;
	private String buyLink;

	public SubscriptionViewData(Integer customerId, Integer subscriptionId, Date planStartDate,
			String subscriptionPlanCode) {
		this.customerId = customerId;
		this.subscriptionId = subscriptionId;
		this.planStartDate = planStartDate;
		this.subscriptionPlanCode = subscriptionPlanCode;
	}

	public SubscriptionViewData(Integer customerId, Integer subscriptionId, Date planStartDate, Date planEndDate,
			String subscriptionPlanCode) {
		this.customerId = customerId;
		this.subscriptionId = subscriptionId;
		this.planStartDate = planStartDate;
		this.planEndDate = planEndDate;
		this.subscriptionPlanCode = subscriptionPlanCode;
	}
}
