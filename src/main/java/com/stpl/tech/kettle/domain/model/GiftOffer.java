package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class GiftOffer {
    private BigDecimal denomination;
    private BigDecimal offer;
    private BigDecimal suggestWalletOffer;
    private Date endDate;
}
