package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.Date;

import com.stpl.tech.kettle.report.metadata.model.TrueCallerSettings;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CustomerResponse {
	private int id;
	private String name;
	private String contact;
	private String email;
	private boolean contactVerified;
	private boolean emailVerified;
	private int unitId;
	private boolean eligibleForSignupOffer;
	private boolean newCustomer;
	private String otp;
	private boolean feedbackRequired;
	private boolean otpVerified;
	private FeedbackOrderMetadata feedbackOrderMetadata;
	private Integer tcId;
	private TrueCallerSettings signInMode;
	private String signUpRefCode;
	private Date lastVisitTime;
	private boolean optOutOfFaceIt = false;
	private Date optOutTime;
	private String faceId;
	private Integer brandId;
	private String gender;
	private Date dateOfBirth;
	private Date anniversary;
	private boolean sourceAcknowledged;
	private SubscriptionInfoDetail subscriptionInfoDetail;

	protected int loyalityPoints;
	protected BigDecimal chaayosCash;
	protected Boolean optWhatsapp;
}
