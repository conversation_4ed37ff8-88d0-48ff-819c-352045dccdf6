package com.stpl.tech.kettle.domain.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class OrderDomain {
	private Order order;

	private WalletOrder walletOrder;

	private boolean includeReceipts;

	private boolean addMetadata;

	private OrderNotification orderNotification;

	private CustomerEventTrack customerEventTrack;

	private Subscription subscription;

}
