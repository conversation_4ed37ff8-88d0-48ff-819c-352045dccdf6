package com.stpl.tech.kettle.domain.model;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDineInView {
	private Integer customerId;
	private Integer activeDineInOrders;
	private Integer dineInOrders;
	private String availedSignupOffer;
	private Date signupOfferExpiryTime;
	private Date lastOrderTime;
}
