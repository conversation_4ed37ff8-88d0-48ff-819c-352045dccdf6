package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionOfferInfoDetail implements Serializable {
    private String subscriptionCode;
    private String customerName;
    private String offerText;
    private Integer offerValue;
    private Integer validDays;
    private Date expiryDate;
    private String subscriptionUrl;

    public SubscriptionOfferInfoDetail(String subscriptionCode, Integer offerValue) {
        this.subscriptionCode = subscriptionCode;
        this.offerValue = offerValue;
    }
}
