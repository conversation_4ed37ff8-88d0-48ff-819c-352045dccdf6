package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.kettle.util.AppUtils;

public enum DaySlot {

    MORNING, LUNCH, EVENING, DINNER, NONE;

    public static DaySlot getCurrentDaySlot() {
        int hour = AppUtils.getCurrentTimestamp().getHours();
        if ((hour >= 5 && hour <= 11)) {
            return MORNING;
        } else if ((hour >= 12 && hour <= 14)) {
            return LUNCH;
        } else if ((hour >= 15 && hour <= 18)) {
            return EVENING;
        } else if ((hour >= 0 && hour <= 4) || (hour >= 19 && hour <= 23)) {
            return DINNER;
        }
        return DINNER;
    }

}
