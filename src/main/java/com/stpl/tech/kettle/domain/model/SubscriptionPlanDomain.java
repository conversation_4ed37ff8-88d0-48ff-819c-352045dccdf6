package com.stpl.tech.kettle.domain.model;

import static com.stpl.tech.kettle.util.Constants.AppConstants.DEFAULT_TIME_ZONE;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.CustomJsonDateDeserializer;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionPlanDomain implements Serializable {

	private static final long serialVersionUID = -9107605083191308147L;

	private String status;

	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	@JsonFormat(timezone = DEFAULT_TIME_ZONE)
	private Date planStartDate;

	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	@JsonFormat(timezone = DEFAULT_TIME_ZONE)
	private Date planEndDate;

	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	@JsonFormat(timezone = DEFAULT_TIME_ZONE)
	private Date renewalTime;

	private String eventType;

	private BigDecimal overAllSaving;

	private String offerDescription;

	private int chaiLeft;

}
