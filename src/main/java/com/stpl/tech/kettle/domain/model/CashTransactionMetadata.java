package com.stpl.tech.kettle.domain.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CashTransactionMetadata {
	private TransactionType transactionType;
	private CashMetadataType cashMetadataType;
	private CashTransactionCategory cashTransactionCategory;
	private CashTransactionCode cashTransactionCode;

	public CashTransactionMetadata(TransactionType transactionType, CashMetadataType cashMetadataType,
			CashTransactionCategory cashTransactionCategory, CashTransactionCode cashTransactionCode) {
		super();
		this.transactionType = transactionType;
		this.cashMetadataType = cashMetadataType;
		this.cashTransactionCode = cashTransactionCode;
		this.cashTransactionCategory = cashTransactionCategory;
	}

}
