package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SubscriptionInfoDetail implements Serializable {

    private static final long serialVersionUID = -6314976048575109390L;
   
    private Integer customerId;

    private String subscriptionCode;

    private boolean hasSubscription;

    private Date startDate;

    private Date endDate;

    private Integer daysLeft;

    private BigDecimal overAllSaving;

    private boolean eligible;

    private String frequencyStrategy;

    private BigDecimal overAllFrequency;

    private BigDecimal frequencyLimit;

    public SubscriptionInfoDetail(Integer customerId, String subscriptionCode, boolean hasSubscription, Date startDate, Date endDate, BigDecimal overAllSaving, Integer daysLeft,String frequencyStrategy,BigDecimal frequencyLimit,BigDecimal overAllFrequency) {
        this.customerId = customerId;
        this.subscriptionCode = subscriptionCode;
        this.hasSubscription = hasSubscription;
        this.startDate = startDate;
        this.endDate = endDate;
        this.overAllSaving = overAllSaving;
        this.daysLeft = daysLeft;
        this.frequencyStrategy = frequencyStrategy;
        this.frequencyLimit = frequencyLimit;
        this.overAllFrequency = overAllFrequency;
    }

}

