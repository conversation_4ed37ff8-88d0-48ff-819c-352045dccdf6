package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ComplimentaryDetail implements Serializable {

	private static final long serialVersionUID = 3316880555276904189L;

	@Id
	private String _id;

	
	protected boolean isComplimentary;

	protected Integer reasonCode;

	protected String reason;

	public void setIsComplimentary(boolean value) {
		this.isComplimentary = value;
	}

	public boolean isIsComplimentary() {
		return isComplimentary;
	}
}
