package com.stpl.tech.kettle.domain.model;


import com.stpl.tech.kettle.notification.Notification;
import com.stpl.tech.master.core.external.acl.service.TokenDao;
import com.stpl.tech.kettle.util.AppUtils;
import io.jsonwebtoken.Claims;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackTokenInfo implements TokenDao, Notification, Serializable {


    private String contactNumber;
    private String unitName;
    private String customerName;
    private Integer orderId;
    private String orderSource;
    private String feedbackSource;
    private Integer feedbackId;
    private Integer feedbackEventId;
    private Integer rating;


    public FeedbackTokenInfo(String contactNumber, String unitName, String customerName, Integer orderId,
                             String orderSource, String feedbackSource, Integer feedbackId, Integer feedbackEventId) {
        super();
        this.contactNumber = contactNumber;
        this.unitName = unitName;
        this.customerName = customerName;
        this.orderId = orderId;
        this.orderSource = orderSource;
        this.feedbackSource = feedbackSource;
        this.feedbackId = feedbackId;
        this.feedbackEventId = feedbackEventId;
    }

    public Map<String, Object> createClaims() {
        Map<String, Object> claims = new HashMap<String, Object>();
        claims.put("contactNumber", this.contactNumber);
        claims.put("unitName", this.unitName);
        claims.put("customerName", this.customerName);
        claims.put("orderId", this.orderId);
        claims.put("orderSource", this.orderSource);
        claims.put("feedbackId", this.feedbackId);
        claims.put("feedbackSource", this.feedbackSource);
        claims.put("feedbackEventId", this.feedbackEventId);
        return claims;
    }

    public void parseClaims(Claims claims) {
        this.contactNumber = claims.get("contactNumber", String.class);
        this.unitName = claims.get("unitName", String.class);
        this.customerName = claims.get("customerName", String.class);
        this.orderId = claims.get("orderId", Integer.class);
        this.orderSource = claims.get("orderSource", String.class);
        this.feedbackId = claims.get("feedbackId", Integer.class);
        this.feedbackSource = claims.get("feedbackSource", String.class);
        this.feedbackEventId = claims.get("feedbackEventId", Integer.class);
    }

    @Override
    public String getNotificationMessage() {
        return rating != null && rating <= 3 ? "Time : " + AppUtils.getCurrentTimeISTString() + "\nUnit : " + unitName
                + "\nCustomer : " + customerName + "\nFeedback Source : " + feedbackSource + "\nRating : " + rating
                : null;
    }

    @Override
    public String toString() {
        return "FeedbackTokenInfo{" +
                "contactNumber='" + contactNumber + '\'' +
                ", unitName='" + unitName + '\'' +
                ", customerName='" + customerName + '\'' +
                ", orderId=" + orderId +
                ", orderSource='" + orderSource + '\'' +
                ", feedbackSource='" + feedbackSource + '\'' +
                ", feedbackId=" + feedbackId +
                ", feedbackEventId=" + feedbackEventId +
                ", rating=" + rating +
                '}';
    }
}

