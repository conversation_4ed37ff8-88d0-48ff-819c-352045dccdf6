package com.stpl.tech.kettle.domain.model;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;
import com.stpl.tech.master.domain.model.BillType;
import com.stpl.tech.master.domain.model.IdCodeName;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ToString
public class OrderItem implements Serializable {

	@Serial
	private static final long serialVersionUID = 4778973176476607094L;
	@Id
	private String _id;

	protected int itemId;

	protected int productId;

	protected String productName;

	protected String itemName;
	protected String productAliasName;

	protected IdCodeName productCategory;
	@Field
	protected IdCodeName productSubCategory;
	@Field
	protected int quantity;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal price;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal originalPrice;

	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal totalAmount;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal amount;

	protected DiscountDetail discountDetail;

	protected ComplimentaryDetail complimentaryDetail;

	protected String dimension;

	protected BillType billType;

	protected OrderItemComposition composition;

	protected int recipeId;

	protected String itemCode;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal tax;

	protected String code;

	protected List<TaxDetail> taxes;

	protected Integer reasonId;

	protected Boolean bookedWastage;

	protected String cardType;

	protected Boolean takeAway;

	protected String recipeProfile;
	protected Date validUpto;
	protected String voucherCode;
	protected BigDecimal offerAmount;

	protected String recomCategory;
	protected Boolean taxDeductedByPartner;
	protected Boolean hasPreference;
	protected PreferenceDetail preferenceDetail;
	protected String partnerTaxType;

	protected String sourceCategory;
	protected String sourceSubCategory;

	protected int productType;

	protected Integer qtyAddedByCustomer;

	protected Boolean recProd;

	protected String orderItemRemark;
	protected BigDecimal prepTime;

	protected Boolean isexploreMoreOptionsProduct;

	protected String saveChaiName;

	protected String productAttr;

	protected boolean loyaltyBurned;

//	protected Integer loyaltyBurnPoints;

	protected String itemKey;

	protected String isBestPairedItem;

	protected String stationCategory;

	protected Integer tableOrderId;

	protected String productDescription;

	protected MilkVariant milkVariant;

	protected String specialMilkAddon;

	protected String isHoldOn = "N";

	protected int productSubType;

	protected ComboQunantityStrategy comboQuantityStrategy = ComboQunantityStrategy.REGULAR;


	protected Integer ruleNumber;

	public String getProductDescription() {
		return productDescription;
	}

	public void setProductDescription(String productDescription) {
		this.productDescription = productDescription;
	}

	public String getRecomCategory() {
		return recomCategory;
	}

	public void setRecomCategory(String recomCategory) {
		this.recomCategory = recomCategory;
	}

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public int getItemId() {
		return itemId;
	}

	public void setItemId(int value) {
		this.itemId = value;
	}

	public int getProductId() {
		return productId;
	}

	public void setProductId(int value) {
		this.productId = value;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String value) {
		this.productName = value;
	}

	public IdCodeName getProductCategory() {
		return productCategory;
	}

	public void setProductCategory(IdCodeName value) {
		this.productCategory = value;
	}

	public IdCodeName getProductSubCategory() {
		return productSubCategory;
	}

	public void setProductSubCategory(IdCodeName value) {
		this.productSubCategory = value;
	}

	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int value) {
		this.quantity = value;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal value) {
		this.price = value;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal value) {
		this.totalAmount = value;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal value) {
		this.amount = value;
	}

	public DiscountDetail getDiscountDetail() {
		return discountDetail;
	}

	public void setDiscountDetail(DiscountDetail value) {
		this.discountDetail = value;
	}

	public ComplimentaryDetail getComplimentaryDetail() {
		return complimentaryDetail;
	}

	public void setComplimentaryDetail(ComplimentaryDetail value) {
		this.complimentaryDetail = value;
	}

	public String getDimension() {
		return dimension;
	}

	public void setDimension(String value) {
		this.dimension = value;
	}

	public BillType getBillType() {
		return billType;
	}

	public void setBillType(BillType value) {
		this.billType = value;
	}

	public OrderItemComposition getComposition() {
		return composition;
	}

	public void setComposition(OrderItemComposition composition) {
		this.composition = composition;
	}

	public int getRecipeId() {
		return recipeId;
	}

	public void setRecipeId(int value) {
		this.recipeId = value;
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String value) {
		this.itemCode = value;
	}

	public List<TaxDetail> getTaxes() {
		if (Objects.isNull(taxes)) {
			taxes = new ArrayList<>();
		}
		return taxes;
	}

	public BigDecimal getTax() {
		return tax;
	}

	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Integer getReasonId() {
		return reasonId;
	}

	public void setReasonId(Integer reasonId) {
		this.reasonId = reasonId;
	}

	public Boolean getBookedWastage() {
		return bookedWastage;
	}

	public void setBookedWastage(Boolean bookedWastage) {
		this.bookedWastage = bookedWastage;
	}

	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	public Boolean getTakeAway() {
		return takeAway;
	}

	public void setTakeAway(Boolean takeAway) {
		this.takeAway = takeAway;
	}

	public String getSourceCategory() {
		return sourceCategory;
	}

	public void setSourceCategory(String sourceCategory) {
		this.sourceCategory = sourceCategory;
	}

	public String getSourceSubCategory() {
		return sourceSubCategory;
	}

	public void setSourceSubCategory(String sourceSubCategory) {
		this.sourceSubCategory = sourceSubCategory;
	}

	@JsonIgnore
	public Date getValidUpto() {
		return validUpto;
	}

	public void setValidUpto(Date validUpto) {
		this.validUpto = validUpto;
	}

	@JsonIgnore
	public String getVoucherCode() {
		return voucherCode;
	}

	public void setVoucherCode(String voucherCode) {
		this.voucherCode = voucherCode;
	}

	public String getRecipeProfile() {
		return recipeProfile;
	}

	public void setRecipeProfile(String recipeProfile) {
		this.recipeProfile = recipeProfile;
	}

	public BigDecimal getOfferAmount() {
		return offerAmount;
	}

	public void setOfferAmount(BigDecimal offerAmount) {
		this.offerAmount = offerAmount;
	}

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public Boolean getTaxDeductedByPartner() {
		return taxDeductedByPartner;
	}

	public void setTaxDeductedByPartner(Boolean taxDeductedByPartner) {
		this.taxDeductedByPartner = taxDeductedByPartner;
	}

	public Boolean getHasPreference() {
		return hasPreference;
	}

	public void setHasPreference(Boolean hasPreference) {
		this.hasPreference = hasPreference;
	}

	public PreferenceDetail getPreferenceDetail() {
		return preferenceDetail;
	}

	public void setPreferenceDetail(PreferenceDetail preferenceDetail) {
		this.preferenceDetail = preferenceDetail;
	}

	public String getPartnerTaxType() {
		return partnerTaxType;
	}

	public void setPartnerTaxType(String partnerTaxType) {
		this.partnerTaxType = partnerTaxType;
	}

	public int getProductType() {
		return productType;
	}

	public void setProductType(int productType) {
		this.productType = productType;
	}

	public String getOrderItemRemark() {
		return orderItemRemark;
	}

	public void setOrderItemRemark(String orderItemRemark) {
		this.orderItemRemark = orderItemRemark;
	}
	public Integer getQtyAddedByCustomer() {
		return qtyAddedByCustomer;
	}

	public void setQtyAddedByCustomer(Integer qtyAddedByCustomer) {
		this.qtyAddedByCustomer = qtyAddedByCustomer;
	}

	public Boolean getRecProd() {
		return recProd;
	}

	public void setRecProd(Boolean recProd) {
		this.recProd = recProd;
	}

	public String getSaveChaiName() {
		return saveChaiName;
	}

	public void setSaveChaiName(String saveChaiName) {
		this.saveChaiName = saveChaiName;
	}

	public String getProductAttr() {
		return productAttr;
	}

	public void setProductAttr(String productAttr) {
		this.productAttr = productAttr;
	}

	public Integer getTableOrderId() {
		return tableOrderId;
	}

	public void setTableOrderId(Integer tableOrderId) {
		this.tableOrderId = tableOrderId;
	}
	public String getStationCategory() {
		return stationCategory;
	}

	public void setStationCategory(String stationCategory) {
		this.stationCategory = stationCategory;
	}

	public MilkVariant getMilkVariant() {
		return milkVariant;
	}

	public void setMilkVariant(MilkVariant milkVariant) {
		this.milkVariant = milkVariant;
	}

	public String getSpecialMilkAddon() {
		return specialMilkAddon;
	}

	public void setSpecialMilkAddon(String specialMilkAddon) {
		this.specialMilkAddon = specialMilkAddon;
	}

	public String getIsHoldOn() {
		return isHoldOn;
	}

	public void setIsHoldOn(String isHoldOn) {
		this.isHoldOn = isHoldOn;
	}

	public int getProductSubType() {
		return productSubType;
	}

	public void setProductSubType(int productSubType) {
		this.productSubType = productSubType;
	}

	public Integer getRuleNumber() {
		return ruleNumber;
	}

	public void setRuleNumber(Integer ruleNumber) {
		this.ruleNumber = ruleNumber;
	}

}
