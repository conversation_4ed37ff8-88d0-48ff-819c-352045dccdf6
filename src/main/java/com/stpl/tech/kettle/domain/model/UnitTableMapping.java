package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UnitTableMapping implements Serializable, Comparable<UnitTableMapping> {
    private static final long serialVersionUID = 1L;

    private int tableRequestId;
    private int unitId;
    private int tableNumber;
    private Integer customerId;
    private String contact;
    private String customerName;
    private Integer totalOrders;
    private Integer totalAmount;
    private TableStatus tableStatus;

    private Integer noOfPax;

    private String customerType;
    private List<TableOrder> orders = new ArrayList<>();

    private List<String> generatedOrderIds = new ArrayList<>();

    private Integer settledOrderId;

    private boolean serviceChargeApplied;

    private boolean printBillAllowed;

    private String offerCode;

    private Integer pointsRedeemed;

    public UnitTableMapping(int unitId, int tableNumber, TableStatus status) {
        super();
        this.unitId = unitId;
        this.tableNumber = tableNumber;
        this.tableStatus = status;
    }
    public UnitTableMapping(int tableRequestId, int unitId, int tableNumber, Integer customerId, String customerName,
                            Integer totalOrders, Integer totalAmount, TableStatus status, String contact ,
                            Integer noOfPax, String customerType,Integer settledOrderId,boolean serviceChargeApplied,
                            boolean printBillAllowed,String offerCode,Integer pointsRedeemed) {
        super();
        this.tableRequestId = tableRequestId;
        this.unitId = unitId;
        this.tableNumber = tableNumber;
        this.customerId = customerId;
        this.customerName = customerName;
        this.totalOrders = totalOrders;
        this.totalAmount = totalAmount;
        this.tableStatus = status;
        this.contact = contact;
        this.noOfPax = noOfPax;
        this.customerType = customerType;
        this.settledOrderId = settledOrderId;
        this.serviceChargeApplied = serviceChargeApplied;
        this.printBillAllowed = Objects.isNull(settledOrderId) && printBillAllowed;
        this.offerCode = offerCode;
        this.pointsRedeemed = pointsRedeemed;
    }

    @Override
    public int compareTo(UnitTableMapping o) {
        return Integer.valueOf(this.getTableNumber()).compareTo(o.getTableNumber());
    }

}
