package com.stpl.tech.kettle.domain.model;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.master.domain.model.Pair;

import lombok.extern.log4j.Log4j2;



@Log4j2
@Service
public class OrderMappingCache {

    private static final Map<OrderUnitMapping, Pair<String, String>> randomNumberMapping = new HashMap<>();

    private synchronized boolean checkExists(OrderUnitMapping mapping, String randomString) {

        return randomNumberMapping.containsKey(mapping) && randomString.equals(randomNumberMapping.get(mapping).getKey());

    }

    public synchronized void add(OrderUnitMapping mapping, String randomString) throws DataUpdationException {
        log.info(String.format("Adding order request for %s with random string %s", mapping, randomString));
        if (checkExists(mapping, randomString)) {
            throw new DataUpdationException(String.format(
                    "Tried to create a duplicate order request for %s with random string %s", mapping, randomString));
        }
        randomNumberMapping.put(mapping, new Pair<String, String>(randomString, null));
    }

    public synchronized String get(OrderUnitMapping mapping, String randomString){
        log.info(String.format("Get orderid for %s with random string %s", mapping, randomString));
        return randomNumberMapping.get(mapping).getValue();
    }

    public synchronized void setGeneratedOrderId(OrderUnitMapping mapping, String randomString, String generatedOrderId){
        log.info(String.format("Setting order id %s for %s with random string %s", generatedOrderId, mapping,
                randomString));
        if (randomNumberMapping.containsKey(mapping)
                && randomString.equals(randomNumberMapping.get(mapping).getKey())) {
            randomNumberMapping.get(mapping).setValue(generatedOrderId);
        }
    }

}

