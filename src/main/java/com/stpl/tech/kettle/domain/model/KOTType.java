package com.stpl.tech.kettle.domain.model;

public enum KOTType {
	HOT(5, "Hot Beverages"), COLD(6, "Cold Beverages"), FOOD(7, "Food Items"), COMBOS(8, "Combos"),
	BAKERY(10, "<PERSON><PERSON>"), MERCHANDISE(9, "Merchandise"), CL<PERSON><PERSON>ED(-1, "Charity,Book-Wastage");

	private final int id;

	private final String description;

	private KOTType(int id, String description) {
		this.id = id;
		this.description = description;
	}

	public int getId() {
		return id;
	}

	public String getDescription() {
		return description;
	}
}
