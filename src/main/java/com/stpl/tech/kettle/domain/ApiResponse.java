package com.stpl.tech.kettle.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Getter
@Setter
@AllArgsConstructor
public class ApiResponse {
    private String message;
    private Object body;
    private int statusCode;
    private HttpStatus status;


    public ApiResponse(Object body){
        this.body = body;
        this.statusCode = HttpStatus.OK.value();
        this.status = HttpStatus.OK;
    }
}
