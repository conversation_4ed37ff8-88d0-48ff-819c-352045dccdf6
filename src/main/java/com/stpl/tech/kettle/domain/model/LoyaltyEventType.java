package com.stpl.tech.kettle.domain.model;

public enum LoyaltyEventType {
	MIGRATION("Addition"), CONTACT_NUMBER_VERIFICATION("Verification"), EMAIL_VERIFICATION("Verification"),
	REGULAR_REDEMPTION_VERIFICATION("Redemption"), REGULAR_REDEMPTION("Redemption"), OFFER_REDEMPTION("Redemption"),
	OUTLET_VISIT("Addition"), OUTLET_VISIT_NO_ADDITION("No Addition"), ONLINE_REDEMTION("Redemption"),
	ONLINE_ORDERING("Addition"), EXPIRATION("Expiration"), TABLE_ORDER("No Addition"), 	WALLET_RECHARGE("Subtraction"),
	LOYALTY_GIFTING(""),LOYALTY_OFFER("Loyalty Offer");

	private final String type;

	private LoyaltyEventType(String type) {
		this.type = type;
	}

	public String getType() {
		return type;
	}
}
