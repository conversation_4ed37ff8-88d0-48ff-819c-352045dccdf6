package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderDelayReasonData implements Serializable {

    private static final long serialVersionUID = 5568060066969372607L;
    private Integer id;

    private Integer orderId;

    private String partnerOrderId;

    private String partnerOrderName;

    private Date billingServerTime;

    private String riderName;

    private String riderContact;

    private String riderDelayReason;

    private String optionalRiderDelay;

    private Integer riderDelayUpdatedBy;

    private String riderDelayUpdatedByName;
    private String riderDelaySource;

    private Date riderDelayUpdatedAt;

    private Integer unitId;

    private String unitName;

    private String cafeDelayReason;

    private String optionalCafeDelay;

    private Integer cafeDelayUpdatedBy;
    private String cafeDelayUpdatedByName;
    private String cafeDelaySource;
    private Date cafeDelayUpdatedAt;
}
