package com.stpl.tech.kettle.domain.model;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "states",
    "transitions"
})
@XmlRootElement(name = "TransitionStateData")
public class TransitionStateData {
    @XmlElement(required = true)
	private List<StateData> states;
    @XmlElement(required = true)
	private List<TransitionState> transitions;

	public List<StateData> getStates() {
		if (states == null) {
			states = new ArrayList<StateData>();
		}
		return this.states;
	}

	public List<TransitionState> getTransitions() {
		if (transitions == null) {
			transitions = new ArrayList<TransitionState>();
		}
		return this.transitions;
	}
}
