package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TableSettlement {

    private int tableRequestId;
    private int unitId;
    private int tableNumber;

    protected List<Settlement> settlements;
    private OrderDomain walletOrderDomain;
    private OrderDomain orderInfo;

    private List<Integer> tableOrderIds = new ArrayList<>();
    private String serviceChargeApplied;
}
