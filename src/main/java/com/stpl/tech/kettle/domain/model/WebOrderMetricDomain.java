package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.CustomJsonDateDeserializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class WebOrderMetricDomain {
    private Integer customerId;
    private String customerName;
    private Integer unitId;
    private String unitName;
    private Integer orderId;
    private String cartId;
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date orderTime;
    private String offerCode;
    private BigDecimal discountAmount;
    private BigDecimal totalAmount;
    private Integer tableNumber;
    private String scanType;
}
