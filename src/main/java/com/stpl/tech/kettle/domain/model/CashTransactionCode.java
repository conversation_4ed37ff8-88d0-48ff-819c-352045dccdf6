package com.stpl.tech.kettle.domain.model;

public enum CashTransactionCode {
	// transaction for referrer
	SIGNUP_REFERRER,
	// transaction for referent
	SIGNUP_REFERENT,
	// Refer<PERSON>'s first transaction
	REFERENT_TRANSACTION,
	// cash bonus availed on successful 5 referrals
	CASH_BONUS_ON_SUCCESS_5,
	// cash bonus availed on successful 10 referrals
	CASH_BONUS_ON_SUCCESS_10,
	// cash bonus availed on successful 15 referrals
	CASH_BONUS_ON_SUCCESS_15,
	// cash bonus availed on successful 20 referrals
	CASH_BONUS_ON_SUCCESS_20,
	// Discount Applied on order
	ORDER_DISCOUNT,
	// Points expired after validity expiration
	VALIDITY_EXPIRED, CBDAY_CASH_BONUS,
	// Points awarded for cashback
	CASHBACK, CLM_CASH_BONUS;
}
