package com.stpl.tech.kettle.domain.model;


import java.io.Serializable;

public class GyftrVoucherAction implements Serializable{

    private static final long serialVersionUID = -5139021213140255888L;
    String VOUCHERNUMBER;
    String VOUCHERTYPE;
    String PRODUCTNAME;
    String PRODUCTCODE;
    String STATUS;
    String VALUE;

    @Override
    public String toString() {
        return "GyftrVoucherAction [VOUCHERNUMBER=" + VOUCHERNUMBER + ", VOUCHERTYPE=" + VOUCHERTYPE + ", PRODUCTNAME="
                + PRODUCTNAME + ", PRODUCTCODE=" + PRODUCTCODE + ", STATUS=" + STATUS + ", VALUE=" + VALUE + "]";
    }

    public String getVOUCHERNUMBER() {
        return VOUCHERNUMBER;
    }

    public void setVOUCHERNUMBER(String vOUCHERNUMBER) {
        VOUCHERNUMBER = vOUCHERNUMBER;
    }

    public String getVOUCHERTYPE() {
        return VOUCHERTYPE;
    }

    public void setVOUCHERTYPE(String vOUCHERTYPE) {
        VOUCHERTYPE = vOUCHERTYPE;
    }

    public String getPRODUCTNAME() {
        return PRODUCTNAME;
    }

    public void setPRODUCTNAME(String pRODUCTNAME) {
        PRODUCTNAME = pRODUCTNAME;
    }

    public String getPRODUCTCODE() {
        return PRODUCTCODE;
    }

    public void setPRODUCTCODE(String pRODUCTCODE) {
        PRODUCTCODE = pRODUCTCODE;
    }

    public String getSTATUS() {
        return STATUS;
    }

    public void setSTATUS(String sTATUS) {
        STATUS = sTATUS;
    }

    public String getVALUE() {
        return VALUE;
    }

    public void setVALUE(String vALUE) {
        VALUE = vALUE;
    }

}

