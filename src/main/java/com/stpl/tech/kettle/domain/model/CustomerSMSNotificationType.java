package com.stpl.tech.kettle.domain.model;

import org.apache.commons.lang3.text.WordUtils;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.data.kettle.DeliveryDetail;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.Constants.SMSType;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.notification.Messenger;

public enum CustomerSMSNotificationType {
	CUSTOMER_LOGIN_MESSENGER(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String customerName = "Chai Lover";
			if (token instanceof SMSToken) {
				SMSToken sms = (SMSToken) token;
				return String.format(
						"Hi %s,\n" +
								"OTP for Chaayos account login is %s. Download Chaayos app for hassle free ordering.",
						customerName,sms.getToken());
//                return String.format(
//                    "%s is your Chaayos OTP. Introducing Contactless Ordering! Download App - \n%s",
//                    sms.getToken(), sms.getHashKey());
			} else {
				return String.format(
						"Hi %s,\n" +
								"OTP for Chaayos account login is %s. Download Chaayos app for hassle free ordering.",
						customerName,token);
//                return String.format(
//                    "%s is your Chaayos OTP. Introducing Contactless Ordering! Download App - Android & iOS",
//                    token);
			}

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.OTP;
		}
	}, false), OTP_MESSENGER(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String customerName = "Chai Lover";
			if (token instanceof SMSToken) {
				SMSToken sms = (SMSToken) token;
				return String.format(
						"Hi %s,\n" +
								"OTP for Chaayos account login is %s. Download Chaayos app for hassle free ordering.",
						customerName,sms.getToken());
//            return String.format(
//                    "Hi, Chai Lover %s is your Chaayos OTP. Introducing Contactless Ordering! Download App - Android & iOS\n%s",
//                    sms.getToken(), sms.getHashKey());
			} else {
				return String.format(
						"Hi %s,\n" +
								"OTP for Chaayos account login is %s. Download Chaayos app for hassle free ordering.",
						customerName,token);
//                return String.format(
//                    "%s is your Chaayos OTP. Introducing Contactless Ordering! Download App - Android & iOS",
//                    token);
			}

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.OTP;
		}
	}, false), FEEDBACK_MESSAGE(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof FeedbackEventInfo) {
				FeedbackEventInfo info = (FeedbackEventInfo) event;
				if (UnitCategory.COD.name().equals(info.getOrderSource())) {
					return String.format(
							"Hi %s, rate your last Chaayos Delivery and help us to make your experience flawless. Click %s",
							info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
							info.getEventShortUrl());
				} else {
					return String.format(
							"Hi %s, rate your last visit to Chaayos %s and help us to make your experience flawless. Click %s",
							info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
							info.getUnitName(), info.getEventShortUrl());
				}

			}
			return null;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), NPS_MESSAGE(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof FeedbackEventInfo) {
				FeedbackEventInfo info = (FeedbackEventInfo) event;
				if (UnitCategory.COD.name().equals(info.getOrderSource())) {
					return String.format(
							"Hi %s, rate your %s experience & help us to make it flawless for you. Click %s\nFrom Team %s!",
							info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
							info.getBrand().getBrandName(), info.getEventShortUrl(), info.getBrand().getBrandName());
				} else {
					return String.format(
							"Hi %s, rate your %s experience & help us to make it flawless for you. Click %s\nFrom Team %s!",
							info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
							info.getBrand().getBrandName(), info.getEventShortUrl(), info.getBrand().getBrandName());
				}

			}
			return null;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), NPS_OFFER_MESSAGE(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof FeedbackEventInfo) {
				FeedbackEventInfo info = (FeedbackEventInfo) event;
				if (UnitCategory.COD.name().equals(info.getOrderSource())) {
					return String.format(
							"Hi %s, we had like to know about your last %s order. Rate your experience to help us improve our service. Click %s",
							info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
							info.getBrand().getBrandName(), info.getEventShortUrl());
				} else {
					return String.format(
							"Hi %s, we had like to know about your last %s order. Rate your experience to help us improve our service. Click %s",
							info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
							info.getBrand().getBrandName(), info.getEventShortUrl());
				}

			}
			return null;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), ORDER_FEEDBACK_MESSAGE(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof FeedbackEventInfo) {
				FeedbackEventInfo info = (FeedbackEventInfo) event;
				if (UnitCategory.COD.name().equals(info.getOrderSource())) {
					return String.format(
							"Hi %s, We would love to understand your experience with Chaayos. Just spare your 2-3 min to rate us & share your feedback. It will help us bring perfection to you. Visit %s",
							info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
							info.getEventShortUrl());
				} else {
					return String.format(
							"Hi %s, We would love to understand your experience with Chaayos. Just spare your 2-3 min to rate us & share your feedback. It will help us bring perfection to you. Visit %s",
							info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
							info.getEventShortUrl());
				}

			}
			return null;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CHAAYOS_SUBSCRIPTION(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof SubscriptionViewData) {
				SubscriptionViewData info = (SubscriptionViewData) event;
				return String.format(
						"Hi %s, Thank you for subscribing to %s Membership, You will get %s across all Dine-In orders for %s Days valid till %s. -CHAAYOS",
						info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
						info.getSubscriptionName(), info.getOfferDescription(), info.getValidityDays(),
						AppUtils.getDateInMonth(info.getPlanEndDate()));
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CHAAYOS_SUBSCRIPTION_REMINDER(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof SubscriptionViewData) {
				SubscriptionViewData info = (SubscriptionViewData) event;
				return String.format(
						"Hi %s, You've missed %s days of relaxation! Don't miss on your benefits. Use your Chaayos %s & get %s on all orders today. Start saving now. Visit today.",
						info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
						info.getNThDay().toString(), info.getSubscriptionName(), info.getOfferDescription());
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CHAAYOS_SUBSCRIPTION_EXPIRY_REMINDER(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof SubscriptionViewData) {
				SubscriptionViewData info = (SubscriptionViewData) event;
				if (info.getNThDay() == 7) {
					return String.format(
							"Hi %s, Tomorrow never comes! Today is the day for renewing your %s to keep getting %s on all orders at just %s from Chaayos. Renew now - %s",
							info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName())
									: "Chai Lover",
							info.getSubscriptionName(), info.getOfferDescription(), info.getPrice() + "Rs",
							info.getBuyLink());
				} else if (info.getNThDay() == 15) {
					return String.format(
							"Hi %s, Last %s days left to grab %s at Chaayos! Relax today with wide range of chai & your favourite Snacks. Redeem %s now.",
							info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName())
									: "Chai Lover",
							info.getNThDay().toString(), info.getOfferDescription(), info.getSubscriptionName());
				} else if (info.getNThDay() == 30) {
					return String.format(
							"Hi %s, You'll only be a pro for %s days more! To keep relaxing & enjoying %s on all chai & snacks, renew your %s today. -CHAAYOS",
							info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName())
									: "Chai Lover",
							info.getNThDay().toString(), info.getOfferDescription(), info.getSubscriptionName());
				}
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CHAAYOS_SUBSCRIPTION_REMINDER_1(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof SubscriptionViewData) {
				SubscriptionViewData info = (SubscriptionViewData) event;
				return String.format(
						"Hi %s, You've missed %s days of relaxation! Don't miss out on your %s membership benefits & get %s valid till %s. -CHAAYOS",
						info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
						info.getNThDay().toString(), info.getSubscriptionName(), info.getOfferDescription(),
						AppUtils.getDateInMonth(info.getPlanEndDate()));
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CHAAYOS_SUBSCRIPTION_REMINDER_2(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof SubscriptionViewData) {
				SubscriptionViewData info = (SubscriptionViewData) event;
				return String.format(
						"Hi %s, %s days back we all celebrated when you became a %s member. But you still haven't availed your %s benefits. -CHAAYOS",
						info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
						info.getNThDay(), info.getSubscriptionName(), info.getOfferDescription());
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CLM_OFFER(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof NextOffer) {
				NextOffer info = (NextOffer) event;
				return String.format(
						"Hi %s\n\nCongratz! You have won %s OFFER\nVisit Chaayos Cafe Today\n\nUse Code: %s\nExpiry %s",
						info.getFirstName(), info.getText(), info.getOfferCode(), info.getValidityTill());
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CLM_OFFER_DELIVERY(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof NextOffer) {
				NextOffer info = (NextOffer) event;
				return String.format(
						"Hi %s\nCongo! You have won %s offer on %s.\nUse Code %s\nExpiry %s\nOrder Now: %s",
						info.getFirstName(), info.getText(), info.getChannelPartner(), info.getOfferCode(),
						info.getValidityTill(), "chaayos.info/ZT");
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CLM_NEXT_OFFER_FOR_AVAILED(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof NextOffer) {
				NextOffer info = (NextOffer) event;
				return String.format(
						"Hi %s, loved your visits to our Chaayos Cafe. As a note of gratitude, we offer you %s. Redeem by %s. Code %s. T&C apply",
						info.getFirstName(), info.getText(), info.getValidityTill(), info.getOfferCode());
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CLM_DELIVERY_NEXT_OFFER_FOR_AVAILED(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof NextOffer) {
				NextOffer info = (NextOffer) event;
				return String.format(
						"Hi %s, Thank you for ordering online. As a note of gratitude, we offer you %s on %s. Redeem by %s. Code %s. Team Chaayos",
						info.getFirstName(), info.getText(), info.getChannelPartner(), info.getValidityTill(),
						info.getOfferCode());
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CLM_NEXT_OFFER_FOR_NOT_AVAILED(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof NextOffer) {
				NextOffer info = (NextOffer) event;
				return String.format(
						"Hi %s, you just missed the offer! Here's a new one for you: %s Use by %s Code %s at your nearest Chaayos Cafe. T&C Apply",
						info.getFirstName(), info.getText(), info.getValidityTill(), info.getOfferCode());
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CLM_DELIVERY_NEXT_OFFER_FOR_NOT_AVAILED(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof NextOffer) {
				NextOffer info = (NextOffer) event;
				return String.format(
						"Hi %s, you just missed the offer! Here's a new one for you: %s on %s Use by %s Code %s.Order Now! Team Chaayos",
						info.getFirstName(), info.getText(), info.getChannelPartner(), info.getValidityTill(),
						info.getOfferCode());
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CLM_OFFER_REMINDER(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof NextOffer) {
				NextOffer info = (NextOffer) event;
				return String.format(
						"Hi %s, %s days left to avail your chaayos offer of %s before %s. Use code %s Go & grab. T&C Apply",
						info.getFirstName(), info.getDaysLeft(), info.getText(), info.getValidityTill(),
						info.getOfferCode());
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CLM_OFFER_REMINDER_DELIVERY(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof NextOffer) {
				NextOffer info = (NextOffer) event;
				return String.format(
						"Hi %s, %s days left to avail your chaayos offer of %s on %s before %s. Use code %s Go & grab. Team Chaayos",
						info.getFirstName(), info.getDaysLeft(), info.getText(), info.getValidityTill(),
						info.getOfferCode());
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CHAAYOS_SUBSCRIPTION_USED(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof SubscriptionViewData) {
				SubscriptionViewData info = (SubscriptionViewData) event;
				return String.format(
						"Hi %s, Thank you for using %s Membership, You have saved %s till date, and your membership expires on %s. -CHAAYOS\n",
						info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
						info.getSubscriptionName(), "Rs." + info.getTotalSaving().toString(),
						AppUtils.getDateInMonth(info.getPlanEndDate()));
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CHAAYOS_CASH_GIFT(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof ChaayosCashSmsPayload) {
				ChaayosCashSmsPayload info = (ChaayosCashSmsPayload) event;
				return String.format(
						"Hi %s, we're celebrating 10 Years of Chaayos and as a return gift, you win Rs. %s Chaayos cash! Order now to redeem. Valid till %s",
						info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
						info.getAmount(), info.getExpiryDate());
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), RECEIVED_CHAAYOS_CASH(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof ChaayosCashSmsPayload) {
				ChaayosCashSmsPayload info = (ChaayosCashSmsPayload) event;
				return String.format("Hi %s, you have received  Rs. %s Chaayos cash! . Valid till %s",
						info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
						info.getAmount(), info.getExpiryDate());
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), ELABORATED_FEEDBACK_MESSAGE(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof FeedbackEventInfo) {
				FeedbackEventInfo info = (FeedbackEventInfo) event;
				if (UnitCategory.COD.name().equals(info.getOrderSource())) {
					return String.format(
							"Hi %s, you have rated us %s. Please help us understand your feedback for your last Chaayos delivery. Click %s",
							info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
							info.getRating(), info.getEventShortUrl());
				} else {
					return String.format(
							"Hi %s, you have rated us %s. Please help us understand your feedback for your visit to %s. Click %s",
							info.getCustomerName() != null ? WordUtils.capitalizeFully(info.getCustomerName()) : "",
							info.getRating(), info.getUnitName(), info.getEventShortUrl());
				}

			}
			return null;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), MASS_OFFER_WINNER_MESSAGE(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof CustomerOfferData) {
				CustomerOfferData info = (CustomerOfferData) event;
				return String.format(
						"Congratulations! You have won 100 Days of Free Chai at Chaayos. You can redeem this offer at any Chaayos outlet. T&C Apply. Coupon Code: %s , Valid till: %s",
						info.getCouponCode(), info.getValidUntil());
			}
			return null;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), MASS_OFFER_WINNER_MESSAGE_REMINDER(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof CustomerOfferData) {
				CustomerOfferData info = (CustomerOfferData) event;
				return String.format(
						"Chaayos Free Chai Winner: Congratulations %s!\n"
								+ "You have won FREE CHAI FOR A YEAR at Chaayos!\n"
								+ "Use coupon code %s every time you visit us. TnC\n"
								+ "Click on the link to let your friends know\n" + "%s",
						WordUtils.capitalizeFully(info.getCustomerName()), info.getCouponCode(), info.getUrl());
			}
			return null;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), WELCOME_MESSAGE(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String message = String.format(
					"Hi %s, Welcome to Chaayos LoyalTea. Get a Desi Chai FREE on your next trip. For Contactless Ordering Download App https://chaayos.onelink.me/Thm6/wm\nTeam Chaayos!",
					token != null ? WordUtils.capitalizeFully(token.toString().toLowerCase()) : "");
			return message;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), ASYNC_ORDER_SUCCESS_MESSAGE(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String message = String.format(
					"Hi %s, Your order has been placed successfully https://chaayos.onelink.me/Thm6/wm\nTeam Chaayos!",
					token != null ? WordUtils.capitalizeFully(token.toString().toLowerCase()) : "");
			return message;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), ASYNC_ORDER_FAILURE_MESSAGE(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String message = String.format(
					"Hi %s, We failed to place your order, If amount has been deducted it will be refunded back to your account https://chaayos.onelink.me/Thm6/wm\nTeam Chaayos!",
					token != null ? WordUtils.capitalizeFully(token.toString().toLowerCase()) : "");
			return message;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), LOYALTY_REMINDER(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String message = String.format("5 days left! Redeem your FREE desi Chai at your favourite Chaayos cafe");
			return message;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), LOYALTY_REMINDER_2(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String message = String.format("15 days left! Redeem your FREE desi Chai at your favourite Chaayos cafe");
			return message;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), OPT_OUT(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String message = String.format(
					"Dear %s, Your opt-out request at Chaayos has been successfully processed. Thank you. Team Chaayos!",
					token != null ? WordUtils.capitalizeFully(token.toString().toLowerCase()) : "Customer");
			return message;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), SKIPPING_FACE_IT(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String message = String.format(
					"Dear %s, since you have opted out of Chaayos facial recognition, we have not saved any data on your latest visit to Chaayos. Thank you. Team Chaayos!",
					token != null ? WordUtils.capitalizeFully(token.toString().toLowerCase()) : "Customer");
			return message;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), DELIVERY_PROMOTION(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String message = String.format(
					"Hi %s, hope you enjoyed your visit at our cafe. Click www.chaayos.com & get fresh, piping hot Chai & Food Home delivered",
					token != null ? WordUtils.capitalizeFully(token.toString().toLowerCase()) : "");
			return message;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CAFE_PROMOTION(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String message = String.format(
					"Hi %s, Enjoy your 'Meri Wali Chai & sumptuous food menu. Order online @ www.chaayos.com ",
					token != null ? WordUtils.capitalizeFully(token.toString().toLowerCase()) : "");
			return message;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), RIDER_NOTIFICATION(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			// Add message here
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), SUBSCRIPTION_CREATION(new Messenger<Object, String>() {
		public String getMessage(Object object) {
			if (object instanceof Subscription) {
				// Add message here
				return null;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), SUBSCRIPTION_HOLD(new Messenger<Object, String>() {
		public String getMessage(Object object) {
			if (object instanceof Subscription) {
				// Add message here
				return null;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), SUBSCRIPTION_OFF_HOLD(new Messenger<Object, String>() {
		public String getMessage(Object object) {
			if (object instanceof Subscription) {
				// Add message here
				return null;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), SUBSCRIPTION_CANCEL(new Messenger<Object, String>() {
		public String getMessage(Object object) {
			if (object instanceof Subscription) {
				// Add message here
				return null;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), SUBSCRIPTION_EDIT(new Messenger<Object, String>() {
		public String getMessage(Object object) {
			if (object instanceof Subscription) {
				// Add message here
				return null;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), SUBSCRIPTION_ORDER_CANCEL(new Messenger<Object, String>() {
		public String getMessage(Object object) {
			if (object instanceof Subscription) {
				// Add message here
				return null;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), SUBSCRIPTION_ORDER_RESCHEDULE(new Messenger<Object, String>() {
		public String getMessage(Object object) {
			if (object instanceof Subscription) {
				// Add message here
				return null;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), SUBSCRIPTION_ORDER_DELIVERED(new Messenger<Object, String>() {
		public String getMessage(Object object) {
			if (object instanceof Subscription) {
				// Add message here
				return null;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), DELIVERY_ALLOTED_NO_SDP(new Messenger<Object, String>() {
		public String getMessage(Object message) {
			return (String) message;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), DELIVERY_REALLOTMENT_MSG_SDP(new Messenger<Object, String>() {
		public String getMessage(Object event) {
			if (event instanceof DeliveryDetail) {
				DeliveryDetail delivery = (DeliveryDetail) event;
				String message = String.format(
						"Hi %s, your order id %s has been assigned to someone else. Please Ignore the earlier message.",
						delivery.getDeliveryBoyName(), delivery.getGeneratedOrderId());
				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), DELIVERY_ALLOTED_MSG_CUSTOMER(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			if (token instanceof OrderInfo) {
				OrderInfo info = (OrderInfo) token;
				String deliveryBoyName = info.getDeliveryDetails().getDeliveryBoyName();
				if (deliveryBoyName != null) {
					deliveryBoyName = deliveryBoyName.split(" ")[0];
				}
				String message = String.format(
						"Hi %s, Your Chaayos order %s is on its way and you will receive it shortly. To contact our delivery agent %s call at %s\nTeam Chaayos!",
						WordUtils.capitalizeFully(info.getCustomer().getFirstName()),
						info.getOrder().getGenerateOrderId(), WordUtils.capitalizeFully(deliveryBoyName),
						info.getDeliveryDetails().getDeliveryBoyPhoneNum());
				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), FREE_CHAI_DELIVERY(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			if (token instanceof String) {
//                OrderInfo info = (OrderInfo) token;
				String message = String.format(
						"Congratulations! You are one of the lucky people to receive FREE Chaayos Chai Ketli! Fill the form to claim your gift : Chaayos.com/freechaidelivery");
				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), FIRST_VISIT(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			if (token instanceof Customer) {
				Customer customerInfo = (Customer) token;
				String message = String.format(
						"Hi %s! Your 1st visit has earned you a FREE DESI CHAI for your next visit. Download Chaayos app for exclusive offers. chaayos.org/cs_app",
						WordUtils.capitalizeFully(customerInfo.getFirstName()));
				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), OTP_SCREEN(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			if (token instanceof Customer) {
//                OrderInfo info = (OrderInfo) token;
				String message = String.format(
						"Take a break! Feel Relax with Chaayos FREE Chai and Snack. Download app to track FREE Chai and hassle-free Ordering. chaayos.org/cs_app");
				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), PAYMENT_MODE(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			if (token instanceof Customer) {
//                OrderInfo info = (OrderInfo) token;
				String message = "Want perfect Chai time? Order using Chaayos app for seamless order payments. Get 10% extra money on wallet recharge. Download app chaayos.org/cs_app";
				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), REGULAR_CUSTOMER_ZERO_FREE_CHAI(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			if (token instanceof Customer) {
				Customer customerInfo = (Customer) token;
				String message = String.format(
						"Hi %s! Chai time bole to FREE Chai at Chaayos! Download app to track your FREE Chai & visits, special offers & hassle-free Ordering. chaayos.org/cs_app",
						WordUtils.capitalizeFully(customerInfo.getFirstName()));
				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), N_LOYALTEA(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			if (token instanceof Customer) {
				Customer customerInfo = (Customer) token;
				String message = String.format(
						" \"Kitne FREE Chai hai?\" \"Sardar, %s FREE Chai hai!\" Download Chaayos app to redeem your FREE Chai & hassle-free ordering.Download now chaayos.org/cs_app",
						customerInfo.getLoyaltyPoints() / 60);
				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), DELIVERY_CONFIRMATION_MSG_CUSTOMER(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String message = String.format(
					"Your Chaayos order # %s has been delivered.Enjoy your Chai & Food. Visit www.chaayos.com or call 18001202424 for home delivery.",
					token);
			return message;

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), CALLBACK_ASSURED(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			if (token instanceof FeedbackEventInfo) {
				FeedbackEventInfo info = (FeedbackEventInfo) token;
				String domain = info.getBrand().getBrandId().equals(AppConstants.CHAAYOS_BRAND_ID)
						? info.getBrand().getDomain()
						: "";
				String message = String.format(
						"Thank you %s for your valuable feedback. Our team will connect with you within 48hrs. %s",
						info.getCustomerName(), domain);
				return message;

			} else {
				return null;
			}

		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), REFERRAL_SUBSCRIPTION_NOTIFY(new Messenger<Object, String>() {
		public String getMessage(Object object) {
			if (object instanceof ReferralLinkData) {
				ReferralLinkData info = (ReferralLinkData) object;

				String message = String.format("Thanks for visiting Chaayos! Hope you enjoyed your first visit. "
						+ "Now you can Earn %s Chaayos Cash with every successful referral of your family & friends. "
						+ "Your referral code is %s or Click %s to share", info.getAmount(), info.getCode(),
						info.getLink());

				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), REFERRAL_SUCCESS_NOTIFY(new Messenger<Object, String>() {
		public String getMessage(Object object) {
			if (object instanceof ReferralLinkData) {
				ReferralLinkData info = (ReferralLinkData) object;

				String message = String.format(
						"Hi %s, You just earned %s Chaayos Cash as referral bonus. Redeem at any Chaayos Café. Details at https://chaayos.onelink.me/Thm6/rs",
						info.getName(), info.getAmount());

				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), REFERENT_SIGNUP_NOTIFY(new Messenger<Object, String>() { // jisko referal diya
		public String getMessage(Object object) {
			if (object instanceof ReferralLinkData) {
				ReferralLinkData info = (ReferralLinkData) object;

				String message = String.format(
						"Welcome to Chaayos! We have credited %s Chaayos Cash in your account. Visit any Chaayos Cafe to redeem. Download App https://chaayos.onelink.me/Thm6/r",
						info.getAmount());

				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), COVID_19_FAILURE_NOTIFICATION(new Messenger<Object, String>() {
		public String getMessage(Object token) {
			String message = String.format(
					" Dear %s, Thanks for showing interest in Chaayos #giveback. Let us do the right thing by providing free meals to those affected by COVID-19 lockdown. Unfortunately your payment could not be completed. To donate, click here https://cafes.chaayos.com/giveback",
					token != null ? WordUtils.capitalizeFully(token.toString().toLowerCase()) : "Chai Lover");
			return message;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), SIGN_UP_COUPON_NOTIFICATION(new Messenger<Object, String>() {
		public String getMessage(Object object) {
			if (object instanceof CustomerResponse) {
				CustomerResponse info = (CustomerResponse) object;
				String message = String.format(
						"Congratulations %s, You have won a free chai at Chaayos. Use code %s and get your free chai at Chaayos Cafe. T&C* . Valid* for 60 days. App: chaayos.org/dine-in",
						info.getName(), info.getSignUpRefCode());
				return message;
			}
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false), LOYALTY_GIFTING_REMINDER_NOTIFICATION(new Messenger<Object, String>() {
		@Override
		public String getMessage(Object token) {
			if (token instanceof LoyaltyGiftingSMSToken) {
				LoyaltyGiftingSMSToken sms = (LoyaltyGiftingSMSToken) token;
				Integer chai = sms.getTotalTransferedPoints() / 60;
				return String.format(
						"Hi %s, You have received %d free chai gifted by %s. Visit Chaayos today and redeem it from the app chaayos.org/app_download",
						sms.getReceivername(), chai, sms.getSenderName());
			} else {
				return String.format("");
			}
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.TRANSACTIONAL;
		}
	}, false),

	OTP_VIA_IVR(new Messenger<Object, String>() {
		@Override
		public String getMessage(Object object) {
			return null;
		}

		@Override
		public SMSType getSMSType() {
			return SMSType.OPT_VIA_IVR;
		}
	}, false);

	private final Messenger<Object, String> template;
	private final boolean whatsapp;

	private CustomerSMSNotificationType(Messenger<Object, String> template, boolean whatsapp) {
		this.whatsapp = whatsapp;
		this.template = template;
	}

	public boolean isWhatsapp() {
		return whatsapp;
	}
//    private CustomerSMSNotificationType(Messenger<Object, String> template) {
//        this.template = template;
//    }

	public Messenger<Object, String> getTemplate() {
		return template;
	}

	public String getMessage(Object object) {
		return template.getMessage(object);
	}

	public String getMessage(Object object, UnitBasicDetail detail) {
		return template.getMessage(object);
	}
}
