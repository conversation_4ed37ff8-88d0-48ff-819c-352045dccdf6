package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Builder;
import org.springframework.data.annotation.Id;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TaxDetail implements Serializable {

    private static final long serialVersionUID = 5577464341032140963L;
    @Id
    private String _id;

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    protected BigDecimal percentage;

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    protected BigDecimal value;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    protected BigDecimal total;

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    protected BigDecimal taxable;

    protected String type;

    protected String code;

    protected Integer taxId;

}
