package com.stpl.tech.kettle.domain.kettle;

import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WalletSuggestionData implements Serializable {
    @Serial
    private static final long serialVersionUID = 3601369177828503653L;
    private String customerId;
    private Integer amountPayable;
    private String isNewCustomer;
    private Integer latestOrderDayDiff;
    private Integer visitsCount;

    private String storeFormat;
    private Integer dineInFlag;
    private Integer deliveryFlag;
    private Integer daysSinceCustomerAcquired;
    private Integer dineInVisitsCount;
    private Integer visitsCount30Days;
    private Integer visitsCount90Days;
    private Integer netSales;
    private Integer loyaltyPointsBalance;
    private Integer activeSelectMembership;
    private Integer subscriptionExpiryDayDiff;
    private Integer giftCardLastOrderDate;
    private BigDecimal giftCardAmountPurchased;

    private Integer minimumDenomination;
    private Integer minimumDenominationCount;
    private Integer walletRecommendationBaseValue;
    private Double extraValueMultiplier;
    private String profile;
}