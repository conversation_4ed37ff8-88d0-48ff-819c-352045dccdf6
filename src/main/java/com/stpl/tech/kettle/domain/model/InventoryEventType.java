package com.stpl.tech.kettle.domain.model;

public enum InventoryEventType {
	STOCK_IN("STOCK_IN"),
    WASTAGE("WASTAGE"),
    SNAPSHOT("SNAPSHOT"),
    TRANSFER_OUT("TRANSFER_OUT"),
    UPDATE("UPDATE"),
    STOC<PERSON>_OUT("STOCK_OUT"),
    ORDER_PUNCH("ORDER_PUNCH"),
    CAFE_WASTAGE("CAFE_WASTAGE");
	
    private final String value;
    
	InventoryEventType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static InventoryEventType fromValue(String v) {
        for (InventoryEventType c: InventoryEventType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }
}
