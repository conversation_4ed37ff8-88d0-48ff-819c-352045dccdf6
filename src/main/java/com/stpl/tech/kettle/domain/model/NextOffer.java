package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NextOffer implements Serializable {

	private static final long serialVersionUID = -4649921243503268976L;

    private Integer brandId;
    private Integer customerCampaignOfferDetailId;
    private String contactNumber;
    private Integer customerId;
    private String firstName;
    private boolean available;
    private String offerCode;
    private String validityFrom;
    private String validityTill;
    private String text;
    private String customerType;
    private String contentUrl;
    private String notificationType;
    private Boolean isExistingOffer;
    private Integer daysLeft;
    private String channelPartner;
    private Integer channelPartnerId;
    private String couponType;
    private Integer maxUsage;
    private String tnc;


    public NextOffer(String firstName, String text, String validityTill, String offerCode, String channelPartner){
        this.firstName = firstName;
        this.text = text;
        this.validityTill = validityTill;
        this.offerCode = offerCode;
        this.channelPartner = channelPartner;
    }

    public NextOffer(Integer brandId, String contactNumber,Integer customerId,String firstName, boolean available, String offerCode, String validityFrom, String validityTill, String text,
                     String customerType, String contentUrl, String notificationType, String channelPartner,String couponType, Integer channelPartnerId) {
        super();
        this.brandId = brandId;
        this.contactNumber = contactNumber;
        this.customerId = customerId;
        this.firstName = firstName;
        this.available = available;
        this.offerCode = offerCode;
        this.validityFrom = validityFrom;
        this.validityTill = validityTill;
        this.text = text;
        this.customerType = customerType;
        this.contentUrl = contentUrl;
        this.notificationType = notificationType;
        this.channelPartner = channelPartner;
        this.couponType= couponType;
        this.channelPartnerId = channelPartnerId;
    }




    @Override
    public String toString() {
        return "NextOffer{" +
                "brandId=" + brandId +
                ", contactNumber='" + contactNumber + '\'' +
                ", customerId=" + customerId +
                ", firstName='" + firstName + '\'' +
                ", available=" + available +
                ", offerCode='" + offerCode + '\'' +
                ", validityFrom='" + validityFrom + '\'' +
                ", validityTill='" + validityTill + '\'' +
                ", text='" + text + '\'' +
                ", customerType='" + customerType + '\'' +
                ", contentUrl='" + contentUrl + '\'' +
                ", notificationType='" + notificationType + '\'' +
                ", isExistingOffer=" + isExistingOffer +
                ", daysLeft=" + daysLeft +
                ", channelPartner='" + channelPartner + '\'' +
                ", channelPartnerId=" + channelPartnerId +
                ", couponType='" + couponType + '\'' +
                '}';
    }
}

