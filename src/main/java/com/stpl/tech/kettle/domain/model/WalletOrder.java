package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class WalletOrder {

	private BigDecimal rechargeAmount;
	private BigDecimal suggestedAmount;
	private BigDecimal extraAmount;
	private BigDecimal offerAmount;// ON SUGGESTED AMOUNT
	private boolean deductFromWallet;
	private BigDecimal customerPayableAmount;
}
