package com.stpl.tech.kettle.domain.scm;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class WastageEvent {

    protected Integer wastageId;

    protected int unitId;
    protected int generatedBy;
    protected Integer linkedKettleId;
    protected String linkedKettleIdType;
    protected String kettleReason;
    protected String grReason;
    protected Integer linkedGrId;
    protected Date businessDate;
    protected Date generationTime;

    protected String status;
    protected List<WastageData> items;

    protected String type;
    protected List<String> errors;

    public Integer getWastageId() {
        return wastageId;
    }

    public void setWastageId(Integer value) {
        this.wastageId = value;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setGeneratedBy(int value) {
        this.generatedBy = value;
    }

    public int getGeneratedBy() {
        return generatedBy;
    }

    public void setUnitId(int value) {
        this.unitId = value;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date value) {
        this.businessDate = value;
    }

    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date value) {
        this.generationTime = value;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String value) {
        this.status = value;
    }

    public Integer getLinkedGrId() {
        return linkedGrId;
    }

    public void setLinkedGrId(Integer linkedGrId) {
        this.linkedGrId = linkedGrId;
    }

    public Integer getLinkedKettleId() {
        return linkedKettleId;
    }

    public void setLinkedKettleId(Integer linkedKettleId) {
        this.linkedKettleId = linkedKettleId;
    }

    public List<WastageData> getItems() {
        if (items == null) {
            items = new ArrayList<>();
        }
        return items;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLinkedKettleIdType() {
        return linkedKettleIdType;
    }

    public void setLinkedKettleIdType(String linkedKettleIdType) {
        this.linkedKettleIdType = linkedKettleIdType;
    }

    public String getKettleReason() {
        return kettleReason;
    }

    public void setKettleReason(String kettleReason) {
        this.kettleReason = kettleReason;
    }

    public String getGrReason() {
        return grReason;
    }

    public void setGrReason(String grReason) {
        this.grReason = grReason;
    }

    public List<String> getErrors() {
        return errors;
    }

    public void setErrors(List<String> errors) {
        this.errors = errors;
    }

}
