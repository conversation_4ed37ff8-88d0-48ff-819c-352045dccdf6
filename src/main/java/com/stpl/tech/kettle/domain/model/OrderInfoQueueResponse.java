package com.stpl.tech.kettle.domain.model;

import lombok.*;


import java.io.Serializable;

import com.stpl.tech.kettle.core.notification.OrderInfo;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class OrderInfoQueueResponse implements Serializable {

    private static final long serialVersionUID = 7147292731501020524L;
    private Boolean isRoutedToAssembly;
    private OrderInfo orderInfo;
}
