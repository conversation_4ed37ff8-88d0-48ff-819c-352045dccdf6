package com.stpl.tech.kettle.domain.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TableOrderItemStatus {
    protected Integer orderItemId;

    protected Integer orderId;

    protected String status;
    protected String itemCreationTime;

    protected String inProcessTime;

    protected String completionTime;

    protected Integer totalProcessingTime;

    protected String serveTime;
    protected Integer servedBy;



}
