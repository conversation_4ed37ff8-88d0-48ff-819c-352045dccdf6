package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;

import com.stpl.tech.master.domain.model.IdCodeName;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrderFeedbackMetadata implements Serializable {

	private static final long serialVersionUID = 3793505623542560434L;

	private Integer feedbackId;
	private Integer feedbackEventId;
	private int customerId;
	private IdCodeName feedbackAndReceiptDetails;
	private String feedbackUrl;
}
