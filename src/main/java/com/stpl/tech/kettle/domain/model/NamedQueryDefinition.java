package com.stpl.tech.kettle.domain.model;

public enum NamedQueryDefinition {
    ORDER_FREQUENCY_FOR_OFFER("SELECT\n" +
            "    od.CUSTOMER_ID customerId,\n" +
            "    MAX(BILLING_SERVER_TIME) lastOrderTime,\n" +
            "    SUM(CASE\n" +
            "        WHEN\n" +
            "            od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE\n" +
            "                    WHEN\n" +
            "                        HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5\n" +
            "                    THEN\n" +
            "                        SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),\n" +
            "                            1)\n" +
            "                    ELSE CURRENT_DATE\n" +
            "                END),\n" +
            "                INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')\n" +
            "        THEN\n" +
            "            1\n" +
            "        ELSE 0\n" +
            "    END) orderToday,\n" +
            "    SUM(CASE\n" +
            "        WHEN\n" +
            "            od.BILLING_SERVER_TIME > :lastTimestamp \n" +
            "        THEN\n" +
            "            1\n" +
            "        ELSE 0\n" +
            "    END) orderInLastHour\n" +
            "FROM\n" +
            "    ORDER_DETAIL od\n" +
            "WHERE\n" +
            "    od.CUSTOMER_ID = :customer\n" +
            "    and od.OFFER_CODE = :code\n" +
            "    and (od.BUSINESS_DATE IS NULL OR od.BUSINESS_DATE > :businessDate)\n" +
            "    and od.ORDER_STATUS <> 'CANCELLED'\n" +
            "GROUP BY od.CUSTOMER_ID");

    public final String query;


    private NamedQueryDefinition(String query) {
        this.query = query;
    }


    public String getQuery() {
        return query;
    }
}
