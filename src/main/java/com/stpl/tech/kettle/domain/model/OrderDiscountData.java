package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrderDiscountData implements Serializable {

	private static final long serialVersionUID = 4778973176476607094L;

	@Id
	private String _id;

	protected String partnerName;

	protected String discountName;

	protected String discountType;

	protected String discountCategory;

	protected Float discountValue;

	protected Float discountAmount;

	protected Boolean discountIsTaxed;

	protected Float discountAppliedOn;

	protected String voucherCode;

	protected String isPartnerDiscount;

}
