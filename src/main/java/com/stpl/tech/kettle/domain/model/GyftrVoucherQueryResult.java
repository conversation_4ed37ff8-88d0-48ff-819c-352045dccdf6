package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import lombok.Getter;
import lombok.Setter;

@JsonPropertyOrder({ "DurationEndDate", "DurationStartDate", "ErrorCode", "ErrorMsg", "LastConsumedDate",
        "LastConsumedShopcode", "Message", "ProductCode", "ProductName", "ResultType", "Status", "Value",
        "VoucherNumber", "VoucherType" })
@Getter
@Setter
public class GyftrVoucherQueryResult implements Serializable {
    
    private static final long serialVersionUID = 1065995386221083185L;


    @JsonProperty("DurationEndDate")
    private String endDate;
    @JsonProperty("DurationStartDate")
    private String startDate;
    @JsonProperty("ErrorCode")
    private String errorCode;
    @JsonProperty("ErrorMsg")
    private String erroMsg;
    @JsonProperty("LastConsumedDate")
    private String lastConsumedDate;
    @JsonProperty("LastConsumedShopCode")
    private String lastConsumedShopCode;
    @JsonProperty("Message")
    private String message;
    @JsonProperty("ProductCode")
    private String productCode;
    @JsonProperty("ProductName")
    private String productName;
    @JsonProperty("ResultType")
    private String resultType = null;
    @JsonProperty("Status")
    private String status;
    @JsonProperty("Value")
    private String value;
    @JsonProperty("VoucherNumber")
    private String voucherNumber;
    @JsonProperty("VoucherType")
    private String voucherType;

    @Override
    public String toString() {
        return "GyftrVoucherQueryResult [endDate=" + endDate + ", startDate=" + startDate + ", errorCode=" + errorCode
                + ", erroMsg=" + erroMsg + ", lastConsumedDate=" + lastConsumedDate + ", lastConsumedShopCode="
                + lastConsumedShopCode + ", message=" + message + ", productCode=" + productCode + ", productName="
                + productName + ", resultType=" + resultType + ", status=" + status + ", value=" + value
                + ", voucherNumber=" + voucherNumber + ", voucherType=" + voucherType + "]";
    }

}

