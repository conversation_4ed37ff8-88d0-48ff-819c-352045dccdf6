package com.stpl.tech.kettle.domain.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WalletSuggestionCustomerInfo implements Serializable {


    @Serial
    private static final long serialVersionUID = -7536292633771430433L;
    private String customerId;
    private String brandId;
    private String  customerPayableAmount;
}
