package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.master.domain.model.Brand;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackEventInfo {

    private Integer feedbackEventId;
    private String eventStatus;
    private String eventSource;
    private String eventLongUrl;
    private String eventShortUrl;
    private String eventShortUrlId;
    private Date eventGenerationTime;
    private Date eventNotificationTime;
    private Date eventTriggerTime;
    private Date eventCompletionTime;
    private Integer feedbackId;
    private int customerId;
    private String contactNumber;
    private String emailId;
    private String orderSource;
    private Integer unitId;
    private String unitName;
    private Integer orderId;
    private Integer rating;
    private String customerName;
    private FeedbackEventType type;
    private Brand brand;

}

