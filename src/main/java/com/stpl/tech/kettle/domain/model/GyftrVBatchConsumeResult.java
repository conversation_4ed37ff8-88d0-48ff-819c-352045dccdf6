package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GyftrVBatchConsumeResult implements Serializable {

	private static final long serialVersionUID = -4934049061600189450L;

	String AuthorizationCode;
	String ErrorCode;
	String ErrorMsg;
	String FailedVoucherNumber;
	String Message;
	String ResultType;
	String LastConsumedShopCode;
	String LastConsumedDate;
	List<GyftrVoucherAction> VOUCHERACTION = new ArrayList<>();

	@Override
	public String toString() {
		return "GyftrVBatchConsumeResult [AuthorizationCode=" + AuthorizationCode + ", ErrorCode=" + ErrorCode
				+ ", ErrorMsg=" + ErrorMsg + ", FailedVoucherNumber=" + FailedVoucherNumber + ", Message=" + Message
				+ ", ResultType=" + ResultType + ", LastConsumedShopCode=" + LastConsumedShopCode
				+ ", LastConsumedDate=" + LastConsumedDate + ", VOUCHERACTION=" + VOUCHERACTION + "]";
	}

}
