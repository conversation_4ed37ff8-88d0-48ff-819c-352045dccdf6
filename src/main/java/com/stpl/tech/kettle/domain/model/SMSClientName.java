package com.stpl.tech.kettle.domain.model;

public enum SMSClientName {

    SOLUTION_INFINI("SOLUTIONS_INFINI"),
    SMS_GUPSHUP("SMS_GUPSHUP");
    private final String value;

    SMSClientName(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static SMSClientName fromValue(String v) {
        for (SMSClientName c: SMSClientName.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
