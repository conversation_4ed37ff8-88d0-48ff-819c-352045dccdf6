package com.stpl.tech.kettle.domain.model;

import java.util.List;
import java.util.Objects;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class OrderFetchStrategy {

	private boolean fetchSettlement;
	private boolean fetchItems;
	private boolean fetchTaxes;
	private boolean fetchPrintDetails;
	private boolean onlyDelivery;
	private boolean donotFetchCustomerAddress;
	private List<OrderStatus> orderStatuses;
	private int terminalId;
	private boolean pageable;
	private Integer start;
	private Integer batchSize;

	
	public OrderFetchStrategy(boolean fetchSettlement, boolean fetchItems, boolean fetchTaxes,
			boolean fetchPrintDetails, boolean onlyDelivery, List<OrderStatus> orderStatuses, int terminalId, boolean donotFetchCustomerAddress) {
		super();
		this.fetchSettlement = fetchSettlement;
		this.fetchItems = fetchItems;
		this.fetchTaxes = fetchTaxes;
		this.fetchPrintDetails = fetchPrintDetails;
		this.onlyDelivery = onlyDelivery;
		this.orderStatuses = orderStatuses;
		this.terminalId = terminalId;
		this.donotFetchCustomerAddress = donotFetchCustomerAddress;
	}

	public OrderFetchStrategy(boolean fetchSettlement, boolean fetchItems, boolean fetchTaxes,
							  boolean fetchPrintDetails,boolean onlyDelivery, List<OrderStatus> orderStatuses,int terminalId,boolean donotFetchCustomerAddress,
							  boolean pageable, Integer start,Integer batchSize){
		super();
		this.fetchSettlement = fetchSettlement;
		this.fetchItems = fetchItems;
		this.fetchTaxes = fetchTaxes;
		this.fetchPrintDetails = fetchPrintDetails;
		this.onlyDelivery = onlyDelivery;
		this.orderStatuses = orderStatuses;
		this.terminalId = terminalId;
		this.donotFetchCustomerAddress = donotFetchCustomerAddress;
		this.pageable = pageable;
		this.start = Objects.isNull(start) ? 1 : start;
		this.batchSize = Objects.isNull(batchSize) ? 50 : batchSize;
	}

}
