package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.data.annotation.Id;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;

public class TransactionDetail implements Serializable {

	private static final long serialVersionUID = -3256468249758673855L;
	@Id
	private String _id;

	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal saleAmount;

	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal totalAmount;

	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal taxableAmount;

	protected DiscountDetail discountDetail;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal paidAmount;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal roundOffValue;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal collectionAmount;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal savings;

	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal tax;
	protected BigDecimal collectionTax;
	protected List<TaxDetail> taxes;

	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal serviceCharge;

	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal serviceChargePercent;

	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal serviceTaxAmount;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public BigDecimal getSaleAmount() {
		return saleAmount;
	}

	public void setSaleAmount(BigDecimal value) {
		this.saleAmount = value;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal value) {
		this.totalAmount = value;
	}

	public BigDecimal getTaxableAmount() {
		return taxableAmount;
	}

	public void setTaxableAmount(BigDecimal value) {
		this.taxableAmount = value;
	}

	public DiscountDetail getDiscountDetail() {
		return discountDetail;
	}

	public void setDiscountDetail(DiscountDetail value) {
		this.discountDetail = value;
	}

	public BigDecimal getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(BigDecimal value) {
		this.paidAmount = value;
	}

	public BigDecimal getRoundOffValue() {
		return roundOffValue;
	}

	public void setRoundOffValue(BigDecimal value) {
		this.roundOffValue = value;
	}

	public BigDecimal getSavings() {
		return savings;
	}

	public void setSavings(BigDecimal value) {
		this.savings = value;
	}

	public BigDecimal getTax() {
		return tax;
	}

	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	public BigDecimal getCollectionTax() {
		return collectionTax;
	}

	public void setCollectionTax(BigDecimal collectionTax) {
		this.collectionTax = collectionTax;
	}

	public List<TaxDetail> getTaxes() {
		if (Objects.isNull(taxes)) {
			taxes = new ArrayList<>();
		}
		return taxes;
	}

	public void setTaxes(List<TaxDetail> taxes) {
		this.taxes = taxes;
	}

	public BigDecimal getCollectionAmount() {
		return collectionAmount;
	}

	public void setCollectionAmount(BigDecimal collectionAmount) {
		this.collectionAmount = collectionAmount;
	}

	public void setServiceCharge(BigDecimal serviceCharge) {
		this.serviceCharge = serviceCharge;
	}

	public BigDecimal getServiceCharge() {
		return serviceCharge;
	}

	public BigDecimal getServiceChargePercent() {
		return serviceChargePercent;
	}

	public void setServiceChargePercent(BigDecimal serviceChargePercent) {
		this.serviceChargePercent = serviceChargePercent;
	}

	public BigDecimal getServiceTaxAmount() {
		return serviceTaxAmount;
	}

	public void setServiceTaxAmount(BigDecimal serviceTaxAmount) {
		this.serviceTaxAmount = serviceTaxAmount;
	}
}
