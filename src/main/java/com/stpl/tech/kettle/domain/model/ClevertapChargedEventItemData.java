package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class ClevertapChargedEventItemData implements Serializable {

	private static final long serialVersionUID = -7651749077666324881L;

	@JsonProperty("ProductId")
	@SerializedName("ProductId")
	Integer productId;
	@JsonProperty("ProductName")
	@SerializedName("ProductName")
	String productName;
	@JsonProperty("ProductType")
	@SerializedName("ProductType")
	String productType;
	@JsonProperty("ProductSubType")
	@SerializedName("ProductSubType")
	String productSubType;
	@JsonProperty("FoodClass")
	@SerializedName("FoodClass")
	String foodClass;
	@JsonProperty("PaxCount")
	@SerializedName("PaxCount")
	Integer paxCount;
	@JsonProperty("ItemTotalAmount")
	@SerializedName("ItemTotalAmount")
	BigDecimal itemTotalAmount;
	@JsonProperty("ItemAmountPaid")
	@SerializedName("ItemAmountPaid")
	BigDecimal itemAmountPaid;
	@JsonProperty("Quantity")
	@SerializedName("Quantity")
	Integer quantity;
}
