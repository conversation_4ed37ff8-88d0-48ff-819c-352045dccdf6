package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Document
public class OrderResponse implements Serializable {
    private static final long serialVersionUID = -7134496687115315120L;
    private String _id;
    private Order order;

    private Customer customer;

    private UnitBasicDetail unit;

    private DeliveryResponse deliveryDetail;

    private Map<NotificationType,Boolean> communicationType;

    public OrderResponse(UnitBasicDetail unit, Order order, Customer customer, DeliveryResponse deliveryDetails,Map<NotificationType,Boolean> communicationType) {
        super();
        this.order = order;
        this.customer = customer;
        this.unit = unit;
        this.deliveryDetail = deliveryDetails;
        this.communicationType = communicationType;
    }


}
