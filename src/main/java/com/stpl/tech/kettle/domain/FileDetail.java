package com.stpl.tech.kettle.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FileDetail {

	private String bucket;

	private String key;

	private String name;

	private String url;

	public FileDetail(String bucket, String key, String url) {
		this.key = key;
		this.url = url;
		this.bucket = bucket;
	}

	@Override
	public String toString() {
		return "FileDetail [bucket=" + bucket + ", key=" + key + ", url=" + url + ", name=" + name + "]";
	}
}
