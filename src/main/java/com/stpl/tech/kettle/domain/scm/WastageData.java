package com.stpl.tech.kettle.domain.scm;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalSixPrecisionDeserializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WastageData {


    protected Integer id;

    @JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
    protected BigDecimal quantity;

    protected Integer productId;
    protected Integer skuId;
    @JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
    protected BigDecimal price;
    @JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
    protected BigDecimal cost;

    protected String comment;
    protected Integer reasonCode;


}

