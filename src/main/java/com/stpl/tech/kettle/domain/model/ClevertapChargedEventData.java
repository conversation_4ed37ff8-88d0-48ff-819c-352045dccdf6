package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class ClevertapChargedEventData implements Serializable {

	private static final long serialVersionUID = 6907937719991423703L;

	@JsonProperty("Pax")
	@SerializedName("Pax")
	private Integer pax;
	@JsonProperty("Items")
	@SerializedName("Items")
	private List<ClevertapChargedEventItemData> items;
	@JsonProperty("UnitId")
	@SerializedName("UnitId")
	private Integer unitId;
	@JsonProperty("BrandId")
	@SerializedName("BrandId")
	private Integer brandId;
	@JsonProperty("OrderId")
	@SerializedName("OrderId")
	private Integer orderId;
	@JsonProperty("UnitName")
	@SerializedName("UnitName")
	private String unitName;
	@JsonProperty("OfferCode")
	@SerializedName("OfferCode")
	private String offerCode;
	@JsonProperty("OrderType")
	@SerializedName("OrderType")
	private String orderType;
	@JsonProperty("CustomerId")
	@SerializedName("CustomerId")
	private Integer customerId;
	@JsonProperty("FoodAmount")
	@SerializedName("FoodAmount")
	private BigDecimal foodAmount;
	@JsonProperty("OfferClass")
	@SerializedName("OfferClass")
	private String offerClass;
	@JsonProperty("OrderSource")
	@SerializedName("OrderSource")
	private String orderSource;
	@JsonProperty("OrderStatus")
	@SerializedName("OrderStatus")
	private String orderStatus;
	@JsonProperty("TotalAmount")
	@SerializedName("TotalAmount")
	private BigDecimal totalAmount;
	@JsonProperty("BusinessDate")
	@SerializedName("BusinessDate")
	private String businessDate;
	@JsonProperty("DiscountRate")
	@SerializedName("DiscountRate")
	private BigDecimal discountRate;
	@JsonProperty("FoodQuantity")
	@SerializedName("FoodQuantity")
	private Integer foodQuantity;
	@JsonProperty("GcPaymentFlag")
	@SerializedName("GcPaymentFlag")
	private Integer gcPaymentFlag;
	@JsonProperty("IsNewCustomer")
	@SerializedName("IsNewCustomer")
	private String isNewCustomer;
	@JsonProperty("OfferDetailId")
	@SerializedName("OfferDetailId")
	private Integer offerDetailId;
	@JsonProperty("TaxableAmount")
	@SerializedName("TaxableAmount")
	private BigDecimal taxableAmount;
	@JsonProperty("TotalQuantity")
	@SerializedName("TotalQuantity")
	private Integer totalQuantity;
	@JsonProperty("BeverageAmount")
	@SerializedName("BeverageAmount")
	private BigDecimal beverageAmount;
	@JsonProperty("DiscountAmount")
	@SerializedName("DiscountAmount")
	private BigDecimal discountAmount;
	@JsonProperty("GcPurchaseFlag")
	@SerializedName("GcPurchaseFlag")
	private Integer gcPurchaseFlag;
	@JsonProperty("OrderFoodClass")
	@SerializedName("OrderFoodClass")
	private String orderFoodClass;
	@JsonProperty("PointsRedeemed")
	@SerializedName("PointsRedeemed")
	private Integer pointsRedeemed;
	@JsonProperty("PreviousOrderId")
	@SerializedName("PreviousOrderId")
	private Integer previousOrderId;
	@JsonProperty("BeverageQuantity")
	@SerializedName("BeverageQuantity")
	private Integer beverageQuantity;
	@JsonProperty("ChannelPartnerId")
	@SerializedName("ChannelPartnerId")
	private Integer channelPartnerId;
	@JsonProperty("BillingServerTime")
	@SerializedName("BillingServerTime")
	private String billingServerTime;
	@JsonProperty("DeliveryPartnerId")
	@SerializedName("DeliveryPartnerId")
	private Integer deliveryPartnerId;
	@JsonProperty("OverallOrderCount")
	@SerializedName("OverallOrderCount")
	private BigInteger overallOrderCount;
	@JsonProperty("OrderSourceOrderCount")
	@SerializedName("OrderSourceOrderCount")
	private BigInteger orderSourceOrderCount;
	@JsonProperty("PreviousOrderTimediff")
	@SerializedName("PreviousOrderTimediff")
	private BigInteger previousOrderTimediff;
	@JsonProperty("PreviousSourceOrderId")
	@SerializedName("PreviousSourceOrderId")
	private Integer previousSourceOrderId;
	@JsonProperty("SubscriptionPurchaseFlag")
	@SerializedName("SubscriptionPurchaseFlag")
	private Integer subscriptionPurchaseFlag;
	@JsonProperty("PreviousBillingServerTime")
	@SerializedName("PreviousBillingServerTime")
	private String previousBillingServerTime;
	@JsonProperty("PreviousSourceOrderTimediff")
	@SerializedName("PreviousSourceOrderTimediff")
	private BigInteger previousSourceOrderTimediff;
	@JsonProperty("PreviousSourceBillingServerTime")
	@SerializedName("PreviousSourceBillingServerTime")
	private String previousSourceBillingServerTime;

	@JsonProperty("SubscriptionName")
	@SerializedName("SubscriptionName")
	private String subscriptionName;
	@JsonProperty("OfferDescription")
	@SerializedName("OfferDescription")
	private String offerDescription;
	@JsonProperty("ValidDays")
	@SerializedName("ValidDays")
	private Integer validDays;
	@JsonProperty("PlanEndDate")
	@SerializedName("PlanEndDate")
	private String planEndDate;
	@JsonProperty("CustomerName")
	@SerializedName("CustomerName")
	private String customerName;
	@JsonProperty("SelectOverallSaving")
	@SerializedName("SelectOverallSaving")
	private Integer selectOverallSaving;
	@JsonProperty("Savings")
	@SerializedName("Savings")
	private BigDecimal selectSavingAmount;
	@JsonProperty("NextOfferText")
	@SerializedName("NextOfferText")
	private String nextOfferText;
	@JsonProperty("ValidityTill")
	@SerializedName("ValidityTill")
	private String validityTill;
	@JsonProperty("ChannelPartner")
	@SerializedName("ChannelPartner")
	private String channelPartner;
	@JsonProperty("LoyalTeaTotalCount")
	@SerializedName("LoyalTeaTotalCount")
	private Integer loyalTeaTotalCount;
	@JsonProperty("TotalLoyalTeaPoint")
	@SerializedName("TotalLoyalTeaPoint")
	private Integer totalLoyalTeaPoint;
	@JsonProperty("LoyalTeaPoints")
	@SerializedName("LoyalTeaPoints")
	private Integer loyalTeaPoints;
	@JsonProperty("LoyalTeaCount")
	@SerializedName("LoyalTeaCount")
	private Integer loyalTeaCount;
	@JsonProperty("OrderAmt")
	@SerializedName("OrderAmt")
	private BigDecimal orderAmt;
	@JsonProperty("EarnedLoyalTeaPoint")
	@SerializedName("EarnedLoyalTeaPoint")
	private Integer earnedLoyalTeaPoint;
	@JsonProperty("SavingText")
	@SerializedName("SavingText")
	private String savingText;
	@JsonProperty("SavingAmt")
	@SerializedName("SavingAmt")
	private BigDecimal savingAmt;
	@JsonProperty("OrderFeedBackUrl")
	@SerializedName("OrderFeedBackUrl")
	private String orderFeedBackUrl;
	@JsonProperty("OrderRecieptUrl")
	@SerializedName("OrderRecieptUrl")
	private String orderRecieptUrl;
	@JsonProperty("IsSubscriptionUsed")
	@SerializedName("IsSubscriptionUsed")
	private Boolean isSubscriptionUsed;
	@JsonProperty("SubscriptionValidityInDays")
	@SerializedName("SubscriptionValidityInDays")
	private Integer subscriptionValidityInDays;

	@JsonProperty("WalletPurchaseAmount")
	@SerializedName("WalletPurchaseAmount")
	private Integer walletPurchaseAmount;
	@JsonProperty("WalletPendingAmount")
	@SerializedName("WalletPendingAmount")
	private Integer walletPendingAmount;
	@JsonProperty("IsWalletPurchased")
	@SerializedName("IsWalletPurchased")
	private Boolean IsWalletPurchased;
	@JsonProperty("WalletExtraAmount")
	@SerializedName("WalletExtraAmount")
	private BigDecimal walletExtraAmount;
	@JsonProperty("ItemCode")
	@SerializedName("ItemCode")
	private String itemCode;
	@JsonProperty("CashPendingAmount")
	@SerializedName("CashPendingAmount")
	private String cashPendingAmount;
	@JsonProperty("VoucherCode")
	@SerializedName("VoucherCode")
	private String voucherCode;
	@JsonProperty("SMSTemplateDate")
	@SerializedName("SMSTemplateDate")
	private String smsTemplateDate;
	@JsonProperty("UsedAmount")
	@SerializedName("UsedAmount")
	private Integer usedAmount;
	@JsonProperty("CashBackAmount")
	@SerializedName("CashBackAmount")
	private BigDecimal cashBackAmount;
	@JsonProperty("CashBackAllotmentStartDate")
	@SerializedName("CashBackAllotmentStartDate")
	private String cashBackAllotmentStartDate;
	@JsonProperty("CashBackAllotmentEndDate")
	@SerializedName("CashBackAllotmentEndDate")
	private String cashBackAllotmentEndDate;
	@JsonProperty("RefundAmount")
	@SerializedName("RefundAmount")
	private Integer refundAmount;
	@JsonProperty("WalletSavingAmount")
	@SerializedName("WalletSavingAmount")
	private String walletSavingAmount;
	@JsonProperty("GenerateOrderId")
	@SerializedName("GenerateOrderId")
	private String generateOrderId;
	@JsonProperty("IsSmsSubscriber")
	@SerializedName("IsSmsSubscriber")
	private boolean isSmsSubscriber;
	@JsonProperty("IsWhatsAppOptIn")
	@SerializedName("IsWhatsAppOptIn")
	private boolean isWhatsAppOptIn;
	@JsonProperty("CustomerContactNumber")
	@SerializedName("CustomerContactNumber")
	private String customerContactNumber;
	@JsonProperty("IsLoyaltyUnlocked")
	@SerializedName("IsLoyaltyUnlocked")
	private Boolean isLoyaltyUnlocked;
	@JsonProperty("WalletUsed")
	@SerializedName("WalletUsed")
	private Boolean walletUsed;
	@JsonProperty("SubscriptionValidityDaysLeft")
	@SerializedName("SubscriptionValidityDaysLeft")
	private Integer subscriptionValidityDaysLeft;
	@JsonProperty("IsSubscriptionPurchased")
	@SerializedName("IsSubscriptionPurchased")
	private Boolean isSubscriptionPurchased;

	@JsonProperty("UnitCity")
	@SerializedName("UnitCity")
	private String unitCity;

	@JsonProperty("UnitRegion")
	@SerializedName("UnitRegion")
	private String unitRegion;

	@JsonProperty("OrderSourceVersion")
	@SerializedName("OrderSourceVersion")
	private String orderSourceVersion;

	@JsonProperty("UnitSubCategory")
	@SerializedName("UnitSubCategory")
	private String unitSubCat;

	@JsonProperty("AbTestData")
	@SerializedName("AbTestData")
	private Integer abTestData;
}
