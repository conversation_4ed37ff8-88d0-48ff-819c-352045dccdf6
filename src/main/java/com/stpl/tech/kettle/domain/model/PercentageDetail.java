package com.stpl.tech.kettle.domain.model;


import java.io.Serializable;
import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;
import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PercentageDetail implements Serializable {

    private static final long serialVersionUID = -1805006842537845217L;

    @Id
    private String _id;

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    protected BigDecimal percentage;

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    protected BigDecimal value;

}
