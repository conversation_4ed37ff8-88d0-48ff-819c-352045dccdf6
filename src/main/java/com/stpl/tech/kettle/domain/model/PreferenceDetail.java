package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PreferenceDetail implements Serializable {

    private static final long serialVersionUID = -6754016111615165702L;
    private Integer preferenceId;

    private String preferenceName;

    private String preferenceType;

}

