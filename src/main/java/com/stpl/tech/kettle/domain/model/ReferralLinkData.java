package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ReferralLinkData {

	private String name;
	private String link;
	private int amount;
	private String code;
	private int totalAmount;

	public ReferralLinkData(String name, String link, int amount, String code) {
		super();
		this.name = name;
		this.link = link;
		this.amount = amount;
		this.code = code;
	}

}
