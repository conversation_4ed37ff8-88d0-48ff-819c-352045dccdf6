package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CashMetadataWrapper {
	private Integer customerId;
	private Integer referentId;
	private String customerRefCode;
	private Integer referralMappingId;
	private Integer cashLogDataId;
	private Integer orderId;
	private BigDecimal transactionAmount;
	private Date transactionTime;
	private String contactnumber;
	private CashTransactionMetadata metadata;
}
