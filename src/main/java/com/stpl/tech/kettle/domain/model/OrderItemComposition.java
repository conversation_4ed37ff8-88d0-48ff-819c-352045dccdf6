package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.data.annotation.Id;

import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;


public class OrderItemComposition implements Serializable {

    private static final long serialVersionUID = -5083045930894371724L;

    @Id
    private String _id;

    protected List<IngredientVariantDetail> variants;

    protected List<IngredientProductDetail> products;

    protected List<IngredientProductDetail> addons;

    protected List<OrderItem> menuProducts;

    protected List<String> options;

    protected List<IngredientProductDetail> others;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public List<IngredientVariantDetail> getVariants() {
        if (Objects.isNull(variants)) {
            variants = new ArrayList<>();
        }
        return variants;
    }

    public void setVariants(List<IngredientVariantDetail> variants) {
        this.variants = variants;
    }

    public List<IngredientProductDetail> getProducts() {
        if (Objects.isNull(products)) {
            products = new ArrayList<>();
        }
        return products;
    }

    public void setProducts(List<IngredientProductDetail> products) {
        this.products = products;
    }

    public List<IngredientProductDetail> getAddons() {
        if (Objects.isNull(addons)) {
            addons = new ArrayList<>();
        }
        return addons;
    }

    public void setAddons(List<IngredientProductDetail> addons) {
        this.addons = addons;
    }

    public List<OrderItem> getMenuProducts() {
        if (Objects.isNull(menuProducts)) {
            menuProducts = new ArrayList<>();
        }
        return menuProducts;
    }

    public void setMenuProducts(List<OrderItem> menuProducts) {
        this.menuProducts = menuProducts;
    }

    public List<String> getOptions() {
        if (Objects.isNull(options)) {
            options = new ArrayList<>();
        }
        return options;
    }

    public void setOptions(List<String> options) {
        this.options = options;
    }

    public boolean hasDefaultVariant() {
        for (IngredientVariantDetail variant : getVariants()) {
            if (!variant.isDefaultSetting()) {
                return true;
            }
        }
        return false;
    }

    public List<IngredientProductDetail> getOthers() {
        if (Objects.isNull(others)) {
            others = new ArrayList<>();
        }
        return others;
    }

    public void setOthers(List<IngredientProductDetail> others) {
        this.others = others;
    }
}

