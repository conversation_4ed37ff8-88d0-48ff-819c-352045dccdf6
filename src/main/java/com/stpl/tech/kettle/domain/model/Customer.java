package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;
import com.stpl.tech.master.domain.model.Address;

import lombok.Data;

@Document
@Data
public class Customer implements Serializable {

    private static final long serialVersionUID = -5975124835916759966L;
    
    @Id
    private String _id;


    protected int id;

    protected String firstName;

    protected String middleName;

    protected String lastName;

    protected String countryCode;

    protected String contactNumber;

    protected String emailId;
    @Field
    protected boolean emailVerified;
    @Field
    protected int loyaltyPoints;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    protected BigDecimal chaayosCash;

    protected Integer registrationUnitId;

    protected String acquisitionSource;

    protected String acquisitionToken;
    @Field
    protected boolean contactNumberVerified;
    @Field
    protected boolean availedSignupOffer;
    @Field
    protected Integer lastOrderId;
    @Field
    protected Integer orderCount;

    protected Date lastOrderTime;

    protected boolean smsSubscriber;

    protected boolean emailSubscriber;

    protected boolean loyaltySubscriber;

    protected boolean isBlacklisted;

    protected boolean internal;

    protected List<Address> addresses;

    protected Date addTime;

    protected Date numberVerificationTime;

    protected TrueCallerVerifiedProfile trueCallerProfile;

    protected boolean isDND;

    protected boolean optOutOfFaceIt = false;

    protected Date optOutTime;

    protected Date lastNPSTime;

    private String refCode;

    private String isRefSubscriber;

    private String refAcquisitionSource;

    private Date referredOn;

    private Integer referralDataId;

    private Integer referrerId;

    private String signUpRefCode; // ref code of referrer used on signup

    private boolean isReferrerAwarded;

    private String faceId;

    protected Integer acquisitionBrandId;

    protected boolean chaayosCustomer;

    private String gender;

    private Date dateOfBirth;

    private Date anniversary;

    private boolean isNewCustomerForBrand;

    private boolean signUpOfferExpired;
    private Date signupOfferExpiryTime;

    private String customerAppId;
    private String optWhatsapp;
    private SubscriptionInfoDetail subscriptionInfoDetail;
    @Field
    private CustomerTransaction customerTransaction;


    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }


    public int getId() {
        return id;
    }


    public void setId(int value) {
        this.id = value;
    }

    public String getFirstName() {
        return firstName;
    }


    public void setFirstName(String value) {
        this.firstName = value;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String value) {
        this.middleName = value;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String value) {
        this.lastName = value;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String value) {
        this.countryCode = value;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String value) {
        this.contactNumber = value;
    }

    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String value) {
        this.emailId = value;
    }

    public boolean isEmailVerified() {
        return emailVerified;
    }

    public void setEmailVerified(boolean value) {
        this.emailVerified = value;
    }

    public int getLoyaltyPoints() {
        return loyaltyPoints;
    }

    public void setLoyaltyPoints(int value) {
        this.loyaltyPoints = value;
    }

    public Integer getRegistrationUnitId() {
        return registrationUnitId;
    }

    public void setRegistrationUnitId(Integer value) {
        this.registrationUnitId = value;
    }

    public String getAcquisitionSource() {
        return acquisitionSource;
    }

    public void setAcquisitionSource(String value) {
        this.acquisitionSource = value;
    }

    public String getAcquisitionToken() {
        return acquisitionToken;
    }

    public void setAcquisitionToken(String value) {
        this.acquisitionToken = value;
    }

    public boolean isContactNumberVerified() {
        return contactNumberVerified;
    }

    public void setContactNumberVerified(boolean value) {
        this.contactNumberVerified = value;
    }

    public boolean isAvailedSignupOffer() {
        return availedSignupOffer;
    }

    public void setAvailedSignupOffer(boolean value) {
        this.availedSignupOffer = value;
    }

    public Integer getLastOrderId() {
        return lastOrderId;
    }

    public void setLastOrderId(Integer value) {
        this.lastOrderId = value;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer value) {
        this.orderCount = value;
    }


    public Date getLastOrderTime() {
        return lastOrderTime;
    }

    public void setLastOrderTime(Date value) {
        this.lastOrderTime = value;
    }

    public boolean isSmsSubscriber() {
        return smsSubscriber;
    }

    public void setSmsSubscriber(boolean value) {
        this.smsSubscriber = value;
    }

    public boolean isEmailSubscriber() {
        return emailSubscriber;
    }


    public void setEmailSubscriber(boolean value) {
        this.emailSubscriber = value;
    }

    public boolean isLoyaltySubscriber() {
        return loyaltySubscriber;
    }


    public void setLoyaltySubscriber(boolean value) {
        this.loyaltySubscriber = value;
    }

    public List<Address> getAddresses() {
        if (Objects.isNull(addresses)) {
            addresses = new ArrayList<Address>();
        }
        return this.addresses;
    }

    public void setAddresses(List<Address> addresses) {
        this.addresses = addresses;
    }

    public boolean isBlacklisted() {
        return isBlacklisted;
    }

    public void setBlacklisted(boolean blacklisted) {
        isBlacklisted = blacklisted;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getNumberVerificationTime() {
        return numberVerificationTime;
    }

    public void setNumberVerificationTime(Date numberVerificationTime) {
        this.numberVerificationTime = numberVerificationTime;
    }

    public TrueCallerVerifiedProfile getTrueCallerProfile() {
        return trueCallerProfile;
    }

    public void setTrueCallerProfile(TrueCallerVerifiedProfile trueCallerProfile) {
        this.trueCallerProfile = trueCallerProfile;
    }

    public boolean getIsDND() {
        return isDND;
    }

    public void setIsDND(boolean isDND) {
        this.isDND = isDND;
    }

    public Date getLastNPSTime() {
        return lastNPSTime;
    }

    public void setLastNPSTime(Date lastNPSTime) {
        this.lastNPSTime = lastNPSTime;
    }

    public boolean isInternal() {
        return internal;
    }

    public void setInternal(boolean internal) {
        this.internal = internal;
    }

    public String getRefCode() {
        return refCode;
    }

    public void setRefCode(String refCode) {
        this.refCode = refCode;
    }

    public String getIsRefSubscriber() {
        return isRefSubscriber;
    }

    public void setIsRefSubscriber(String isRefSubscriber) {
        this.isRefSubscriber = isRefSubscriber;
    }

    public String getRefAcquisitionSource() {
        return refAcquisitionSource;
    }

    public void setRefAcquisitionSource(String refAcquisitionSource) {
        this.refAcquisitionSource = refAcquisitionSource;
    }

    public Date getReferredOn() {
        return referredOn;
    }

    public void setReferredOn(Date referredOn) {
        this.referredOn = referredOn;
    }

    public Integer getReferralDataId() {
        return referralDataId;
    }

    public void setReferralDataId(Integer referralDataId) {
        this.referralDataId = referralDataId;
    }

    public Integer getReferrerId() {
        return referrerId;
    }

    public void setReferrerId(Integer referralId) {
        this.referrerId = referralId;
    }

    public String getSignUpRefCode() {
        return signUpRefCode;
    }

    public void setSignUpRefCode(String signUpRefCode) {
        this.signUpRefCode = signUpRefCode;
    }

    public boolean isReferrerAwarded() {
        return isReferrerAwarded;
    }

    public void setReferrerAwarded(boolean isReferrerAwarded) {
        this.isReferrerAwarded = isReferrerAwarded;
    }

    public BigDecimal getChaayosCash() {
        return chaayosCash;
    }

    public void setChaayosCash(BigDecimal chaayosCash) {
        this.chaayosCash = chaayosCash;
    }

    public boolean isOptOutOfFaceIt() {
        return optOutOfFaceIt;
    }

    public void setOptOutOfFaceIt(boolean optOutOfFaceIt) {
        this.optOutOfFaceIt = optOutOfFaceIt;
    }

    public Date getOptOutTime() {
        return optOutTime;
    }

    public void setOptOutTime(Date optOutTime) {
        this.optOutTime = optOutTime;
    }

    public String getFaceId() {
        return faceId;
    }

    public void setFaceId(String faceId) {
        this.faceId = faceId;
    }

    public Integer getAcquisitionBrandId() {
        return acquisitionBrandId;
    }

    public void setAcquisitionBrandId(Integer acquisitionBrandId) {
        this.acquisitionBrandId = acquisitionBrandId;
    }

    public boolean isChaayosCustomer() {
        return chaayosCustomer;
    }

    public void setChaayosCustomer(boolean chaayosCustomer) {
        this.chaayosCustomer = chaayosCustomer;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Date getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(Date dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public Date getAnniversary() {
        return anniversary;
    }

    public void setAnniversary(Date anniversary) {
        this.anniversary = anniversary;
    }

    public boolean isNewCustomerForBrand() {
        return isNewCustomerForBrand;
    }

    public void setNewCustomerForBrand(boolean newCustomerForBrand) {
        isNewCustomerForBrand = newCustomerForBrand;
    }

    public boolean isSignUpOfferExpired() {
        return signUpOfferExpired;
    }

    public void setSignUpOfferExpired(boolean signUpOfferExpired) {
        this.signUpOfferExpired = signUpOfferExpired;
    }

    public Date getSignupOfferExpiryTime() {
        return signupOfferExpiryTime;
    }

    public void setSignupOfferExpiryTime(Date signupOfferExpiryTime) {
        this.signupOfferExpiryTime = signupOfferExpiryTime;
    }

    public String getCustomerAppId() {
        return customerAppId;
    }

    public void setCustomerAppId(String customerAppId) {
        this.customerAppId = customerAppId;
    }

    public SubscriptionInfoDetail getSubscriptionInfoDetail() {
        return subscriptionInfoDetail;
    }

    public void setSubscriptionInfoDetail(SubscriptionInfoDetail subscriptionInfoDetail) {
        this.subscriptionInfoDetail = subscriptionInfoDetail;
    }

    public String getOptWhatsapp() {
        return optWhatsapp;
    }

    public void setOptWhatsapp(String optWhatsapp) {
        this.optWhatsapp = optWhatsapp;
    }

    public CustomerTransaction getCustomerTransaction() {
        return customerTransaction;
    }

    public void setCustomerTransaction(CustomerTransaction customerTransaction) {
        this.customerTransaction = customerTransaction;
    }
}

