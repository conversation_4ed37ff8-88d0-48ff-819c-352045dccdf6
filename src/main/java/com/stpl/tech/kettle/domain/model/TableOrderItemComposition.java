package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
public class TableOrderItemComposition {

    @Builder.Default
    protected List<TableOrderItem> menuProducts = new ArrayList<>();

    protected List<TableOrderItem> paidAddOns = new ArrayList<>();

}
