package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CustomerOffer implements Serializable {

	private static final long serialVersionUID = 1533611673695071717L;

	private int offerDetailId;
	private int customerId;
	private String offerCode;
	private Date availTime;
	private boolean availed;
	private int orderId;
}
