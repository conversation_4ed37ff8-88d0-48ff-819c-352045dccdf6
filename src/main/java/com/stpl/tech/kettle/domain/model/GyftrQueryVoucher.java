package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder({ "vQueryVoucherResult" })
public class GyftrQueryVoucher implements Serializable {

    private static final long serialVersionUID = -8809194514164081407L;

    @JsonProperty("vQueryVoucherResult")
    List<GyftrVoucherQueryResult> vQueryVoucherResult = new ArrayList<>();

    @Override
    public String toString() {
        return "GyftrQueryVoucher [vQueryVoucherResult=" + vQueryVoucherResult + "]";
    }

}
