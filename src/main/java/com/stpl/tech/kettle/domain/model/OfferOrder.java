package com.stpl.tech.kettle.domain.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OfferOrder {

    private Order order;
    private String contact;
    private boolean newCustomer;
    private String couponCode;
    private String appliedOfferMessage;
    private boolean error;
    private int errorCode;
    private String errorMessage;
    private String offerDescription;
    private boolean manualOverride;
    private Boolean awardLoyalty;
    private BigDecimal prepaidAmount;
    private boolean otpRequired;

}
