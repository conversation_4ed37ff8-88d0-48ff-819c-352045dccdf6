package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrderMetadata implements Serializable {

    private static final long serialVersionUID = -9176250779625403395L;

    @Id
    private String _id;


    protected int metadataId;

    protected int orderId;

    protected String attributeName;

    protected String attributeValue;

}
