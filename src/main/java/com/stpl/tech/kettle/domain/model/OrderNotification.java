package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderNotification implements Serializable {

	private static final long serialVersionUID = -2278521991655758230L;
	private Integer walletPurchaseAmt;// cashCardPurchase
	private Integer walletPendingAmount;// walletBalance
	private Boolean isWalletPurchased;
	private String itemCode; // cashCardPurchase
	private String cashPendingAmount;
	private String voucherCode;
	private String smsTemplateDate;
	private Integer usedAmount;// cashCardRedemption
	private BigDecimal cashBackAmount;// cashCardCashbackAllotment
	private String cashBackAllotmentStartDate;
	private String cashBackAllotmentEndDate;
	private Integer refundAmount;// cashCardRefund
	private String subscriptionName;
	private String offerDescription;// SUBSCRIPTION OFFER DESCRIPTION
	private Integer validDays;
	private String planEndDate;
	private String customerName;
	private Integer selectOverallSaving;// selectOverallSaving
	private String generateOrderId;
	private BigDecimal selectSavingAmount;// selectSavingAmount
	// private Integer nthDay;//chaayosSelect nthDay Reminder
	private String nextOfferText;// for CLM_OFFER, CLM
	private String offerCode;
	private String validityTill;
	private Integer daysLeft;// days left for subscription
	private String channelPartner;
	private Integer loyalTeaTotalCount;
	private Integer totalLoyalTeaPoint;
	private Integer loyalTeaPoints;// actual loyalty points mapped to loyalty score by col acquired points
	private Integer loyalTeaCount;
	private String cafeName;
	private BigDecimal orderAmt;
	private Integer earnedLoyalTeaPoint;
	private String savingText;
	private BigDecimal savingAmt;// info.getOrder().getTransactionDetail().getSavings() for whatsapp Notification
	private String orderFeedBackUrl;
	private String orderRecieptUrl;
	private Boolean isSubscriptionPurched;
	private Boolean isSubscriptionUsed;
	private Integer subscriptionValidityInDays;
	private String walletSavingAmount;
	private boolean isSmsSubscriber;
	private boolean isWhatsAppOptIn;
	private String customerContactNumber;
	private Boolean isLoyaltyUnlocked;
	private BigDecimal walletExtraAmount;
	private Boolean walletUsed;

	private int chaiLeft;
}
