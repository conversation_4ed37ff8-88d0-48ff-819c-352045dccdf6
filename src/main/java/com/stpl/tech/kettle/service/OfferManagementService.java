package com.stpl.tech.kettle.service;

import java.util.List;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.domain.model.CreateNextOfferRequest;
import com.stpl.tech.kettle.domain.model.CreateOrderResult;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerRepeatType;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.master.domain.model.CloneCouponData;
import com.stpl.tech.master.domain.model.CouponCloneResponse;

public interface OfferManagementService {

	void updateCouponUsageByOne(int customerId, String offerCode, int orderId);

	void createNBOandDNBOOffers(Order order, CreateOrderResult result, OrderInfo info, StringBuilder sb);

	OrderInfo applyDineInPostOrderOffer(OrderInfo info, Order order);

	OrderInfo applyDeliveryPostOrderOffer(OrderInfo info, Order order);

	OrderInfo createNBOCustomerReceipt(OrderInfo info, Order order);

	NextOffer createNextOffer(Integer campaignId, Integer orderId, Integer brandId, Integer unitId, Customer customer,
			Integer applicableUnitId, String applicableUnitRegion, CreateNextOfferRequest request,
			OrderNotification orderNotification, boolean isOfferGeneratedByOffer);

	CouponCloneResponse getCloneCode(String startDay, List<String> contactNumbers, CustomerRepeatType type,
			String cloneCode, int usageCount, int validityInDays, String prefix, Integer applicableUnitId,
			String applicableRegion) throws DataUpdationException;

	CouponCloneResponse createCoupon(CloneCouponData info, List<String> contactNumbers, String startDay,
			Integer applicableUnitId, String applicableRegion) throws DataUpdationException;

	void removeSecondFreeChai(Integer customerId,String signupOfferStatus);

	void setCouponCodeAsInactive(Integer couponDetailId);

}
