package com.stpl.tech.kettle.service.impl;

import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.FieldValue;
import com.google.cloud.firestore.SetOptions;
import com.google.firebase.cloud.FirestoreClient;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.domain.enums.FireStoreNotificationType;
import com.stpl.tech.kettle.domain.firestore.FireStoreOrderNotification;
import com.stpl.tech.kettle.service.FireStoreService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Log4j2
public class FireStoreServiceImpl implements FireStoreService {

    private final Map<Integer, DocumentReference> unitWiseFireStoreDocument = new ConcurrentHashMap<>();

    @Autowired
    EnvironmentProperties environmentProperties;

    @Override
    public boolean shouldRouteThroughFireStore(Integer unitId) {
        if (environmentProperties.isAssemblyFirestoreEnabledForAll()) {
            return true;
        }
        if (Objects.nonNull(environmentProperties.getAssemblyFirestoreUnits()) && !environmentProperties.getAssemblyFirestoreUnits().equalsIgnoreCase("")) {
            return Arrays.stream(environmentProperties.getAssemblyFirestoreUnits().split(",")).mapToInt(Integer::parseInt).boxed().toList().contains(unitId);
        }
        return false;
    }

    @Override
    public boolean sendOrderNotificationThroughFireStore(Integer unitId, Integer orderId) {
        try {
            DocumentReference docRef;
            if (unitWiseFireStoreDocument.containsKey(unitId)) {
                docRef = unitWiseFireStoreDocument.get(unitId);
            } else {
                docRef = FirestoreClient.getFirestore().collection(unitId.toString()).document("ORDERS");
                unitWiseFireStoreDocument.put(unitId, docRef);
            }
            FireStoreOrderNotification fireStoreOrderNotification = new FireStoreOrderNotification(orderId, FieldValue.serverTimestamp(), FireStoreNotificationType.ORDER.name());
            docRef.set(fireStoreOrderNotification, SetOptions.merge()).get();
            return true;
        } catch (Exception e) {
            log.error("Exception Occurred while writing to Fire store for UnitId : {} and OrderId : {}", unitId, orderId, e);
            // routing through firebase again
        }
        return false;
    }
}
