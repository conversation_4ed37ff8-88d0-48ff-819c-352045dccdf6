package com.stpl.tech.kettle.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.stpl.tech.kettle.cache.EmployeeCache;
import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.EmployeeMealData;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.repository.kettle.EmployeeMealAllowanceDataDao;
import com.stpl.tech.kettle.service.EmployeeMealService;
import com.stpl.tech.kettle.service.OrderSearchService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.Constants.TransactionConstants;
import com.stpl.tech.master.domain.model.IdCodeName;

@Service
public class EmployeeMealServiceImpl implements EmployeeMealService {

	@Autowired
	private OrderSearchService orderSearchService;

	@Autowired
	private ProductCache productCache;

	@Autowired
	private EmployeeCache employeeCache;

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private EmployeeMealAllowanceDataDao employeeMealAllowanceDataDao;

	@Override
	public void validateEmployeeMeal(Order order) throws DataUpdationException {
		if (!TransactionUtils.isEmployeeMeal(order)) {
			return;
		}
		StringBuilder sb = new StringBuilder();
		boolean hasEmployeeMeal = false;
		int employeeMealCount = 0;
		Map<String, Pair<Integer, Integer>> map = TransactionUtils.getEmployeeMealProductThreshold();
		List<EmployeeMealData> existingEmployeeMeal = orderSearchService
				.getEmployeeMealData(order.getEmployeeIdForMeal());
		if (!CollectionUtils.isEmpty(existingEmployeeMeal)) {
			for (EmployeeMealData data : existingEmployeeMeal) {
				if (data.getProductTypeId() == TransactionConstants.BEVERAGE_COLD_PRODUCT_TYPE
						|| data.getProductTypeId() == TransactionConstants.BEVERAGE_HOT_PRODUCT_TYPE) {
					Pair<Integer, Integer> pair = map.get(TransactionConstants.BEVERAGE_PRODUCT_TYPE_STR);
					Pair<Integer, Integer> newPair = Pair.of(pair.getFirst(), pair.getSecond() + data.getQuantity());
					map.put(TransactionConstants.BEVERAGE_PRODUCT_TYPE_STR, newPair);
				} else if (data.getProductTypeId() == TransactionConstants.FOOD_PRODUCT_TYPE) {
					Pair<Integer, Integer> pair = map.get(TransactionConstants.FOOD_PRODUCT_TYPE_STR);
					Pair<Integer, Integer> newPair = Pair.of(pair.getFirst(), pair.getSecond() + data.getQuantity());
					map.put(TransactionConstants.FOOD_PRODUCT_TYPE_STR, newPair);
				}
			}
		}
		for (OrderItem item : order.getOrders()) {
			if (Objects.nonNull(item.getComplimentaryDetail()) && item.getComplimentaryDetail().isIsComplimentary()
					&& item.getComplimentaryDetail().getReasonCode() == AppConstants.EMPLOYEE_MEAL_ID) {
				hasEmployeeMeal = true;
				employeeMealCount++;
				IdCodeName category = productCache
						.getProductCategory(productCache.getProductById(item.getProductId()).getType()).getDetail();
				item.setProductCategory(category);
				if (category.getId() != TransactionConstants.BEVERAGE_COLD_PRODUCT_TYPE
						&& category.getId() != TransactionConstants.BEVERAGE_HOT_PRODUCT_TYPE
						&& category.getId() != TransactionConstants.FOOD_PRODUCT_TYPE) {
					sb.append("Cannot Order products in category " + category.getCode() + " for employee meal\n");
				}
				if (!employeeCache.getEmployeeMealProducts().contains(item.getProductId())) {
					sb.append("Cannot Order " + item.getProductName() + " for employee meal\n");
				} else {
					if (!employeeCache.getEmployeeMealDimensions().contains(item.getDimension())) {
						sb.append("Cannot Order " + item.getProductName() + " of size " + item.getDimension()
								+ " for employee meal\n");
					}
					Pair<Integer, Integer> pair = null;
					Pair<Integer, Integer> newPair = null;
					if (category.getId() == TransactionConstants.BEVERAGE_COLD_PRODUCT_TYPE
							|| category.getId() == TransactionConstants.BEVERAGE_HOT_PRODUCT_TYPE) {
						pair = map.get(TransactionConstants.BEVERAGE_PRODUCT_TYPE_STR);
						newPair = Pair.of(pair.getFirst(), pair.getSecond() + item.getQuantity());
						map.put(TransactionConstants.BEVERAGE_PRODUCT_TYPE_STR, newPair);
					} else {
						pair = map.get(TransactionConstants.FOOD_PRODUCT_TYPE_STR);
						newPair = Pair.of(pair.getFirst(), pair.getSecond() + item.getQuantity());
						map.put(TransactionConstants.FOOD_PRODUCT_TYPE_STR, newPair);
					}
					if (newPair.getFirst() < (newPair.getSecond())) {
						sb.append("Cannot Order more than " + pair.getFirst() + " quantity for category "
								+ category.getCode() + ", Total Quanity is " + newPair.getSecond()
								+ " which is greater than threshold\n");
					}
				}
			}
		}
		if (hasEmployeeMeal && employeeMealCount != order.getOrders().size()) {
			sb.append("One of the items is not marked as employee meal. Please mark all items as employee meal\n");
		}

		/*
		 * if (hasEmployeeMeal && employeeMealCount > 2) { sb.append(
		 * "Cannot Order more than 2 items in employee meal\n"); }
		 */
		if (sb.toString().length() > 0) {
			throw new DataUpdationException("Employee Meal Issue : \n" + sb.toString());
		}

		// No customer for employee meal
		// hence no Loyalty score problems
		if (order.isEmployeeMeal()) {
			order.setCustomerId(properties.getDummyCustomerId());
			order.setCustomerName(null);
		}
	}

	@Override
	public void validatePaidEmployeeMeal(Order order) throws DataUpdationException {
		if (!TransactionUtils.isPaidEmployeeMeal(order)) {
			return;
		}

		BigDecimal orderAmount = order.getTransactionDetail().getPaidAmount();
		BigDecimal spent = employeeMealAllowanceDataDao.findSumOfAmountByEmployeeIdAndOrderTime(
				order.getEmployeeIdForMeal(),
				AppUtils.getPaidEmployeeMealStartDate(properties.getEmployeeMealMonthlyStartDate()),
				AppUtils.getStartOfBusinessDay(AppUtils.getBusinessDate()));
		long days = AppUtils.numberOfMealDays(properties.getEmployeeMealMonthlyStartDate());
		if (properties.getEmployeeMealDayLimit().longValue() < days) {
			days = properties.getEmployeeMealDayLimit().longValue();
		}
		BigDecimal perDayLimit = properties.getEmployeeMealPerDayAmountLimit();
		BigDecimal spentToday = employeeMealAllowanceDataDao.findSumOfAmountTodayByEmployeeIdAndOrderTime(
				order.getEmployeeIdForMeal(), AppUtils.getStartOfBusinessDay(AppUtils.getBusinessDate()));
		BigDecimal available = (new BigDecimal(days).multiply(perDayLimit)).subtract(spent);

		BigDecimal availableAllowanceLimit = available.subtract(spentToday);
		if (orderAmount.compareTo(availableAllowanceLimit) > 0) {
			throw new DataUpdationException(String.format(
					"Employee Meal Issue : order amount (%f) is greater then available allowance limit (%f)",
					orderAmount.setScale(2, BigDecimal.ROUND_HALF_UP),
					availableAllowanceLimit.setScale(2, BigDecimal.ROUND_HALF_UP)));
		}

	}

}
