package com.stpl.tech.kettle.service;

import java.io.IOException;
import java.io.UnsupportedEncodingException;

import com.stpl.tech.master.notification.ShortUrlData;

public interface SMSWebServiceClient extends MessagingClient {
	public String getSMSRequest(String message, String contactNumber) throws UnsupportedEncodingException;

	public ShortUrlData getShortUrl(String url) throws IOException;

	public void updateShortUrl(ShortUrlData url) throws IOException;

	public String getOTPRequestViaIVR(String token, String contactNumber, String ivrId) throws IOException;

}
