package com.stpl.tech.kettle.service.impl;

import java.util.Objects;

import javax.jms.JMSException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.stpl.tech.kettle.domain.model.NotificationPayload;
import com.stpl.tech.kettle.service.CustomerCommunicationEventPublisher;
import com.stpl.tech.kettle.service.SQSNotificationService;
import com.stpl.tech.kettle.util.Constants.AppConstants;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class CustomerCommunicationEventPublisherImpl implements CustomerCommunicationEventPublisher {

	@Autowired
	private SQSNotificationService sqsNotificationService;

	@Override
	public void publishCustomerCommunicationEvent(String env, NotificationPayload event) throws JMSException {
		try {
			if (Objects.nonNull(event)) {
				if (AppConstants.WA_OPT_IN.equals(event.getMessageType())
						|| "WELCOME_MESSAGE".equals(event.getMessageType())) {
					sqsNotificationService.publishToSQS(env, new Gson().toJson(event), "_WHATSAPP_SIGNUP");
					log.info("COMMUNICATION MESSAGE STATUS PUBLISHED SUCCESSFULLY :::: {} ", new Gson().toJson(event));
				}
			}
		} catch (Exception e) {
			log.error("COMMUNICATION MESSAGE STATUS FAILED :::: {} ", new Gson().toJson(event));
		}
	}
}
