package com.stpl.tech.kettle.service.impl;

import com.stpl.tech.kettle.converter.Converters;
import com.stpl.tech.kettle.data.kettle.CashData;
import com.stpl.tech.kettle.data.kettle.CustomerBrandMapping;
import com.stpl.tech.kettle.data.kettle.CustomerFavChaiMapping;
import com.stpl.tech.kettle.data.kettle.CustomerFavChaiMapping;
import com.stpl.tech.kettle.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.data.kettle.CustomerOfferDetail;
import com.stpl.tech.kettle.data.kettle.LoyaltyScore;
import com.stpl.tech.kettle.data.kettle.OrderItemMetaDataDetail;
import com.stpl.tech.kettle.data.kettle.SubscriptionPlan;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.CustomerOffer;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.SavedChaiOrderedDomain;
import com.stpl.tech.kettle.mapper.CustomerOfferDetailMapper;
import com.stpl.tech.kettle.repository.kettle.CashDataDao;
import com.stpl.tech.kettle.repository.kettle.CustomerBrandMappingDao;
import com.stpl.tech.kettle.repository.kettle.CustomerDao;
import com.stpl.tech.kettle.repository.kettle.CustomerFavChaiMappingDao;
import com.stpl.tech.kettle.repository.kettle.CustomerOfferDao;
import com.stpl.tech.kettle.repository.kettle.CustomerProductFeedbackDao;
import com.stpl.tech.kettle.repository.kettle.LoyaltyScoreDao;
import com.stpl.tech.kettle.repository.kettle.OrderDetailDao;
import com.stpl.tech.kettle.repository.kettle.OrderItemMetaDataDetailDao;
import com.stpl.tech.kettle.repository.kettle.SubscriptionPlanDao;
import com.stpl.tech.kettle.service.CustomerService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferLastRedemptionView;
import jakarta.persistence.NoResultException;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Service
@AllArgsConstructor
@Log4j2
public class CustomerServiceImpl implements CustomerService {

	@Autowired
	private CustomerDao customerDao;

	@Autowired
	private CustomerProductFeedbackDao customerProductFeedbackDao;

	@Autowired
	private CustomerBrandMappingDao customerBrandMappingDao;

	@Autowired
	private CustomerOfferDao customerOfferDao;

	@Autowired
	private OrderDetailDao orderDetailDao;

	@Autowired
	private LoyaltyScoreDao loyaltyScoreDao;

	@Autowired
	private CashDataDao cashDataDao;

	@Autowired
	private SubscriptionPlanDao subscriptionPlanDao;

    @Autowired
    private OrderItemMetaDataDetailDao itemMetaDataDetailDao;

    @Autowired
    private CustomerFavChaiMappingDao favChaiMappingDao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerInfo getCustomerInfo(Integer customerId) {
		Optional<CustomerInfo> customerInfo = customerDao.findById(customerId);
		if(customerInfo.isEmpty()) {
			log.info("Customer not found in DB for id {}",customerId);
		}
		return customerInfo.get();
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveCustomerInfoBrandWise(int customerId, int brandId, int orderId, Date billingServerTime,boolean isSpecialOrder) {

		CustomerBrandMapping customerBrandMapping = customerBrandMappingDao.findByCustomerIdAndBrandId(customerId,
				brandId);

        if (Objects.isNull(customerBrandMapping)) {
            customerBrandMapping = new CustomerBrandMapping(customerId, brandId, 0,0);
        }
        int prevTotalOrder = customerBrandMapping.getTotalOrder();
        if(Objects.isNull(customerBrandMapping.getTotalSpecialOrder())){
            customerBrandMapping.setTotalSpecialOrder(0);
        }
        int prevTotalSpecialOrder = customerBrandMapping.getTotalSpecialOrder();
        if(isSpecialOrder){
            customerBrandMapping.setTotalSpecialOrder(prevTotalSpecialOrder + 1);
            customerBrandMapping.setLastSpecialOrderId(orderId);
            customerBrandMapping.setLastSpecialOrderTime(billingServerTime);
        }
        else {
            customerBrandMapping.setTotalOrder(prevTotalOrder + 1);
            customerBrandMapping.setLastOrderId(orderId);
            customerBrandMapping.setLastOrderTime(billingServerTime);
        }
        customerBrandMappingDao.save(customerBrandMapping);
        if (prevTotalOrder > 0 || prevTotalSpecialOrder > 0) {
            return false;
        }
        return true;
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerInfo getCustomerInfo(String contactNumber) {
		CustomerInfo customerInfo = customerDao.findByContactNumber(contactNumber);
		return customerInfo;
	}

	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CustomerOffer> getOfferDetail(int customerId, String offerCode) {
		List<CustomerOfferDetail> customerOfferDetailList = customerOfferDao.findAllByCustomerIdAndOfferCode(customerId,
				offerCode);
		if (Objects.nonNull(customerOfferDetailList)) {
			return CustomerOfferDetailMapper.INSTANCE.toDomainList(customerOfferDetailList);
		} else {
			return new ArrayList<>();
		}
	}

	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public Integer checkCouponUsage(int customerId, String code) {
		return orderDetailDao.checkCouponUsage(customerId, code);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean getCustomerOrders(int customerId) {
		List<Integer> orders = orderDetailDao.getCustomerOrderDetails(customerId);
		if (Objects.nonNull(orders) && !orders.isEmpty()) {
			return true;
		}
		return false;
	}

	private CustomerInfo getCustomerInfo(String code, String contactNumber) {
		String countryCode = getCountryCode(code);
		try {
			CustomerInfo customerInfo = customerDao.findByContactNumber(contactNumber);
			return customerInfo;
		} catch (NoResultException e) {
			log.error(String.format("Did not find Customer with Contact Number : %s", contactNumber));
			return null;
		}
	}

	@Override
	public Customer getCustomer(String code, String contactNumber) {
		CustomerInfo customer = getCustomerInfo(code, contactNumber);
		LoyaltyScore score = null;
		BigDecimal cash = null;
		if (customer != null) {
			score = getLoyaltyScore(customer.getCustomerId());
			cash = getAvailableCash(customer.getCustomerId());
		}
		// TODO use map struct
		return Converters.convert(customer, score, cash);
	}

	@Override
	public LoyaltyScore getLoyaltyScore(Integer customerId) {
		return loyaltyScoreDao.findByCustomerId(customerId);

	}

	@Override
	public BigDecimal getAvailableCash(Integer customerId) {
		CashData cash = cashDataDao.findByCustomerId(customerId);
		if (cash == null) {
			return BigDecimal.ZERO;
		}
		return cash.getCurrentAmount();
	}

	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean getValidOfferFlag(OfferOrder offerOrder, CouponDetail couponDetail) {
		SubscriptionPlan subscriptionPlan = subscriptionPlanDao.findByCustomerIdAndSubscriptionPlanCodeAndStatus(
				offerOrder.getOrder().getCustomerId(), couponDetail.getCode(), AppConstants.ACTIVE);
		if (Objects.nonNull(subscriptionPlan)) {
			return doOfferValidation(getOrderDetailViaOffer(offerOrder.getOrder().getCustomerId(),
					offerOrder.getCouponCode(), subscriptionPlan.getPlanStartDate(),
					Objects.nonNull(couponDetail.getOffer().getApplicableHour())
							? couponDetail.getOffer().getApplicableHour()
							: 0),
					couponDetail);
		} else {
			return doOfferValidation(getOrderDetailViaOffer(offerOrder.getOrder().getCustomerId(),
					offerOrder.getCouponCode(), couponDetail.getStartDate(),
					Objects.nonNull(couponDetail.getOffer().getApplicableHour())
							? couponDetail.getOffer().getApplicableHour()
							: 0),
					couponDetail);
		}
	}

	@Override
	public OfferLastRedemptionView getOrderDetailViaOffer(Integer customerId, String couponCode, Date businessDate,
			Integer dailyFreqCount) {
		return orderDetailDao.getOrderDetailViaOffer(customerId, couponCode, businessDate,
				AppUtils.getDateBeforeOrAfterInSeconds(AppUtils.getCurrentTimestamp(), -dailyFreqCount * 3600));
	}

	private boolean doOfferValidation(OfferLastRedemptionView offerLastRedemptionView, CouponDetail couponDetail) {
		if (Objects.isNull(offerLastRedemptionView) || Objects.isNull(offerLastRedemptionView.getLastOrderTime())) {
			return false;
		} else if (Objects.nonNull(couponDetail.getOffer().getDailyFrequencyCount())
				&& couponDetail.getOffer().getDailyFrequencyCount() > 0) {
			if (offerLastRedemptionView.getOrderToday() < couponDetail.getOffer().getDailyFrequencyCount()
					&& offerLastRedemptionView.getOrderInLastHour() == 0) {
				return false;
			} else if (offerLastRedemptionView.getOrderToday() >= couponDetail.getOffer().getDailyFrequencyCount()
					|| offerLastRedemptionView.getOrderInLastHour() > 0) {
				return true;
			}
		} else if (Objects.isNull(couponDetail.getOffer().getDailyFrequencyCount())
				|| couponDetail.getOffer().getDailyFrequencyCount() == 0) {
			if (offerLastRedemptionView.getOrderToday() == 0) {
				return false;
			} else {
				return true;
			}
		}
		return false;
	}

	public String getCountryCode(String code) {
		return (code == null ? AppConstants.DEFAULT_COUNTRY_CODE : code);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public Customer getCustomer(int customerId) {
		CustomerInfo customer = getCustomerInfo(customerId);
		LoyaltyScore score = null;
		BigDecimal cash = null;
		if (customer != null) {
			score = getLoyaltyScore(customer.getCustomerId());
			cash = getAvailableCash(customer.getCustomerId());
		}
		// TODO use map struct
		return Converters.convert(customer, score, cash);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void addOfferDetail(int customerId, String offerCode, int orderId) {
		CustomerOfferDetail detail = CustomerOfferDetail.builder().availed(AppConstants.YES)
				.availTime(AppUtils.getCurrentTimestamp()).customerId(customerId).offerCode(offerCode).orderId(orderId)
				.build();

		customerOfferDao.save(detail);
	}

	@Override
	public boolean updateCustomerEmail(Customer customer, String flag) {
		try {
			if (Objects.nonNull(customer.getId())) {
				Optional<CustomerInfo> data = customerDao.findById(customer.getId());
				if (data.isPresent()) {
					CustomerInfo customerInfo = data.get();
					customerInfo.setIsEmailVerified(flag);
					customerDao.save(customerInfo);
					return true;
				}
			}
		} catch (Exception e) {
			log.error("Exception Faced While Updating Email Verified Flag Info for Cutomer Id :: {}", customer.getId());
			return false;
		}
		return false;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerDineInView getCustomerDineInView(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
		return customerProductFeedbackDao.getCustomerDineInView(customerId, brandId, excludeOrderIds);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerEmailData getCustomerEmailData(int customerId, Integer brandId) {
		return customerOfferDao.getCustomerEmailData(customerId, brandId, AppUtils.getBusinessDate());
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateCustomerName(Customer customer) {
		try {
			if (Objects.nonNull(customer.getId())) {
				Optional<CustomerInfo> data = customerDao.findById(customer.getId());
				if (data.isPresent()) {
					CustomerInfo customerInfo = data.get();
					customerInfo.setFirstName(customer.getFirstName());
					customerDao.save(customerInfo);
					return true;
				}
			}
		} catch (Exception e) {
			log.error("Exception Faced While Updating Email Verified Flag Info for Cutomer Id :: {}", customer.getId());
			return false;
		}
		return false;
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
    public Integer getLastOrderedSavedChai(Integer customerId){
        Optional<OrderItemMetaDataDetail> itemMetaDataDetail = Optional.ofNullable(itemMetaDataDetailDao.findTop1ByCustomerIdAndIsSavedChaiOrderByMetadataIdDesc(customerId, AppConstants.YES));
        return itemMetaDataDetail.map(OrderItemMetaDataDetail::getSavedChaiId).orElse(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
    public SavedChaiOrderedDomain getSavedChaiOrderDetail(Integer customerId){
        SavedChaiOrderedDomain savedChaiData = new SavedChaiOrderedDomain();
        List<CustomerFavChaiMapping> favChaiMappings = favChaiMappingDao.findByCustomerIdAndStatus(customerId,AppConstants.ACTIVE);
        Map<Integer,Integer> map = new HashMap<>();
        if(!CollectionUtils.isEmpty(favChaiMappings)){
            favChaiMappings.forEach(mapping -> {
                map.put(mapping.getCustomizationId(),mapping.getTotalOrderCount());
            });
            savedChaiData.setOrderCountMap(map);
        }
        savedChaiData.setLastOrderedSavedChaiId(getLastOrderedSavedChai(customerId));
        return savedChaiData;
    }
}
