package com.stpl.tech.kettle.service;

import java.util.Set;

import com.stpl.tech.kettle.data.kettle.LoyaltyEvents;
import com.stpl.tech.kettle.data.kettle.LoyaltyScore;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.LoyaltyEventType;
import com.stpl.tech.kettle.domain.model.Order;

public interface LoyaltyService {
	public boolean updateScore(int customerId, LoyaltyEventType type, int points, int cumulativePoints, Integer orderId,
			boolean hasSignupOffer, boolean updateLastOrderId);

	public boolean updateScore(int customerId, LoyaltyEventType type, int points, Integer orderId,
			boolean hasSignupOffer, boolean updateLastOrderId);

	public boolean createEventAndUpdate(int customerId, LoyaltyEventType type, int points, Integer orderId,
			boolean hasSignupOffer, boolean updateLastOrderId);

	public boolean createLoyaltyEvent(int customerId, LoyaltyEventType type, int points, Integer orderId);

	public LoyaltyScore getScore(int customerId);

	public boolean createLoyaltyEventAndUpdateLoyaltyScore(Integer loyaltyPoints, Integer customerId, String contactNumber,
														   LoyaltyEventType loyaltyEvent, LoyaltyScore loyaltyScore,Integer orderId);

	public boolean validateLoyaltyPoint(Integer loyaltyPoint, LoyaltyScore loyaltyScore);

	public boolean isLoyaltyAwarded(int customerId, Integer orderId);

	public void updateCustomerId(int customerId, Integer orderId);

	public LoyaltyEvents getTransactionPointsByOrderId(Integer orderId);

	public boolean isLoyaltyAwarded(int customerId, LoyaltyEventType emailVerification);

	void handleZeroAmountOrder(Order order);

	LoyaltyScore getCustomerLoyaltyScore(Integer customerId);
	
	void awardloyalty(Set<Integer> productIds,Order order,Customer customer,OrderDetail orderDetail, boolean forceAwardLoyalty);

	public void handleFailedLoyaltyAwardOrder(Order order , String loyaltyFailedReason,Boolean updateLastOrderId);
	public void expireLoyaltyPoints();

}
