package com.stpl.tech.kettle.service.impl;

import com.stpl.tech.kettle.cache.EnvironmentPropertiesCache;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.exceptions.CardValidationException;
import com.stpl.tech.kettle.service.PartnerCardService;
import com.stpl.tech.kettle.service.VoucherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.stpl.tech.kettle.util.TransactionUtils;

import java.util.List;

@Service
public class VoucherServiceImpl implements VoucherService {


    @Autowired
    private EnvironmentPropertiesCache propertiesCache;

    @Autowired
    @Qualifier("GyftrService")
    private PartnerCardService gyftrService;

    @Override
    @Transactional(noRollbackFor = CardValidationException.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public List<String> verifyVoucher(Order order, boolean consume) throws CardValidationException {
        List<String> voucherList = null;
        if (TransactionUtils.isGyftrCard(propertiesCache.isGyftrActive(), order.getOrders().get(0).getCardType())) {
            voucherList = gyftrService.verifyVoucher(order, false);
            if (voucherList == null) {
                throw new CardValidationException("Gyftr vouchers are invalid.Please fill correct Voucher Code");
            }
        }
        return voucherList;
    }

	@Override
	public boolean isGyftrCard(String cardType) {
		return TransactionUtils.isGyftrCard(propertiesCache.isGyftrActive(), cardType);
	}

}
