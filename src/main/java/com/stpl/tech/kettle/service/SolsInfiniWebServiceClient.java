package com.stpl.tech.kettle.service;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import org.json.JSONException;
import org.json.JSONObject;

import com.stpl.tech.kettle.domain.model.SMSConfiguration;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.notification.ShortUrlData;

public class SolsInfiniWebServiceClient extends AbstractSMSClient {
	private static SMSWebServiceClient OTP_INSTANCE;

	private static SMSWebServiceClient OTP_INSTANCE_VIA_IVR;

	private static Map<Integer, SMSWebServiceClient> TRANSACTIONAL_INSTANCES = new HashMap<Integer, SMSWebServiceClient>();

	private SolsInfiniWebServiceClient(SMSConfiguration config, SMSConfiguration shortUrlConfig) {
		super(config, shortUrlConfig);
	}

	public static SMSWebServiceClient getOTPClient() {
		if (OTP_INSTANCE == null) {
			// Change this to A5c35e52d90927b2c067412beb4a1b763
			SMSConfiguration config = new SMSConfiguration("CHAAYS", "A5c35e52d90927b2c067412beb4a1b763",
					"https://api-alerts.kaleyra.com/v4/?");
			SMSConfiguration shortUrlConfig = new SMSConfiguration("CHAAYS", "A5c35e52d90927b2c067412beb4a1b763",
					"https://api-alerts.kaleyra.com/v5/?");
			OTP_INSTANCE = new SolsInfiniWebServiceClient(config, shortUrlConfig);
		}
		return OTP_INSTANCE;
	}

	public static SMSWebServiceClient getOTPClientViaIVR() {
		if (OTP_INSTANCE_VIA_IVR == null) {
			SMSConfiguration config = new SMSConfiguration("Ae390b6a0651bbceeafb4b161499791c9",
					"https://api-voice.kaleyra.com/v1/?");
			OTP_INSTANCE_VIA_IVR = new SolsInfiniWebServiceClient(config, null);
		}
		return OTP_INSTANCE_VIA_IVR;
	}

	public static SMSWebServiceClient getTransactionalClient() {
		if (!TRANSACTIONAL_INSTANCES.containsKey(AppConstants.CHAAYOS_BRAND_ID)) {
			// Change this to Adb50be6e8634e5ae3c42c97b4dc21cef
			SMSConfiguration config = new SMSConfiguration("CHAYOS", "Adb50be6e8634e5ae3c42c97b4dc21cef",
					"https://api-alerts.kaleyra.com/v4/?");
			SMSConfiguration shortUrlConfig = new SMSConfiguration("CHAYOS", "Adb50be6e8634e5ae3c42c97b4dc21cef",
					"https://api-alerts.kaleyra.com/v5/?");
			TRANSACTIONAL_INSTANCES.put(AppConstants.CHAAYOS_BRAND_ID,
					new SolsInfiniWebServiceClient(config, shortUrlConfig));
		}
		return TRANSACTIONAL_INSTANCES.get(AppConstants.CHAAYOS_BRAND_ID);
	}

	public static SMSWebServiceClient getTransactionalClient(Brand brand) {
		if (brand == null) {
			return getTransactionalClient();
		}
		if (!TRANSACTIONAL_INSTANCES.containsKey(brand.getBrandId())) {
			// Change this to Adb50be6e8634e5ae3c42c97b4dc21cef
			SMSConfiguration config = new SMSConfiguration(brand.getSmsId(), "Adb50be6e8634e5ae3c42c97b4dc21cef",
					"https://api-alerts.kaleyra.com/v4/?");
			SMSConfiguration shortUrlConfig = new SMSConfiguration(brand.getSmsId(),
					"Adb50be6e8634e5ae3c42c97b4dc21cef", "https://api-alerts.kaleyra.com/v5/?");
			TRANSACTIONAL_INSTANCES.put(brand.getBrandId(), new SolsInfiniWebServiceClient(config, shortUrlConfig));
		}
		return TRANSACTIONAL_INSTANCES.get(brand.getBrandId());
	}

	public String getSMSRequest(String message, String contactNumber) throws UnsupportedEncodingException {
		String data = "";
		data += "method=sms";
		data += "&api_key=" + config.getPassCode(); // your loginId
		data += "&sender=" + config.getUserId();
		data += "&message=" + URLEncoder.encode(message, "UTF-8");
		data += "&to=" + URLEncoder.encode(contactNumber, "UTF-8");
		return data;
	}

	public String getOTPRequestViaIVR(String message, String contactNumber, String ivrId) throws IOException {
		String data = "";
		JSONObject jsonObject = new JSONObject();
		try {
			jsonObject.put("OTP", message);
		} catch (JSONException e) {
			// do nothing
		}
		String s = jsonObject.toString();
		data += "api_key=" + config.getPassCode();
		; // your loginId
		data += "&method=voice.call";
		data += "&play=ivr:" + ivrId;
		data += "&numbers=" + URLEncoder.encode(contactNumber, "UTF-8");
		data += "&meta=" + URLEncoder.encode(s, "UTF-8");
		return data;
	}

	public ShortUrlData getShortUrl(String url) throws IOException {
		String data = "";
		data += "method=txtly.create";
		data += "&api_key=" + config.getPassCode(); // your loginId
		data += "&format=json";
		data += "&track=1";
		data += "&advanced=1";
		data += "&url=" + URLEncoder.encode(url, "UTF-8");
		String response = callService(shortURLConfig.getUrl(), data);
		JSONObject object;
		System.out.println(data);
		try {
			object = new JSONObject(response);
			return object != null && object.get("status").equals("OK")
					? new ShortUrlData(object.get("id") + "", (String) object.get("txtly"))
					: null;
		} catch (JSONException e) {
			return null;
		}
	}

	@Override
	public boolean checkSuccess(String response) {
		try {
			JSONObject object = new JSONObject(response);
			return object != null && object.get("status").equals("OK");
		} catch (JSONException e) {
			return false;
		}

	}

	public static void main(String[] args) throws IOException {
		// Change this to A5c35e52d90927b2c067412beb4a1b763
		SMSConfiguration config = new SMSConfiguration("CHAYOS", "Adb50be6e8634e5ae3c42c97b4dc21cef",
				"http://api-alerts.solutionsinfini.com/v3/?");
		SMSConfiguration shortUrlConfig = new SMSConfiguration("CHAYOS", "Adb50be6e8634e5ae3c42c97b4dc21cef",
				"https://api-alerts.kaleyra.com/v5/?");
		SolsInfiniWebServiceClient client = new SolsInfiniWebServiceClient(config, shortUrlConfig);
		ShortUrlData data = client.getShortUrl("http://www.facebook.com");

		client.updateShortUrl(new ShortUrlData("5898", "http://www.facebook.com"));
		System.out.println(client.checkSuccess(
				"{\"status\":\"OK\",\"data\":{\"group_id\":3378755653,\"0\":{\"id\":\"3378755653-1\",\"customid\":\"\",\"customid1\":\"\",\"customid2\":\"\",\"mobile\":\"9599597740\",\"status\":\"AWAITED-DLR\"}},\"message\":\"Campaign of 1 numbers Submitted successfully.\"}"));
	}

	@Override
	public void updateShortUrl(ShortUrlData url) throws IOException {
		String data = "";
		data += "method=txtly.create";
		data += "&api_key=" + config.getPassCode(); // your loginId
		data += "&format=json";
		data += "&track=1";
		data += "&advanced=1";
		data += "&url=" + URLEncoder.encode(url.getUrl(), "UTF-8");
		data += "&id=" + url.getId();
		callService(shortURLConfig.getUrl(), data);
	}

}
