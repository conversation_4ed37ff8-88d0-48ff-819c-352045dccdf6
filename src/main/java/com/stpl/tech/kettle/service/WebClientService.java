package com.stpl.tech.kettle.service;

import java.io.IOException;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;

import com.stpl.tech.kettle.exceptions.WebServiceCallException;

public interface WebClientService {

	String getRequest(String url) throws Exception;

	String postRequest(String url, Map<String, String> bodyMap) throws Exception;

	String postRequest(String url, Object o) throws Exception;

	String postRequestWithAuthInternal(String url, Map<String, String> bodyMap, String authInternal);

	String getRequestWithAuthInternal(String url, String authInternal);

	String getRequest(String url, Object body, String authInternal, Map<String, String> uriVariables) throws Exception;

	String postRequest(String url, Object body, String authInternal, Map<String, String> uriVariables);

	String getRequestWithAuth(String url, String bodyText, String auth, Map<String, String> uriVariables);

	<T> T parseResponse(String response, Class<T> c);

	<T> T callWebServiceWithTimeout(Class<T> clazz, String endPoint, Object object, int socketTimeOut,
			int connectionTimeout) throws WebServiceCallException;

	String createPostRequestWithTimeout(String url, Object object, int readTimeOut,
			int connectionTimeout);
}
