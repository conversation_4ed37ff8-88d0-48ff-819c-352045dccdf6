package com.stpl.tech.kettle.service;

import java.io.IOException;
import java.util.Date;

import javax.jms.JMSException;

import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.NotificationPayload;
import com.stpl.tech.kettle.domain.model.OTPMapper;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderEmailEntryType;
import com.stpl.tech.master.domain.model.Brand;

public interface NotificationService {

	void triggerEmail(Brand brand, OrderDetail orderDetail, Order order, Customer customer, Date currentTimestamp,
			StringBuilder sb);

	void generateOrderEmailEvent(OrderEmailEntryType type, int orderId, int retryCount, String emailId,
			boolean isSystemGenerated, boolean isEmailVerified, Date currentTimestamp, String contact);

	void sendCharityOrderNotification(OrderDetail orderDetail, Date currentTimestamp);

	void generateOrderEmailEvent(OrderEmailEntryType type, int orderId, int retryCount, Customer customer,
			boolean isSystemGenerated, Date currentTimestamp);


	boolean sendNotification(String type, String message, String contact, MessagingClient client,
			boolean sendNotification, NotificationPayload payload) throws IOException, JMSException;

	OTPMapper getOTPMapperInstance();

	boolean sendOTPRequestViaIVR(String type, String contact, SMSWebServiceClient client, boolean sendNotification,
			NotificationPayload payload, String token, Integer ivrId);

}
