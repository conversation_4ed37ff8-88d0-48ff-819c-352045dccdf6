package com.stpl.tech.kettle.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.cache.BrandMetaDataCache;
import com.stpl.tech.kettle.cache.CampaignCache;
import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.core.ReceiptType;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.kettle.LoyaltyScore;
import com.stpl.tech.kettle.data.master.CouponDetailData;
import com.stpl.tech.kettle.data.master.DeliveryCouponDetailData;
import com.stpl.tech.kettle.domain.CustomerMappingTypes;
import com.stpl.tech.kettle.domain.model.CampaignStrategy;
import com.stpl.tech.kettle.domain.model.CreateNextOfferRequest;
import com.stpl.tech.kettle.domain.model.CreateOrderResult;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.CustomerRepeatType;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.kettle.domain.model.SubscriptionInfoDetail;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.repository.kettle.LoyaltyScoreDao;
import com.stpl.tech.kettle.repository.master.CouponDetailDataDao;
import com.stpl.tech.kettle.service.CouponService;
import com.stpl.tech.kettle.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.service.OfferManagementService;
import com.stpl.tech.kettle.service.OrderNotificationService;
import com.stpl.tech.kettle.service.SMSWebServiceClient;
import com.stpl.tech.kettle.service.SolsInfiniWebServiceClient;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.core.ReceiptType;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignMapping;
import com.stpl.tech.master.domain.model.CloneCouponData;
import com.stpl.tech.master.domain.model.CouponCloneRequest;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.master.domain.model.CouponData;
import com.stpl.tech.master.domain.model.IdentifierType;
import com.stpl.tech.master.domain.model.Unit;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class OfferManagementServiceImpl implements OfferManagementService {

	@Autowired
	private CouponDetailDataDao couponDetailDataDao;

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private CampaignCache campaignCache;

	@Autowired
	private ProductCache productCache;

	@Autowired
	private CustomerOfferManagementService customerOfferManagementService;

	@Autowired
	private CouponService couponService;

	@Autowired
	private LoyaltyScoreDao loyaltyScoreDao;

	@Autowired
	private UnitCacheService unitCacheService;

	@Autowired
	private BrandMetaDataCache brandMetaDataCache;

	@Autowired
	private OrderNotificationService orderNotificationService;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateCouponUsageByOne(int customerId, String offerCode, int orderId) {
		couponDetailDataDao.updateCouponUsageByOne(offerCode);
	}

	@Override
	public void createNBOandDNBOOffers(Order order, CreateOrderResult result, OrderInfo info, StringBuilder sb) {
		try {
		long startTime = System.currentTimeMillis();
		if (properties.isDinePostOrderOfferEnabled()) {
			info = applyDineInPostOrderOffer(info, order);

		} else {
			log.info(
					"DINE_IN_POST_ORDER_OFFER - Skipping as the environment property is disabled - Customer Id : {}, Order Id {} ",
					info.getCustomer().getId(), order.getOrderId());
		}
		sb.append(String.format(
				"\n----------- ,STEP 14-1, - ,Create Post Order Offers-NBO, ---------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));

		startTime = System.currentTimeMillis();
		if (properties.isDeliveryPostOrderOfferEnabled()) {
			info = applyDeliveryPostOrderOffer(info, order);
		} else {
			log.info(
					"DELIVERY_POST_ORDER_OFFER - Skipping as the environment property is disabled - Customer Id : {}, Order Id {} ",
					info.getCustomer().getId(), order.getOrderId());
		}
		sb.append(String.format(
				"\n----------- ,STEP 14-2, - ,Create Post Order Offers-DNBO, ---------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));
		startTime = System.currentTimeMillis();
		if (Objects.nonNull(info.getNextOffer()) || Objects.nonNull(info.getNextDeliveryOffer())) {
			info = createNBOCustomerReceipt(info, order);
		}
		sb.append(String.format("\n----------- ,STEP 14-3, - ,Customer print, ---------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));
		}catch (Exception e) {
			log.info("Exception ocurred during createNBOandDNBOOffers for order {} of customer {} ",order.getOrderId(),order.getCustomerId());
		}
	}

	@Override
	public OrderInfo applyDineInPostOrderOffer(OrderInfo info, Order order) {
		log.info("DINE_IN_POST_ORDER_OFFER - Starting the Process - Customer Id : {}, Order Id {} ",
				info.getCustomer().getId(), order.getOrderId());
		if (orderApplicableForPostOrderOffer(info.getOrder(), info.getUnit(), order.getCustomerId())) {
			Integer campaignId = campaignCache.getCurrentNBOCampaign(order.getUnitId());
			if (campaignId != null && campaignId > 0) {
				NextOffer offer = createNextOffer(campaignId, order.getOrderId(), info.getBrand().getBrandId(),
						info.getOrder().getUnitId(), info.getCustomer(), null, null, null, info.getOrderNotification(),
						true);
				if (offer != null) {
					info.setNextOffer(offer);
				}
			} else {
				log.info(
						"POST_ORDER_OFFER - Skipping as no campaign found for - Customer Id : {}, Order Id {}, unit id : {} ",
						info.getCustomer().getId(), order.getOrderId(), order.getUnitId());
			}
		} else {
			log.info(
					"DINE_IN_POST_ORDER_OFFER - Skipping as the order is not applicale for post order Offer - Customer Id : {}, Order Id {} ",
					info.getCustomer().getId(), order.getOrderId());
		}
		return info;
	}

	@Override
	public OrderInfo applyDeliveryPostOrderOffer(OrderInfo info, Order order) {
		log.info("DELIVERY_POST_ORDER_OFFER - Starting the Process - Customer Id : {}, Order Id {} ",
				info.getCustomer().getId(), order.getOrderId());
		if (orderApplicableForDeliveryPostOrderOffer(info.getOrder(), info.getUnit(), order.getCustomerId())) {
			Integer campaignId = campaignCache.getCurrentDNBOCampaign(order.getUnitId());
			if (Objects.nonNull(campaignId) && campaignId > 0) {
				NextOffer offer = createDNBOOffer(campaignId, order.getOrderId(), info.getBrand().getBrandId(),
						info.getOrder().getUnitId(), info.getCustomer(), null, null, null, info.getOrderNotification(),
						true);
				if (Objects.nonNull(offer)) {
					info.setNextDeliveryOffer(offer);
				}
			} else {
				log.info(
						"POST_ORDER_OFFER - Skipping as no campaign found for - Customer Id : {}, Order Id {}, unit id : {} ",
						info.getCustomer().getId(), order.getOrderId(), order.getUnitId());
			}
		} else {
			log.info(
					"DELIVERY_POST_ORDER_OFFER - Skipping as the order is not applicale for post order Offer - Customer Id : {}, Order Id {} ",
					info.getCustomer().getId(), order.getOrderId());
		}
		return info;
	}

	@Override
	public OrderInfo createNBOCustomerReceipt(OrderInfo info, Order order) {
		try {
			boolean needsCafeOrderPrint = !TransactionUtils.isSpecialOrder(order)
					|| TransactionUtils.isPaidEmployeeMeal(order);
			if (needsCafeOrderPrint) {
				String communicationPrint = TransactionUtils
						.getReceiptObject(properties.getChaayosBaseUrl(), ReceiptType.CUSTOMER_COMMUNICATION, info,
								properties.getBasePath(), properties.getRawPrintingSatus(), properties)
						.getContent();
				info.getAdditionalReceipts().add(communicationPrint);
				info.getAndroidReceipts().put(ReceiptType.CUSTOMER_COMMUNICATION, communicationPrint);
			}
		} catch (Exception e) {
			log.error("Error in generating next best offer customer communication receipt", e);
		}
		return info;
	}

	private boolean orderApplicableForPostOrderOffer(Order order, Unit unit, Integer customerId) {
		return !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId()) && !filterCafe(unit.getName())
				&& !TransactionUtils.isSpecialOrder(order.getOrderType()) && !order.isGiftCardOrder()
				&& !isSubscriptionProductOnly(order) && !TransactionUtils.isCODOrder(order.getSource())
				&& campaignCache.hasApplicableNBOOffer(unit.getId()) && !isSubscribedCustomer(customerId);
	}

	private boolean isSubscriptionProductOnly(Order order) {
		for (OrderItem orderItem : order.getOrders()) {
			if (!productCache.getSubscriptionProductDetails().containsKey(orderItem.getProductId())) {
				return false;
			}
		}
		return true;
	}

	private boolean isSubscribedCustomer(Integer customerId) {
		SubscriptionInfoDetail detail = customerOfferManagementService.getSubscriptionInfoDetail(customerId);
		if (Objects.nonNull(detail)) {
			log.info("Customer subscription status : {}", detail.isHasSubscription());
			return detail.isHasSubscription();
		}
		return false;
	}

	private boolean filterCafe(String name) {
		return name != null && (name.toLowerCase().contains("cod") || name.toLowerCase().contains(" dk ")
				|| name.toLowerCase().contains(" ck ") || name.toLowerCase().contains("odc")
				|| name.toLowerCase().contains("test") || name.toLowerCase().contains("kitchen")
				|| name.toLowerCase().contains("dark"));
	}

	private boolean orderApplicableForDeliveryPostOrderOffer(Order order, Unit unit, Integer customerId) {
		return !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId()) && !filterCafe(unit.getName())
				&& !TransactionUtils.isSpecialOrder(order.getOrderType()) && !order.isGiftCardOrder()
				&& !isSubscriptionProductOnly(order) && !TransactionUtils.isCODOrder(order.getSource())
				&& campaignCache.hasApplicableDNBOOffer(unit.getId()) && !isSubscribedCustomer(customerId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public NextOffer createNextOffer(Integer campaignId, Integer orderId, Integer brandId, Integer unitId,
			Customer customer, Integer applicableUnitId, String applicableUnitRegion, CreateNextOfferRequest request,
			OrderNotification orderNotification, boolean isOfferGeneratedByOffer) {
		int customerId = customer.getId();
		log.info(
				"POST_ORDER_OFFER -- Fetching campaign detail for campaign id : {}, orderId : {}, customer id : {}, unit id : {}, brand id : {}",
				campaignId, orderId, customer.getId(), unitId, brandId);
		CampaignDetail campaign = campaignCache.getCampaign(campaignId);
		CouponCloneResponse clonedCoupon = null;
		String notificationType = "SMS";
		CustomerCampaignOfferDetail postOrderOfferCreationSuccess = null;
		Map<String, Map<Integer, CampaignMapping>> couponMap = campaign.getMappings();
		String startDay = AppUtils.getDateString(AppUtils.getNextDate(AppUtils.getBusinessDate()),
				AppConstants.DATE_FORMAT);
		try {
			log.info("DINE_IN_POST_ORDER_OFFER - Starting the Process - Customer Id : {}, Order Id {} ", customerId,
					orderId);
			if (!customerOfferManagementService.hasCustomerReceivedPostOrderOffer(customerId,
					properties.getDinePostOrderOfferCheckLastNDaysValue(), campaign.getCampaignId())) {

				CustomerDineInView oneView = customerOfferManagementService.getCustomerDineInView(customerId, brandId,
						Arrays.asList(orderId));
				if (oneView != null) {
					log.info(
							"DINE_IN_POST_ORDER_OFFER - Found Customer One View - Customer Id : {}, Order Id {} and customer {}",
							customerId, orderId, oneView);
					if (isCustomerEligibleForPostOrderOffer(oneView)) {
						log.info(
								"DINE_IN_POST_ORDER_OFFER - Customer is eligble for the offer - Customer Id : {}, Order Id {} ",
								customerId, orderId);
						CustomerRepeatType type = null;
						if (orderId != null && orderId >= 0) {
							type = CustomerRepeatType.getCustomerRepeatTypeAfterPlacingOrder(
									oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
									oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
									AppConstants.getValue(oneView.getAvailedSignupOffer()));
						} else {
							type = CustomerRepeatType.getCustomerRepeatType(
									oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
									oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
									AppConstants.getValue(oneView.getAvailedSignupOffer()));
						}
						CampaignMapping cloneCode = campaignCache.getCloneCode(type, 1, campaignId);
						CustomerMappingTypes customerMappingTypes = new CustomerMappingTypes(type.name(), 1,
								couponMap.get(type.name()).get(1).getValidityInDays(), type.getDefaultCloneCode(),
								couponMap.get(type.name()).get(1).getReminderDays());
						if (cloneCode != null) {
							log.info(
									"POST-ORDER-OFFER : Clone Code found :: {} for customer type :: {} and validity in days :: {}",
									cloneCode.getCode(), type.name(), customerMappingTypes.getValidityInDays());
							if (campaign.isCouponClone()) {
								if (CustomerRepeatType.NEW.equals(type)
										&& AppConstants.LOYAL_TEA_COUPON_CODE.equals(cloneCode.getCode())) {
									startDay = AppUtils.getDateString(AppUtils.getNextDate(oneView.getLastOrderTime()),
											AppUtils.DATE_FORMAT_STRING);
									clonedCoupon = getCloneCode(startDay, Arrays.asList(customer.getContactNumber()),
											type, cloneCode.getCode(), 1, type.getValidityInDays(),
											campaign.getCouponPrefix(), applicableUnitId, applicableUnitRegion);
								} else {
									clonedCoupon = getCloneCode(startDay, Arrays.asList(customer.getContactNumber()),
											type, cloneCode.getCode(), 1, customerMappingTypes.getValidityInDays(),
											campaign.getCouponPrefix(), applicableUnitId, applicableUnitRegion);
								}
								postOrderOfferCreationSuccess = customerOfferManagementService.createPostOrderOffer(
										brandId, unitId, campaignId, customerId, orderId, customer.getContactNumber(),
										customer.getCountryCode(),
										clonedCoupon.getMappings().get(customer.getContactNumber()),
										customer.getFirstName(), cloneCode.getDesc(), clonedCoupon.getCode(), campaign,
										type, 1, request);
								if (postOrderOfferCreationSuccess != null) {
									log.info(
											"DINE_IN_POST_ORDER_OFFER - Generated Clone Coupon Successfully - Customer Id : {}, Order Id {} ",
											customerId, orderId);
									String contentUrl = getContentUrl(type);
									if (CustomerRepeatType.NEW.equals(type)
											&& !AppConstants.LOYAL_TEA_COUPON_CODE.equals(cloneCode.getCode())) {
										removeSecondFreeChai(customerId,SignupOfferStatus.FORCE_EXPIRED.name());
									}
									NextOffer offer = getNextOffer(brandId, customer.getContactNumber(), customerId,
											customer.getFirstName(), postOrderOfferCreationSuccess, type,
											notificationType, contentUrl);
									sendPostOrderOfferNotification(offer, postOrderOfferCreationSuccess,
											customer.getOptWhatsapp(), orderNotification, isOfferGeneratedByOffer);
									offer.setIsExistingOffer(false);
									return offer;
								} else {
									log.info(
											"DINE_IN_POST_ORDER_OFFER - Failed to Generate Clone Coupon - Customer Id : {}, Order Id {} ",
											customerId, orderId);
									setCouponCodeAsInactive(clonedCoupon.getMappings().get(customer.getContactNumber())
											.getCouponDetailId());
								}
							} else {
								CouponDetailData couponDetail = couponDetailDataDao
										.findByCouponCode(cloneCode.getCode());
								CouponData couponData = getCouponData(couponDetail, customer.getContactNumber());
								couponService.addCustomerMappingCouponData(couponDetail, customer.getContactNumber());
								postOrderOfferCreationSuccess = customerOfferManagementService.createPostOrderOffer(
										brandId, unitId, campaignId, customerId, orderId, customer.getContactNumber(),
										customer.getCountryCode(), couponData, customer.getFirstName(),
										cloneCode.getDesc(), cloneCode.getCode(), campaign, type, 1, request);
								if (postOrderOfferCreationSuccess != null) {
									log.info(
											"DINE_IN_POST_ORDER_OFFER - Generated Clone Coupon Successfully - Customer Id : {}, Order Id {} ",
											customerId, orderId);
									String contentUrl = getContentUrl(type);
									NextOffer offer = getNextOffer(brandId, customer.getContactNumber(), customerId,
											customer.getFirstName(), postOrderOfferCreationSuccess, type,
											notificationType, contentUrl);
									sendPostOrderOfferNotification(offer, postOrderOfferCreationSuccess,
											customer.getOptWhatsapp(), orderNotification, isOfferGeneratedByOffer);
									offer.setIsExistingOffer(false);
									return offer;
								}

							}
						} else {
							log.info(
									"DINE_IN_POST_ORDER_OFFER - Skipping as no clone code is defined for : {}, Order Id {}, CustomerType {}, Journey No : {}, Campaign Id {}  ",
									customerId, orderId, type, 1, campaignId);

						}
					} else {
						log.info(
								"DINE_IN_POST_ORDER_OFFER - Skipping as the customer is not eligible for offer - Customer Id : {}, Order Id {} ",
								customerId, orderId);

					}
				} else {
					log.info(
							"DINE_IN_POST_ORDER_OFFER - Skipping as the customer one view data not found - Customer Id : {}, Order Id {} ",
							customerId, orderId);
				}
			} else {
				if (orderId != null && orderId > 0) {
					log.info(
							"DINE_IN_POST_ORDER_OFFER - Skipping as the customer has already recieved the offer - Customer Id : {}, Order Id {} ",
							customerId, orderId);
				} else {
					log.info("DINE_IN_POST_ORDER_OFFER - getting existing offer created for customer id : {}",
							customerId);
					CustomerDineInView oneView = customerOfferManagementService.getCustomerDineInView(customerId,
							brandId, Arrays.asList(orderId));
					if (oneView != null) {
						postOrderOfferCreationSuccess = customerOfferManagementService
								.getActiveCustomerOffer(customerId, CampaignStrategy.NBO.name());
						if (postOrderOfferCreationSuccess != null) {
							CustomerRepeatType type;
							String customerTypeName = getCustomerType(campaign,
									postOrderOfferCreationSuccess.getCampaignCloneCode());
							if (Objects.nonNull(customerTypeName)) {
								type = CustomerRepeatType.getTypeFromName(customerTypeName);
							} else {
								type = CustomerRepeatType.getCustomerRepeatType(
										oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
										oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
										AppConstants.getValue(oneView.getAvailedSignupOffer()));
							}
							String contentUrl = getContentUrl(type);
							NextOffer offer = getNextOffer(brandId, customer.getContactNumber(), customerId,
									customer.getFirstName(), postOrderOfferCreationSuccess, type, notificationType,
									contentUrl);
							offer.setIsExistingOffer(true);
							sendPostOrderOfferNotification(offer, postOrderOfferCreationSuccess,
									customer.getOptWhatsapp(), orderNotification, isOfferGeneratedByOffer);
							return offer;
						}
					}
				}
			}
			log.info("DINE_IN_POST_ORDER_OFFER - Ending the Process - Customer Id : {}, Order Id {} ", customerId,
					orderId);

		} catch (Exception e) {
			log.error("DINE_IN_POST_ORDER_OFFER - Error In Creating Offer - Customer Id : {}, Order Id {} ", customerId,
					orderId, e);
			if (clonedCoupon != null && clonedCoupon.getMappings().containsKey(customer.getContactNumber())
					&& clonedCoupon.getMappings().get(customer.getContactNumber()).getCouponDetailId() != null) {
				setCouponCodeAsInactive(
						clonedCoupon.getMappings().get(customer.getContactNumber()).getCouponDetailId());
			}
		}
		return null;
	}

	private boolean isCustomerEligibleForPostOrderOffer(CustomerDineInView oneView) {
		return oneView.getActiveDineInOrders() >= 0 || oneView.getDineInOrders() >= 0;
	}

	private String getContentUrl(CustomerRepeatType type) {
		String contentUrl = null;
		switch (type) {
		case REGISTER:
			contentUrl = "CLM_REGISTER";
			break;
		case REPEAT:
			contentUrl = "CLM_REPEAT";
			break;
		case DORMANT:
			contentUrl = "CLM_DORMANT";
			break;
		case NEW:
			contentUrl = "LOYAL_TEA";
			break;
		case DEFAULT:
			contentUrl = "DEFAULT";
		default:
			contentUrl = "LOYAL_TEA";
		}
		return contentUrl;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public CouponCloneResponse getCloneCode(String startDay, List<String> contactNumbers, CustomerRepeatType type,
			String cloneCode, int usageCount, int validityInDays, String prefix, Integer applicableUnitId,
			String applicableRegion) throws DataUpdationException {
		if (type.equals(CustomerRepeatType.NEW) && AppConstants.LOYAL_TEA_COUPON_CODE.equals(cloneCode)) {
			CouponCloneResponse response = new CouponCloneResponse();
			String endDay = AppUtils.getDateString(AppUtils.getDayBeforeOrAfterDay(
					AppUtils.getDate(startDay, AppUtils.DATE_FORMAT_STRING), validityInDays - 1));
			response.setCode(cloneCode);
			response.setDescription("A Free Chai");
			response.setIdentifier(IdentifierType.CONTACT_NUMBER);
			Map<String, CouponData> dataMap = new HashMap<>();
			for (String contactNumber : contactNumbers) {
				dataMap.put(contactNumber, new CouponData(cloneCode, startDay, endDay, 1, null, null));
			}
			response.setMappings(dataMap);
			return response;
		} else {
			CouponDetailData clone = couponDetailDataDao.findByCouponCode(cloneCode);
			CloneCouponData cloneCoupon = getCloneCouponData(usageCount, validityInDays, prefix, clone,
					applicableUnitId, applicableRegion);
			return createCoupon(cloneCoupon, contactNumbers, startDay, applicableUnitId, applicableRegion);
		}

	}

	private CloneCouponData getCloneCouponData(int usageCount, int validityInDays, String prefix,
			CouponDetailData clone, Integer applicableUnitId, String applicableRegion) {
		CloneCouponData data = new CloneCouponData();
		data.setCloneCouponCode(clone.getCouponCode());
		data.setOfferDetailId(clone.getOfferDetail().getOfferDetailId());
		data.setPrefix(prefix);
		data.setUsageCount(usageCount);
		data.setMaxCustomerUsage(clone.getMaxCustomerUsage());
		data.setValidityInDays(validityInDays);
		data.setApplicableRegion(applicableRegion);
		data.setApplicableUnitId(applicableUnitId);
		return data;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public CouponCloneResponse createCoupon(CloneCouponData info, List<String> contactNumbers, String startDay,
			Integer applicableUnitId, String applicableRegion) throws DataUpdationException {
		CouponCloneRequest request = new CouponCloneRequest();
		request.setIdentifierType(IdentifierType.CONTACT_NUMBER);
		request.setCode(info.getCloneCouponCode());
		request.setPrefix(info.getPrefix());
		request.setStartDay(startDay);
		request.setUsageCount(info.getUsageCount());
		request.setMaxCustomerUsage(info.getMaxCustomerUsage());
		request.setValidDays(info.getValidityInDays());
		request.getIdentifier().addAll(contactNumbers);
		request.setApplicableRegion(applicableRegion);
		request.setApplicableUnitId(applicableUnitId);
		return couponService.generateCoupon(request);
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public void removeSecondFreeChai(Integer customerId,String signupOfferStatus) {
        LoyaltyScore score = loyaltyScoreDao.findByCustomerId(customerId);
        if (Objects.nonNull(score) && AppConstants.NO.equals(score.getAvailedSignupOffer())) {
            score.setAvailedSignupOffer(AppConstants.YES);
            score.setSignupOfferExpired(AppConstants.YES);
            score.setSignupOfferExpiryTime(AppUtils.getCurrentTimestamp());
            score.setSignupOfferStatus(signupOfferStatus);
            loyaltyScoreDao.save(score);
        }
    }

	private NextOffer getNextOffer(Integer brandId, String contactNumber, Integer customerId, String firstName,
			CustomerCampaignOfferDetail r, CustomerRepeatType type, String notificationType, String contentUrl) {
		String channelPartner = null;
		if (Objects.isNull(r)) {
			return new NextOffer(brandId, contactNumber, customerId, firstName, false, null, null, null, null, null,
					null, null, null, null, null);
		}
		if (Objects.nonNull(r.getChannelPartner())) {

			channelPartner = unitCacheService.getChannelPartnerById(r.getChannelPartner()).getName();
		}
		return new NextOffer(brandId, contactNumber, customerId, firstName, true, r.getCouponCode(),
				AppUtils.getDateString(r.getCouponStartDate()), AppUtils.getDateString(r.getCouponEndDate()),
				r.getOfferText(), type.name(), contentUrl, notificationType, channelPartner, r.getCouponType(),
				r.getChannelPartner());
	}

	private String sendPostOrderOfferNotification(NextOffer offer,
			CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp,
			OrderNotification orderNotification, boolean isOfferGeneratedByOffer) {
		boolean status = false;
		Brand brand = brandMetaDataCache.getBrandMetaData().get(offer.getBrandId());
		SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
		log.info("Next Off De {} ::::: {}", offer.toString(), offer.isAvailable());
		if (Objects.nonNull(offer) && offer.isAvailable()) {
			log.info("Next Best Offer SMS Details to be send to customer :: {}", offer.getContactNumber());
			status = orderNotificationService.sendNextBestOfferNotification(offer, smsWebServiceClient,
					postOrderOfferCreationSuccess, optWhatsapp, orderNotification, isOfferGeneratedByOffer);
		}
		log.info("Message Status For Customer {} :: {}", offer.getContactNumber(), status);
		return "SMS";
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void setCouponCodeAsInactive(Integer couponDetailId) {
		Optional<CouponDetailData> data = couponDetailDataDao.findById(couponDetailId);
		if (data.isPresent()) {
			CouponDetailData coupon = data.get();
			coupon.setCouponStatus(AppConstants.IN_ACTIVE);
			couponDetailDataDao.save(coupon);
		}
	}

	private CouponData getCouponData(CouponDetailData couponDetail, String contactNumber) {
		if (Objects.nonNull(couponDetail)) {
			CouponData couponData = new CouponData(couponDetail.getCouponCode(),
					AppUtils.getDateString(couponDetail.getStartDate()),
					AppUtils.getDateString(couponDetail.getEndDate()), couponDetail.getUsageCount(),
					couponDetail.getCouponDetailId(), couponDetail.getOfferDetail().getOfferDetailId());
			return couponData;
		}
		return null;
	}

	private String getCustomerType(CampaignDetail campaignDetail, String code) {
		for (Map.Entry<String, Map<Integer, CampaignMapping>> entry : campaignDetail.getMappings().entrySet()) {
			for (Map.Entry<Integer, CampaignMapping> innerEntry : entry.getValue().entrySet()) {
				if (innerEntry.getValue().getCode().equals(code)) {
					return entry.getKey();
				}
			}
		}
		log.info("No customer type found for clone code :: {}", code);
		return null;
	}

	protected NextOffer createDNBOOffer(Integer campaignId, Integer orderId, Integer brandId, Integer unitId,
			Customer customer, Integer applicableUnitId, String applicableUnitRegion, CreateNextOfferRequest request,
			OrderNotification orderNotification, boolean isOfferGeneratedByOrder) {
		int customerId = customer.getId();
		CampaignDetail campaign = campaignCache.getCampaign(campaignId);
		DeliveryCouponDetailData deliveryCoupon = null;
		String notificationType = "SMS";
		CustomerCampaignOfferDetail postOrderOfferCreationSuccess = null;
		Map<String, Map<Integer, CampaignMapping>> couponMap = campaign.getMappings();
		try {
			log.info(
					"DELIVERY_POST_ORDER_OFFER - Starting the Process - Customer Id : {}, Order Id {}, Campaign Id : {} ",
					customerId, orderId, campaignId);
			if (!customerOfferManagementService.hasCustomerReceivedDNBO(customerId,
					properties.getDinePostOrderOfferCheckLastNDaysValue(), campaign.getCampaignId())) {
				CustomerDineInView oneView = customerOfferManagementService.getCustomerDineInView(customerId, brandId,
						Arrays.asList(orderId));
				if (oneView != null) {
					log.info(
							"DELIVERY_POST_ORDER_OFFER - Found Customer One View - Customer Id : {}, Order Id {} and customer {}",
							customerId, orderId, oneView);
					if (isCustomerEligibleForPostOrderOffer(oneView)) {
						log.info(
								"DELIVERY_POST_ORDER_OFFER - Customer is eligble for the offer - Customer Id : {}, Order Id {} ",
								customerId, orderId);
						CustomerRepeatType type = null;
						if (orderId != null && orderId >= 0) {
							type = CustomerRepeatType.getCustomerRepeatTypeAfterPlacingOrder(
									oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
									oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
									AppConstants.getValue(oneView.getAvailedSignupOffer()));
						} else {
							type = CustomerRepeatType.getCustomerRepeatType(
									oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
									oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
									AppConstants.getValue(oneView.getAvailedSignupOffer()));
						}
						CampaignMapping cloneCode = campaignCache.getCloneCode(type, 1, campaignId);
						CustomerMappingTypes customerMappingTypes = new CustomerMappingTypes(type.name(), 1,
								couponMap.get(type.name()).get(1).getValidityInDays(), type.getDefaultCloneCode(),
								type.getReminderDays());
						if (cloneCode != null) {
							log.info(
									"DELIVERY_POST_ORDER_OFFER : Clone Code found :: {} for customer type :: {} and validity in days :: {}",
									cloneCode.getCode(), type.name(), customerMappingTypes.getValidityInDays());
							if (campaign.isCouponClone()) {
								deliveryCoupon = customerOfferManagementService
										.getDeliveryCloneCode(cloneCode.getCode(), brandId, true);
								if (Objects.nonNull(deliveryCoupon)) {
									Integer couponDelay = Objects.nonNull(campaign.getCouponApplicableAfter())
											? campaign.getCouponApplicableAfter()
											: 0;
									Date startDate = AppUtils.getNextDate(
											AppUtils.addDays(AppUtils.getCurrentTimestamp(), couponDelay - 1));
									if (AppUtils.isBefore(startDate, deliveryCoupon.getStartDate())) {
										startDate = deliveryCoupon.getStartDate();
									}
									Date endDate = AppUtils.getDayBeforeOrAfterDay(startDate,
											cloneCode.getValidityInDays() - 1);
									if (AppUtils.isBefore(deliveryCoupon.getEndDate(), endDate)) {
										endDate = deliveryCoupon.getEndDate();
									}
									CouponData couponData = new CouponData(deliveryCoupon.getCouponCode(),
											AppUtils.getDateString(startDate, AppUtils.DATE_FORMAT_STRING),
											AppUtils.getDateString(endDate, AppUtils.DATE_FORMAT_STRING),
											campaign.getUsageLimit(), null, null);
									postOrderOfferCreationSuccess = customerOfferManagementService
											.createDeliveryPostOrderOffer(brandId, unitId, campaignId, customer,
													orderId, customer.getContactNumber(), customer.getCountryCode(),
													couponData, customer.getFirstName(), cloneCode.getDesc(),
													deliveryCoupon.getMasterCoupon(), deliveryCoupon,
													campaign.isCouponClone(), campaign, type, 1, request);

									if (postOrderOfferCreationSuccess != null) {
										log.info(
												"DELIVERY_POST_ORDER_OFFER - Generated Clone Coupon Successfully - Customer Id : {}, Order Id {} ",
												customerId, orderId);
										String contentUrl = getContentUrl(type);
										NextOffer offer = getNextOffer(brandId, customer.getContactNumber(), customerId,
												customer.getFirstName(), postOrderOfferCreationSuccess, type,
												notificationType, contentUrl);
										orderNotificationService.sendDeliveryPostOrderOfferNotification(offer, postOrderOfferCreationSuccess,
												customer.getOptWhatsapp(), orderNotification, isOfferGeneratedByOrder);
										offer.setIsExistingOffer(false);
										return offer;
									} else {
										log.info(
												"DELIVERY_POST_ORDER_OFFER - Failed to Generate Clone Coupon - Customer Id : {}, Order Id {} ",
												customerId, orderId);
									}
								} else {
									log.info(
											"DELIVERY_POST_ORDER_OFFER - No partner coupon found for - Master coupon : {}",
											cloneCode.getCode());
								}
							} else {
								log.info(
										"DELIVERY_POST_ORDER_OFFER - Fetching master coupon detail as isClone Coupon is {}",
										campaign.isCouponClone());
								deliveryCoupon = customerOfferManagementService
										.getDeliveryCloneCode(cloneCode.getCode(), brandId, false);
								if (Objects.nonNull(deliveryCoupon)) {
									Integer couponDelay = Objects.nonNull(campaign.getCouponApplicableAfter())
											? campaign.getCouponApplicableAfter()
											: 0;
									Date startDate = AppUtils.getNextDate(
											AppUtils.addDays(AppUtils.getCurrentTimestamp(), couponDelay - 1));
									if (AppUtils.isBefore(startDate, deliveryCoupon.getStartDate())) {
										startDate = deliveryCoupon.getStartDate();
									}
									Date endDate = AppUtils.getDayBeforeOrAfterDay(startDate,
											cloneCode.getValidityInDays() - 1);
									if (AppUtils.isBefore(deliveryCoupon.getEndDate(), endDate)) {
										endDate = deliveryCoupon.getEndDate();
									}
									CouponData couponData = new CouponData(deliveryCoupon.getMasterCoupon(),
											AppUtils.getDateString(startDate, AppUtils.DATE_FORMAT_STRING),
											AppUtils.getDateString(endDate, AppUtils.DATE_FORMAT_STRING),
											campaign.getUsageLimit(), null, null);
									postOrderOfferCreationSuccess = customerOfferManagementService
											.createDeliveryPostOrderOffer(brandId, unitId, campaignId, customer,
													orderId, customer.getContactNumber(), customer.getCountryCode(),
													couponData, customer.getFirstName(), cloneCode.getDesc(),
													deliveryCoupon.getMasterCoupon(), deliveryCoupon,
													campaign.isCouponClone(), campaign, type, 1, request);
									if (postOrderOfferCreationSuccess != null) {
										log.info(
												"DELIVERY_POST_ORDER_OFFER - Generated Clone Coupon Successfully - Customer Id : {}, Order Id {} ",
												customerId, orderId);
										String contentUrl = getContentUrl(type);
										NextOffer offer = getNextOffer(brandId, customer.getContactNumber(), customerId,
												customer.getFirstName(), postOrderOfferCreationSuccess, type,
												notificationType, contentUrl);
										orderNotificationService.sendDeliveryPostOrderOfferNotification(offer, postOrderOfferCreationSuccess,
												customer.getOptWhatsapp(), orderNotification, isOfferGeneratedByOrder);
										offer.setIsExistingOffer(false);
										return offer;
									} else {
										log.info(
												"DELIVERY_POST_ORDER_OFFER - Failed to Generate Clone Coupon - Customer Id : {}, Order Id {} ",
												customerId, orderId);
									}
								} else {
									log.info("DELIVERY_POST_ORDER_OFFER - No Master coupon found for : {}",
											cloneCode.getCode());
								}
							}
						} else {
							log.info(
									"DELIVERY_POST_ORDER_OFFER - Skipping as no clone code is defined for : {}, Order Id {}, CustomerType {}, Journey No : {}, Campaign Id {}  ",
									customerId, orderId, type, 1, campaignId);

						}
					} else {
						log.info(
								"DELIVERY_POST_ORDER_OFFER - Skipping as the customer is not eligible for offer - Customer Id : {}, Order Id {} ",
								customerId, orderId);

					}
				} else {
					log.info(
							"DELIVERY_POST_ORDER_OFFER - Skipping as the customer one view data not found - Customer Id : {}, Order Id {} ",
							customerId, orderId);
				}
			} else {
				if (orderId != null && orderId > 0) {
					log.info(
							"DELIVERY_POST_ORDER_OFFER - Skipping as the customer has already recieved the offer - Customer Id : {}, Order Id {} ",
							customerId, orderId);
				} else {
					log.info("DELIVERY_POST_ORDER_OFFER - getting existing offer created for customer id : {}",
							customerId);
					CustomerDineInView oneView = customerOfferManagementService.getCustomerDineInView(customerId,
							brandId, Arrays.asList(orderId));
					if (oneView != null) {
						postOrderOfferCreationSuccess = customerOfferManagementService
								.getActiveCustomerOffer(customerId, CampaignStrategy.DELIVERY_NBO.name());
						if (postOrderOfferCreationSuccess != null) {
							CustomerRepeatType type;
							String customerTypeName = getCustomerType(campaign,
									postOrderOfferCreationSuccess.getCampaignCloneCode());
							if (Objects.nonNull(customerTypeName)) {
								type = CustomerRepeatType.getTypeFromName(customerTypeName);
							} else {
								type = CustomerRepeatType.getCustomerRepeatType(
										oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
										oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
										AppConstants.getValue(oneView.getAvailedSignupOffer()));
							}
							String contentUrl = getContentUrl(type);
							NextOffer offer = getNextOffer(brandId, customer.getContactNumber(), customerId,
									customer.getFirstName(), postOrderOfferCreationSuccess, type, notificationType,
									contentUrl);
							offer.setIsExistingOffer(true);
							orderNotificationService.sendDeliveryPostOrderOfferNotification(offer, postOrderOfferCreationSuccess,
									customer.getOptWhatsapp(), orderNotification, isOfferGeneratedByOrder);
							return offer;
						}
					}
				}
			}
			log.info("DELIVERY_POST_ORDER_OFFER - Ending the Process - Customer Id : {}, Order Id {} ", customerId,
					orderId);

		} catch (Exception e) {
			log.error("DELIVERY_POST_ORDER_OFFER - Error In Creating Offer - Customer Id : {}, Order Id {} ",
					customerId, orderId, e);
		}
		return null;

	}

}
