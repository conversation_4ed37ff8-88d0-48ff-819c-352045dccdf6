package com.stpl.tech.kettle.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.stpl.tech.kettle.domain.model.ClevertapChargedEventData;
import com.stpl.tech.kettle.domain.model.EventUploadRequest;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.ProfileUploadRequest;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.repository.clm.CustomerInfoDataDao;
import com.stpl.tech.kettle.util.AppUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.converter.CleverTapConverter;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.CleverTapProfilePushTrack;
import com.stpl.tech.kettle.data.kettle.EventPushTrack;
import com.stpl.tech.kettle.domain.model.CleverTapPushResponse;
import com.stpl.tech.kettle.domain.model.OrderFeedbackMetadata;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.publisher.CleverTapEventPublisher;
import com.stpl.tech.kettle.service.AsyncExecutorService;
import com.stpl.tech.kettle.service.CleverTapDataPushService;
import com.stpl.tech.kettle.service.OrderNotificationService;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.Constants.CleverTapConstants;
import com.stpl.tech.kettle.util.Constants.CleverTapEvents;

import lombok.extern.log4j.Log4j2;
import org.springframework.util.CollectionUtils;

@Service
@Log4j2
public class AsyncExecutorServiceImpl implements AsyncExecutorService {

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private OrderNotificationService orderNotificationService;

	@Autowired
	private CleverTapEventPublisher cleverTapEventPublisher;

	@Autowired
	private CleverTapDataPushService cleverTapDataPushService;

	@Autowired
	private ProductCache productCache;

	@Autowired
	private CleverTapConverter cleverTapConverter;

	@Autowired
	private CustomerInfoDataDao customerInfoDataDao;

	@Override
	@Async(value = "taskExecutor")
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void pushDataToThirdPartyAnalytics(OrderInfo info, boolean isOrderCancelled,Map<Integer, OrderNotification> orderNotificationMap) {
		if (!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(info.getCustomer().getId())) {
			try {
				info.getRefrenceOrderIds().add(info.getOrder().getOrderId());
				boolean clvFlag = properties.getCleverTapEnabled();

				boolean clvNBOFlag = info.getChannelPartner().getId() == AppConstants.CHANNEL_PARTNER_DINE_IN_APP
						&& Objects.nonNull(info.getNextOffer()) && Objects.nonNull(info.getNextOffer().getContentUrl())
						&& Objects.nonNull(info.getNextOffer().isAvailable());

				boolean fbFlag = properties.getFacebookPushEnabled()
						&& info.getOrder().getOrderType().equals(AppConstants.ORDER_TYPE_REGULAR)
						&& info.getChannelPartner().getId() != AppConstants.CHANNEL_PARTNER_SWIGGY
						&& !info.getOrder().isGiftCardOrder();

				boolean sendCODOrderFeedbackNotification = info.getChannelPartner()
						.getId() == AppConstants.CHANNEL_PARTNER_SWIGGY
						&& info.getChannelPartner().getId() == AppConstants.CHANNEL_PARTNER_ZOMATO
						&& properties.getSendFeedbackMessageForCODOrders();
				if ((fbFlag || clvFlag || clvNBOFlag) && !sendCODOrderFeedbackNotification) {

					try {
						if (clvFlag) {
							if (info.getBrand().getBrandId().equals(1) && !isOrderCancelled) {
								OrderFeedbackMetadata orderFeedbackMetadata = new OrderFeedbackMetadata();
								try {
									orderNotificationService.generateOrderFeedbackNotification(info,
											orderFeedbackMetadata);
								} catch (Exception e) {
									log.error(
											"Exception while generating order feedback and receipt deatils for orderId ::{}",
											info.getOrder().getOrderId(), e);
								}
								try {
									orderNotificationService.createOrderNotificationData(info.getOrderNotification(),
											info);
								} catch (Exception e) {
									log.error("Exception while saving order notification data for orderId ::{}",
											info.getOrder().getOrderId(), e);
								}
								pushDataToCleverTapNew(info, isOrderCancelled, orderFeedbackMetadata,orderNotificationMap);
							}
						}
						if (!isOrderCancelled) {
							if (fbFlag) {
								cleverTapEventPublisher.publishCleverTapEvent(properties.getEnvironmentType().name(),
										info.getOrder(), false);
							}
						}
					} catch (Exception e) {
						log.info("Exception occured during pushing data to third party analytics", e);
					}

				}

			} catch (Exception e) {
				log.error("Error while  pushing data to thir party analytics {}", e.getMessage());
			}
		}

	}
	private void pushDataToCleverTap(OrderInfo info, boolean isOrderCancelled,
			OrderFeedbackMetadata orderFeedbackMetadata,Map<Integer, OrderNotification> orderNotificationMap) {
		log.info("Checking and updating data for the customer:{}", info.getOrder().getCustomerId());
		try {
			cleverTapDataPushService.pushUserToCleverTap(info.getOrder().getCustomerId());
		} catch (Exception e) {
			log.error("error while pushing customer data to clevertap {}", e.getMessage());
			cleverTapDataPushService.persistProfileTrack(new CleverTapProfilePushTrack(info.getOrder().getCustomerId(),
					AppConstants.ERROR, CleverTapConstants.REGULAR));
		}

		if (!isOrderCancelled) {
			log.info("Publishing order event detail to clevertap for orderid: {}", info.getOrder().getOrderId());
			try {
				CleverTapPushResponse response = cleverTapDataPushService.uploadEvent(info.getRefrenceOrderIds(),
						CleverTapConstants.REGULAR, orderNotificationMap);
				try {
					if (Objects.nonNull(response) && (response.getStatus().equalsIgnoreCase("Success")
							|| response.getStatus().equalsIgnoreCase("PUSHED_TO_QUEUE"))) {
						orderNotificationService.updateFeedbackEventStatus(orderFeedbackMetadata, true);
					} else {
						orderNotificationService.updateFeedbackEventStatus(orderFeedbackMetadata, false);
					}
				} catch (Exception e) {
					log.error("Error while updating Feedbcak event status for order with id :{}",
							info.getOrder().getOrderId(), e);
				}
				if (Objects.nonNull(response)) {
					cleverTapDataPushService.persistEventTracks(response.getEvents());
				}
			} catch (Exception e) {
				log.error("error while pushing clevertap event data {}", e);
				cleverTapDataPushService
						.persistEventTrack(new EventPushTrack(CleverTapEvents.CHARGED, info.getOrder().getOrderId(),
								AppConstants.ERROR, CleverTapConstants.REGULAR, AppConstants.CLEVERTAP));
			}
		}
	}

	@Override
	@Async(value = "taskExecutor")
	public void deleteReciepts(int unitId, int orderId) {
		try {
			String basePath = properties.getBasePath() + AppConstants.FORWARD_SLASH + unitId
					+ AppConstants.ORDER_RECEIPT_PATH + orderId;
			FileUtils.deleteDirectory(new File(basePath));
		} catch (Exception e) {
			log.info("Exception occured during cleaning order receipts for order {} ", orderId, e);
		}
	}

	@Override
	public void pushDataToCleverTapForPartner(OrderInfo info, boolean isOrderCancelled, Map<Integer, OrderNotification> orderNotificationMap) {
		try {
			boolean clvFlag = properties.getCleverTapEnabled();
			if(clvFlag){
				orderNotificationService.createOrderNotificationData(info.getOrderNotification(),info);
				pushPartnerDataToCleverTap(info,isOrderCancelled);
			}
		}catch (Exception e){
			log.error("Error in pushing data to clevertap for partner for orderId : {} with error : {}",info.getOrder().getOrderId(),e.getMessage());
		}
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	private void pushPartnerDataToCleverTap(OrderInfo info,boolean isOrderCancelled) {
		log.info("Checking and updating data for the customer with partner customer id :{}", info.getPartnerCustomerId());
		Integer customerId = customerInfoDataDao.getKettleCustomerId(info.getPartnerCustomerId());
		if (Objects.nonNull(customerId)) {
			try {
				cleverTapDataPushService.pushUserToCleverTap(customerId);
			} catch (Exception e) {
				log.error("Error in pushing user data for partner to cleverTap for order id : {} and partner customer id : {}"
						, info.getOrder().getOrderId(), info.getPartnerCustomerId());
				cleverTapDataPushService.persistProfileTrack(
						new CleverTapProfilePushTrack(customerId,
								AppConstants.ERROR, CleverTapConstants.REGULAR));
			}
			if (!isOrderCancelled) {
				log.info("Publishing new order event detail for partner to clevertap for orderid: {}",
						info.getOrder().getOrderId());
				try {
					Map<Integer, OrderNotification> orderNotificationMap = new HashMap<>();
					Map<Integer, Integer> orderListMap = new HashMap<>();
					orderNotificationMap.putIfAbsent(info.getOrder().getOrderId(), info.getOrderNotification());
					orderListMap.put(info.getOrder().getOrderId(), customerId);
					CleverTapPushResponse response = cleverTapDataPushService.uploadEventForPartner(
							orderListMap, getOrderType(info), CleverTapConstants.REGULAR, orderNotificationMap);
					cleverTapDataPushService.persistEventTracks(response.getEvents());
				} catch (Exception e) {
					log.error("error while pushing clevertap event data {}", e);
					cleverTapDataPushService.persistEventTrack(
							new EventPushTrack(CleverTapEvents.CHARGED, info.getOrder().getOrderId(), AppConstants.ERROR,
									CleverTapConstants.REGULAR, AppConstants.CLEVERTAP));
				}
			}
		}else{
			log.info("unable to push data to cleverTap for customer with partner customer id : {}",info.getPartnerCustomerId());
		}
	}

	private String getOrderType(OrderInfo info) {
		for (OrderItem item : info.getOrder().getOrders()) {
			if (Objects.nonNull(productCache.getSubscriptionProductDetail(item.getProductId()))) {
				return CleverTapEvents.SUBSCRIPTION_PURCHASED_EVENT;
			} else if (info.getOrder().isGiftCardOrder()) {
				return CleverTapEvents.WALLET_PURCHASED_EVENT;
			}
		}
		return CleverTapEvents.CHARGED;
	}

	private void pushDataToCleverTapNew(OrderInfo info, boolean isOrderCancelled,
										OrderFeedbackMetadata orderFeedbackMetadata,Map<Integer, OrderNotification> orderNotificationMap){
		log.info("Checking and updating data for the customer:{}", info.getOrder().getCustomerId());
		List<ProfileUploadRequest> profileUploadRequestList = null;
		List<EventUploadRequest> eventUploadRequestList=null;
		try{
			profileUploadRequestList = cleverTapDataPushService.getUserProfileForCleverTap(
					Arrays.asList(info.getOrder().getCustomerId()), CleverTapConstants.REGULAR);
		} catch (Exception e) {
			log.error("error while pushing customer data to clevertap {}", e.getMessage());
			cleverTapDataPushService.persistProfileTrack(new CleverTapProfilePushTrack(info.getOrder().getCustomerId(),
					AppConstants.ERROR, CleverTapConstants.REGULAR));
		}
		if(!isOrderCancelled){
			log.info("Publishing order event detail to clevertap for orderid: {}", info.getOrder().getOrderId());
			try {
				eventUploadRequestList = cleverTapDataPushService.getEventDataList(info.getRefrenceOrderIds(),
						CleverTapConstants.REGULAR, orderNotificationMap);
			}catch (Exception e){
				log.error("error while pushing clevertap event data {}", e);
				cleverTapDataPushService
						.persistEventTrack(new EventPushTrack(CleverTapEvents.CHARGED, info.getOrder().getOrderId(),
								AppConstants.ERROR, CleverTapConstants.REGULAR, AppConstants.CLEVERTAP));
			}
		}
		createEventAndProfileListAndPushDataToQueue(info,profileUploadRequestList,eventUploadRequestList,orderFeedbackMetadata);
	}

	private void createEventAndProfileListAndPushDataToQueue(OrderInfo info,List<ProfileUploadRequest> profileUploadRequestList
			,List<EventUploadRequest> eventUploadRequestList,OrderFeedbackMetadata orderFeedbackMetadata){
		List<Object> resultList = new ArrayList<>();
		List<CleverTapProfilePushTrack> profilePushTrackList = new ArrayList<>();
		if(!CollectionUtils.isEmpty(profileUploadRequestList)){
			for(ProfileUploadRequest p : profileUploadRequestList){
				resultList.add(p);
				CleverTapProfilePushTrack profilePushTrack = new CleverTapProfilePushTrack(info.getOrder().getCustomerId()
						,CleverTapConstants.PUSHED_TO_QUEUE, CleverTapConstants.REGULAR);
				profilePushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
				profilePushTrackList.add(profilePushTrack);
			}
		}
		List<EventPushTrack> eventPushTrackList = new ArrayList<>();
		boolean loyaltyWallet = false;
		try {
			if(!StringUtils.isEmpty(properties.getLoyaltWalletOfferCode()) &&
					properties.getLoyaltWalletOfferCode().equals(info.getOrder().getOfferCode())){
				loyaltyWallet = true;
			}
		}catch (Exception e){
			log.info("Error in checking Loyalty trade order : {}",e.getMessage());
		}
		if(!CollectionUtils.isEmpty(eventUploadRequestList)){
			EventPushTrack eventPushTrack;
			if(loyaltyWallet && eventUploadRequestList.size()==1){
				resultList.add(eventUploadRequestList.get(0));
				eventPushTrack = new EventPushTrack(CleverTapEvents.LOYALTY_WALLET_TRADE,info.getRefrenceOrderIds().get(0),
						CleverTapConstants.PUSHED_TO_QUEUE, CleverTapConstants.REGULAR,AppConstants.CLEVERTAP);
				eventPushTrackList.add(eventPushTrack);
			}else{
				for(EventUploadRequest e : eventUploadRequestList){
					resultList.add(e);
					if (e.getEvtData() instanceof ClevertapChargedEventData) {
						ClevertapChargedEventData eventData = (ClevertapChargedEventData) e.getEvtData();
						eventPushTrack = new EventPushTrack(e.getEvtName(),eventData.getOrderId(),
								CleverTapConstants.PUSHED_TO_QUEUE, CleverTapConstants.REGULAR,AppConstants.CLEVERTAP);
						eventPushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
						eventPushTrackList.add(eventPushTrack);
					}
				}
			}
		}
		pushDataToQueueAndUpdatefeedbackEventStatus(resultList,info.getOrder().getCustomerId(),info.getOrder().getOrderId()
				,orderFeedbackMetadata,profilePushTrackList,eventPushTrackList);
	}

	private void pushDataToQueueAndUpdatefeedbackEventStatus(List<Object> payload,Integer identity,Integer orderId
			,OrderFeedbackMetadata orderFeedbackMetadata,List<CleverTapProfilePushTrack> profilePushTrackList
			,List<EventPushTrack> eventPushTrackList){

		CleverTapPushResponse cleverTapPushResponse = cleverTapDataPushService.publishToCleverTapQueueNew(payload,
				identity,CleverTapConstants.REGULAR);
		if(Objects.nonNull(cleverTapPushResponse)){
			if(CleverTapConstants.FAILED_TO_PUSHED.equals(cleverTapPushResponse.getStatus())){
				for(CleverTapProfilePushTrack p : profilePushTrackList){
					p.setStatus(CleverTapConstants.FAILED_TO_PUSHED);
				}
				for(EventPushTrack e : eventPushTrackList){
					e.setStatus(CleverTapConstants.FAILED_TO_PUSHED);
				}
			}
			try {
				cleverTapDataPushService.persistEventTracks(eventPushTrackList);
				cleverTapDataPushService.persistProfileTrack(profilePushTrackList);
			}catch (Exception e){
				log.info("Error in persisting event and profile data : {}",e.getMessage());
			}
			try {
				if ((cleverTapPushResponse.getStatus().equalsIgnoreCase("Success")
						|| cleverTapPushResponse.getStatus().equalsIgnoreCase("PUSHED_TO_QUEUE"))) {
					orderNotificationService.updateFeedbackEventStatus(orderFeedbackMetadata, true);
				} else {
					orderNotificationService.updateFeedbackEventStatus(orderFeedbackMetadata, false);
				}
			} catch (Exception e) {
				log.error("Error while updating Feedbcak event status for order with id :{}",
						orderId, e);
			}
		}

	}

}
