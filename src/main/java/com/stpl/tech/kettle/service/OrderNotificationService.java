package com.stpl.tech.kettle.service;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.data.kettle.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.domain.model.FeedbackSource;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.OrderFeedbackMetadata;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.exceptions.DataUpdationException;

public interface OrderNotificationService {
	public void createOrderNotificationData(OrderNotification orderNotification, OrderInfo info)
			throws DataUpdationException;

	public void generateOrderFeedbackNotification(OrderInfo info, OrderFeedbackMetadata orderFeedbackMetadata);

	OrderFeedbackMetadata generateOrderFeedbackDetails(FeedbackSource source, OrderInfo orderInfo, boolean feedback,
			boolean generateReceipt, OrderFeedbackMetadata orderFeedbackMetadata);

	void updateFeedbackEventStatus(OrderFeedbackMetadata orderFeedbackMetadata, boolean isOrderNotificationSent);

	boolean sendNextBestOfferNotification(NextOffer offer, SMSWebServiceClient smsWebServiceClient,
			CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp,
			OrderNotification orderNotification, boolean isOfferGeneratedByOrder);

	String sendDeliveryPostOrderOfferNotification(NextOffer offer,
			CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp,
			OrderNotification orderNotification, boolean isOfferGeneratedByOrder);

	boolean sendDeliveryNextBestOfferNotification(NextOffer offer, SMSWebServiceClient smsWebServiceClient,
			CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp,
			boolean isOfferGeneratedByOrder);
}
