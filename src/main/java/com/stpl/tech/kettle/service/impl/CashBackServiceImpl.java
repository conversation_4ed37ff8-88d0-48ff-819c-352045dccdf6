package com.stpl.tech.kettle.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.data.kettle.CashData;
import com.stpl.tech.kettle.data.kettle.CashPacketData;
import com.stpl.tech.kettle.data.kettle.CashPacketLogData;
import com.stpl.tech.kettle.domain.model.CashBackOfferDTO;
import com.stpl.tech.kettle.domain.model.CashMetadataType;
import com.stpl.tech.kettle.domain.model.CashMetadataWrapper;
import com.stpl.tech.kettle.domain.model.CashPacketEventStatus;
import com.stpl.tech.kettle.domain.model.CashTransactionCategory;
import com.stpl.tech.kettle.domain.model.CashTransactionCode;
import com.stpl.tech.kettle.domain.model.CashTransactionMetadata;
import com.stpl.tech.kettle.domain.model.TransactionType;
import com.stpl.tech.kettle.repository.kettle.CashDataDao;
import com.stpl.tech.kettle.repository.kettle.CashPacketDataDao;
import com.stpl.tech.kettle.repository.kettle.CashPacketLogDataDao;
import com.stpl.tech.kettle.repository.kettle.CustomerDao;
import com.stpl.tech.kettle.repository.master.CouponDetailDataDao;
import com.stpl.tech.kettle.repository.master.OfferDetailDataDao;
import com.stpl.tech.kettle.service.CashBackOfferCache;
import com.stpl.tech.kettle.service.CouponService;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderMetadata;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.service.CashBackService;
import com.stpl.tech.kettle.service.CashPacketService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.TransactionUtils;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class CashBackServiceImpl implements CashBackService {

	@Autowired
	EnvironmentProperties props;

	@Autowired
	CashPacketService cashPacketService;

	@Autowired
	CustomerDao customerDao;

	@Autowired
	CashDataDao cashDataDao;

	@Autowired
	CashPacketDataDao cashPacketDataDao;

	@Autowired
	CashPacketLogDataDao cashPacketLogDataDao;

	@Autowired
	CashBackOfferCache cashBackOfferCache;

	@Autowired
	CouponService couponService;

	@Autowired
	UnitCacheService unitCacheService;

	@Override
	public void checkCashBack(Order order) {
		if (props.allowDohfulCash() && order.getBrandId().equals(AppConstants.DOHFUL_BRAND_ID) &&
				AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(order.getOfferCode()) && order.getCashRedeemed() != null
				&& order.getCashRedeemed().intValue() > 0){
			order.setCashBackReceived(false);
			return;
		}
		CashBackOfferDTO cashBackOfferDTO = cashBackOfferCache.getOfferDataForUnit(order.getUnitId());
		BigDecimal amount = order.getTransactionDetail().getTaxableAmount();
		BigDecimal cashBackAwarded = BigDecimal.ZERO;
		if (Objects.isNull(cashBackOfferDTO)) {
			log.info("No valid cashback offer running for unit id : {}", order.getUnitId());
		}else{
			cashBackAwarded = AppUtils.percentOf(amount, props.getCashBackPercentage());
			cashBackAwarded = cashBackAwarded.setScale(0, RoundingMode.HALF_UP);
		}
		boolean isGiftCardOrder = hasGiftCard(order);
		cashBackAwarded = cashBackAwarded.setScale(0, RoundingMode.HALF_UP);
		if (!TransactionUtils.isCODOrder(order.getSource()) && !isGiftCardOrder
				&& cashBackAwarded.compareTo(BigDecimal.ONE) >= 0 && AppUtils.isBetweenDatesAbs(
				AppUtils.getBusinessDate(), props.getCashBackStartDate(), props.getCashBackEndDate())) {
			order.setCashBackAwarded(cashBackAwarded);
			order.setCashBackReceived(true);
			order.setCashBackStartDate(props.getCashBackStartDate());
			order.setCashBackEndDate(props.getCashBackEndDate());
			//OrderMetadata orderMetadata = new OrderMetadata();
			OrderMetadata cashBack = new OrderMetadata();
			cashBack.setAttributeName("CASHBACK_AWARD");
			cashBack.setAttributeValue(cashBackAwarded.toString());
			//  order.getMetadataList().add(orderMetadata);
			order.getMetadataList().add(cashBack);
		} else {
			// OrderMetadata orderMetadata = new OrderMetadata();
			// order.getMetadataList().add(orderMetadata);
			order.setCashBackReceived(false);
		}
	}

	private Integer checkCashBackOfferAndGetValue(String offerCode){
		String prefix = props.getCashBackCouponCodePrefix();
		if(offerCode.startsWith(prefix)){
			String digits = offerCode.substring(prefix.length());
			if(digits.matches("\\d+")){
				return Integer.parseInt(digits);
			}
		}
		return 0;
	}

	private Integer checkCashBackOfferForNewCustomerAndGetValue(String offerCode){
		String prefix = props.getCashBackCouponCodeForNewCustomerPrefix();
		try {
			if (offerCode.startsWith(prefix)) {
				String digits = offerCode.substring(prefix.length());
				if (digits.matches("\\d+")) {
					return Integer.parseInt(digits);
				}
			}
		}catch (Exception e){
			log.error("Error in Validating cashback Coupon For new Customer",e);
		}
		return 0;
	}

	private boolean isValidCityForCashBack(int unitId){
		try {
			UnitBasicDetail unitBasicDetail = unitCacheService.getUnitBasicDetailById(unitId);
			List<String> validUnitList = props.getValidUnitList();
			if(Objects.nonNull(validUnitList) && !validUnitList.isEmpty()){
				for(String unit : validUnitList){
					if(unitBasicDetail.getCity().toLowerCase().equals(unit.toLowerCase())){
						return true;
					}
				}
			}
		}catch (Exception e){
			log.error("Error in validating city for cashback ",e);
		}
		return false;
	}

	@Override
	public void awardCashBackOffer(Order order) {
		if (props.allowDohfulCash() && order.getBrandId().equals(AppConstants.DOHFUL_BRAND_ID) &&
				AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(order.getOfferCode()) && order.getCashRedeemed() != null
				&& order.getCashRedeemed().intValue() > 0){
			order.setCashBackReceived(false);
			return;
		}
		try{
			if(order.getUnitId() == 0){
				log.info("Invalid unit found while awarding cashback offer");
			}
			BigDecimal amount = order.getTransactionDetail().getTaxableAmount();
			BigDecimal cashBackAwarded = BigDecimal.ZERO;
			Integer cashBackValue = 0;
			int validityInDays = 0;
			int lagDays = 0;
			if(Objects.nonNull(order.getOfferCode()) && !order.getOfferCode().isEmpty()){
				cashBackValue = checkCashBackOfferAndGetValue(order.getOfferCode());
			}
			else if(order.isNewCustomer() && isValidCityForCashBack(order.getUnitId())){
				if(Objects.nonNull(props.getCashBackCouponCodeForNewCustomer()) && !props.getCashBackCouponCodeForNewCustomer().isEmpty()) {
					cashBackValue = checkCashBackOfferForNewCustomerAndGetValue(props.getCashBackCouponCodeForNewCustomer());
					if(cashBackValue>0) {
						order.setOfferCode(props.getCashBackCouponCodeForNewCustomer());
					}
				}
			}
			if(cashBackValue!=0){
				cashBackAwarded = AppUtils.percentOf(amount,BigDecimal.valueOf(cashBackValue));
				validityInDays = props.getValidityForCashBackCoupon();
				lagDays = 0;
			}else {
				CashBackOfferDTO cashBackOfferDTO = cashBackOfferCache.getOfferDataForUnit(order.getUnitId());
				if (Objects.isNull(cashBackOfferDTO)) {
					log.info("No valid cashback offer running for unit id : {}", order.getUnitId());
					return;
				} else {
					cashBackAwarded = AppUtils.percentOf(amount, cashBackOfferDTO.getCashbackPercentage());
					validityInDays = cashBackOfferDTO.getValidityInDays();
					lagDays = cashBackOfferDTO.getLagDays();
				}
			}
			boolean isGiftCardOrder = hasGiftCard(order);
			cashBackAwarded = cashBackAwarded.setScale(0, RoundingMode.HALF_UP);
			if (!TransactionUtils.isCODOrder(order.getSource()) && !isGiftCardOrder
					&& cashBackAwarded.compareTo(BigDecimal.ONE) >= 0) {
				order.setCashBackAwarded(cashBackAwarded);
				order.setCashBackReceived(true);
				Date startDate = AppUtils.addDays(AppUtils.getBusinessDate(), lagDays);
				order.setCashBackStartDate(startDate);
				order.setCashBackEndDate(AppUtils.addDays(startDate, validityInDays));
				order.setCashBackLagDays(lagDays);
				OrderMetadata cashBack = new OrderMetadata();
				cashBack.setAttributeName("CASHBACK_OFFER");
				cashBack.setAttributeValue(cashBackAwarded.toString());
				order.getMetadataList().add(cashBack);
			} else {
				log.info("Unable to award cash back");
				order.setCashBackReceived(false);
			}
		}catch (Exception e){
			log.info("Error while allowing cash back for generated id : {}",order.getGenerateOrderId());
		}
	}

	public boolean hasGiftCard(Order order) {
		for (OrderItem item : order.getOrders()) {
			if (AppUtils.isGiftCard(item.getCode())) {
				return true;
			}
		}
		return false;
	}

	@Override
	public void processCashRedemption(Order order, OrderDetail orderDetail, StringBuilder sb) throws DataUpdationException {
		if (AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(order.getOfferCode()) && order.getCashRedeemed() != null
				&& order.getCashRedeemed().intValue() > 0) {
			// cash redemption
			long time = System.currentTimeMillis();
			cashPacketService.redeemCash(order.getCustomerId(), order.getCashRedeemed(), orderDetail.getOrderId());

			sb.append("\n########## , STEP K, - , Time Taken To update cash redemption----------,"
					+ (System.currentTimeMillis() - time));
		} else if (AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(order.getOfferCode())
				&& (order.getCashRedeemed() == null || order.getCashRedeemed().intValue() == 0)) {
			throw new DataUpdationException("Could not find chaayos cash for the customer");
		}

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean allotCashBack(BigDecimal amount, Integer customerId, int orderId, Date activationTime,
								 Date expirationDate, int lagDays) {
		String refCode = customerDao.findById(customerId).get().getRefCode();
		return addCashForCashBack(customerId, refCode, orderId, amount, activationTime, expirationDate, lagDays);
	}

	public boolean addCashForCashBack(int customerId, String customerRefCode, int orderId, BigDecimal cashBack,
									  Date activationTime, Date expiration, int lagDays) {
		CashTransactionMetadata metadata = new CashTransactionMetadata(TransactionType.CREDIT,
				CashMetadataType.CASHBACK, CashTransactionCategory.CASH_BONUS, CashTransactionCode.CASHBACK);
		CashMetadataWrapper wrapper = new CashMetadataWrapper();
		wrapper.setMetadata(metadata);
		wrapper.setCustomerId(customerId);
		wrapper.setCustomerRefCode(customerRefCode);
		wrapper.setReferralMappingId(null);
		wrapper.setOrderId(orderId);
		// search existing
		CashData cash = cashDataDao.findByCustomerId(wrapper.getCustomerId());
		if (cash == null) {
			// create new in case of new customer
			cash = createCashDataObject(wrapper.getCustomerId(), wrapper.getCustomerRefCode());
			cashDataDao.save(cash);
		}
		wrapper.setTransactionAmount(cashBack);
		wrapper.setTransactionTime(AppUtils.getCurrentTimestamp());
		addCashPacket(cash, wrapper, AppUtils.getCurrentTimestamp(), expiration, activationTime, lagDays);
		return true;
	}

	private CashData createCashDataObject(int customerId, String refCode) {
		CashData cash = new CashData();
		cash.setCustomerId(customerId);
		cash.setAccumulatedAmount(BigDecimal.ZERO);
		cash.setCurrentAmount(BigDecimal.ZERO);
		cash.setExpiredAmount(BigDecimal.ZERO);
		cash.setRedeemedAmount(BigDecimal.ZERO);
		cash.setRetainedAmount(BigDecimal.ZERO);
		cash.setReferralCode(refCode);
		cash.setCreationTime(AppUtils.getCurrentTimestamp());
		cash.setLastUpdateTime(AppUtils.getCurrentTimestamp());
		return cash;
	}

	private CashPacketData addCashPacket(CashData cashData, CashMetadataWrapper wrapper, Date creationDate, Date expirationDate, Date activationTime, Integer lagDays) {
		CashPacketEventStatus packetStatus = getPacketStatus(wrapper);

		CashPacketData packet = new CashPacketData();

		packet.setCashDataId(cashData.getCashDataId());
		packet.setCreationDate(creationDate);
		packet.setCreationTime(wrapper.getTransactionTime());
		packet.setCustomerId(cashData.getCustomerId());
		packet.setExpirationDate(expirationDate);
		packet.setInitialExpirationDate(expirationDate);
		packet.setTransactionCode(wrapper.getMetadata().getCashTransactionCode().name());
		packet.setTransactionCodeType(wrapper.getMetadata().getCashTransactionCategory().name());
		packet.setLastUpdateTime(wrapper.getTransactionTime());
		packet.setReferentId(wrapper.getReferentId());
		packet.setReferralDataId(wrapper.getReferralMappingId());
		packet.setCurrentAmount(wrapper.getTransactionAmount());
		packet.setInitialAmount(wrapper.getTransactionAmount());
		packet.setRedeemedAmount(BigDecimal.ZERO);
		packet.setRetainedAmount(BigDecimal.ZERO);
		packet.setExpiredAmount(BigDecimal.ZERO);
		if(activationTime != null){
			packet.setActivationTime(activationTime);
		}
		if(lagDays>0){
			packet.setEventStatus(CashPacketEventStatus.READY_FOR_ACTIVATION.name());
		}else{
			cashData.setAccumulatedAmount(cashData.getAccumulatedAmount().add(wrapper.getTransactionAmount()));
			cashData.setCurrentAmount(cashData.getCurrentAmount().add(wrapper.getTransactionAmount()));
			cashDataDao.save(cashData);
			packet.setEventStatus(CashPacketEventStatus.ACTIVE.name());
		}
		cashPacketDataDao.save(packet);

		addPacketLog(false, packet, wrapper.getTransactionAmount(), wrapper);

		return packet;
	}

	private CashPacketEventStatus getPacketStatus(CashMetadataWrapper wrapper) {
		/*
		 * Referrer will have this packet as initiated until referent makes 1st
		 * Successful transaction.
		 *
		 */
		if(CashTransactionCode.SIGNUP_REFERRER.equals(wrapper.getMetadata().getCashTransactionCode())){
			return CashPacketEventStatus.INITIATED;
		} else if(CashTransactionCode.CASHBACK.equals(wrapper.getMetadata().getCashTransactionCode())){
			return CashPacketEventStatus.READY_FOR_ACTIVATION;
		}else {
			return CashPacketEventStatus.ACTIVE;
		}
	}

	private void addPacketLog(boolean update, CashPacketData packet, BigDecimal packetTransactionValue,
							  CashMetadataWrapper wrapper) {
		if (update && CashTransactionCode.CASHBACK.name().equals(packet.getTransactionCode())) {
			CashPacketLogData log = cashPacketLogDataDao.findByCashPacketIdAndTransactionCode(packet.getCashPacketId(),
					CashTransactionCode.CASHBACK.name());
			if (log != null) {
				log.setCashLogDataId(wrapper.getCashLogDataId());
				return;
			}
		}

		CashPacketLogData log = new CashPacketLogData();
		log.setCashPacketId(packet.getCashPacketId());
		log.setTransactionAmount(packetTransactionValue);
		log.setTransactionCode(wrapper.getMetadata().getCashTransactionCode().name());
		log.setTransactionCodeType(wrapper.getMetadata().getCashTransactionCategory().name());
		log.setTransactionReason("");
		log.setTransactionType(wrapper.getMetadata().getTransactionType().name());
		log.setTransactionTime(wrapper.getTransactionTime());
		log.setOrderId(wrapper.getOrderId());
		log.setCashLogDataId(wrapper.getCashLogDataId());
		cashPacketLogDataDao.save(log);
	}
}
