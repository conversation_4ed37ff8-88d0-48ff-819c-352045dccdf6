package com.stpl.tech.kettle.service.impl;

import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.data.kettle.OrderItemMetaDataDetail;
import com.stpl.tech.kettle.domain.model.MilkVariant;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.repository.kettle.OrderItemMetaDataDetailDao;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.data.kettle.OrderMetadataDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderMetadata;
import com.stpl.tech.kettle.repository.kettle.OrderMetadataDetailDao;
import com.stpl.tech.kettle.service.OrderMetadataService;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class OrderMetadataServiceImpl implements OrderMetadataService {

    @Autowired
    private OrderMetadataDetailDao orderMetadataDetailDao;

    @Autowired
    private OrderItemMetaDataDetailDao orderItemMetaDataDetailDao;

    @Autowired
    private ProductCache productCache;



    private void setPriceProfileMetadata(Order order , Integer orderId){
        if(Objects.nonNull(order.getUnitPriceProfile())){
            OrderMetadataDetail detail = new OrderMetadataDetail();
            detail.setOrderId(orderId);
            detail.setAttributeName(AppConstants.PRICE_PROFILE_ID);
            detail.setAttributeValue(String.valueOf(order.getUnitPriceProfile().getPriceProfileId()));
            orderMetadataDetailDao.save(detail);
            OrderMetadataDetail detail1 = new OrderMetadataDetail();
            detail1.setOrderId(orderId);
            detail1.setAttributeName(AppConstants.PRICE_PROFILE_VERSION);
            detail1.setAttributeValue(String.valueOf(order.getUnitPriceProfile().getPriceProfileVersion()));
            orderMetadataDetailDao.save(detail1);

        }
    }

    private void setServiceChargeMetadata(Order order, Integer orderId) {
        if (Objects.nonNull(order.getServiceChargeRemovedKey()) && !AppConstants.TAKE_AWAY.equals(order.getSource()) && !order.isGiftCardOrder()) {
            OrderMetadataDetail keyDetail = new OrderMetadataDetail();
            keyDetail.setOrderId(orderId);
            keyDetail.setAttributeName(AppConstants.SERVICE_CHARGE_REMOVED);
            keyDetail.setAttributeValue(order.getServiceChargeRemovedKey().getServiceChargeRemoved());
            orderMetadataDetailDao.save(keyDetail);

            OrderMetadataDetail valueDetail = new OrderMetadataDetail();
            valueDetail.setOrderId(orderId);
            valueDetail.setAttributeName(AppConstants.SERVICE_CHARGE_VALUE);
            valueDetail.setAttributeValue(order.getServiceChargeRemovedKey().getServiceChargeValue().
                    setScale(2, RoundingMode.HALF_UP).toString());
            orderMetadataDetailDao.save(valueDetail);
        }
    }

    @Override
    public void addMetadataDetails(Order order, Integer orderId) {
        try {
            setPriceProfileMetadata(order,orderId);
            setServiceChargeMetadata(order, orderId);
            List<OrderMetadata> list = order.getMetadataList();
            if (list != null && !list.isEmpty()) {
                for (OrderMetadata data : list) {
                    OrderMetadataDetail detail = new OrderMetadataDetail();
                    detail.setOrderId(orderId);
                    detail.setAttributeName(data.getAttributeName());
                    detail.setAttributeValue(data.getAttributeValue());
                    orderMetadataDetailDao.save(detail);
                }
            }
        } catch (Exception e) {
            log.error("Error While Saving Metadata for Order Id {}", orderId, e);
        }
    }

    @Override
    public void addOrderItemMetaDataDetails(OrderItem item, Integer orderItemId,Integer orderId, Integer customerId,Integer brandId,Boolean isCodOrder, Boolean isDineAndSnpOrder) {
        OrderItemMetaDataDetail orderItemMetaDataDetail = new OrderItemMetaDataDetail();
        orderItemMetaDataDetail.setOrderItemId(orderItemId);
        orderItemMetaDataDetail.setOrderId(orderId);
        orderItemMetaDataDetail.setCustomerId(customerId);
        if(Objects.nonNull(item.getIsBestPairedItem())){
            orderItemMetaDataDetail.setIsRecommended(item.getIsBestPairedItem());
        }
        if(Objects.nonNull(item.getItemName()) && Boolean.TRUE.equals(isCodOrder)){
            orderItemMetaDataDetail.setItemName(item.getItemName());
        }
        else{
            if(Objects.nonNull(item.getPreferenceDetail()) && Objects.nonNull(item.getPreferenceDetail().getPreferenceId())){
                orderItemMetaDataDetail.setIsSavedChai(AppConstants.YES);
                orderItemMetaDataDetail.setSavedChaiId(item.getPreferenceDetail().getPreferenceId());
            }
        }
        if (Objects.nonNull(item.getQtyAddedByCustomer()) && item.getQtyAddedByCustomer() > 0) {
            orderItemMetaDataDetail.setAddedBy(AppConstants.CUSTOMER);
        } else if(!isDineAndSnpOrder) {
            orderItemMetaDataDetail.setAddedBy(AppConstants.CRE);
        }
        if(isDineAndSnpOrder && Objects.nonNull(item.getRuleNumber())){
            orderItemMetaDataDetail.setIsRecommended(AppUtils.setStatus(Boolean.TRUE));
        } else if(Objects.nonNull(item.getRecProd())) {
            orderItemMetaDataDetail.setIsRecommended(AppUtils.setStatus(item.getRecProd()));
        } else {
            orderItemMetaDataDetail.setIsRecommended(AppUtils.setStatus(Boolean.FALSE));
        }
        if(Objects.nonNull(item.getRuleNumber())){
            orderItemMetaDataDetail.setRecomRuleId(item.getRuleNumber());
        }
        if (Objects.nonNull(item.getIsexploreMoreOptionsProduct())) {
            orderItemMetaDataDetail.setExploreMore(AppUtils.setStatus(item.getIsexploreMoreOptionsProduct()));
        } else {
            orderItemMetaDataDetail.setExploreMore(AppUtils.setStatus(false));
        }
       // orderItemMetaDataDetail.setIsSavedChai(AppUtils.getYOrN(StringUtils.isEmpty(item.getSaveChaiName()) ? false : true));
        if(Objects.nonNull(item.getPreferenceDetail()) && Objects.nonNull(item.getPreferenceDetail().getPreferenceId())){
            orderItemMetaDataDetail.setIsSavedChai(AppConstants.YES);
            orderItemMetaDataDetail.setSavedChaiId(item.getPreferenceDetail().getPreferenceId());
        }
        setSpecialMilkVariant(orderItemMetaDataDetail,item,brandId);
        orderItemMetaDataDetailDao.save(orderItemMetaDataDetail);
    }

    private void setSpecialMilkVariant(OrderItemMetaDataDetail orderItemMetaDataDetail , OrderItem  orderItem , Integer brandId){
        if(Objects.nonNull(orderItem.getComposition()) && !CollectionUtils.isEmpty(orderItem.getComposition().getOptions())){
           orderItem.getComposition().getOptions().forEach(option ->{
               if(Objects.nonNull(productCache.getSpecialMilkVariant(option))){
                   orderItemMetaDataDetail.setSpecialMilkVariant(productCache.getSpecialMilkVariant(option));
                   MilkVariant milkVariant = MilkVariant.builder().productName(option).productId(productCache.getSpecialMilkVariant(option))
                           .profile(AppUtils.getMilkVariantPaidAddonPrefix(option) + orderItem.getRecipeProfile())
                           .scmProductId(AppUtils.getMilkVariantPaidAddonSCMProduct(option,brandId)).
                           scmProductName(AppUtils.getMilkVariantPaidAddonSCMProductName(option,brandId)).build();
                   orderItem.setMilkVariant(milkVariant);
               }
           });
        }
    }


}
