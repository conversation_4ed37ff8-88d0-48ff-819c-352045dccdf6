package com.stpl.tech.kettle.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.stpl.tech.kettle.cache.UnitDroolVersionMappingCache;
import com.stpl.tech.kettle.cache.WalletCacheService;
import com.stpl.tech.kettle.core.config.DroolsConfig;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.CashCardOffer;
import com.stpl.tech.kettle.data.kettle.SubscriptionPlanEvent;
import com.stpl.tech.kettle.domain.kettle.WalletSuggestionData;
import com.stpl.tech.kettle.domain.model.*;
import com.stpl.tech.kettle.exceptions.BaseException;
import com.stpl.tech.kettle.repository.kettle.SubscriptionPlanEventDao;
import com.stpl.tech.kettle.service.CustomerDataService;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import org.kie.api.runtime.KieSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.repository.kettle.CashCardOfferDao;
import com.stpl.tech.kettle.cache.CampaignCache;
import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.data.kettle.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.data.kettle.SubscriptionPlan;
import com.stpl.tech.kettle.data.master.DeliveryCouponDetailData;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.kettle.exceptions.WebErrorCode;
import com.stpl.tech.kettle.offer.strategy.OfferActionStrategy;
import com.stpl.tech.kettle.offer.strategy.OfferStrategyHelper;
import com.stpl.tech.kettle.repository.kettle.CustomerCampaignOfferDao;
import com.stpl.tech.kettle.repository.kettle.CustomerCampaignOfferDetailDao;
import com.stpl.tech.kettle.repository.kettle.CustomerOfferDao;
import com.stpl.tech.kettle.repository.kettle.SubscriptionPlanDao;
import com.stpl.tech.kettle.repository.master.DeliveryCouponDetailDataDao;
import com.stpl.tech.kettle.service.CouponService;
import com.stpl.tech.kettle.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.service.CustomerService;
import com.stpl.tech.kettle.service.DroolService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.adapter.JSONSerializer;
import com.stpl.tech.kettle.util.consumptionHelper.CouponMappingHelper;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignMapping;
import com.stpl.tech.master.domain.model.CouponData;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.master.domain.model.CouponMappingType;
import com.stpl.tech.master.domain.model.FrequencyOfferType;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.OfferCategoryType;
import com.stpl.tech.master.domain.model.OfferComboItem;
import com.stpl.tech.master.domain.model.OfferComboItemGroup;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.OfferMetaDataType;
import com.stpl.tech.kettle.domain.model.SavedChaiOrderedDomain;
import com.stpl.tech.util.DroolFileType;
import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class CustomerOfferManagementServiceImpl implements CustomerOfferManagementService {

	@Autowired
	private CustomerService customerService;

	@Autowired
	private SubscriptionPlanDao subscriptionPlanDao;

	@Autowired
	private ProductCache productCache;

	@Autowired
	private UnitCacheService unitCacheService;

	@Autowired
	private CustomerCampaignOfferDao customerCampaignOfferDao;

	@Autowired
	private CampaignCache campaignCache;

	@Autowired
	private DeliveryCouponDetailDataDao deliveryCouponDetailDataDao;

	@Autowired
	private CustomerCampaignOfferDetailDao campaignOfferDetailDao;

	@Autowired
	private CouponService couponService;

	@Autowired
	private CustomerOfferDao customerOfferDetailDao;


	@Autowired
	private WalletCacheService walletCacheService;

	@Autowired
    private DroolsConfig droolsConfig;

	@Autowired
	private CustomerDataService customerDataService;


	@Autowired
	private SubscriptionPlanEventDao subscriptionPlanEventDao;

	@Autowired
	private CashCardOfferDao cashCardOfferDao;


    @Autowired
    private DroolService droolService;

    @Autowired
    private EnvironmentProperties properties;

    @Autowired
    private UnitDroolVersionMappingCache unitDroolVersionMappingCache;


	@Override
	public OfferOrder applyCoupoun(OfferOrder offerOrder, BigDecimal offerValue)
			throws DataNotFoundException, OfferValidationException {
		if (offerOrder.getOrder() != null) {
			//
			CouponDetail couponDetail = couponService.getCouponDetail(offerOrder.getCouponCode(), false, true, false);
			if (isApplicable(offerOrder, couponDetail)) {
				offerOrder = modifyOrder(offerOrder, couponDetail, offerValue);
			}
			return offerOrder;
		} else {
			throw new OfferValidationException("Could not find order in this request", WebErrorCode.NOT_AVAILABLE);
		}
	}

	private boolean isApplicable(OfferOrder offerOrder, CouponDetail coupon)
			throws DataNotFoundException, OfferValidationException {
		long startTime = System.currentTimeMillis();
		checkPreConditions(offerOrder, coupon);
		checkCouponMappings(offerOrder, coupon);
		checkOfferSubscriptionValidity(offerOrder, coupon);
		log.info("Validating is coupon eligible ends in {}ms", System.currentTimeMillis() - startTime);
		return true;
	}

	private boolean checkPreConditions(OfferOrder offerOrder, CouponDetail coupon)
			throws OfferValidationException, DataNotFoundException {
		if (coupon == null) {
			throw new OfferValidationException("Coupon Not Found", WebErrorCode.INVALID_COUPON);
		} else if (!AppConstants.ACTIVE.equals(coupon.getStatus().toString())) {
			throw new OfferValidationException("Coupon " + coupon.getCode() + " has expired", WebErrorCode.EXPIRED);
		} else if (!AppConstants.ACTIVE.equals(coupon.getOffer().getStatus().toString())) {
			throw new OfferValidationException("Offer for " + coupon.getCode() + " has expired", WebErrorCode.EXPIRED);
		}
		offerOrder.setOfferDescription(coupon.getOffer().getDescription());
		checkZeroTaxProducts(offerOrder, coupon);
		checkCustomer(offerOrder, coupon);
		checkReUsablity(offerOrder, coupon);
		checkUsageLimit(coupon);
		if (coupon.getOffer().isValidateCustomer()) {
			checkCustomerUsageLimit(offerOrder.getOrder().getCustomerId(), coupon);
		}
		checkCouponExpiry(coupon);
		checkOrderAmount(offerOrder, coupon);
		checkMinimumItems(offerOrder, coupon);
		checkManualOverride(offerOrder, coupon);
		return true;
	}

	private void checkZeroTaxProducts(OfferOrder offerOrder, CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		for (OrderItem item : offerOrder.getOrder().getOrders()) {
			if (!AppConstants.ZERO_TAX_PRODUCTS_SET.contains(item.getProductId())) {
				if (AppUtils.isGiftCard(item.getCode()) || AppUtils.isZeroTaxProduct(item.getCode())
						|| (Objects.nonNull(productCache.getSubscriptionProductDetails().get(item.getProductId()))
								&& !coupon.getOffer().getType()
										.equalsIgnoreCase(OfferCategoryType.PERCENTAGE_ITEM_STRATEGY.name()))) {
					throw new OfferValidationException("Offer is not applicable on " + item.getProductName(),
							WebErrorCode.INSUFFICIENT_DATA);
				}
			}
		}
		log.info("checkZeroTaxProducts ends in {}ms", System.currentTimeMillis() - startTime);
	}

	private void checkCustomer(OfferOrder offerOrder, CouponDetail coupon)
			throws OfferValidationException, DataNotFoundException {
		long startTime = System.currentTimeMillis();
		OfferDetail offer = coupon.getOffer();
		if (!offer.isValidateCustomer()) {
			return;
		}
		if (offerOrder.getOrder().getCustomerId() == null || offerOrder.getOrder().getCustomerId() <= 5) {
			throw new OfferValidationException("Coupon can be availed by registered customers only",
					WebErrorCode.CUSTOMER_NOT_FOUND);
		}
		if (offerOrder.getOrder().getCustomerId() != null) {
			offerOrder.setContact(
					customerService.getCustomerInfo(offerOrder.getOrder().getCustomerId()).getContactNumber());
		}
		if (offer.getOfferScope().equals(AppConstants.OFFER_SCOPE_CORPORATE) && offer.getEmailDomain() != null) {
			CustomerInfo customer = null;
			try {
				customer = customerService.getCustomerInfo(offerOrder.getOrder().getCustomerId());
			} catch (Exception e) {
				log.error("Customer Not Found", e);
			}
			if (customer != null && AppUtils.getStatus(customer.getIsEmailVerified()) && customer.getEmailId() != null
					&& customer.getEmailId().endsWith(offer.getEmailDomain())) {
				return;
			} else {
				throw new OfferValidationException(
						"Corporate Coupon cannot be availed as the registered customer email Id is incorrect or not verified",
						WebErrorCode.INSUFFICIENT_DATA);
			}
		}
		log.info("COUPON_APPLY checkCustomer ends in {}ms", System.currentTimeMillis() - startTime);
	}

	private void checkReUsablity(OfferOrder offerOrder, CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		if (!coupon.isReusable() && coupon.getUsage() > 1) {
			throw new OfferValidationException("This Coupon cannot be used again.", WebErrorCode.EXPIRED);
		}

		if (!coupon.isReusableByCustomer()) {
			if (offerOrder.getOrder().getCustomerId() != null && !customerService
					.getOfferDetail(offerOrder.getOrder().getCustomerId(), offerOrder.getCouponCode()).isEmpty()) {
				throw new OfferValidationException(
						"Customer has already availed the Coupon, This Coupon cannot be used again.",
						WebErrorCode.EXPIRED);
			}
		}
		log.info("COUPON_APPLY checkReUsablity ends in {}ms", System.currentTimeMillis() - startTime);
	}

	public void checkUsageLimit(CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		if (coupon.getMaxUsage() != null && coupon.getMaxUsage().compareTo(coupon.getUsage()) < 1) {
			throw new OfferValidationException("Coupon has crossed maximum usage Limit", WebErrorCode.EXPIRED);
		}
		log.info("COUPON_APPLY checkUsageLimit ends in {}ms", System.currentTimeMillis() - startTime);
	}

	public void checkCustomerUsageLimit(int customerId, CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		if (coupon.getMaxCustomerUsage() != null) {
			Integer customerUsageCount = customerService.checkCouponUsage(customerId, coupon.getCode());
			if (customerUsageCount != null && coupon.getMaxCustomerUsage().compareTo(customerUsageCount) < 1)
				throw new OfferValidationException("Coupon has crossed maximum usage Limit for the customer",
						WebErrorCode.EXPIRED);
		}
		log.info("COUPON_APPLY checkCustomerUsageLimit ends in {}ms", System.currentTimeMillis() - startTime);
	}

	public void checkCouponExpiry(CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		Date today = AppUtils.getCurrentDate();
		if (today.compareTo(coupon.getStartDate()) < 0) {
			throw new OfferValidationException(
					"Coupon can be availed only after "
							+ new SimpleDateFormat("dd MMM yyyy").format(coupon.getStartDate()),
					WebErrorCode.NOT_AVAILABLE);
		}
		if (today.compareTo(coupon.getEndDate()) > 0) {
			throw new OfferValidationException(
					"Coupon has expired on " + new SimpleDateFormat("dd MMM yyyy").format(coupon.getEndDate()),
					WebErrorCode.EXPIRED);
		}
		if (today.compareTo(coupon.getOffer().getEndDate()) > 0) {
			throw new OfferValidationException(
					"Offer has expired on " + new SimpleDateFormat("dd MMM yyyy").format(coupon.getEndDate()),
					WebErrorCode.EXPIRED);
		}
		log.info("COUPON_APPLY checkCouponExpiry ends in {}ms", System.currentTimeMillis() - startTime);
	}

	private void checkOrderAmount(OfferOrder offerOrder, CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		// minimum order amount check
		if (offerOrder.getOrder().getTransactionDetail().getPaidAmount()
				.compareTo(new BigDecimal(coupon.getOffer().getMinValue())) == -1) {
			throw new OfferValidationException(
					"Minimum Paid Amount to apply this coupon is Rs." + coupon.getOffer().getMinValue(),
					WebErrorCode.INSUFFICIENT_DATA);
		}
		// maximum order amount check
		if (coupon.getOffer().getMaxBillValue() != null
				&& BigDecimal.ZERO.compareTo(coupon.getOffer().getMaxBillValue()) != 0 && offerOrder.getOrder()
						.getTransactionDetail().getPaidAmount().compareTo(coupon.getOffer().getMaxBillValue()) == 1) {
			throw new OfferValidationException(
					"Maximum Paid Amount to apply this coupon is Rs." + coupon.getOffer().getMinValue(),
					WebErrorCode.INSUFFICIENT_DATA);
		}
		log.info("COUPON_APPLY checkOrderAmount ends in {}ms", System.currentTimeMillis() - startTime);
	}

	private void checkMinimumItems(OfferOrder offerOrder, CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		int totalItems = 0;
		for (OrderItem item : offerOrder.getOrder().getOrders()) {
			totalItems += item.getQuantity();
		}
		if (totalItems < coupon.getOffer().getMinItemCount()) {
			throw new OfferValidationException(
					"This Coupon requires minimum " + coupon.getOffer().getMinItemCount() + " items",
					WebErrorCode.INSUFFICIENT_DATA);
		}
		log.info("COUPON_APPLY checkMinimumItems ends in {}ms", System.currentTimeMillis() - startTime);
	}

	private void checkManualOverride(OfferOrder offerOrder, CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		if (coupon.isManualOverride()) {
			offerOrder.setManualOverride(true);
			throw new OfferValidationException(
					"Coupon is to be punched manually, Offer Description: " + coupon.getOffer().getDescription(),
					WebErrorCode.NOT_AVAILABLE);
		}
		log.info("COUPON_APPLY checkManualOverride ends in {}ms", System.currentTimeMillis() - startTime);
	}

	private void checkCouponMappings(OfferOrder offerOrder, CouponDetail coupon)
			throws OfferValidationException, DataNotFoundException {
		long startTime = System.currentTimeMillis();
		if (Objects.isNull(coupon.getMappings()) || coupon.getMappings().isEmpty()) {
			return;
		}
		for (CouponMappingType type : CouponMappingType.values()) {
			if (!type.equals(CouponMappingType.FIRST_ORDER) && !type.equals(CouponMappingType.PAYMENT_MODE)
					&& !type.equals(CouponMappingType.FREEBIE_PRODUCT)) {
				if (type.equals(CouponMappingType.NEW_CUSTOMER)
						&& coupon.getMappings().containsKey(CouponMappingType.NEW_CUSTOMER.name())) {
					CustomerInfo customer;
					if (Objects.nonNull(offerOrder.getContact())) {
						customer = customerService.getCustomerInfo(offerOrder.getContact());
					} else {
						customer = customerService.getCustomerInfo(offerOrder.getOrder().getCustomerId());
					}
					boolean isNew = customerService.getCustomerOrders(customer.getCustomerId());
					if (isNew) {
						offerOrder.setNewCustomer(true);
					}
				}
				checkCouponMapping(coupon.getMappings().get(type.name()),
						CouponMappingHelper.getValueOf(type, offerOrder, unitCacheService, productCache), type);
			} else if (type.equals(CouponMappingType.PAYMENT_MODE)
					&& coupon.getMappings().containsKey(CouponMappingType.PAYMENT_MODE.name())) {
				offerOrder.getOrder().setAllowedPaymentIds(getPaymentModes(coupon.getMappings().get(type.name())));
			} else if (type.equals(CouponMappingType.FIRST_ORDER)
					&& coupon.getMappings().containsKey(CouponMappingType.FIRST_ORDER.name())) {
				// check order count in customer object
				Set<CouponMapping> mappingList = coupon.getMappings().get(type.name());
				if (mappingList != null && !mappingList.isEmpty()) {
					Customer customer = customerService.getCustomer(AppConstants.DEFAULT_COUNTRY_CODE,
							offerOrder.getContact());
					if (customer.getOrderCount() != null && customer.getOrderCount() > 0) {
						throw new OfferValidationException("Coupon is applicable on first order only.",
								WebErrorCode.COUPON_APPLICABLE_FIRST_ORDER);
					}
				}
			}
		}
		log.info("COUPON_APPLY checkCouponMappings ends in {}ms", System.currentTimeMillis() - startTime);
	}

	@Override
	public void checkCouponMapping(Set<CouponMapping> mappingList, List<CouponMapping> values, CouponMappingType type)
			throws OfferValidationException, DataNotFoundException {
		if (mappingList != null && !mappingList.isEmpty()) {
			for (CouponMapping value : values) {
				if (mappingList.contains(value)) {
					return;
				}
			}
			throw new OfferValidationException(
					CouponMappingHelper.getErrorStatement(type, mappingList, unitCacheService, productCache),
					CouponMappingHelper.getErrorCode(type));
		}
	}

	public List<Integer> getPaymentModes(Set<CouponMapping> values) {
		if (values == null || values.size() == 0) {
			return null;
		}
		List<Integer> ids = new ArrayList<>();
		for (CouponMapping value : values) {
			try {
				ids.add(Integer.valueOf(value.getValue()));
			} catch (Exception e) {
				log.error("Error in checking coupon payment mode mappings:", e);
			}
		}
		return ids != null && ids.size() > 0 ? ids : null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public Boolean updateOfferApplicationDetails(String couponCode, Integer customerId, Integer orderId,
			BigDecimal savings) {
		try {
			log.info(
					"Updating CustomerCampaignOfferDetail for for customer id {} , coupon code: {} for savings of {} in order id ",
					customerId, couponCode, savings, orderId);
			CustomerCampaignOfferDetail offer = customerCampaignOfferDao.findByCustomerIdAndCouponCode(customerId,
					couponCode);
			if (offer == null) {
				return false;
			}
			offer.setOfferApplyCount(offer.getOfferApplyCount() == null ? 1 : offer.getOfferApplyCount() + 1);
			offer.setOverallSavings(
					offer.getOverallSavings() == null ? savings : AppUtils.add(offer.getOverallSavings(), savings));
			offer.setOfferApplyLastOrderId(orderId);
			offer.setOfferApplyLastTime(AppUtils.getCurrentTimestamp());
			offer.setIsOfferApplied(AppConstants.YES);
			Date today = AppUtils.getBusinessDate();
			Date creationDate = AppUtils.getBusinessDate(offer.getCouponGenerationTime());
			Integer gapInDay = AppUtils.getAbsDaysDiff(today, creationDate);
			offer.setGapInDays(gapInDay);
			customerCampaignOfferDao.save(offer);
			return true;
		} catch (Exception e) {
			log.error("Error updating CustomerCampaignOfferDetail for customer{} and coupon {}", customerId, couponCode,
					e);
		}
		return false;
	}

	private void checkOfferSubscriptionValidity(OfferOrder offerOrder, CouponDetail coupon)
			throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		if (Boolean.TRUE.equals(Objects.nonNull(coupon.getOffer().isFrequencyApplicable())
				&& coupon.getOffer().isFrequencyApplicable() && Objects.nonNull(coupon.getOffer().getFrequencyCount()))
				&& coupon.getOffer().getFrequencyCount() > 0) {
			if (FrequencyOfferType.TIME_BASED.name().equals(coupon.getOffer().getFrequencyStrategy())) {
				if (customerService.getValidOfferFlag(offerOrder, coupon)) {
					throw new OfferValidationException("Maximum Usage Count Reached for the Customer",
							WebErrorCode.MAX_LIMIT_REACHED);
				}
			} else if (FrequencyOfferType.QUANTITY_BASED.name().equals(coupon.getOffer().getFrequencyStrategy())) {
				SubscriptionPlan subscriptionPlan = subscriptionPlanDao
						.findByCustomerIdAndSubscriptionPlanCodeAndStatus(offerOrder.getOrder().getCustomerId(),
								offerOrder.getCouponCode(), AppConstants.ACTIVE);
				if (subscriptionPlan.getOverAllFrequency().compareTo(subscriptionPlan.getFrequencyLimit()) >= 0) {
					throw new OfferValidationException("Maximum Available Quantity Already Availed",
							WebErrorCode.MAX_LIMIT_REACHED);
				} else if (subscriptionPlan.getOverAllFrequency().compareTo(subscriptionPlan.getFrequencyLimit()) < 0) {
					coupon.getOffer().setMinQuantity(Math.abs(AppUtils
							.subtract(subscriptionPlan.getOverAllFrequency(), subscriptionPlan.getFrequencyLimit())
							.intValue()));
					return;
				}
				throw new OfferValidationException("Maximum Available Quantity Already Availed",
						WebErrorCode.MAX_LIMIT_REACHED);
			}
		}
		log.info("COUPON_APPLY checkOfferSubscriptionValidity ends in {}ms", System.currentTimeMillis() - startTime);
	}

	private OfferOrder modifyOrder(OfferOrder offerOrder, CouponDetail coupon, BigDecimal offerValue)
			throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		try {
			Map<String, OrderItem> foundItems = null;
			String offerType = coupon.getOffer().getType();
			if (OfferCategoryType.COMBO_STRATEGY.name().equalsIgnoreCase(offerType)) {
				foundItems = findComboItems(offerOrder, coupon);
			}
			Class<? extends OfferActionStrategy> strategyClass = OfferStrategyHelper
					.getStrategy(OfferCategoryType.valueOf(offerType));
			if (offerValue != null) {
				strategyClass.newInstance().applyStrategy(offerOrder, coupon, productCache, foundItems, offerValue);
			} else {
				strategyClass.newInstance().applyStrategy(offerOrder, coupon, productCache, foundItems);
			}

			offerOrder.setAwardLoyalty(
					coupon.getOffer().getRemoveLoyalty() != null && coupon.getOffer().getRemoveLoyalty() ? false
							: true);
			offerOrder.setOtpRequired(coupon.getOffer().getOtpRequired());
		} catch (OfferValidationException e) {
			throw e;
		} catch (Exception e) {
			log.error("ERROR while modifying order", e);
			offerOrder.setError(true);
			offerOrder.setErrorMessage("ERROR while modifying order");
			offerOrder.setOrder(null);
		}
		log.info("COUPON_APPLY modifyOrder ends in {}ms", System.currentTimeMillis() - startTime);
		return offerOrder;
	}

	private Map<String, OrderItem> findComboItems(OfferOrder offerOrder, CouponDetail coupon)
			throws ClassNotFoundException, OfferValidationException {
		Map<String, OrderItem> foundItems = new HashMap<>();
		OfferDetail offerDetail = coupon.getOffer();
		Stack<OfferComboItemGroup> groups = new Stack<>();
		Stack<OfferComboItemGroup> groupsNotFound = new Stack<>();
		for (IdCodeName mapping : offerDetail.getMetaDataMappings()) {
			if (OfferMetaDataType.valueOf(mapping.getName()).equals(OfferMetaDataType.COMBO_ITEM_GROUP)
					&& mapping.getStatus().equalsIgnoreCase(AppConstants.ACTIVE)) {
				OfferComboItemGroup comboGroup = (OfferComboItemGroup) JSONSerializer.toJSON(mapping.getCode(),
						Class.forName(mapping.getType()));
				groups.push(comboGroup);
			}
		}
		while (!groups.empty()) {
			OfferComboItemGroup group = groups.pop();
			boolean flag = false;
			for (OrderItem item : offerOrder.getOrder().getOrders()) {
				OfferComboItem comboItem = adapt(item);
				if (group.getItems().contains(comboItem)) {
					foundItems.put(comboItem.toString(), item);
					flag = true;
					break;
				}
			}
			if (!flag) {
				groupsNotFound.push(group);
			}
		}

		if (groupsNotFound.empty()) {
			return foundItems;
		} else {
			StringBuilder message = new StringBuilder(
					"Please add one item from the following group(s) with the suggested quantity: <br/>");
			int count = 0;
			while (!groupsNotFound.empty()) {
				message.append("GROUP_").append(++count).append(": ").append(groupsNotFound.pop().toString())
						.append("<br/>");
			}
			throw new OfferValidationException(message.toString(), WebErrorCode.INSUFFICIENT_DATA);
		}
	}

	private OfferComboItem adapt(OrderItem item) {
		return new OfferComboItem(item.getProductId(), item.getProductName(), item.getQuantity(), item.getDimension());
	}

	@Override
	public SubscriptionInfoDetail getSubscriptionInfoDetail(int customerId) {
		SubscriptionPlan plan = subscriptionPlanDao.getSubscriptionByCustomerId(customerId,
				SubscriptionEventType.SUBSCRIPTION_CANCELLED.name());
		if (Objects.nonNull(plan) && !plan.getEventType().equals(SubscriptionEventType.SUBSCRIPTION_CANCELLED.name())) {
			return new SubscriptionInfoDetail(plan.getCustomerId(), plan.getSubscriptionPlanCode(),
					!plan.getPlanEndDate().before(AppUtils.getBusinessDate())
							&& AppConstants.ACTIVE.equals(plan.getStatus()),
					plan.getPlanStartDate(), plan.getPlanEndDate(),
					Objects.nonNull(plan.getOverAllSaving()) ? plan.getOverAllSaving() : BigDecimal.ZERO,
					(int) AppUtils.getActualDayDifference(AppUtils.getBusinessDate(), plan.getPlanEndDate()),plan.getFrequencyStrategy(),plan.getFrequencyLimit(),plan.getOverAllFrequency());
		}
		return null;
	}

	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public boolean hasCustomerReceivedPostOrderOffer(int customerId, Integer lastNDays, Integer campaignId) {
		List<CustomerCampaignOfferDetail> results = customerCampaignOfferDao.getAlreadyReceivedOffer(customerId,
				AppConstants.ACTIVE, AppUtils.addDays(AppUtils.getBusinessDate(), lastNDays * -1));

		return isCampaignNBOType(results);
	}

	private boolean isCampaignNBOType(List<CustomerCampaignOfferDetail> results) {
		if (results != null && results.size() != 0) {
			log.info("Number of active customer offers are :: {}", results.size());
			for (CustomerCampaignOfferDetail detail : results) {
				CampaignDetail campaignDetail = campaignCache.getCampaign(detail.getCampaignId());
				if (Objects.nonNull(campaignDetail) && CampaignStrategy.NBO.name().equals(campaignDetail.getCampaignStrategy())) {
					log.info("Active customer offers found with customer campaign offer detail id :: {}",
							detail.getCapmpaignOfferDetailId());
					return true;
				}
			}
		}
		log.info("No active NBO offer found for customer");
		return false;
	}

	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public CustomerCampaignOfferDetail getActiveCustomerOffer(int customerId, String strategy) {
		return offerWithStrategy(customerCampaignOfferDao.findAllByCustomerIdAndStatusAndCouponEndDate(customerId,
				AppConstants.ACTIVE, AppUtils.getBusinessDate()), strategy);
	}

	@Override
	public CustomerDineInView getCustomerDineInView(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
		return customerService.getCustomerDineInView(customerId, brandId, excludeOrderIds);
	}

	private CustomerCampaignOfferDetail offerWithStrategy(List<CustomerCampaignOfferDetail> detailList,
			String strategy) {
		for (CustomerCampaignOfferDetail detail : detailList) {
			CampaignDetail campaignDetail = campaignCache.getCampaign(detail.getCampaignId());
			if (Objects.nonNull(campaignDetail) && campaignDetail.getCampaignStrategy().equals(strategy)) {
				return detail;
			}
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public CustomerCampaignOfferDetail createPostOrderOffer(Integer brandId, Integer unitId, Integer campaignId,
			int customerId, Integer orderId, String contactNumber, String countryCode, CouponData response,
			String firstName, String description, String cloneCopunCode, CampaignDetail campaignDetail,
			CustomerRepeatType type, Integer journeyNumber, CreateNextOfferRequest request) {
		CustomerCampaignOfferDetail detail = get(brandId, unitId, campaignId, response, description, cloneCopunCode,
				customerId, orderId, contactNumber, countryCode, firstName, campaignDetail, type, journeyNumber);
		detail.setCouponType(CouponType.INTERNAL.name());
		if (Objects.nonNull(request)) {
			detail.setUtmMedium(request.getUtmMedium());
			detail.setUtmSource(request.getUtmSource());
		}
		customerCampaignOfferDao.save(detail);
		return detail;
	}

	private CustomerCampaignOfferDetail get(Integer brandId, Integer unitId, Integer campaignId, CouponData data,
			String offerText, String cloneCode, Integer customerId, Integer orderId, String contactNumber,
			String countryCode, String firstName, CampaignDetail campaignDetail, CustomerRepeatType type,
			Integer journey) {
		CustomerCampaignOfferDetail detail = new CustomerCampaignOfferDetail();
		CampaignMapping mapping = isNextJourney(campaignDetail, journey, type);
		detail.setCampaignCloneCode(cloneCode);
		detail.setCampaignId(campaignId);
		setCustomerData(detail, customerId, contactNumber, countryCode, firstName);
		detail.setIsNotificationRequired(AppConstants.YES);
		detail.setOfferCreateOrderId(orderId);
		detail.setStatus(AppConstants.ACTIVE);
		detail.setBrandId(brandId);
		detail.setUnitId(unitId);
		detail.setCurrentJourneyNumber(journey);
		detail.setCustomerType(type.name());
		if (Objects.nonNull(data)) {
			detail.setCouponCode(data.getCoupon());
			detail.setCouponDetailId(data.getCouponDetailId());
			detail.setOfferDetailId(data.getOfferDetailId());
			detail.setCouponStartDate(AppUtils.getDate(data.getStartDate(), AppUtils.DATE_FORMAT_STRING));
			detail.setCouponEndDate(AppUtils.getDate(data.getEndDate(), AppUtils.DATE_FORMAT_STRING));
			detail.setReminderDays(campaignDetail.getMappings().get(type.name()).get(journey).getReminderDays());
			if (Objects.nonNull(mapping)) {
				detail.setNextJourneyNumber(journey + 1);
				detail.setNextOfferDate(AppUtils.addDays(detail.getCouponEndDate(), 1));
			}
		}
		detail.setCouponGenerationTime(AppUtils.getCurrentTimestamp());
		detail.setOfferText(offerText);
		return detail;
	}

	private CampaignMapping isNextJourney(CampaignDetail campaignDetail, Integer journey, CustomerRepeatType type) {
		CampaignMapping mapping = campaignDetail.getMappings().get(type.name()).get(journey + 1);
		if (Objects.nonNull(mapping)) {
			log.info("Customer next journey found customer type : {}, journey : {}", type.name(), mapping.getJourney());
		} else {
			log.info("No next journey found for customer type : {}, journey : {}", type.name(), journey + 1);
		}
		return mapping;
	}

	private void setCustomerData(CustomerCampaignOfferDetail detail, int customerId, String contactNumber,
			String countryCode, String firstName) {
		detail.setContactNumber(contactNumber);
		detail.setCountryCode(countryCode);
		detail.setCustomerId(customerId);
		detail.setFirstName(firstName);
	}

	@Override
	public boolean hasCustomerReceivedDNBO(int customerId, Integer lastNDays, Integer campaignId) {
		List<CustomerCampaignOfferDetail> results = customerCampaignOfferDao.getAlreadyReceivedOffer(customerId,
				AppConstants.ACTIVE, AppUtils.addDays(AppUtils.getBusinessDate(), lastNDays * -1));
		return isCampaignNBOType(results);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public DeliveryCouponDetailData getDeliveryCloneCode(String code, Integer brandId, Boolean getClonedCoupon) {
		DeliveryCouponDetailData data;
		if (getClonedCoupon) {
			data = deliveryCouponDetailDataDao.getDeliveryCoupon(code, AppUtils.getCurrentTimestamp(), brandId, 3,
					DeliveryCouponStatus.AVAILABLE.name(), AppConstants.getValue(false));
			if (Objects.nonNull(data)) {
				data.setDeliveryCouponStatus(DeliveryCouponStatus.PROCESSING.name());
				deliveryCouponDetailDataDao.save(data);
			}
		} else {
			Date currentdate = AppUtils.getCurrentTimestamp();
			data = deliveryCouponDetailDataDao.getMasterCoupon(code, currentdate, currentdate, brandId, 3,
					DeliveryCouponStatus.AVAILABLE.name());
		}
		return data;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public CustomerCampaignOfferDetail createDeliveryPostOrderOffer(Integer brandId, Integer unitId, Integer campaignId,
			Customer customer, Integer orderId, String contactNumber, String countryCode, CouponData response,
			String firstName, String description, String cloneCouponCode, DeliveryCouponDetailData deliveryCoupon,
			Boolean isCloneCoupon, CampaignDetail campaignDetail, CustomerRepeatType type, Integer journeyNumber,
			CreateNextOfferRequest request) {
		CustomerCampaignOfferDetail detail = get(brandId, unitId, campaignId, response, description, cloneCouponCode,
				customer.getId(), orderId, contactNumber, countryCode, firstName, campaignDetail, type, journeyNumber);
		detail.setCouponType(CouponType.EXTERNAL.name());
		detail.setChannelPartner(deliveryCoupon.getChannelPartnerId());
		if (Objects.nonNull(request)) {
			detail.setUtmMedium(request.getUtmMedium());
			detail.setUtmSource(request.getUtmSource());
		}

		couponService.updateAllocationOfCoupon(deliveryCoupon, customer.getId(), customer.getContactNumber(),
				campaignId, orderId);
		campaignOfferDetailDao.save(detail);
		return detail;
	}

	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public CustomerEmailData getCustomerEmailData(int customerId, Integer brandId) {
		return customerOfferDetailDao.getCustomerEmailData(customerId, brandId, AppUtils.getBusinessDate());
	}

    @Override
    public DenominationValueData getDenominationOffer(Integer unitId) {
        return walletCacheService.getAllActiveDenominationValues(unitId);
    }

    @Override
    public DenominationValueData getDirectWalletOffers(Integer unitId) {
        DenominationValueData data = getDenominationOffer(unitId);
        if (Objects.nonNull(unitId)) {

			List<CashCardOffer> activeOffers= cashCardOfferDao.getActiveCashCardOffer(unitId,AppUtils.getCurrentBusinessDate(), AppConstants.ACTIVE,AppConstants.DINE_IN_CHANNEL_PARTNER);
			Map<Integer, DenominationOfferPercentage> activeDenominationMap =new HashMap<>();
			List<String> denominationList = new ArrayList<>();
			try {
				if(Objects.nonNull(activeOffers) && activeOffers.size()>0){

					for (CashCardOffer offer:activeOffers){
						if(Objects.nonNull(offer.getDenomination()) && !activeDenominationMap.containsKey(offer.getDenomination().intValue()) ){
							DenominationOfferPercentage offerPercentage = DenominationOfferPercentage.builder().denomination(offer.getDenomination().intValue()).build();
							Integer extraAmount = Objects.nonNull(offer.getPercentage()) ? offer.getDenomination().multiply(offer.getPercentage()).intValue() : BigDecimal.ZERO.intValue();
							BigDecimal extra = new BigDecimal (extraAmount/100);
							offerPercentage.setExtraValue(extra.intValue());
							activeDenominationMap.put(offerPercentage.getDenomination(),offerPercentage);
							denominationList.add(offerPercentage.getDenomination().toString());
						}
					}
					data.setDenominationOfferMap(activeDenominationMap);
					data.setActiveDenomination(denominationList);
				}
			}catch (Exception e){
				log.error("Error getting denomination offer from cash Card Detail",e);
			}
		}
		return data;
	}

	@Override
	public void refreshDenominationOfferValue(){
		walletCacheService.removeAllActiveDenominationValues();
	}

	@Override
	public void refreshUnitDenominationOfferValue(Integer unitId){
		walletCacheService.removeUnitActiveDenominationValues(unitId);
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public WalletRecommendationDetail getSuggestWalletOfferExtraAmount(WalletSuggestionCustomerInfo customerData, Integer unitId) throws Exception {

        try {
            Stopwatch watch = Stopwatch.createUnstarted();
            Map<String, DroolVersionDomain> unitDroolVersionMapping =  unitDroolVersionMappingCache.getUnitDroolVersionMapping(unitId);
            if (Objects.nonNull(customerData.getCustomerPayableAmount()) && Objects.nonNull(customerData.getCustomerId()) && Objects.nonNull(customerData.getBrandId())) {
                BigDecimal totalLeftAmount = new BigDecimal(customerData.getCustomerPayableAmount());
                Integer totalPayableAmount = totalLeftAmount.intValueExact();
                watch.start();
                WalletSuggestionData customerWalletData = customerDataService.getCustomerWalletSuggestionData(customerData.getCustomerId(), customerData.getBrandId());
                log.info("Step 1 : Get Customer info  : {}ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
                customerWalletData.setAmountPayable(totalPayableAmount);
                watch.start();
				String walletSheetVersion = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.WALLET_DECISION.name()) ?
						unitDroolVersionMapping.get(DroolFileType.WALLET_DECISION.name()).getVersion() : null;
                if(!droolService.isDroolContainerInitializeForWallerDecision(walletSheetVersion)){
                    droolService.initailizeDroolContainer(DroolFileType.WALLET_DECISION.getFilename(), walletSheetVersion);
                }
                KieSession kieSession = droolsConfig.getKieContainerForWalletDecision(walletSheetVersion).newKieSession();
                kieSession.insert(customerWalletData);
                kieSession.fireAllRules();
                kieSession.destroy();
                log.info("Step 2 : Set Wallet Suggestion Drool for Customer  : {}ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
                log.info("Printing Value of suggestionData::{}", new Gson().toJson(customerWalletData));
                DenominationValueData denominationValueData = getDenominationOffer(unitId);
//                Map<Integer,DenominationOfferPercentage> activeDenominationMap;
				if(Objects.nonNull(customerWalletData)&& Objects.nonNull(customerWalletData.getMinimumDenomination()) &&
						Objects.nonNull(customerWalletData.getMinimumDenominationCount()) &&Objects.nonNull(denominationValueData) && Objects.nonNull(denominationValueData.getDenominationOfferMap()) ){
					return calculateWalletSuggestion(customerWalletData,denominationValueData.getDenominationOfferMap());
				}
			}
			else{
				throw new BaseException("Invalid body Parameters");
			}
		}catch (Exception e){
			log.error("Error getting wallet suggestion from walletSuggestionDroolsConfig",e);
		}

		return null;
	}

	private WalletRecommendationDetail calculateWalletSuggestion(WalletSuggestionData customerWalletData, Map<Integer,DenominationOfferPercentage> activeDenominationMap){

		List<Integer> denominationArray = new ArrayList<>(activeDenominationMap.keySet());
		Collections.sort(denominationArray);
		Integer suggestWalletAmount = 0;
		Integer extraWalletAmount = 0;
		Integer totalPayableAmount = customerWalletData.getAmountPayable();
		for(int i=denominationArray.size()-1; i>=0;i--){
			if(totalPayableAmount >= denominationArray.get(i) && denominationArray.get(i)>=customerWalletData.getMinimumDenomination()){
				Integer quantity = totalPayableAmount/denominationArray.get(i);
				if(quantity > 0){
					totalPayableAmount = totalPayableAmount - (quantity*denominationArray.get(i));
				}
				suggestWalletAmount += denominationArray.get(i) * quantity;
				extraWalletAmount += (activeDenominationMap.get(denominationArray.get(i)).getExtraValue()) * quantity;
			}
		}
		if (totalPayableAmount <200 && totalPayableAmount >= 0) {
			if ( ((customerWalletData.getMinimumDenomination()*customerWalletData.getMinimumDenominationCount()) - totalPayableAmount) > customerWalletData.getWalletRecommendationBaseValue()) {
				suggestWalletAmount += (customerWalletData.getMinimumDenomination()*customerWalletData.getMinimumDenominationCount());
				extraWalletAmount +=  (customerWalletData.getMinimumDenominationCount() * (activeDenominationMap.get((customerWalletData.getMinimumDenomination())).getExtraValue()));
			}
			else {
				suggestWalletAmount += (customerWalletData.getMinimumDenomination()*(customerWalletData.getMinimumDenominationCount() +1)) ;
				extraWalletAmount +=  ((customerWalletData.getMinimumDenominationCount() +1) * (activeDenominationMap.get(customerWalletData.getMinimumDenomination()).getExtraValue()));
			}
			totalPayableAmount = 0;
		}
		else if(totalPayableAmount<0){
			suggestWalletAmount += (customerWalletData.getMinimumDenomination()*customerWalletData.getMinimumDenominationCount());
			extraWalletAmount +=  (customerWalletData.getMinimumDenominationCount() * (activeDenominationMap.get((customerWalletData.getMinimumDenomination())).getExtraValue()));
		}
		//Double extraAmount = (extraWalletAmount*customerWalletData.getExtraValueMultiplier())/10;
		Double extraAmount = extraWalletAmount*customerWalletData.getExtraValueMultiplier();

		return WalletRecommendationDetail.builder().walletSuggestion(suggestWalletAmount).extraAmount(extraAmount.intValue()).incrementStepper(customerWalletData.getMinimumDenomination()).build();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void addSubscriptionSaving(int customerId, Order order, Pair<CouponDetail, Product> subscriptionObj) {
		log.info("Adding Over All Saving For Customer Id ::{}",customerId);
		SubscriptionPlan subscriptionPlan = subscriptionPlanDao.findByCustomerIdAndStatus(customerId,AppConstants.ACTIVE);
		if(Objects.nonNull(subscriptionPlan)) {
			subscriptionPlan.setOverAllSaving(AppUtils.add(order.getTransactionDetail().getSavings(), subscriptionPlan.getOverAllSaving()));
			Optional<SubscriptionPlanEvent> subscriptionPlanEvent = subscriptionPlanEventDao.findById(subscriptionPlan.getLastRenewalEventId());
			if (subscriptionPlanEvent.isPresent()) {
				SubscriptionPlanEvent subscription = subscriptionPlanEvent.get();
				if (FrequencyOfferType.TIME_BASED.name().equals(subscriptionPlan.getFrequencyStrategy()) || FrequencyOfferType.TIME_QUANTITY_BASED.name().equals(subscriptionPlan.getFrequencyStrategy())) {
					subscriptionPlan.setOverAllFrequency(AppUtils.add(subscriptionPlan.getOverAllFrequency(), BigDecimal.ONE));
				} else if (FrequencyOfferType.QUANTITY_BASED.name().equals(subscriptionPlan.getFrequencyStrategy())) {
					subscriptionPlan.setOverAllFrequency(AppUtils.add(subscriptionPlan.getOverAllFrequency(), getOrderWithSubscriptionCode(order, subscriptionPlan.getSubscriptionPlanCode())));
				}
				if (Objects.nonNull(subscriptionPlan.getOverAllFrequency()) && subscriptionPlan.getOverAllFrequency().compareTo(subscriptionPlan.getFrequencyLimit()) >= 0) {
					subscriptionPlan.setStatus(AppConstants.IN_ACTIVE);
					subscription.setStatus(AppConstants.IN_ACTIVE);
				}
				subscriptionPlanDao.save(subscriptionPlan);
				subscriptionPlanEventDao.save(subscription);
			}
			SubscriptionPlanEvent subscriptionPlanEvents = subscriptionPlanEventDao.addSubscriptionEventSaving(customerId, AppUtils.getCurrentDate(), AppConstants.ACTIVE, 1);
			if(Objects.nonNull(subscriptionPlanEvents)) {
				subscriptionPlanEvents.setSubscriptionSavings(AppUtils.add(order.getTransactionDetail().getSavings(), subscriptionPlanEvents.getSubscriptionSavings()));
				subscriptionPlanEventDao.save(subscriptionPlanEvents);
			}
		}
		else {
			log.info("No subscription found");
		}

	}


	private BigDecimal getOrderWithSubscriptionCode(Order order, String subscriptionPlanCode) {
		BigDecimal frequencyUsed = BigDecimal.ZERO;
		for (OrderItem orderItem : order.getOrders()){
			if(Objects.nonNull(orderItem.getDiscountDetail().getDiscountReason()) &&
					orderItem.getDiscountDetail().getDiscountReason().equals(subscriptionPlanCode)){
				frequencyUsed = AppUtils.add(frequencyUsed,AppUtils.divide(
						AppUtils.subtract(
								AppUtils.multiply(orderItem.getPrice(),
										BigDecimal.valueOf(orderItem.getQuantity())),orderItem.getAmount()),orderItem.getPrice()));
//				((price*quantity)-amt_paid)/price

			}
		}
		return frequencyUsed;
	}

    @Override
    public Integer getLastOrderedSavedChai(Integer customerId){
        return customerService.getLastOrderedSavedChai(customerId);
    }

    @Override
    public SavedChaiOrderedDomain getSavedChaiOrderDetail(Integer customerId){
        return customerService.getSavedChaiOrderDetail(customerId);
    }

}
