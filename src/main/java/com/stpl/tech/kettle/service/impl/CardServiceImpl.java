package com.stpl.tech.kettle.service.impl;

import com.stpl.tech.kettle.data.kettle.CashCardDetail;
import com.stpl.tech.kettle.domain.model.CashCardStatus;
import com.stpl.tech.kettle.exceptions.CardValidationException;
import com.stpl.tech.kettle.repository.kettle.CashCardDetailDao;
import com.stpl.tech.kettle.service.CardService;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Log4j2
@Service
public class CardServiceImpl implements CardService {

    @Autowired
    CashCardDetailDao cashCardDetailDao;
    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
    public CashCardDetail getCardDetail(int customerId, String cardNumber, boolean runValidations)
            throws CardValidationException {
        CashCardDetail cashCardDetail = cashCardDetailDao.findByCardNumber(cardNumber);
        if(Objects.isNull(cashCardDetail)){
            String message = "Did not find any card with card number : " + cardNumber;
            log.info(message);
            throw new CardValidationException(message);
        }
        if (runValidations) {
            validateCard(cashCardDetail, customerId, BigDecimal.ZERO);
        }
        return cashCardDetail;

    }

    private void validateCard(CashCardDetail cashCardDetail, int customerId, BigDecimal amount)
            throws CardValidationException {
        if (cashCardDetail.getCashPendingAmount().subtract(amount).compareTo(BigDecimal.ZERO) < 0) {
            throw new CardValidationException("Card does not have sufficient value : Card Value "
                    + cashCardDetail.getCashPendingAmount() + " : Amount Charged :" + amount);
        }
        if (cashCardDetail.getCustomerId() != null && cashCardDetail.getCustomerId() != customerId) {
            throw new CardValidationException("Card is not linked with the customer");
        }
        /*
         * if (cashCardDetail.getEndDate().before(AppUtils.getCurrentBusinessDate() )) {
         * throw new CardValidationException("Card has expired on: " +
         * cashCardDetail.getEndDate()); }
         */
        if (cashCardDetail.getCardStatus().equals(CashCardStatus.INITIATED.name())) {
            throw new CardValidationException("Card has not been purchased.");
        }
        if (cashCardDetail.getCardStatus().equals(CashCardStatus.EXPIRED.name())) {
            throw new CardValidationException("Card has expired");
        }
        if (cashCardDetail.getCardStatus().equals(CashCardStatus.BLOCKED.name())) {
            throw new CardValidationException("Card is invalid");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CashCardDetail> getActiveCashCards(int customerId) {
        return cashCardDetailDao.findActiveCashCards(customerId,BigDecimal.ZERO, AppConstants.ACTIVE);
    }

}
