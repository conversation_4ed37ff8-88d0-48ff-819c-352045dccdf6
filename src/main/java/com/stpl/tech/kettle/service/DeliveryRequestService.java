package com.stpl.tech.kettle.service;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Unit;

public interface DeliveryRequestService {
    OrderInfo addDeliveryDetails(OrderInfo info, Unit unit);

    DeliveryResponse createDeliveryRequest(OrderInfo orderInfo, Unit unit);

    Boolean checkIfAutomated(Integer deliveryPartnerId);

    DeliveryResponse saveDeliveryDetails(int unitId, DeliveryResponse response);

    OrderInfo addDeliveryDetails(OrderInfo info, Unit unit, IdCodeName deliveryPartner);
}
