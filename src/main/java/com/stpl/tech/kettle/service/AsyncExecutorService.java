package com.stpl.tech.kettle.service;

import java.util.Map;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.domain.model.OrderNotification;

public interface AsyncExecutorService {

	void pushDataToThirdPartyAnalytics(OrderInfo info, boolean isOrderCancelled,
			Map<Integer, OrderNotification> orderNotificationMap);

	void deleteReciepts(int unitId, int orderId);

	void pushDataToCleverTapForPartner(OrderInfo info, boolean isOrderCancelled, Map<Integer, OrderNotification> orderNotificationMap);
}
