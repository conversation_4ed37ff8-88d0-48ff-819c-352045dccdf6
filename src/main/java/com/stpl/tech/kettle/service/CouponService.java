package com.stpl.tech.kettle.service;

import java.util.List;

import com.stpl.tech.kettle.data.master.CouponDetailData;
import com.stpl.tech.kettle.data.master.DeliveryCouponDetailData;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.master.domain.model.CouponCloneRequest;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.master.domain.model.CouponDetail;

public interface CouponService {

	void applyLoyaltyCode(Order order);

	void checkAmex(Order order) throws DataUpdationException;

	CouponCloneResponse generateCoupon(CouponCloneRequest coupon) throws DataUpdationException;

	CouponDetail getModalCoupon(String modelCouponCode);

	CouponDetail getCouponDetail(String couponCode, boolean getAll, boolean getParentMapping, boolean applyLimit);

	CouponDetail addCouponDetailData(String code, String newCode);

	CouponDetail searchCoupon(String couponCode, boolean applyLimit);

	void addCustomerMappingCouponData(CouponDetailData couponDetail, String contactNumber);

	CouponDetail updateCoupon(CouponDetail coupon, boolean updateMappings);

	void updateAllocationOfCoupon(DeliveryCouponDetailData deliveryCoupon, Integer customerId, String contactNumber,
			Integer campaignId, int orderId);

	List<String> getValidCoupon(List<String> coupons);
}
