package com.stpl.tech.kettle.service.impl;

import com.stpl.tech.kettle.domain.kettle.CustomerType;
import com.stpl.tech.kettle.domain.kettle.WalletSuggestionData;
import com.stpl.tech.kettle.repository.clm.CustomerInfoDataDao;
import com.stpl.tech.kettle.service.CustomerDataService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.TransactionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class CustomerDataServiceimpl  implements CustomerDataService {

    @Autowired
    private CustomerInfoDataDao customerInfoDataDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public WalletSuggestionData getCustomerWalletSuggestionData(String customerId, String brandId){

        try {
            if(Objects.nonNull(customerId) && Objects.nonNull(brandId)) {
                List<Object[]> customerData =  customerInfoDataDao.getCustomerWalletInfo(Integer.valueOf(customerId), Integer.valueOf(brandId));
                if(Objects.isNull(customerData) || customerData.isEmpty() ){
                    return setCustomerWalletInfo(null);
                }
                return  setCustomerWalletInfo(customerData.get(0));
            }
        }catch (Exception e){
            log.error("Error getting Customer Info from CLM One View for customer::"+customerId,e);
        }
        return  setCustomerWalletInfo(null);
    }

    private  WalletSuggestionData setCustomerWalletInfo(Object[] customerData){
        if(Objects.isNull(customerData)){
            return  WalletSuggestionData.builder().isNewCustomer("Y").build();
        }else{
            return WalletSuggestionData.builder()
                    .isNewCustomer("N")
                    .latestOrderDayDiff(Objects.nonNull(customerData[7]) ? AppUtils.getActualDayDifference(
                            AppUtils.getDate(String.valueOf(customerData[7]),AppUtils.DATE_FORMAT_STRING),
                            AppUtils.getCurrentDate()): -1000000)
                    .visitsCount(Integer.valueOf(String.valueOf(customerData[2])))
                    .visitsCount90Days(Integer.valueOf(String.valueOf(customerData[5])))
                    .build();
        }
    }

    @Override
    public String getCustomerType(Integer customerId, Integer brandId){
        if(customerId!=null && customerId <= 5) return CustomerType.UNREGISTER.name();
        Object[] customerDroolResult = customerInfoDataDao.getCustomerType(customerId,brandId);
        if(Objects.nonNull(customerDroolResult)){
            if(Objects.isNull(customerDroolResult[1]) || (Integer)customerDroolResult[1] < 1 ){
               return CustomerType.NEW.name();
            }else if (Objects.nonNull(customerDroolResult[0])){
                return TransactionUtils.getCustomerType((String) customerDroolResult[0]);
            }
        }
            return CustomerType.NEW.name();
    }
}