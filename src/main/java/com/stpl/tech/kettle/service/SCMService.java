package com.stpl.tech.kettle.service;

import java.util.Collection;
import java.util.List;

import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.scm.WastageEvent;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.master.domain.model.Consumable;

public interface SCMService {
	void checkBookWastage(Order o) throws DataUpdationException, DataNotFoundException;

	WastageEvent getWastageEvent(Order o, Collection<Consumable> values, boolean forVerification)
			throws DataNotFoundException;

	List<String> verifyWastageBookingInSumo(WastageEvent event) throws DataUpdationException;

	void bookWastage(Order o) throws DataNotFoundException;

	void persistWastageToSumo(WastageEvent event, int wastageEventId);

	void setWastageSumoId(int orderId, int sumoId);
}
