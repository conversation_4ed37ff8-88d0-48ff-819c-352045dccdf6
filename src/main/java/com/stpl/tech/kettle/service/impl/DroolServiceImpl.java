package com.stpl.tech.kettle.service.impl;

import com.stpl.tech.kettle.core.config.DroolsConfig;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.DroolsDecisionTableData;
import com.stpl.tech.kettle.domain.FileDetail;
import com.stpl.tech.kettle.repository.kettle.DroolsDecisionTableDataDao;
import com.stpl.tech.kettle.service.DroolService;
import com.stpl.tech.kettle.service.FileArchiveService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.util.DroolFileType;
import jakarta.persistence.NoResultException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.mock.web.MockMultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;


@Service
@Log4j2
public class DroolServiceImpl implements DroolService{

    private static final String DROOLS_FOR_WALLET_DECISION = "drools/wallet_decision";

    private static final String DROOLS_FOR_DENOMINATION_DECISION = "drools/denomination_decision";

    @Autowired
    private DroolsConfig droolsConfig;

    @Autowired
    private EnvironmentProperties env;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private DroolsDecisionTableDataDao droolsDecisionDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addNewDroolFile(MultipartFile file, String droolFileType, boolean persist){
        try {
            if(Objects.isNull(droolFileType)){
                log.info("Error while updating drool sheet as drool file type in null");
                return false;
            }
            String fileTypeName = DroolFileType.valueOf(droolFileType).getFilename();
            DroolsDecisionTableData currentDroolData = getDecisionTableData(droolFileType);
            if(Objects.isNull(currentDroolData)){
                currentDroolData = DroolsDecisionTableData.builder().fileName(fileTypeName + "_v0.0.xls").build();
            }
            String fileName = getNextVersion(currentDroolData.getFileName().split(".xls")[0]) + ".xls";
            String version = "v" + fileName.split("_v")[1].split(".xls")[0];
            if(persist){
                String baseDir = env.getEnvironmentType().name().toLowerCase() + "/" + fileTypeName + "/" + version;
                log.info(":::::: Request to upload New Drool decision table ::::::");
                FileDetail s3File = fileArchiveService.saveFileToS3(env.getS3BucketForDrools(),
                        baseDir, fileName, file, true);
                if (s3File != null) {
                    log.info("URL ::::: {}", s3File.getUrl());
                }
                saveCurrentAndDeactivateOther(DroolsDecisionTableData.builder()
                        .type(DroolFileType.valueOf(droolFileType).name())
                        .creationTime(AppUtils.getCurrentTimestamp())
                        .fileName(fileName)
                        .status(AppConstants.PROCESSING)
                        .version(version)
                        .isDefault(AppConstants.NO)
                        .build());
            }
        }catch (Exception e){
            return false;
        }
        return true;
    }

    private void resetDroolContainer(String droolFileType, String version) {
        if (DroolFileType.WALLET_DECISION.equals(DroolFileType.valueOf(droolFileType))) {
            droolsConfig.initDroolConfigForWalletDecision(version);
        }
        if (DroolFileType.DENOMINATION_DECISION.equals(DroolFileType.valueOf(droolFileType))) {
            droolsConfig.initDroolConfigForDenominationPercentage(version);
        }
    }

    @Override
    public void downloadRecipeMedia(HttpServletResponse response, String fileName, String droolFileType, String version) throws IOException {
        String s3RecipeMediaBucket = env.getS3BucketForDrools();
        String envType = env.getEnvironmentType().name().toLowerCase();
        FileDetail fileDetail = new FileDetail(s3RecipeMediaBucket, envType + "/" + DroolFileType.valueOf(droolFileType).getFilename() + "/" + version + "/" + fileName, null);
        File file = fileArchiveService.getFileFromS3(env.getBasePath() + File.separator + "s3", fileDetail);
        setFileToResponse(response, fileName, file);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateVersion(String fileName, String droolFileType, String version) throws IOException {
        try {
            if(Objects.isNull(droolFileType)){
                log.info("Error while updating drool sheet as drool file type in null");
                return false;
            }
            getFileFromS3andDownload(DroolFileType.valueOf(droolFileType).getFilename(),fileName, version);
            resetDroolContainer(droolFileType, version);
//            deactivateOther(droolFileType);
            activateCurrentFile(droolFileType, fileName, version);
        }catch (Exception e ){
            return false;
        }
        return true;
    }

    @Override
    public List<DroolsDecisionTableData> fetchAllFileByType(String fileType) {
        try {
            List<DroolsDecisionTableData> data = droolsDecisionDao.findByTypeOrderById(fileType);
            List<DroolsDecisionTableData> result = new ArrayList<>();
            if (!CollectionUtils.isEmpty(data)) {
                for(DroolsDecisionTableData e : data){
                    if(!AppConstants.DECLINE.equals(e.getStatus())){
                        result.add(e);
                    }
                }
                return result;
            }
        } catch (NoResultException e) {
            log.error("No decision table data found for type : {}", fileType);
        } catch (Exception e) {
            log.error("Exception occurred while fetching decision table data for type : {} ", fileType);
        }
        return new ArrayList<>();
    }

    private void setFileToResponse(HttpServletResponse response, String fileName, File file) throws IOException {
        if (Objects.nonNull(file)) {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.addHeader("Content-Disposition", "attachment; filename=" + file.getName());
            byte[] bytesArray = new byte[(int) file.length()];
            response.setContentLength(bytesArray.length);
            try {
                OutputStream outputStream = response.getOutputStream();
                InputStream inputStream = new FileInputStream(file);
                int counter = 0;
                while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
                    outputStream.write(bytesArray, 0, counter);
                    outputStream.flush();
                }
                outputStream.close();
                inputStream.close();
            } catch (IOException e) {
                log.error("Encountered error while writing file to response stream", e);
                throw e;
            } finally {
                response.getOutputStream().flush();
                file.delete(); // delete the temporary file created after completing request
            }
        }
    }

    private static String getNextVersion(String fileName) {
        Double version = Double.valueOf(fileName.split("_v")[1]);
        version = version + .1;
        version = BigDecimal.valueOf(version).setScale(1, RoundingMode.HALF_UP).doubleValue();
        return (fileName.split("_v")[0] + "_v" + version);
    }

    private void saveCurrentAndDeactivateOther(DroolsDecisionTableData data) {
        //deactivateOther(data.getType());
        declineExistingProcessingFile(data.getType(), data.getFileName());
        droolsDecisionDao.save(data);
    }

    public DroolsDecisionTableData getDecisionTableData(String type) {
        try {
            DroolsDecisionTableData data = droolsDecisionDao.findTop1ByTypeOrderByCreationTimeDesc(type);
            if (Objects.nonNull(data)) {
                return data;
            }
        } catch (NoResultException e) {
            log.error("No decision table data found for type : {} and status :{}", type);
        } catch (Exception e) {
            log.error("Exception occurred while fetching decision table data for type : {}", type);
        }
        return null;
    }


    public void deactivateOther(String type) {
        try {
            DroolsDecisionTableData droolsData = droolsDecisionDao.findByTypeAndStatus(type , AppConstants.ACTIVE);
            if(Objects.nonNull(droolsData)) {
                droolsData.setStatus(AppConstants.IN_ACTIVE);
                droolsDecisionDao.save(droolsData);
            }
        } catch (Exception e) {
            log.error("Exception occurred while deactivating other decision table data for type : {} ", type);
            log.error("Error while deactivating drool",e);
        }
    }

    public void activateCurrentFile(String type, String fileName, String version) {
        try {
            DroolsDecisionTableData droolsData = droolsDecisionDao.findByTypeAndFileNameAndVersion(type , fileName, version);
            if(Objects.nonNull(droolsData)) {
                droolsData.setStatus(AppConstants.ACTIVE);
                droolsDecisionDao.save(droolsData);
            }
        } catch (Exception e) {
            log.error("Exception occurred while activating decision table data for type : {} and fileName : {} ", type, fileName);
        }
    }

    public void declineExistingProcessingFile(String droolFileType,String fileName){
        try {
            droolsDecisionDao.declineExistingProcessingFile(droolFileType,AppConstants.DECLINE,AppConstants.PROCESSING,fileName);
        }catch (Exception e){
            log.error("Exception occurred while change status of existing Drool file for type : {} and fileName : {}",droolFileType,fileName);
            log.error("Error while deactivating other",e);
        }
    }

    @Override
    public void initailizeDroolContainer(String droolFileType, String version){
        try{
            processAndDownloadDroolFile(droolFileType, version);
            if(DroolFileType.WALLET_DECISION.getFilename().equals(droolFileType)) {
                droolsConfig.initDroolConfigForWalletDecision(version);
            }
            if(DroolFileType.DENOMINATION_DECISION.getFilename().equals(droolFileType)){
                droolsConfig.initDroolConfigForDenominationPercentage(version);
            }
        }catch (Exception e){
            log.info("Error in initialize drool container for offerDecision : {}",e.getMessage());
        }
    }

    public void processAndDownloadDroolFile(String droolFileType, String version) throws Exception {
        String fileTypeName = droolFileType;
        try {
            List<String> statuses = Arrays.asList(AppConstants.ACTIVE, AppConstants.PROCESSING);
            List<DroolsDecisionTableData> currentDroolData = droolsDecisionDao.findByTypeAndStatusIn(droolFileType.toUpperCase(),statuses);
            boolean isFileUpdated = false;
            if (Objects.nonNull(currentDroolData) && !currentDroolData.isEmpty()) {
                for (DroolsDecisionTableData data : currentDroolData) {
                    if (AppConstants.PROCESSING.equals(data.getStatus()) && !isFileUpdated) {
                        getFileFromS3andDownload(fileTypeName, data.getFileName(),version);
                        declineExistingProcessingFile(droolFileType.toUpperCase(),data.getFileName());
//                        deactivateOther(droolFileType.toUpperCase());
                        activateCurrentFile(droolFileType.toUpperCase(), data.getFileName(), version);
                        isFileUpdated = true;
                    }
                }
                if(!isFileUpdated){
                    DroolsDecisionTableData data = currentDroolData.get(0);
                    File file = null;
                    if(AppConstants.ACTIVE.equals(data.getStatus())) {
                        String filePath = Objects.nonNull(version) ?env.getDroolBasePath() + "/drools/" + fileTypeName + "/" + version + "/" + fileTypeName + ".xls" :
                                env.getDroolBasePath() + "/drools/" + fileTypeName + "/default/" + fileTypeName + ".xls";
                        file = Paths.get(filePath).toFile();
                    }
                    if(Objects.isNull(file) || !file.exists()){
                        getFileFromS3andDownload(fileTypeName, data.getFileName(), version);
                    }
                }
            }
            else{
                copyFromResource(droolFileType);
            }
        }catch (Exception e){
            log.info("Error in Processing Drool file : {}",e.getMessage());
            log.info("Drool File not available on S3");
        }

    }
    
    public void copyFromResource(String droolFileType){
        String destinationDirectory = "";
        String sourceFileDirectory = "";
        if(DroolFileType.WALLET_DECISION.getFilename().equals(droolFileType)) {
            destinationDirectory = env.getBasePath() + "/" + DROOLS_FOR_WALLET_DECISION;
            sourceFileDirectory = DROOLS_FOR_WALLET_DECISION + "/" + droolFileType + ".xls";
        }
        if(DroolFileType.DENOMINATION_DECISION.getFilename().equals(droolFileType)){
            destinationDirectory = env.getBasePath() + "/" + DROOLS_FOR_DENOMINATION_DECISION;
            sourceFileDirectory = DROOLS_FOR_DENOMINATION_DECISION + "/" + droolFileType + ".xls";
        }
        try {
            fileArchiveService.saveFileToDestinationPath(sourceFileDirectory,destinationDirectory,droolFileType);
        }catch (Exception e){
            log.info("Error in saving drool file into data folder : {}",e.getMessage());
        }
    }

    @Override
    public boolean isDroolContainerInitializeForWallerDecision(String version){
        return Objects.nonNull(droolsConfig.getKieContainerForWalletDecision(version));
    }

    @Override
    public boolean isDroolContainerInitializeForDenominationPercentage(String version){
        return Objects.nonNull(droolsConfig.getKieContainerObjectForDenominationPercentage(version));
    }

    private void getFileFromS3andDownload(String fileTypeName,String fileName, String version) throws Exception {
        OutputStream os;
        String s3RecipeMediaBucket = env.getS3BucketForDrools();
        String envType = env.getEnvironmentType().name().toLowerCase();
        String path = env.getDroolBasePath() + "/drools/" + fileTypeName + "/";
        String filePath = Objects.nonNull(version) ? envType + "/" + fileTypeName + "/" + version + "/" + fileName :
                envType + "/" + fileTypeName + "/default/" + DroolFileType.valueOf(fileTypeName).getFilename();
        FileDetail fileDetail = new FileDetail(s3RecipeMediaBucket, filePath, null);
        File file = fileArchiveService.getFileFromS3(env.getDroolBasePath() + File.separator + "s3", fileDetail);
        FileInputStream input = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile("file",
                file.getName(), "text/plain", IOUtils.toByteArray(input));
        String newFilePath = Objects.nonNull(version) ? path + version + "/" + fileTypeName + ".xls" : path + "default/" + fileTypeName + ".xls";
        File newFile = new File(newFilePath);
        if (!newFile.exists()) {
            Path dirPath = Paths.get(path + (Objects.nonNull(version) ? version : "default"));
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
            }
            boolean isFileCreated = newFile.createNewFile();
            if (!isFileCreated) {
                log.info("Unable to create file {} skipping reset process", newFile.getAbsolutePath());
                throw new Exception("Unable to Create file");
            }
        }
        os = new FileOutputStream(newFile);
        os.write(multipartFile.getBytes());
        os.close();
        os.flush();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean setDefaultDroolSheet(String droolFileType,String fileName, String version) {
        try {
            String s3RecipeMediaBucket = env.getS3BucketForDrools();
            String envType = env.getEnvironmentType().name().toLowerCase();
            FileDetail fileDetail = new FileDetail(s3RecipeMediaBucket,envType + "/" + DroolFileType.valueOf(droolFileType).getFilename() + "/" + version + "/" + fileName, null);
            File file = fileArchiveService.getFileFromS3(env.getDroolBasePath() + File.separator + "s3",fileDetail);
            try (FileInputStream input = new FileInputStream(file)) {
                MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), "text/plain", IOUtils.toByteArray(input));
                String filePath = env.getDroolBasePath() + "/drools/" + DroolFileType.valueOf(droolFileType).getFilename() + "/default";
                Path dirPath = Paths.get(filePath);
                Path newFilePath = dirPath.resolve(DroolFileType.valueOf(droolFileType).getFilename() + ".xls");
                if (!Files.exists(dirPath)) {
                    Files.createDirectories(dirPath);
                }
                if (Files.deleteIfExists(newFilePath)) {
                    log.info("Existing file {} deleted successfully", newFilePath);
                }
                try (OutputStream os = new FileOutputStream(newFilePath.toFile())) {
                    os.write(multipartFile.getBytes());
                    log.info("New file created and written: {}", newFilePath);
                }
                String fileTypeName = DroolFileType.valueOf(droolFileType).getFilename();
                String baseDir = env.getEnvironmentType().name().toLowerCase() + "/" + fileTypeName + "/default";
                log.info(":::::: Request to upload default file to S3 ::::::");
                FileDetail s3File = fileArchiveService.saveFileToS3(env.getS3BucketForDrools(),
                        baseDir, DroolFileType.valueOf(droolFileType).getFilename() + ".xls", file, true);
                if (s3File != null) {
                    log.info("URL ::::: {}", s3File.getUrl());
                }
                setFileAsDefault(droolFileType, version);
                resetDroolContainer(droolFileType,null);
            } catch (IOException e) {
                log.error("Error processing the file: {}", e.getMessage());
                throw new RuntimeException("File processing failed", e);
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public void setFileAsDefault(String droolFileType, String version) {
        droolsDecisionDao.setIsDefaultStatus(droolFileType.toUpperCase(), AppConstants.NO);
        DroolsDecisionTableData currentDroolData = droolsDecisionDao.findByTypeAndVersion(droolFileType.toUpperCase(), version);
        if (Objects.nonNull(currentDroolData)) {
            currentDroolData.setIsDefault(AppConstants.YES);
            droolsDecisionDao.save(currentDroolData);
        }
    }

    @Override
    public boolean inactivateVersion(String droolFileType, String fileName, String version) {
        try {
            String folderPath = env.getEnvironmentType().name().toLowerCase() + "/" + DroolFileType.valueOf(droolFileType).getFilename() + "/" + version;
            Path filePath = Paths.get(folderPath);
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("File deleted from server: " + folderPath);
            } else {
                log.info("File does not exist on server: " + folderPath);
            }
            setFileStatus(droolFileType, version, AppConstants.IN_ACTIVE);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public void setFileStatus(String droolFileType, String version, String status) {
        DroolsDecisionTableData currentDroolData = droolsDecisionDao.findByTypeAndVersion(droolFileType.toUpperCase(), version);
        if (Objects.nonNull(currentDroolData)) {
            currentDroolData.setStatus(status);
            currentDroolData.setIsDefault(AppConstants.NO);
            droolsDecisionDao.save(currentDroolData);
        }
    }

}
