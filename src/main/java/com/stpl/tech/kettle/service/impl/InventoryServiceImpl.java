package com.stpl.tech.kettle.service.impl;


import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.service.InventoryService;
import com.stpl.tech.kettle.service.SQSNotificationService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.master.inventory.model.QuantityResponseData;

import lombok.extern.log4j.Log4j2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.jms.JMSException;
import java.util.Objects;


@Service
@Log4j2
public class InventoryServiceImpl implements InventoryService {
    @Autowired
    private SQSNotificationService sqsNotificationService;

    @Autowired
    UnitCacheService unitCacheService;

    @Override
    public void publishInventorySQSFifo(String env, QuantityResponseData response) throws JMSException {
        String unitZoneKey = "_INVENTORY" +
                (Objects.nonNull(unitCacheService.getUnitBasicDetailById(response.getUnitId()).getUnitZone())
                        ? "_" + unitCacheService.getUnitBasicDetailById(response.getUnitId()).getUnitZone().toUpperCase() : "_NORTH");
        unitZoneKey = unitZoneKey + ".fifo";
        log.info("Publishing Inventory");
        sqsNotificationService.publishToSQSFifo(env,response,unitZoneKey, AppUtils.getRegion(EnvType.valueOf(env)));
    }
}
