package com.stpl.tech.kettle.service.impl;

import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Scanner;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonElement;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.service.FireStoreService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.stpl.tech.kettle.notification.FirebaseNotification;
import com.stpl.tech.kettle.service.FirebaseNotificationService;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Pair;

import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
@DependsOn("taskExecutor")
public class FirebaseNotificationServiceImpl implements FirebaseNotificationService {
	private static final String BASE_URL = "https://fcm.googleapis.com";
	private static final String MESSAGING_SCOPE = "https://www.googleapis.com/auth/firebase.messaging";
	private static final String[] SCOPES = { MESSAGING_SCOPE };
	private static final Integer TIMEOUT = 250;
	private static final String KEY_PROJECT_ID = "project_id";
	private Map<EnvType, GoogleCredential> credentialMap = new HashMap<>();

	private ObjectMapper mapper = new ObjectMapper();

	private BlockingQueue<Pair<EnvType, FirebaseNotification>> notificationQueue = new LinkedBlockingQueue<>();

	@Autowired
	@Qualifier("taskExecutor")
	private Executor taskExecutor;

	@Autowired
	private FireStoreService fireStoreService;

	@Autowired
	private EnvironmentProperties properties;

	@PostConstruct
	public void startPublisher() {
		log.info("POST-CONSTRUCT FirebaseNotificationServiceImpl - STARTED");
		taskExecutor.execute(() -> {
			while (true) {
				try {
					Pair<EnvType, FirebaseNotification> msg = notificationQueue.take();
					taskExecutor.execute(() -> sendNotificationFromQueue(msg.getKey(), msg.getValue()));
				} catch (InterruptedException e) {
					log.error("Error while running publish message of Pubnub", e);
				}
			}
		});
	}

	public static class TestNotification implements FirebaseNotification {

		@Override
		public String getTopic() {
			return "test";
		}

		@Override
		public String getTitle() {
			return "test message title";
		}

		@Override
		public String getMessage() {
			return "test message text";
		}

		@Override
		public String sendToAndroid() {
			return null;
		}

		@Override
		public IdCodeName getData() {
			return new IdCodeName(1, "100", "100");
		}

	}

	private String getAccessToken(GoogleCredential googleCredential) throws IOException {
		googleCredential.refreshToken();
		return googleCredential.getAccessToken();
	}

	/**
	 * Create HttpURLConnection that can be used for both retrieving and publishing.
	 *
	 * @return Base HttpURLConnection.
	 * @throws IOException
	 */
	private HttpURLConnection getConnection(EnvType env) throws IOException {
		GoogleCredential googleCredential = getGoogleCredential(env);
		// [START use_access_token]
		HttpURLConnection httpURLConnection = null;
		if (StringUtils.isNotBlank(googleCredential.getServiceAccountProjectId())) {
			// log.info("Sending FCM message for product Id :" +
			// googleCredential.getServiceAccountProjectId());
			URL url = new URL(
					BASE_URL + "/v1/projects/" + googleCredential.getServiceAccountProjectId() + "/messages:send");
			httpURLConnection = (HttpURLConnection) url.openConnection();
			httpURLConnection.setRequestProperty("Authorization", "Bearer " + getAccessToken(googleCredential));
			httpURLConnection.setRequestProperty("Content-Type", "application/json; UTF-8");
		}
		return httpURLConnection;
	}

	private GoogleCredential getGoogleCredential(EnvType type) throws IOException {
		if (!credentialMap.containsKey(type)) {
			addGoogleCredentialToMap(type);
		}
		return credentialMap.get(type);
	}

	private void addGoogleCredentialToMap(EnvType type) throws IOException {
		// System.out.println(GOOGLE_SERVICE_ACCOUNT);
		// InputStream inputStream =
		// FirebaseNotificationServiceImpl.class.getClassLoader().getResourceAsStream(keyFile);
		String keyFile = "firebase-" + type.name().toLowerCase() + ".json";
		log.info("keyFile: " + keyFile);
		InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(keyFile);
		GoogleCredential googleCredential = GoogleCredential.fromStream(inputStream)
				.createScoped(Arrays.asList(SCOPES));
		credentialMap.put(type, googleCredential);
	}

	/**
	 * inputStreamToString
	 * 
	 * @param inputStream
	 * @return
	 * @throws IOException
	 */
	private String inputStreamToString(InputStream inputStream) throws IOException {
		StringBuilder stringBuilder = new StringBuilder();
		Scanner scanner = new Scanner(inputStream);
		while (scanner.hasNext()) {
			stringBuilder.append(scanner.nextLine());
		}
		scanner.close();
		return stringBuilder.toString();
	}

	/**
	 * Send Notification
	 * 
	 * @param notificationData
	 * @return
	 */
	public boolean sendNotification(EnvType env, FirebaseNotification notificationData) {
		return addToQueue(notificationData, env);
	}

	private boolean addToQueue(FirebaseNotification notificationData, EnvType env) {
		boolean flag = false;
		try {
			notificationQueue.add(new Pair<>(env, notificationData));
			flag = true;
		} catch (Exception e) {
			log.error(":::: Error while adding notification data to FCM queue ::::", e);
		}
		return flag;
	}

	private void sendNotificationFromQueue(EnvType env, FirebaseNotification notificationData) {
		Map<String, String> map = mapper.convertValue(notificationData.getData(), new TypeReference<>() {});
		Integer unitId = Integer.parseInt(map.get("unitId"));
		Integer orderId = Integer.parseInt(map.get("orderId"));
		if (fireStoreService.shouldRouteThroughFireStore(unitId)) {
			if (fireStoreService.sendOrderNotificationThroughFireStore(unitId, orderId)) {
				log.info("Notification Sent through Fire store for Order Id : {}", unitId);
			}
		}
		HttpURLConnection connection = null;
		try {

			JsonObject jMessage = new JsonObject();
			jMessage.addProperty("topic", notificationData.getTopic());
			jMessage.add("notification", getMessageNotification(notificationData));
			jMessage.add("data", getMessageData(notificationData.getData()));
			if (Objects.nonNull(notificationData.sendToAndroid())) {
				jMessage.add("android", getSendToAndroid(notificationData.sendToAndroid()));
			}
			JsonObject jFcm = new JsonObject();
			jFcm.add("message", jMessage);
			log.info("fcmMessage to be sent is : {}" , jFcm.toString());
			connection = getConnection(env);
			if (connection == null) {
				log.info("Failed to send notification connected did not establish");
			}
			connection.setDoOutput(true);
			DataOutputStream outputStream = new DataOutputStream(connection.getOutputStream());
			outputStream.writeBytes(jFcm.toString());
			outputStream.flush();
			outputStream.close();

			int responseCode = connection.getResponseCode();
			if (responseCode == 200) {
				String response = inputStreamToString(connection.getInputStream());
				 log.info("Message sent to Firebase for delivery, response: {} for : {}" ,response, jFcm.toString());
			} else {
				String response = inputStreamToString(connection.getErrorStream());
				log.info("Unable to send message to Firebase: {} for : {}" ,response, jFcm.toString());
			}

		} catch (Exception e) {
			log.error("Exception occurred while sending notification", e);
		} finally {
			if (Objects.nonNull(connection)) {
				connection.disconnect();
			}
		}
	}
	private JsonElement getSendToAndroid(String sendToAndroid) {
		JsonObject response = new JsonObject();
		response.addProperty("priority", "high");
		return response;
	}


	private JsonObject getMessageNotification(FirebaseNotification notificationData) {
		JsonObject response = new JsonObject();
		if (StringUtils.isNotBlank(notificationData.getTitle())) {
			response.addProperty("title", notificationData.getTitle());
		}
		if (StringUtils.isNotBlank(notificationData.getMessage())) {
			response.addProperty("body", notificationData.getMessage());
		}
		return response;
	}

	private JsonObject getMessageData(Object notificationData) {
		if (notificationData != null) {
			JsonObject response = new JsonObject();
			Gson gson = new Gson();
			response.addProperty("payload", gson.toJson(notificationData));
			return response;
		}
		return null;
	}

	public static void main(String[] args) {
		TestNotification notification = new TestNotification();
		FirebaseNotificationServiceImpl impl = new FirebaseNotificationServiceImpl();
		try {
			impl.sendNotification(EnvType.DEV, notification);
		} catch (Exception e) {
			log.error("Error while sending push Notification", e);
		}
	}
}
