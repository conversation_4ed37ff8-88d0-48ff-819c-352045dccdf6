package com.stpl.tech.kettle.service.impl;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.data.kettle.RulesEventData;
import com.stpl.tech.kettle.repository.kettle.RulesEventDataDao;
import com.stpl.tech.kettle.service.RulesEventService;

@Service
public class RulesEventServiceImpl implements RulesEventService {

	@Autowired
	private RulesEventDataDao eventDataDao;

	@Override
	public void attach(int optionResultEventId, int orderId) {
		Optional<RulesEventData> data = eventDataDao.findById(optionResultEventId);
		if (data.isPresent()) {
			RulesEventData resultData = data.get();
			resultData.setOrderId(orderId);
			eventDataDao.save(resultData);
		}
	}
}
