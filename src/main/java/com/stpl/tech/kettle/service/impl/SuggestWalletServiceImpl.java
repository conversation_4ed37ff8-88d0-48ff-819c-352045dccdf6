package com.stpl.tech.kettle.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.cache.UnitDroolVersionMappingCache;
import com.stpl.tech.kettle.core.config.DroolsConfig;
import com.stpl.tech.kettle.domain.model.DenominationOfferPercentage;
import com.stpl.tech.kettle.domain.kettle.DenominationPercentageDroolData;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import com.stpl.tech.kettle.service.DroolService;
import com.stpl.tech.kettle.service.SuggestWalletService;
import com.stpl.tech.util.DroolFileType;
import lombok.extern.slf4j.Slf4j;
import org.kie.api.runtime.KieSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.domain.model.DenominationValueData;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;



@Service
@Slf4j
public class SuggestWalletServiceImpl implements SuggestWalletService {

    @Autowired
    private DroolsConfig droolsConfig;

    @Autowired
    private DroolService droolService;

    @Autowired
    private UnitDroolVersionMappingCache unitDroolVersionMappingCache;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public DenominationValueData getAllActiveDenominationValues(Integer unitId){
        Map<Integer, DenominationOfferPercentage> activeDenominationMap =null;
        try {
            DenominationPercentageDroolData defaultData = DenominationPercentageDroolData.builder().isDefault("Y").build();
            Map<String, DroolVersionDomain> unitDroolVersionMapping =  unitDroolVersionMappingCache.getUnitDroolVersionMapping(unitId);
            Stopwatch watch = Stopwatch.createUnstarted();
            watch.start();
            String denominationSheetVersion = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.DENOMINATION_DECISION.name()) ?
                    unitDroolVersionMapping.get(DroolFileType.DENOMINATION_DECISION.name()).getVersion() : null;
            if(!droolService.isDroolContainerInitializeForDenominationPercentage(denominationSheetVersion)){
                droolService.initailizeDroolContainer(DroolFileType.DENOMINATION_DECISION.getFilename(), denominationSheetVersion);
            }
            KieSession kieSession = droolsConfig.getKieContainerObjectForDenominationPercentage(denominationSheetVersion).newKieSession();
            kieSession.insert(defaultData);
            kieSession.fireAllRules();
            log.info("Getting Default denomination list from droolsConfig: {}ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
            List<String> denominationList = Arrays.asList(defaultData.getAvailableDenomination().split(", "));
            Integer baseValue = defaultData.getBaseValue();
            Integer stepperValue = defaultData.getStepperValue();
            activeDenominationMap =new HashMap<>();
            watch.start();
            for(String denomination: denominationList){
                DenominationPercentageDroolData deno = DenominationPercentageDroolData.builder().isDefault("N").denominationValue(Integer.valueOf(denomination)).build();
                kieSession.insert(deno);
                kieSession.fireAllRules();
                DenominationOfferPercentage valueOffer = DenominationOfferPercentage.builder().denomination(Integer.valueOf(denomination)).extraValue(deno.getExtraValue()).build();
                activeDenominationMap.put(Integer.valueOf(denomination),valueOffer);
            }
            kieSession.destroy();
            log.info("Getting Denomination Offer value from droolsConfig: {}ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
            return DenominationValueData.builder().denominationOfferMap(activeDenominationMap).activeDenomination(denominationList).stepperValue(stepperValue).baseValue(baseValue).build();
        }catch (Exception e){
            log.error("Error Getting Denomination Offer value from droolsConfig",e);
        }
        return null;
    }
}