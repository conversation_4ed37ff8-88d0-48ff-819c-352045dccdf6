package com.stpl.tech.kettle.service;

import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDomain;
import com.stpl.tech.kettle.domain.model.TableOrderItemStatusRequest;
import com.stpl.tech.kettle.domain.model.TableResponse;
import com.stpl.tech.kettle.domain.model.TableViewOrder;
import com.stpl.tech.kettle.domain.model.UnitOrderRequest;
import com.stpl.tech.kettle.domain.model.UnitTableMapping;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.exceptions.TemplateRenderingException;

import java.util.List;

public interface TableOrderManagementService {
	void addOrderTableMapping(OrderDetail orderDetail, Integer tableRequestId, Customer customer, Boolean toBeRedeemed) throws DataUpdationException;
	public List<UnitTableMapping> getTablesForUnit(Integer unitId);
    public UnitTableMapping reserveTableForUnit(Integer unitId, Integer tableNumber , Integer noOfPax) throws DataNotFoundException;

	public boolean closeTableForUnit(Integer tableRequestId);
	public UnitTableMapping getTableSummary(Integer tableRequestId);
	public UnitTableMapping changeTable(int tableRequestId, int tableNumber) throws DataUpdationException;
	public TableResponse generateSettlementReceipt(int tableRequestId) throws TemplateRenderingException;

	public List<Integer> getTableOrders(int tableRequestId);

	public TableViewOrder consolidateOrders(Integer tableRequestId);

	public UnitTableMapping updatePaxForTableOrder(Integer tableRequestId , Integer noOfPax) throws DataUpdationException;

	public void updateTableSettlementOrder(int tableRequestId,Integer settledOrderId);

	public boolean isTableAlreadySettled(int tableRequestId);

	public Boolean saveTableOrderItemStatuses(TableOrderItemStatusRequest tableOrderItemStatusRequest);

	public void initiateOrderItemStatus(OrderDetail orderDetail, Integer tableRequestId) throws DataUpdationException;

	public boolean processItemOnHold(List<Integer> orderItemIds);
	public boolean setServiceChargeApplicable(Integer tableRequestId ,String flag);
	public boolean ValidateServiceCharge(Order order,String serviceChargeApplied);
	public void updateTableDataForBillPrint(Order order, String serviceChargeApplied, String isGeneratedOrderId);
	public boolean validateBillPrint(OrderDomain order, String serviceChargeApplied);
	public boolean validateOrderItems(Order order);
	public List<Order> getCafeOrdersForUnit(UnitOrderRequest request);

	public boolean isDreamFolksVoucherCodeUsed(String voucherCode);

}
