package com.stpl.tech.kettle.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.CashData;
import com.stpl.tech.kettle.data.kettle.CashLogData;
import com.stpl.tech.kettle.data.kettle.CashPacketData;
import com.stpl.tech.kettle.data.kettle.CashPacketLogData;
import com.stpl.tech.kettle.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.domain.model.CashMetadataType;
import com.stpl.tech.kettle.domain.model.CashMetadataWrapper;
import com.stpl.tech.kettle.domain.model.CashPacketEventStatus;
import com.stpl.tech.kettle.domain.model.CashTransactionCategory;
import com.stpl.tech.kettle.domain.model.CashTransactionCode;
import com.stpl.tech.kettle.domain.model.CashTransactionMetadata;
import com.stpl.tech.kettle.domain.model.TransactionType;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.repository.kettle.CashDataDao;
import com.stpl.tech.kettle.repository.kettle.CashLogDataDao;
import com.stpl.tech.kettle.repository.kettle.CashPacketDataDao;
import com.stpl.tech.kettle.repository.kettle.CashPacketLogDataDao;
import com.stpl.tech.kettle.repository.kettle.CustomerDao;
import com.stpl.tech.kettle.service.CashPacketService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class CashPacketServiceImpl implements CashPacketService {

	@Autowired
	private CustomerDao customerDao;

	@Autowired
	private CashPacketDataDao packetDataDao;

	@Autowired
	private CashDataDao cashDataDao;

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private CashPacketLogDataDao packetLogDataDao;
	
	@Autowired
	private CashLogDataDao cashLogDataDao;

	@Override
	public void activateReferralCashPacket(Integer referentId, Integer referrerId, Integer orderId) {
		CashPacketData cashPacket = packetDataDao.getInitiatedCashPacket(referentId, referrerId,
				CashPacketEventStatus.INITIATED.toString());
		if (cashPacket != null) {
			// only activation time is set here.
			Date activationDate = AppUtils.isDev(props.getEnvironmentType())
					? AppUtils.getDate(AppUtils.getCurrentTimestamp())
					: AppUtils.getNextDate(AppUtils.getBusinessDate(AppUtils.getCurrentTimestamp()));
			cashPacket.setActivationTime(activationDate);
			cashPacket.setEventStatus(CashPacketEventStatus.READY_FOR_ACTIVATION.name());
			packetDataDao.save(cashPacket);
			Optional<CustomerInfo> data = customerDao.findById(referentId);
			if (data.isPresent()) {
				CustomerInfo c = data.get();
				c.setIsReferrerAwarded(AppConstants.YES);
				try {
					if (!AppUtils.isBlank(c.getRefCode())) {
						sendReferralMessage(c, "CHAAYOS_POS", "SMS");
					}
				} catch (Exception e) {
					log.info("Unable to send Referral SMS", e);
				}

				customerDao.save(c);
			}

		}
	}

	private void sendReferralMessage(CustomerInfo c, String campaign, String source)
			throws IOException, URISyntaxException {
		// TODO
	}

	private void addCashLog(CashData cash, CashMetadataWrapper wrapper) {
		CashLogData log = new CashLogData();
		log.setCashDataId(cash.getCashDataId());
		log.setOrderId(wrapper.getOrderId());
		log.setTransactionAmount(wrapper.getTransactionAmount());
		log.setTransactiontime(wrapper.getTransactionTime());
		log.setTransactionCode(wrapper.getMetadata().getCashTransactionCode().name());
		log.setTransactionCodeType(wrapper.getMetadata().getCashTransactionCategory().name());
		log.setTransactionType(wrapper.getMetadata().getTransactionType().name());
		cashLogDataDao.save(log);
		wrapper.setCashLogDataId(log.getCashLogDataId());
	}

	@Override
	public void redeemCash(Integer customerId, BigDecimal cashRedeemed, Integer orderId) throws DataUpdationException {

		CashMetadataWrapper wrapper = new CashMetadataWrapper();
		CashTransactionMetadata metadata = new CashTransactionMetadata();
		metadata.setCashMetadataType(CashMetadataType.REDEMPTION);
		metadata.setCashTransactionCategory(CashTransactionCategory.REDEMPTION);
		metadata.setCashTransactionCode(CashTransactionCode.ORDER_DISCOUNT);
		metadata.setTransactionType(TransactionType.DEBIT);
		wrapper.setMetadata(metadata);
		wrapper.setTransactionAmount(cashRedeemed);
		wrapper.setOrderId(orderId);
		wrapper.setCustomerId(customerId);
		wrapper.setTransactionTime(AppUtils.getCurrentTimestamp());

		CashData cash = cashDataDao.findByCustomerId(customerId);

		if (BigDecimal.ZERO.equals(cashRedeemed) || cash.getCurrentAmount().compareTo(cashRedeemed) < 0) {
			throw new DataUpdationException(String.format("Insufficent Cash for Customer %S, Required Amount %S",
					cash.getCurrentAmount(), cashRedeemed));
		}

		cash.setCurrentAmount(AppUtils.subtract(cash.getCurrentAmount(), cashRedeemed));
		cash.setRedeemedAmount(AppUtils.add(cash.getRedeemedAmount(), cashRedeemed));
		cash.setLastUpdateTime(wrapper.getTransactionTime());
		addCashLog(cash, wrapper);

		List<CashPacketData> packets = packetDataDao.getCashPackets(cash.getCashDataId(),
				CashPacketEventStatus.ACTIVE.toString());
		for (CashPacketData packet : packets) {
			if (BigDecimal.ZERO.compareTo(cashRedeemed) >= 0) {
				// cashRedeemed is ZERO hence exit loop
				break;
			}
			BigDecimal valueRedeemed = null;
			if (cashRedeemed.compareTo(packet.getCurrentAmount()) <= 0) {
				// cash redeemed is less then current amount
				// sequence of these lines is important
				packet.setCurrentAmount(AppUtils.subtract(packet.getCurrentAmount(), cashRedeemed));
				packet.setRedeemedAmount(AppUtils.add(packet.getRedeemedAmount(), cashRedeemed));
				valueRedeemed = cashRedeemed.add(BigDecimal.ZERO);
				cashRedeemed = BigDecimal.ZERO;
			} else {
				// cash redeemed is more then current amount
				// sequence of these lines is important
				valueRedeemed = packet.getCurrentAmount().add(BigDecimal.ZERO);
				cashRedeemed = AppUtils.subtract(cashRedeemed, packet.getCurrentAmount());
				packet.setRedeemedAmount(AppUtils.add(packet.getRedeemedAmount(), packet.getCurrentAmount()));
				packet.setCurrentAmount(BigDecimal.ZERO);

			}
			packet.setLastUpdateTime(wrapper.getTransactionTime());
			packetDataDao.save(packet);
			addPacketLog(false, packet, valueRedeemed, wrapper);
		}
	}

	private void addPacketLog(boolean update, CashPacketData packet, BigDecimal packetTransactionValue,
			CashMetadataWrapper wrapper) {
		if (update && CashTransactionCode.CASHBACK.name().equals(packet.getTransactionCode())) {
			CashPacketLogData log = packetLogDataDao.findByCashPacketIdAndTransactionCode(packet.getCashPacketId(),
					CashTransactionCode.CASHBACK.name());
			if (log != null) {
				log.setCashLogDataId(wrapper.getCashLogDataId());
				return;
			}
		}

		CashPacketLogData log = new CashPacketLogData();
		log.setCashPacketId(packet.getCashPacketId());
		log.setTransactionAmount(packetTransactionValue);
		log.setTransactionCode(wrapper.getMetadata().getCashTransactionCode().name());
		log.setTransactionCodeType(wrapper.getMetadata().getCashTransactionCategory().name());
		log.setTransactionReason("");
		log.setTransactionType(wrapper.getMetadata().getTransactionType().name());
		log.setTransactionTime(wrapper.getTransactionTime());
		log.setOrderId(wrapper.getOrderId());
		log.setCashLogDataId(wrapper.getCashLogDataId());
		packetLogDataDao.save(log);

	}

}
