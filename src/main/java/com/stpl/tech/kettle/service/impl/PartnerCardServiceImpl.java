package com.stpl.tech.kettle.service.impl;
import com.stpl.tech.kettle.cache.PartnerCache;
import com.stpl.tech.kettle.data.kettle.ExternalPartnerCardDetail;
import com.stpl.tech.kettle.repository.kettle.ExternalPartnerCardDao;
import com.stpl.tech.kettle.repository.master.CouponDetailDataDao;
import com.stpl.tech.kettle.service.PartnerCardService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.master.data.model.ExternalPartnerDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public abstract class  PartnerCardServiceImpl implements PartnerCardService{

    @Autowired
    ExternalPartnerCardDao partnerCardDao;

    @Autowired
    CouponDetailDataDao couponDetailDataDao;

    @Autowired
    PartnerCache partnerCache;


    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public String getUniquePartnerBillNumber() {
        String billNo = AppUtils.generateRandomOrderId(14);
        List<ExternalPartnerCardDetail> cardDetails = partnerCardDao.findByExternalOrderId(billNo);
        if (cardDetails.size() > 0) {
            getUniquePartnerBillNumber();
        }
        return billNo;
    }

    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ExternalPartnerCardDetail> getPartnerCardDetail(String cardNumber, String partnerCode, String status){
        if(status!=null){
            return partnerCardDao.findByCardNumberAndPartnerCodeAndRequestStatus(cardNumber,partnerCode,status);
        }else {
            return partnerCardDao.findByCardNumberAndPartnerCode(cardNumber, partnerCode);
        }
    }


    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public ExternalPartnerDetail getExternalPartner(String partnerCode) {
        return partnerCache.getExternalPartnerMap().get(partnerCode);
    }
}
