package com.stpl.tech.kettle.service.impl;

import com.stpl.tech.kettle.domain.model.OrderMetricDomain;
import com.stpl.tech.kettle.repository.master.ApplicationVersionDetailDataDao;
import com.stpl.tech.kettle.service.OrderService;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    private ApplicationVersionDetailDataDao versionDetailDataDao;

    @Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String,String> setVersionDetails(OrderMetricDomain orderMetricDomain) {
        Map<String,String> value = new HashMap<>();
        if (Objects.isNull(orderMetricDomain.getPosVersion())) {
            String posVersion = versionDetailDataDao.findApplicationVersionByUnitIdAndApplicationNameAndVersionStatusAndTerminal(orderMetricDomain.getOrderMetricData().getUnitId(), AppConstants.POS,
                    AppConstants.ACTIVE, orderMetricDomain.getOrderMetricData().getTerminalId());
            if (!StringUtils.isEmpty(posVersion)) {
                value.put(AppConstants.POS,posVersion);
            }
        }
        else {
            value.put(AppConstants.POS,orderMetricDomain.getPosVersion());
        }
        if (Objects.isNull(orderMetricDomain.getCafeAppVersion())) {
            String cafeAppVersion = versionDetailDataDao.findApplicationVersionByUnitIdAndApplicationNameAndVersionStatusAndTerminal(orderMetricDomain.getOrderMetricData().getUnitId(), AppConstants.CAFE_APP,
                    AppConstants.ACTIVE, orderMetricDomain.getOrderMetricData().getTerminalId());
            if (!StringUtils.isEmpty(cafeAppVersion)) {
                value.put(AppConstants.CAFE_APP,cafeAppVersion);
            }
        }
        else {
            value.put(AppConstants.CAFE_APP,orderMetricDomain.getCafeAppVersion());
        }
        return value;
    }
}
