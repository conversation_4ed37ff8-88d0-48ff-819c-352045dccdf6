package com.stpl.tech.kettle.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.data.kettle.LoyaltyScore;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.CustomerOffer;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.SavedChaiOrderedDomain;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferLastRedemptionView;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

public interface CustomerService {
	CustomerInfo getCustomerInfo(Integer customerId);

	CustomerInfo getCustomerInfo(String contactNumber);

	List<CustomerOffer> getOfferDetail(int customerId, String offerCode);

	Integer checkCouponUsage(int customerId, String code);

	boolean getCustomerOrders(int customerId);

	BigDecimal getAvailableCash(Integer customerId);

	Customer getCustomer(String code, String contactNumber);

	Customer getCustomer(int customerId);

	LoyaltyScore getLoyaltyScore(Integer customerId);

	OfferLastRedemptionView getOrderDetailViaOffer(Integer customerId, String couponCode, Date businessDate,
			Integer dailyFreqCount);

	boolean getValidOfferFlag(OfferOrder offerOrder, CouponDetail couponDetail);

	boolean saveCustomerInfoBrandWise(int customerId, int brandId, int orderId, Date billingServerTime,boolean isSpecialOrder);

	void addOfferDetail(int customerId, String offerCode, int orderId);

	boolean updateCustomerEmail(Customer customer, String flag);

	CustomerDineInView getCustomerDineInView(int customerId, Integer brandId, List<Integer> excludeOrderIds);

	CustomerEmailData getCustomerEmailData(int customerId, Integer brandId);

	boolean updateCustomerName(Customer customer);

	public Integer getLastOrderedSavedChai(Integer customerId);

	public SavedChaiOrderedDomain getSavedChaiOrderDetail(Integer customerId);

}
