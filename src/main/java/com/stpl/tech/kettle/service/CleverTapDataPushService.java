package com.stpl.tech.kettle.service;

import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.data.kettle.CleverTapProfilePushTrack;
import com.stpl.tech.kettle.data.kettle.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.kettle.EventPushTrack;
import com.stpl.tech.kettle.domain.model.CleverTapPushResponse;
import com.stpl.tech.kettle.domain.model.EventUploadRequest;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.ProfileUploadRequest;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;

public interface CleverTapDataPushService {

	CleverTapPushResponse pushUsersToCleverTap(List<Integer> customerIds, String updateType, Boolean... lead)
			throws DataNotFoundException;

	CleverTapPushResponse uploadEvent(List<Integer> orderList, String updateType,
			Map<Integer, OrderNotification> orderNotificationMap);

	CleverTapPushResponse pushUserToCleverTap(Integer customerId, Boolean... lead) throws DataNotFoundException;

	void persistProfileTrack(CleverTapProfilePushTrack entity);

	void persistEventTrack(EventPushTrack entity);

	void persistProfileTrack(List<CleverTapProfilePushTrack> profilePushTracks);

	void persistEventTracks(List<EventPushTrack> eventPushTrack);

	CleverTapPushResponse uploadProfileAttributes(Integer customerId, long epochSeconds, String updateType,
			Object data);

	CleverTapPushResponse publishCustomEvent(Integer customerId, String evtName, long epochSeconds, String updateType,
			Object data);

	public CleverTapPushResponse uploadEventForPartner(Map<Integer,Integer> orderList,String evtName,
													   String updateType,Map<Integer, OrderNotification> orderNotificationMap);

	List<Integer> getCustomerIdsBatch(int batchSize, Integer lastCustomerId);

	ProfileUploadRequest convert(Integer customerId, long epochSeconds, Object data);

	EventUploadRequest convertEventRequest(Integer customerId, long epochSeconds, String evtName, Object data);

	CleverTapPushResponse publishToClevertapQueue(Object payload, boolean event, Integer customerId, String updateType,
			String evtName);
	
	void pushOfferDataToClevertap(NextOffer offer, CustomerCampaignOfferDetail postOrderOfferCreationSuccess);

	public List<ProfileUploadRequest> getUserProfileForCleverTap(List<Integer> customerIds, String updateType)
			throws DataNotFoundException;

	public List<EventUploadRequest> getLeadEventRequestForCleverTap(List<Integer> customerIds, String updateType, Boolean... lead)
			throws DataNotFoundException;
	public List<EventUploadRequest> getEventDataList(List<Integer> orderList, String updateType,
													 Map<Integer, OrderNotification> orderNotificationMap);
	public CleverTapPushResponse publishToCleverTapQueueNew(Object payload,Integer id,String updateType);


}
