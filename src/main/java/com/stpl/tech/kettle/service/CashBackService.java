package com.stpl.tech.kettle.service;

import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.exceptions.DataUpdationException;

import java.math.BigDecimal;
import java.util.Date;

public interface CashBackService {

	void checkCashBack(Order order);

	public void awardCashBackOffer(Order order);

	void processCashRedemption(Order order, OrderDetail orderDetail, StringBuilder sb) throws DataUpdationException;

	boolean allotCashBack(BigDecimal amount, Integer customerId, int orderId, Date creationDate, Date expirationDate, int lagDays);
}
