package com.stpl.tech.kettle.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.stpl.tech.kettle.data.kettle.LoyaltyScore;
import com.stpl.tech.kettle.domain.model.CleverTapRequest;
import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.kettle.service.LoyaltyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;
import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.converter.CleverTapConverter;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.CleverTapProfilePushTrack;
import com.stpl.tech.kettle.data.kettle.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.data.kettle.EventPushTrack;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.data.kettle.OrderItem;
import com.stpl.tech.kettle.domain.model.CleverTapPushResponse;
import com.stpl.tech.kettle.domain.model.ClevertapChargedEventData;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerProfileCleverTap;
import com.stpl.tech.kettle.domain.model.EventUploadRequest;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.ProfileUploadRequest;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.publisher.CleverTapEventPublisher;
import com.stpl.tech.kettle.repository.kettle.CleverTapProfilePushTrackDao;
import com.stpl.tech.kettle.repository.kettle.CustomerDao;
import com.stpl.tech.kettle.repository.kettle.EventPushTrackDao;
import com.stpl.tech.kettle.repository.kettle.OrderDetailDao;
import com.stpl.tech.kettle.service.CleverTapDataPushService;
import com.stpl.tech.kettle.service.CustomerService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.Constants.CleverTapConstants;
import com.stpl.tech.kettle.util.Constants.CleverTapEvents;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class CleverTapDataPushServiceImpl implements CleverTapDataPushService {

	@Autowired
	private CustomerService customerService;

	@Autowired
	private OrderDetailDao orderDetailDao;

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private CleverTapProfilePushTrackDao profilePushTrackDao;

	@Autowired
	private EventPushTrackDao eventPushTrackDao;

	@Autowired
	private CleverTapConverter cleverTapConverter;

	@Autowired
	private CustomerDao customerDao;

	@Autowired
	private CleverTapEventPublisher cleverTapEventPublisher;
	
	@Autowired
	private ProductCache productCache;

	@Autowired
	private LoyaltyService loyaltyService;

	@Autowired
	private EnvironmentProperties env;

	private static Gson gson = new Gson();

	@Override
	public CleverTapPushResponse pushUsersToCleverTap(List<Integer> customerIds, String updateType, Boolean... lead)
			throws DataNotFoundException {
		List<ProfileUploadRequest> uploadRequests = new ArrayList<>();
		List<EventUploadRequest> eventRequest = new ArrayList<>();
		CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
		List<CleverTapProfilePushTrack> customers = new ArrayList<>();
		if (CollectionUtils.isEmpty(customerIds)) {
			return cleverTapPushResponse;
		}
		for (Integer id : customerIds) {
			Customer customer = customerService.getCustomer(id);
			LoyaltyScore loyaltyScore = loyaltyService.getScore(id);
			log.info("converting data for user {} and customer id {}", customer.getContactNumber(), customer.getId());
			CustomerProfileCleverTap profileData = cleverTapConverter.convert(customer);
			if (Objects.nonNull(loyaltyScore)) {
				profileData.setSignupOfferStatus(loyaltyScore.getSignupOfferStatus());
				if ( !AppUtils.getStatus(loyaltyScore.getAvailedSignupOffer()) && SignupOfferStatus.AVAILABLE.name().equalsIgnoreCase(loyaltyScore.getSignupOfferStatus()) && Objects.isNull(loyaltyScore.getSignupOfferExpiryTime())){
					profileData.setSignupOfferExpiryDate(AppUtils.getClevertapFormattedDate(AppUtils.getDateAfterDays(AppUtils.getCurrentDate() ,env.getExpireSignupOfferDays())));
				}
			}
			uploadRequests.add(cleverTapConverter.convert(id, customer.getAddTime().toInstant().getEpochSecond(),
					profileData));
			if (Objects.nonNull(lead) && lead.length > 0 && Objects.nonNull(lead[0]) && lead[0]) {
				EventUploadRequest req = new EventUploadRequest();
				req.setTs(customer.getAddTime().toInstant().getEpochSecond());
				req.setEvtName(CleverTapEvents.LEAD_GENERATED);
				req.setType(CleverTapConstants.EVENT);
				req.setIdentity(id);
				Map<String, Object> data = new HashMap<>();
				data.put("Name", customer.getFirstName());
				data.put("CustomerId", id);
				data.put("AcquisitionSource", customer.getAcquisitionSource());
				req.setEvtData(data);
				eventRequest.add(req);
			}
		}
		if (!CollectionUtils.isEmpty(uploadRequests)) {
			cleverTapPushResponse = publishToClevertapQueue(uploadRequests, false, uploadRequests.get(0).getIdentity(),
					updateType, null);
		}
		if (!CollectionUtils.isEmpty(eventRequest)) {
			publishToClevertapQueue(eventRequest, true, eventRequest.get(0).getIdentity(), updateType,
					CleverTapEvents.LEAD_GENERATED);
		}
		return cleverTapPushResponse;
	}
	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public CleverTapPushResponse uploadEvent(List<Integer> orderList, String updateType,
			Map<Integer, OrderNotification> orderNotificationMap) {
		List<EventUploadRequest> requestList = new ArrayList<>();
		boolean loyaltyWallet = false;
		for (Integer orderId : orderList) {
			Optional<OrderDetail> data = orderDetailDao.findById(orderId);
			if (data.isPresent()) {
				OrderDetail order = data.get();
				String evtName = getOrderType(order);
				ClevertapChargedEventData evt = new ClevertapChargedEventData();
				Optional<CustomerInfo> customerData = customerDao.findById(order.getCustomerId());
				OrderNotification notificationData = null;
				if (Objects.nonNull(orderNotificationMap) && !orderNotificationMap.isEmpty()) {
					if (orderNotificationMap.containsKey(orderId) && Objects.nonNull(orderNotificationMap.get(orderId))
							&& customerData.isPresent()) {
						notificationData = orderNotificationMap.get(orderId);
					}
				}
				if (customerData.isPresent()) {
					try {
						if (evtName.equals(CleverTapEvents.CHARGED)) {
							evt = cleverTapConverter.convertChargedEventOrderData(order, customerData.get(),
									orderNotificationMap.get(orderId));
						} else if (evtName.equalsIgnoreCase(CleverTapEvents.SUBSCRIPTION_PURCHASED_EVENT)) {
							evt = cleverTapConverter.convertSubscriptionEventOrderData(order, customerData.get(),
									orderNotificationMap.get(orderId));
						} else if (evtName.equalsIgnoreCase(CleverTapEvents.WALLET_PURCHASED_EVENT)) {
							evt = cleverTapConverter.convertWalletEventOrderData(order, customerData.get(),
									orderNotificationMap.get(orderId));
						}
						EventUploadRequest request = cleverTapConverter.convert(order, evtName, evt);
						requestList.add(request);
						if(!StringUtils.isEmpty(properties.getLoyaltWalletOfferCode()) &&
							properties.getLoyaltWalletOfferCode().equals(order.getDiscountReason())){
							loyaltyWallet = true;
						}
					} catch (Exception e) {
						log.info("Exception occured while converting clevertap event request ", e);
					}
				} else {
					log.info("customer not exist for customerId: {} tagged with order", order.getCustomerId());
				}
			}
		}
		if (!CollectionUtils.isEmpty(requestList)) {
			if(loyaltyWallet && requestList.size()==1){
				return publishToClevertapQueue(requestList, true, orderList.get(0), updateType, CleverTapEvents.LOYALTY_WALLET_TRADE);
			}
			if(!CollectionUtils.isEmpty(orderList) && orderList.size()==2){
				return publishToClevertapQueue(requestList, true, orderList.get(1), updateType, CleverTapEvents.CHARGED);
			}
			return publishToClevertapQueue(requestList, true, orderList.get(0), updateType,CleverTapEvents.CHARGED);
		}
		log.info("Clevertap request list empty for {}", orderList);
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public CleverTapPushResponse pushUserToCleverTap(Integer customerId, Boolean... lead) throws DataNotFoundException {
		return pushUsersToCleverTap(Arrays.asList(customerId), CleverTapConstants.REGULAR, lead);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void persistProfileTrack(CleverTapProfilePushTrack profilePushTrack) {
		try {
			log.info("trying to save clevertap users");
			profilePushTrackDao.save(profilePushTrack);
		} catch (Exception e) {
			log.error("Error while saving data for clevertap profile push {}", e.getMessage());
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void persistEventTrack(EventPushTrack eventPushTrack) {
		try {
			log.info("trying to save clevertap events");
			eventPushTrackDao.save(eventPushTrack);
		} catch (Exception e) {
			log.error("Error while saving data for clevertap events push {}", e.getMessage());
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void persistProfileTrack(List<CleverTapProfilePushTrack> profilePushTracks) {
		try {
			log.info("trying to save clevertap users");
			profilePushTrackDao.saveAll(profilePushTracks);
		} catch (Exception e) {
			log.error("Error while saving data for clevertap profile push {}", e.getMessage());
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void persistEventTracks(List<EventPushTrack> eventPushTracks) {
		try {
			log.info("trying to save clevertap events");
			eventPushTrackDao.saveAll(eventPushTracks);
		} catch (Exception e) {
			log.error("Error while saving data for clevertap events push {}", e.getMessage());
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public CleverTapPushResponse uploadProfileAttributes(Integer customerId, long epochSeconds, String updateType,
			Object data) {
		ProfileUploadRequest request = convert(customerId, epochSeconds, data);
		return publishToClevertapQueue(request, false, customerId, updateType, null);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public CleverTapPushResponse publishCustomEvent(Integer customerId, String evtName, long epochSeconds,
			String updateType, Object data) {
		EventUploadRequest request = convertEventRequest(customerId, epochSeconds, evtName, data);
		return publishToClevertapQueue(request, true, customerId, updateType, evtName);
	}

	@Override
	public CleverTapPushResponse uploadEventForPartner(Map<Integer, Integer> orderList, String evtName, String updateType, Map<Integer, OrderNotification> orderNotificationMap) {
		CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
		ArrayList<EventUploadRequest> requestList = new ArrayList<>();
		List<EventPushTrack> orders = new ArrayList<>();
		for(Map.Entry<Integer,Integer> entry : orderList.entrySet()){
			Integer orderId = entry.getKey();
			Integer customerId = entry.getValue();
			OrderDetail order = orderDetailDao.findByOrderId(orderId);
			ClevertapChargedEventData evt = new ClevertapChargedEventData();
			CustomerInfo customerInfo = customerDao.findByCustomerId(customerId);
			OrderNotification notificationData = null;
			if (Objects.nonNull(orderNotificationMap) && !orderNotificationMap.isEmpty()) {
				if (orderNotificationMap.containsKey(orderId) && Objects.nonNull(orderNotificationMap.get(orderId)) && Objects.nonNull(customerInfo)) {
					notificationData = orderNotificationMap.get(orderId);
				}
			}
			log.info("Printing notification data :{}", new Gson().toJson(notificationData));
			order.setCustomerId(customerId);
			if (customerInfo != null) {
				if (evtName.equals(CleverTapEvents.CHARGED)) {
					evt = cleverTapConverter.convertChargedEventOrderData(order, customerInfo, orderNotificationMap.get(orderId));
				}
				EventUploadRequest request = cleverTapConverter.convert(order, evtName, evt);
				requestList.add(request);
			}
			else {
				log.info("customer not exist for customerId: {} tagged with order", order.getCustomerId());
			}
		}
		if (!requestList.isEmpty()){
			CleverTapRequest uploadRequest = new CleverTapRequest();
			uploadRequest.setD(requestList);
			try {
				log.info("Uploading {} orders data to clevertap with startId and endId as {},{}", requestList.size(),
						orderList.get(0), orderList.get(orderList.size() - 1));
				//LOG.info("Event json formed {}", new Gson().toJson(requestList));
				cleverTapEventPublisher.publishCleverTapEvent(properties.getEnvironmentType().name(), gson.toJson(uploadRequest), true);
//                cleverTapPushResponse = AbstractRestTemplate.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT, uploadRequest, CleverTapPushResponse.class, env.getCleverTapAccountId(), env.getCleverTapPasscode(), env);
				cleverTapPushResponse.setStatus("PUSHED_TO_QUEUE");
				for (Integer orderId : orderList.keySet()) {
					EventPushTrack eventPushTrack = new EventPushTrack(evtName, orderId, cleverTapPushResponse.getStatus(), updateType, AppConstants.CLEVERTAP);
					eventPushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
					orders.add(eventPushTrack);
				}
			} catch (Exception e) {
				log.error("error while uploading events {}", e.getMessage());
				log.info("Failed IN Sending {} customers data to clevertap with startId and endId as {},{}",
						requestList.size(), orderList.get(0), orderList.get(orderList.size() - 1));
				log.info("######### order events,{}", StringUtils.join(orderList.keySet(), ","));
				log.error("Error in pushing data to clevertap  with startId and endId as {},{}", orderList.get(0),
						orderList.get(orderList.size() - 1), e);
				for (Integer orderId : orderList.keySet()) {
					orders.add(new EventPushTrack(evtName, orderId, AppConstants.ERROR, updateType, AppConstants.CLEVERTAP));
				}
			}
		}
		cleverTapPushResponse.setEvents(orders);
		return cleverTapPushResponse;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public List<Integer> getCustomerIdsBatch(int batchSize, Integer lastCustomerId) {
		return customerDao.getCustomerIdsBatch(batchSize, lastCustomerId, AppConstants.EXCLUDE_CUSTOMER_IDS);
	}

	@Override
	public ProfileUploadRequest convert(Integer customerId, long epochSeconds, Object data) {
		ProfileUploadRequest request = new ProfileUploadRequest();
		request.setIdentity(customerId);
		request.setTs(epochSeconds);
		request.setType(CleverTapConstants.PROFILE);
		request.setProfileData(data);
		return request;

	}

	@Override
	public EventUploadRequest convertEventRequest(Integer customerId, long epochSeconds, String evtName, Object data) {
		EventUploadRequest request = new EventUploadRequest();
		request.setIdentity(customerId);
		request.setEvtName(evtName);
		request.setTs(epochSeconds);
		request.setType(CleverTapConstants.EVENT);
		request.setEvtData(data);
		return request;

	}

	@Override
	public CleverTapPushResponse publishToClevertapQueue(Object payload, boolean event, Integer id, String updateType,
			String evtName) {
		CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
		CleverTapProfilePushTrack pushTrack = null;
		EventPushTrack eventTrack = null;
		if (Objects.nonNull(payload)) {
			Map<String, Object> uploadRequest = new HashMap<>();
			if (payload instanceof List) {
				uploadRequest.put("d", payload);
			} else {
				uploadRequest.put("d", Arrays.asList(payload));
			}
			try {
				// LOG.info("Event json formed {}", new Gson().toJson(requestList));
//				cleverTapPushResponse = AbstractRestTemplate.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT,
//						uploadRequest, CleverTapPushResponse.class, env.getCleverTapAccountId(),
//						env.getCleverTapPasscode(), env);
				if (properties.clevertapLogEnable()) {
					log.info("clevertap data : {}", gson.toJson(uploadRequest));
				}
				cleverTapEventPublisher.publishCleverTapEvent(properties.getEnvironmentType().name(),
						gson.toJson(uploadRequest), true);
				cleverTapPushResponse.setStatus("PUSHED_TO_QUEUE");
				if (event) {
					eventTrack = new EventPushTrack(evtName, id, cleverTapPushResponse.getStatus(), updateType,
							AppConstants.CLEVERTAP);
					eventTrack.setPublishTime(AppUtils.getCurrentTimestamp());
				} else {
					pushTrack = new CleverTapProfilePushTrack(id, cleverTapPushResponse.getStatus(), updateType);
					pushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
				}
			} catch (Exception e) {
				log.error("error while uploading offer attributes  to clevertap for customner {} to clevertap {}", id,
						e.getMessage());
				if (event) {
					eventTrack = new EventPushTrack(evtName, -1, AppConstants.ERROR, CleverTapConstants.EVENT,
							AppConstants.CLEVERTAP);
				} else {
					pushTrack = new CleverTapProfilePushTrack(id, AppConstants.ERROR, updateType);
				}
			}
		}
		if (event) {
			cleverTapPushResponse.setEvents(Arrays.asList(eventTrack));
			persistEventTrack(eventTrack);
		} else {
			cleverTapPushResponse.setProfiles(Arrays.asList(pushTrack));
			persistProfileTrack(Arrays.asList(pushTrack));
		}
		return cleverTapPushResponse;
	}

	@Override
	@Async("taskExecutor")
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void pushOfferDataToClevertap(NextOffer offer, CustomerCampaignOfferDetail postOrderOfferCreationSuccess) {
		long startTime = System.currentTimeMillis();
		uploadProfileAttributes(offer.getCustomerId(), AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(),
				CleverTapConstants.REGULAR,
				cleverTapConverter.convert(offer, CleverTapEvents.NEXT_BEST_OFFER, postOrderOfferCreationSuccess));
		log.info("Sending Offer Data to Clevertap in seprate thread took {} ms",
				System.currentTimeMillis() - startTime);

	}

	private String getOrderType(OrderDetail order) {
		for (OrderItem item : order.getOrderItems()) {
			if (productCache.getSubscriptionProductDetailsById(item.getProductId()).isPresent()) {
				return CleverTapEvents.SUBSCRIPTION_PURCHASED_EVENT;
			} else if (AppUtils.getStatus(order.getGiftCardOrder())) {
				return CleverTapEvents.WALLET_PURCHASED_EVENT;
			}
		}
		return CleverTapEvents.CHARGED;
	}

	@Override
	public List<ProfileUploadRequest> getUserProfileForCleverTap(List<Integer> customerIds, String updateType)
			throws DataNotFoundException{
		List<ProfileUploadRequest> uploadRequests = new ArrayList<>();
		if (CollectionUtils.isEmpty(customerIds)) {
			return uploadRequests;
		}
		for (Integer id : customerIds) {
			Customer customer = customerService.getCustomer(id);
			LoyaltyScore loyaltyScore = loyaltyService.getScore(id);
			log.info("converting data for user {} and customer id {}", customer.getContactNumber(), customer.getId());
			CustomerProfileCleverTap profileData = cleverTapConverter.convert(customer);
			if (Objects.nonNull(loyaltyScore)) {
				profileData.setSignupOfferStatus(loyaltyScore.getSignupOfferStatus());
				if ( !AppUtils.getStatus(loyaltyScore.getAvailedSignupOffer()) && SignupOfferStatus.AVAILABLE.name().equalsIgnoreCase(loyaltyScore.getSignupOfferStatus()) && Objects.isNull(loyaltyScore.getSignupOfferExpiryTime())){
					profileData.setSignupOfferExpiryDate(AppUtils.getClevertapFormattedDate(AppUtils.getDateAfterDays(AppUtils.getCurrentDate() ,env.getExpireSignupOfferDays())));
				}
			}
			uploadRequests.add(cleverTapConverter.convert(id, customer.getAddTime().toInstant().getEpochSecond(),
					profileData));
		}
		return uploadRequests;
	}

	@Override
	public List<EventUploadRequest> getLeadEventRequestForCleverTap(List<Integer> customerIds, String updateType, Boolean... lead)
			throws DataNotFoundException {
		List<EventUploadRequest> eventRequest = new ArrayList<>();
		if (CollectionUtils.isEmpty(customerIds)) {
			return eventRequest;
		}
		for (Integer id : customerIds) {
			Customer customer = customerService.getCustomer(id);
			log.info("converting data for Lead Event for user {} and customer id {}", customer.getContactNumber(), customer.getId());
			if (Objects.nonNull(lead) && lead.length > 0 && Objects.nonNull(lead[0]) && lead[0]) {
				EventUploadRequest req = new EventUploadRequest();
				req.setTs(customer.getAddTime().toInstant().getEpochSecond());
				req.setEvtName(CleverTapEvents.LEAD_GENERATED);
				req.setType(CleverTapConstants.EVENT);
				req.setIdentity(id);
				Map<String, Object> data = new HashMap<>();
				data.put("Name", customer.getFirstName());
				data.put("CustomerId", id);
				data.put("AcquisitionSource", customer.getAcquisitionSource());
				req.setEvtData(data);
				eventRequest.add(req);
			}
		}
		return eventRequest;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public List<EventUploadRequest> getEventDataList(List<Integer> orderList, String updateType,
													 Map<Integer, OrderNotification> orderNotificationMap){
		List<EventUploadRequest> requestList = new ArrayList<>();
		for (Integer orderId : orderList) {
			Optional<OrderDetail> data = orderDetailDao.findById(orderId);
			if (data.isPresent()) {
				OrderDetail order = data.get();
				String evtName = getOrderType(order);
				ClevertapChargedEventData evt = new ClevertapChargedEventData();
				Optional<CustomerInfo> customerData = customerDao.findById(order.getCustomerId());
				OrderNotification notificationData = null;
				if (Objects.nonNull(orderNotificationMap) && !orderNotificationMap.isEmpty()) {
					if (orderNotificationMap.containsKey(orderId) && Objects.nonNull(orderNotificationMap.get(orderId))
							&& customerData.isPresent()) {
						notificationData = orderNotificationMap.get(orderId);
					}
				}
				if (customerData.isPresent()) {
					try {
						if (evtName.equals(CleverTapEvents.CHARGED)) {
							evt = cleverTapConverter.convertChargedEventOrderData(order, customerData.get(),
									orderNotificationMap.get(orderId));
						} else if (evtName.equalsIgnoreCase(CleverTapEvents.SUBSCRIPTION_PURCHASED_EVENT)) {
							evt = cleverTapConverter.convertSubscriptionEventOrderData(order, customerData.get(),
									orderNotificationMap.get(orderId));
						} else if (evtName.equalsIgnoreCase(CleverTapEvents.WALLET_PURCHASED_EVENT)) {
							evt = cleverTapConverter.convertWalletEventOrderData(order, customerData.get(),
									orderNotificationMap.get(orderId));
						}
						EventUploadRequest request = cleverTapConverter.convert(order, evtName, evt);
						requestList.add(request);
					} catch (Exception e) {
						log.info("Exception occured while converting clevertap event request ", e);
					}
				} else {
					log.info("customer not exist for customerId: {} tagged with order", order.getCustomerId());
				}
			}
		}
		return requestList;
	}

	@Override
	public CleverTapPushResponse publishToCleverTapQueueNew(Object payload,Integer id,String updateType){
		CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
		if (Objects.nonNull(payload)) {
			Map<String, Object> uploadRequest = new HashMap<>();
			if (payload instanceof List) {
				uploadRequest.put("d", payload);
			} else {
				uploadRequest.put("d", Arrays.asList(payload));
			}
			try {
				if (properties.clevertapLogEnable()) {
					log.info("clevertap data : {}", gson.toJson(uploadRequest));
				}
				cleverTapEventPublisher.publishCleverTapEvent(properties.getEnvironmentType().name(),
						gson.toJson(uploadRequest), true);
				cleverTapPushResponse.setStatus(CleverTapConstants.PUSHED_TO_QUEUE);
			}catch (Exception e){
				log.error("error while uploading offer attributes  to clevertap for customner {} to clevertap {}", id,
						e.getMessage());
				cleverTapPushResponse.setStatus("FAILED_TO_PUSHED");
			}
		}
		return cleverTapPushResponse;
	}


}
