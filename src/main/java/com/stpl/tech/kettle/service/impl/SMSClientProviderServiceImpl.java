package com.stpl.tech.kettle.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.cache.EnvironmentPropertiesCache;
import com.stpl.tech.kettle.domain.model.SMSClientName;
import com.stpl.tech.kettle.exceptions.SMSClientAllocationException;
import com.stpl.tech.kettle.service.SMSClientProviderService;
import com.stpl.tech.kettle.service.SMSGupshupWebServiceClient;
import com.stpl.tech.kettle.service.SMSWebServiceClient;
import com.stpl.tech.kettle.service.SolsInfiniWebServiceClient;
import com.stpl.tech.kettle.util.Constants.SMSType;
import com.stpl.tech.master.domain.model.ApplicationName;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class SMSClientProviderServiceImpl implements SMSClientProviderService {
	@Autowired
	private EnvironmentPropertiesCache propertiesCache;

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.master.core.external.notification.service.impl.
	 * SMSClientProviderService#getSMSClient(com.stpl.tech.master.core.notification.
	 * sms.SMSType, com.stpl.tech.master.domain.model.ApplicationName)
	 */
	@Override
	public SMSWebServiceClient getSMSClient(SMSType type, ApplicationName applicationName) {
		String clientName = propertiesCache.getcompanyConfigAttributes(applicationName.name()).get(type.getValue());
		log.info("Application name : " + applicationName.name() + " SMS type  : " + type.getKey() + "   Client Name : "
				+ clientName);
		if (clientName == null) {
			try {
				throw new SMSClientAllocationException(
						"No mapping found in CONFIG_ATTRIBUTE_VALUE table for application : " + applicationName.name()
								+ " for SMS type  : " + type.getKey() + " key : " + type.getValue());
			} catch (SMSClientAllocationException e) {
				log.info(e.getMessage(), e);
			}
		}

		if (clientName.equalsIgnoreCase(SMSClientName.SOLUTION_INFINI.name())) {
			switch (type) {
			case OTP:
				return SolsInfiniWebServiceClient.getOTPClient();
			case OPT_VIA_IVR:
				return SolsInfiniWebServiceClient.getOTPClientViaIVR();
			case TRANSACTIONAL:
				return SolsInfiniWebServiceClient.getTransactionalClient();
			case PROMOTIONAL:
				log.info("Promotional client not available");
				break;
			}
		} else if (clientName.equalsIgnoreCase(SMSClientName.SMS_GUPSHUP.name())) {
			switch (type) {
			case OTP:
				return SMSGupshupWebServiceClient.getOTPInstance();
			case TRANSACTIONAL:
				return SMSGupshupWebServiceClient.getRegularInstance();
			case PROMOTIONAL:
				return SMSGupshupWebServiceClient.getPromotionalInstance();
			}
		}
		return null;
	}
}
