package com.stpl.tech.kettle.service.impl;

import java.io.IOException;
import java.util.*;

import javax.jms.JMSException;

import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.cache.EnvironmentPropertiesCache;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.data.kettle.OrderEmailNotification;
import com.stpl.tech.kettle.data.master.NotificationLogDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.NotificationPayload;
import com.stpl.tech.kettle.domain.model.OTPMapper;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderEmailEntryType;
import com.stpl.tech.kettle.repository.kettle.OrderEmailNotificationDao;
import com.stpl.tech.kettle.repository.master.NotificationDao;
import com.stpl.tech.kettle.service.CustomerCommunicationEventPublisher;
import com.stpl.tech.kettle.service.CustomerService;
import com.stpl.tech.kettle.service.MessagingClient;
import com.stpl.tech.kettle.service.NotificationService;
import com.stpl.tech.kettle.service.SMSWebServiceClient;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.Brand;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class NotificationServiceImpl implements NotificationService {

	@Autowired
	private OrderEmailNotificationDao emailNotificationDao;

	@Autowired
	private EnvironmentPropertiesCache propertiesCache;

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private NotificationDao notificationDao;

	@Autowired
	CustomerCommunicationEventPublisher customerCommunicationEventPublisher;

	@Override
	public void triggerEmail(Brand brand, OrderDetail orderDetail, Order order, Customer customer,
			Date currentTimestamp, StringBuilder sb) {
		long time = System.currentTimeMillis();
		// check if email should be trigger for brand or not
		if (brand.getSendEmail() && !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId())
				&& !TransactionUtils.isCODOrder(orderDetail.getOrderSource())) {
			generateOrderEmailEvent(OrderEmailEntryType.ORDER, orderDetail.getOrderId(), 1, customer, true,
					currentTimestamp);
			if (Objects.nonNull(sb)) {
				sb.append("\n########## , STEP G, - , Customer Email Event Creation----------,"
						+ (System.currentTimeMillis() - time));
			}
		} else {
			if (Objects.nonNull(sb)) {
				sb.append("\n########## , STEP G, - , Customer Email Event Skipped----------,"
						+ (System.currentTimeMillis() - time));
			}
		}

	}

	@Override
	public void generateOrderEmailEvent(OrderEmailEntryType type, int orderId, int retryCount, String emailId,
			boolean isSystemGenerated, boolean isEmailVerified, Date currentTimestamp, String contact) {
		OrderEmailNotification notification = new OrderEmailNotification(type.name(), orderId, retryCount,
				AppConstants.getValue(!isSystemGenerated), currentTimestamp);
		notification.setEmailAddress(emailId);
		if (contact != null) {
			notification.setContact(contact);
		}
		notification.setIsEmailDelivered("N");
		notification.setIsEmailVerified(AppConstants.getValue(isEmailVerified));
		emailNotificationDao.save(notification);
	}

	public void sendCharityOrderNotification(OrderDetail orderDetail, Date currentTimestamp) {
		String mailIds = propertiesCache.getCharityMailIds();
		if (mailIds != null && !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(orderDetail.getCustomerId())
				&& !TransactionUtils.isCODOrder(orderDetail.getOrderSource())) {
			generateOrderEmailEvent(OrderEmailEntryType.CHARITY_ORDER, orderDetail.getOrderId(), 1, mailIds, true, true,
					currentTimestamp, null);
		}
	}

	public void generateOrderEmailEvent(OrderEmailEntryType type, int orderId, int retryCount, Customer customer,
			boolean isSystemGenerated, Date currentTimestamp) {
		String emailId = customer == null || customer.getEmailId() == null || customer.getEmailId().trim().length() == 0
				? properties.getUndeliveredEmail()
				: customer.getEmailId();
		boolean isEmailVerified = customer == null || customer.getEmailId() == null ? true : customer.isEmailVerified();

		if (type.equals(OrderEmailEntryType.ORDER) && Objects.nonNull(customer)
				&& (Objects.isNull(customer.getEmailId()) || Objects.isNull(customer.getContactNumber()))) {
			customerService.updateCustomerEmail(customer, AppConstants.NO);
			return;
		}
		generateOrderEmailEvent(type, orderId, retryCount, emailId, isSystemGenerated, isEmailVerified,
				currentTimestamp, customer.getContactNumber());
	}



	@Override
	public OTPMapper getOTPMapperInstance() {
		return OTPMapper.getInstance();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean sendOTPRequestViaIVR(String type, String contact, SMSWebServiceClient client,
			boolean sendNotification, NotificationPayload payload, String token, Integer ivrId) {
		boolean status = false;
		if (Objects.nonNull(propertiesCache.getInternalNos()) && propertiesCache.getInternalNos().contains(contact)) {
			log.info(" Message not sent to internal contact No : {}", contact);
			sendNotification = false;
		}
		if (sendNotification) {
			status = client.sendOTPRequestViaIVR(token, contact, ivrId);
		} else {
			log.info("OTP via IVR skipped for contact Number :{}", contact);
			status = true;
		}
		try {
			createNotification(type, token, contact, client, status);
		} catch (Exception e) {
			log.error("Error while logging customer otp via ivr notification", e);
		}
		return status;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean sendNotification(String type, String message, String contact, MessagingClient client,
			boolean sendNotification, NotificationPayload payload) throws IOException, JMSException {
		boolean status = false;
		try {
			log.info("payload : {}  ,is whatsaAppOptIn : {} , isSendWhatsaApp :  {} ,  senndWhatsappNotification : {} ",new Gson().toJson(payload),
					payload.isWhatsappOptIn(),sendWhatsappNotification(sendNotification,
							payload.isWhatsappOptIn(), payload.isSendWhatsapp()),payload.isSendWhatsapp());
			if (Objects.nonNull(payload) && payload.isWhatsappOptIn() && sendWhatsappNotification(sendNotification,
					payload.isWhatsappOptIn(), payload.isSendWhatsapp())) {
				customerCommunicationEventPublisher
						.publishCustomerCommunicationEvent(properties.getEnvironmentType().name(), payload);
				return true;
			}
		} catch (Exception e) {
			log.error("Exception Faced While Sending Whatsapp Notification  {}", payload.getCustomerId());
		}
		if (propertiesCache.getInternalNos() != null && propertiesCache.getInternalNos().contains(contact)) {
			log.info(" Message not sent to internal contact No : {}", contact);
			sendNotification = false;
		}

		if (sendNotification) {
			status = client.sendMessage(message, contact);
		} else {
			log.info("SMS Skipped, Message : {} \nContact Number : {}", message, contact);
			status = true;
		}
		try {
			createNotification(type, message, contact, client, status);
		} catch (Exception e) {
			log.error("Error while logging customer notification", e);
		}
		return status;
	}

	private boolean sendWhatsappNotification(Boolean props, Boolean whatsappOptIn, Boolean sendWhatsapp) {
		return props && (whatsappOptIn && sendWhatsapp);
	}

	private void createNotification(String type, String message, String contact, MessagingClient client,
			boolean sendNotification) {

		NotificationLogDetail notificationLogDetail = new NotificationLogDetail();
		notificationLogDetail.setType(type);
		notificationLogDetail.setNotificationTime(AppUtils.getCurrentTimestamp());
		notificationLogDetail.setMessage(message);
		notificationLogDetail.setContact(contact);
		notificationLogDetail.setNotificationSent(AppUtils.setStatus(sendNotification));
		notificationLogDetail.setServiceClient(client.getClass().getSimpleName());
		notificationDao.save(notificationLogDetail);
	}
}
