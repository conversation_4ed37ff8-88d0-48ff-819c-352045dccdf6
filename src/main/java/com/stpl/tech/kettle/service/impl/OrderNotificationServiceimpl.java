package com.stpl.tech.kettle.service.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.itextpdf.html2pdf.HtmlConverter;
import com.stpl.tech.kettle.cache.BrandMetaDataCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.converter.CleverTapConverter;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.kettle.OrderNotificationData;
import com.stpl.tech.kettle.domain.FileDetail;
import com.stpl.tech.kettle.domain.model.CleverTapPushResponse;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.CustomerSMSNotificationType;
import com.stpl.tech.kettle.domain.model.FeedbackEventInfo;
import com.stpl.tech.kettle.domain.model.FeedbackEventStatus;
import com.stpl.tech.kettle.domain.model.FeedbackSource;
import com.stpl.tech.kettle.domain.model.FeedbackStatus;
import com.stpl.tech.kettle.domain.model.FeedbackTokenInfo;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.NotificationPayload;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderFeedbackMetadata;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.exceptions.EmailGenerationException;
import com.stpl.tech.kettle.mapper.OrderNotificationMapper;
import com.stpl.tech.kettle.notification.ReceiptNotification;
import com.stpl.tech.kettle.notification.receipt.OrderEmailReceipt;
import com.stpl.tech.kettle.repository.kettle.OrderNotificationDataDao;
import com.stpl.tech.kettle.service.CleverTapDataPushService;
import com.stpl.tech.kettle.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.service.FeedbackManagementService;
import com.stpl.tech.kettle.service.FileArchiveService;
import com.stpl.tech.kettle.service.NotificationService;
import com.stpl.tech.kettle.service.OrderNotificationService;
import com.stpl.tech.kettle.service.SMSWebServiceClient;
import com.stpl.tech.kettle.service.SolsInfiniWebServiceClient;
import com.stpl.tech.kettle.service.TokenService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.Constants.CleverTapConstants;
import com.stpl.tech.kettle.util.Constants.CleverTapEvents;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.notification.ShortUrlData;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class OrderNotificationServiceimpl implements OrderNotificationService {

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private OrderNotificationDataDao notificationDataDao;

	@Autowired
	private OrderNotificationMapper orderNotificationMapper;

	@Autowired
	private FileArchiveService fileArchiveService;

	@Autowired
	private CustomerOfferManagementService customerOfferManagementService;

	@Autowired
	private UnitCacheService cacheService;

	@Autowired
	private FeedbackManagementService feedbackManagementService;

	@Autowired
	private TokenService tokenService;

	@Autowired
	private CleverTapDataPushService cleverTapDataPushService;

	@Autowired
	private CleverTapConverter cleverTapConverter;

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private BrandMetaDataCache brandMetaDataCache;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void createOrderNotificationData(OrderNotification orderNotification, OrderInfo info)
			throws DataUpdationException {

		OrderNotificationData notificationData = orderNotificationMapper.toData(orderNotification);
		notificationData.setOrderId(info.getOrder().getOrderId());
		notificationData.setUnitName(info.getOrder().getUnitName());
		if(StringUtils.isBlank(notificationData.getGeneratedOrderId())) {
			notificationData.setGeneratedOrderId(info.getOrder().getGenerateOrderId());
		}
		notificationDataDao.save(notificationData);
	}

	@Override
	public void generateOrderFeedbackNotification(OrderInfo info, OrderFeedbackMetadata orderFeedbackMetadata) {
		if (!properties.getAutomatedNPSSMS()) {
			log.info("Skipping send automated NPS sms");
			return;
		}
		boolean isCustomerAvailable = info.getCustomer().isSmsSubscriber() && !info.getCustomer().isBlacklisted()
				&& !info.getCustomer().getIsDND()
				&& !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(info.getOrder().getCustomerId());
		if (isCustomerAvailable) {
			if ((Objects.isNull(info.getCustomer().getOptWhatsapp())
					|| (Objects.nonNull(info.getCustomer().getOptWhatsapp())
							&& AppConstants.NO.equalsIgnoreCase(info.getCustomer().getOptWhatsapp())))) {
				boolean sendOrderFeedbackNotification = AppUtils
						.sendOrderFeedbackNotification(AppUtils.getCurrentTimestamp());
				if (sendOrderFeedbackNotification) {
					generateOrderFeedbackDetails(FeedbackSource.SMS, info, true, false, orderFeedbackMetadata);
				}
			} else if (Objects.nonNull(info.getCustomer().getOptWhatsapp())
					&& AppConstants.YES.equalsIgnoreCase(info.getCustomer().getOptWhatsapp())) {
				generateOrderFeedbackDetails(FeedbackSource.SMS, info, true, true, orderFeedbackMetadata);
			}
		}

		if (orderFeedbackMetadata != null && Objects.nonNull(orderFeedbackMetadata.getFeedbackAndReceiptDetails())) {
			if (!properties.getSystemGeneratedNotifications()) {
				info.getOrderNotification()
						.setOrderFeedBackUrl(orderFeedbackMetadata.getFeedbackAndReceiptDetails().getShortCode());
				info.getOrderNotification()
						.setOrderRecieptUrl(orderFeedbackMetadata.getFeedbackAndReceiptDetails().getName());
			} else {
				// TODO Ishman - Send Notification
			}
		}

	}

	@Override
	public OrderFeedbackMetadata generateOrderFeedbackDetails(FeedbackSource source, OrderInfo orderInfo,
			boolean feedback, boolean generateReceipt, OrderFeedbackMetadata orderFeedbackMetadata) {
		IdCodeName codeName = new IdCodeName();
		if (generateReceipt) {
			String fromEmail = (AppUtils.isDev(properties.getEnvironmentType()) ? EnvType.DEV.name() + " " : "")
					+ AppConstants.CHAAYOS_RECEIPT;
			CustomerEmailData customerEmailData = customerOfferManagementService
					.getCustomerEmailData(orderInfo.getCustomer().getId(), orderInfo.getOrder().getBrandId());
			ReceiptNotification notification = new ReceiptNotification(new OrderEmailReceipt("webapps/kettle-service",
					orderInfo.getUnit(), orderInfo, AppUtils.getFormattedEmail(fromEmail, properties.getRecieptEmail()),
					orderInfo.getCustomer().getEmailId(), properties.getBasePath(),
					orderInfo.getCustomer().isEmailVerified(), null, null, properties.getBillPromotion(), false,
					customerEmailData), sendEmailToCustomer(orderInfo.getOrder()));
			try {
				createReceiptPdf(notification.body(), orderInfo.getOrder().getGenerateOrderId(), orderInfo.getUnit(),
						codeName);
			} catch (EmailGenerationException e) {
				log.error("Exception while order reciept email generation for orderid ::{}",
						orderInfo.getOrder().getOrderId(), e);
			}
		}
		if (feedback) {
			try {
				getOrderFeedBackUrl(source, codeName, orderInfo, orderFeedbackMetadata);
			} catch (Exception e) {
				log.info("Exception Faced While Generating Order Feedback URL::{}", orderInfo.getOrder().getOrderId(),
						e);
			}
		}
		if (generateReceipt && Objects.nonNull(codeName.getName())) {
			if (orderFeedbackMetadata.getFeedbackAndReceiptDetails() != null) {
				orderFeedbackMetadata.getFeedbackAndReceiptDetails().setName(codeName.getName());
			} else {
				orderFeedbackMetadata.setFeedbackAndReceiptDetails(codeName);
			}
		} else {
			orderFeedbackMetadata.setFeedbackAndReceiptDetails(codeName);
		}
		return orderFeedbackMetadata;
	}

	private boolean sendEmailToCustomer(Order order) {
		return order.getSubscriptionDetail() == null
				|| (order.getSubscriptionDetail() != null && order.getSubscriptionDetail().isEmailNotification());
	}

	private void createReceiptPdf(String receiptContent, String orderId, Unit unit, IdCodeName codeName) {
		try {
			if (StringUtils.isNotBlank(receiptContent) && StringUtils.isNotBlank(orderId)) {
				String kioskPath = properties.getBasePath() + "/" + unit.getId() + "/whatsapp/orders";
				File kioskFolder = new File(kioskPath);
				if (!kioskFolder.exists()) {
					kioskFolder.mkdirs();
				}
				String fileName = "orderReceipt-" + orderId + ".pdf";
				String receiptPath = kioskPath + "/" + fileName;
				File pdfFile = new File(receiptPath);
				if (!pdfFile.exists()) {
					pdfFile.createNewFile();
				}
				try (OutputStream outputStream = new FileOutputStream(pdfFile)) {
					HtmlConverter.convertToPdf(receiptContent, outputStream);
					outputStream.flush();

					String baseDir = "whatsapp/" + unit.getId() + "/orders";
					try {
						FileDetail s3File = fileArchiveService.saveFileToS3(properties.getS3Bucket(), baseDir, pdfFile,
								true, 7);
						if (s3File != null) {
//                            codeName.setName(SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(s3File.getUrl()).getUrl());
//							codeName.setName(SolsInfiniWebServiceClient.getTransactionalClient()
//									.getShortUrl(properties.getOrderReceipt() + "/" + unit.getId() + "/orders/" + fileName)
//									.getUrl()); TODO
						} else {
							log.error("Error uploading report to S3.");
						}
						pdfFile.delete();
					} catch (Exception e) {
						log.error("Encountered error while uploading report to S3", e);
						if(pdfFile.exists()) {
							pdfFile.delete();
						}
					}

				} catch (IOException e) {
					/*
					 * String errorMsg = "Unable to create receipt pdf "; LOG.error(errorMsg, e);
					 */
					log.error("Exception Occurred while converting html to pdf");
					if(pdfFile.exists()) {
						pdfFile.delete();
					}
				}
			}
		} catch (Exception ex) {
			log.error("Exception Occurred while creating PDF of Receipt");
		}
	}

	private void getOrderFeedBackUrl(FeedbackSource source, IdCodeName codeName, OrderInfo orderInfo,
			OrderFeedbackMetadata orderFeedbackMetadata) {
		FeedbackEventInfo event = feedbackManagementService.getPendingNPSForCustomer(source,
				orderInfo.getOrder().getOrderId(), orderInfo.getOrder().getCustomerId());
		if (Objects.nonNull(event)) {
			try {
				String unitName = cacheService.getUnitBasicDetailById(event.getUnitId()).getName();
				FeedbackTokenInfo token = new FeedbackTokenInfo(event.getContactNumber(), unitName,
						event.getCustomerName(), event.getOrderId(), event.getOrderSource(), event.getEventSource(),
						event.getFeedbackId(), event.getFeedbackEventId());
				String jwtToken = tokenService.createToken(token, -1L);
				String feedbackUrl = null;
				ShortUrlData shortUrl = null;
				boolean result = false;
				if (properties.getOrderFeedbackType().equals("internal")) {
					feedbackUrl = event.getBrand().getInternalOrderFeedbackUrl();
				} else {
					feedbackUrl = AppConstants.COD.equals(event.getOrderSource()) ?
					// feedback.endpoint.nps.delivery
							event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointNPSDelivery()
							// feedback.endpoint.nps.cafe
							: event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointNPSCafe();
				}
				URIBuilder builder = new URIBuilder(feedbackUrl);
				builder.addParameter("name", event.getCustomerName());
				builder.addParameter("unit", unitName);
				builder.addParameter("token", jwtToken);
				SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient
						.getTransactionalClient(event.getBrand());
				shortUrl = smsWebServiceClient.getShortUrl(builder.build().toURL().toString());
				event.setEventLongUrl(feedbackUrl);
				if (Objects.nonNull(shortUrl)) {
					event.setEventShortUrl(shortUrl.getUrl());
					codeName.setShortCode(shortUrl.getUrl());
				}
				feedbackUrl = builder.build().toURL().toString();
				codeName.setCode(feedbackUrl);
				getOrderFeedbackMetadata(event, codeName, feedbackUrl, orderFeedbackMetadata);
				if (properties.getSystemGeneratedNotifications()) {
//				result = sendNPSMessage(properties.getAutomatedNPSSMS(), event.getContactNumber(),
//						event, smsWebServiceClient);
					feedbackUrl = builder.build().toURL().toString();
					/*
					 * if (result) { Date updateTime =
					 * feedbackService.updateFeedbackEventStatus(event.getFeedbackId(),
					 * event.getFeedbackEventId(), shortUrl, feedbackUrl,
					 * FeedbackEventStatus.NOTIFIED, FeedbackStatus.NOTIFIED);
					 * feedbackService.updateLastNPSTime(updateTime, event.getCustomerId()); } else
					 * { feedbackService.updateFeedbackEventStatus(event.getFeedbackId(),
					 * event.getFeedbackEventId(), shortUrl, feedbackUrl,
					 * FeedbackEventStatus.FAILED, FeedbackStatus.FAILED); }
					 */
					updateFeedbackEventStatus(orderFeedbackMetadata, result);
				}
			} catch (IOException | URISyntaxException e) {
				log.error("Error while generating the feedback url to " + event.getContactNumber(), e);
			}
		}
	}

	@Override
	public void updateFeedbackEventStatus(OrderFeedbackMetadata orderFeedbackMetadata,
			boolean isOrderNotificationSent) {
		if (Objects.nonNull(orderFeedbackMetadata) && Objects.nonNull(orderFeedbackMetadata.getFeedbackId())
				&& Objects.nonNull(orderFeedbackMetadata.getFeedbackEventId())) {
			Date updateTime = null;
			if (properties.sendFeedBackSMSFromClevertap()) {
				if (isOrderNotificationSent) {
					updateTime = feedbackManagementService.updateFeedbackEventStatus(
							orderFeedbackMetadata.getFeedbackId(), orderFeedbackMetadata.getFeedbackEventId(), null,
							orderFeedbackMetadata.getFeedbackUrl(), FeedbackEventStatus.NOTIFIED,
							FeedbackStatus.NOTIFIED);
					feedbackManagementService.updateLastNPSTime(updateTime, orderFeedbackMetadata.getCustomerId());
				} else {
					feedbackManagementService.updateFeedbackEventStatus(orderFeedbackMetadata.getFeedbackId(),
							orderFeedbackMetadata.getFeedbackEventId(), null, orderFeedbackMetadata.getFeedbackUrl(),
							FeedbackEventStatus.FAILED, FeedbackStatus.FAILED);
				}
			}
		}
	}

	private void getOrderFeedbackMetadata(FeedbackEventInfo event, IdCodeName codeName, String feedbackUrl,
			OrderFeedbackMetadata orderFeedbackMetadata) {
		if (Objects.nonNull(event) && Objects.nonNull(codeName)) {
			orderFeedbackMetadata.setFeedbackId(event.getFeedbackId());
			orderFeedbackMetadata.setFeedbackEventId(event.getFeedbackEventId());
			orderFeedbackMetadata.setFeedbackAndReceiptDetails(codeName);
			orderFeedbackMetadata.setFeedbackUrl(feedbackUrl);
			orderFeedbackMetadata.setCustomerId(event.getCustomerId());
		}
	}

	private OrderNotification getOfferNotificationMetadata(OrderNotification orderNotification, NextOffer offer,
			CustomerCampaignOfferDetail postOrderOfferCreationSuccess) {
		orderNotification.setNextOfferText(offer.getText());
		orderNotification.setOfferCode(offer.getOfferCode());
		orderNotification.setValidityTill(offer.getValidityTill());
		orderNotification.setDaysLeft(offer.getDaysLeft());
		return orderNotification;

	}

	private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type,
			CustomerCampaignOfferDetail customerCampaignOfferDetail, String optWhatsapp) {
		try {
			Map<String, String> map = new HashMap<>();
			map.put("couponValidity", AppUtils.getDateInMonth(customerCampaignOfferDetail.getCouponEndDate()));
			map.put("couponCode", customerCampaignOfferDetail.getCouponCode());
			map.put("offerDescription", customerCampaignOfferDetail.getOfferText());
			NotificationPayload payload = new NotificationPayload();
			payload.setContactNumber(customerCampaignOfferDetail.getContactNumber());
			payload.setCustomerId(customerCampaignOfferDetail.getCustomerId());
			payload.setSendWhatsapp(type.isWhatsapp());
			payload.setMessageType(type.name());
			payload.setWhatsappOptIn(AppConstants.YES.equals(optWhatsapp));
			payload.setPayload(map);
			payload.setRequestTime(AppUtils.getCurrentTimestamp());
			return payload;
		} catch (Exception e) {
			log.error("Exception Faced While Generating Notification Payload for Contact ::: {}",
					customerCampaignOfferDetail.getContactNumber());
			return null;
		}
	}

	@Override
	public boolean sendNextBestOfferNotification(NextOffer offer, SMSWebServiceClient smsWebServiceClient,
			CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp,
			OrderNotification orderNotification, boolean isOfferGeneratedByOrder) {
		try {
			long startTime = System.currentTimeMillis();
			String message = null;
			boolean clvFlag = properties.getCleverTapEnabled();
			boolean sysGenNotification = properties.getSystemGeneratedNotifications();
			if (Objects.nonNull(orderNotification) && !sysGenNotification) {
				getOfferNotificationMetadata(orderNotification, offer, postOrderOfferCreationSuccess);
			}
			if (offer.getContentUrl() != null && !AppConstants.LOYAL_TEA_COUPON_CODE
					.equals(postOrderOfferCreationSuccess.getCampaignCloneCode())) {
				message = CustomerSMSNotificationType.CLM_OFFER.getMessage(offer);
				if (sysGenNotification || properties.getIsSendSmsForCampaignBySystem()) {
					return notificationService.sendNotification(CustomerSMSNotificationType.CLM_OFFER.name(), message,
							offer.getContactNumber(), smsWebServiceClient, true, getNotificationPayload(
									CustomerSMSNotificationType.CLM_OFFER, postOrderOfferCreationSuccess, optWhatsapp));
				}
				if (clvFlag && offer.getBrandId().equals(Integer.valueOf(1))) {
					if (isOfferGeneratedByOrder) {
						cleverTapDataPushService.pushOfferDataToClevertap(offer, postOrderOfferCreationSuccess);
						return true;
					} else {
						log.info("Sending offer Details to clevertap for Customer {} ", offer.getCustomerId());
						CleverTapPushResponse response = cleverTapDataPushService.uploadProfileAttributes(
								offer.getCustomerId(), AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(),
								CleverTapConstants.REGULAR, cleverTapConverter.convert(offer,
										CleverTapEvents.NEXT_BEST_OFFER, postOrderOfferCreationSuccess));
						log.info("Sending NBO Data to Clevertap took {} ms", System.currentTimeMillis() - startTime);
						return response.getStatus().equalsIgnoreCase(CleverTapConstants.SUCCESS);
					}
				}
			} else {
				// DONT_SEND LOYALTEA SMS
				return true;

			}

		} catch (Exception e) {
			log.error("Error while sending CLM Repeat SMS to Customer :: {}", offer.getContactNumber());
		}
		return false;
	}

	@Override
	public String sendDeliveryPostOrderOfferNotification(NextOffer offer,
			CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp,
			OrderNotification orderNotification, boolean isOfferGeneratedByOrder) {
		boolean status = false;
		Brand brand = brandMetaDataCache.getBrandMetaData().get(offer.getBrandId());
		// TODO using vortex
		SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
		log.info("Delivery Next Off De {} ::::: {}", offer.toString(), offer.isAvailable());
		if (Objects.nonNull(offer) && offer.isAvailable()) {
			if (isOfferGeneratedByOrder && !properties.getSystemGeneratedNotifications()) {
				if (Objects.nonNull(orderNotification)) {
					getOfferNotificationMetadata(orderNotification, offer, postOrderOfferCreationSuccess);
				}
			}
			log.info("Delivery Next Best Offer SMS Details to be send to customer :: {}", offer.getContactNumber());
			status = sendDeliveryNextBestOfferNotification(offer, smsWebServiceClient, postOrderOfferCreationSuccess,
					optWhatsapp, isOfferGeneratedByOrder);
		}
		log.info("Message Status For Customer {} :: {}", offer.getContactNumber(), status);
		return "SMS";
	}

	@Override
	public boolean sendDeliveryNextBestOfferNotification(NextOffer offer, SMSWebServiceClient smsWebServiceClient,
			CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp,
			boolean isOfferGeneratedByOrder) {
		try {
			long startTime = System.currentTimeMillis();
			String message = null;
			boolean clvFlag = properties.getCleverTapEnabled();
			boolean sysGenNotification = properties.getSystemGeneratedNotifications();
			if (offer.getContentUrl() != null && !AppConstants.LOYAL_TEA_COUPON_CODE
					.equals(postOrderOfferCreationSuccess.getCampaignCloneCode())) {
				message = CustomerSMSNotificationType.CLM_OFFER_DELIVERY.getMessage(offer);
				if (sysGenNotification || (properties.getIsSendSmsForCampaignBySystem())) {
					return notificationService.sendNotification(CustomerSMSNotificationType.CLM_OFFER_DELIVERY.name(),
							message, offer.getContactNumber(), smsWebServiceClient, true,
							getNotificationPayload(CustomerSMSNotificationType.CLM_OFFER_DELIVERY,
									postOrderOfferCreationSuccess, optWhatsapp));

				}
				if (clvFlag && offer.getBrandId().equals(Integer.valueOf(1))) {
					log.info("Sending offer Details to clevertap for Customer {} ", offer.getCustomerId());
					if (isOfferGeneratedByOrder) {
						cleverTapDataPushService.pushOfferDataToClevertap(offer, postOrderOfferCreationSuccess);
						return true;
					} else {
						CleverTapPushResponse response = cleverTapDataPushService.uploadProfileAttributes(
								offer.getCustomerId(), AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(),
								CleverTapConstants.REGULAR, cleverTapConverter.convert(offer,
										CleverTapEvents.DELIVERY_NEXT_BEST_OFFER, postOrderOfferCreationSuccess));
						log.info("Sending DNBO Data to Clevertap took {} ms", System.currentTimeMillis() - startTime);
						return response.getStatus().equalsIgnoreCase(CleverTapConstants.SUCCESS);
					}
				}
			} else {
				// DONT_SEND LOYALTEA SMS
				return true;
			}
		} catch (Exception e) {
			log.error("Error while sending CLM Repeat SMS to Customer :: {}", offer.getContactNumber());
		}
		return false;
	}
}
