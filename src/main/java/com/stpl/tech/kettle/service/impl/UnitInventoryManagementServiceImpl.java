package com.stpl.tech.kettle.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import javax.jms.JMSException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.kettle.cache.EmployeeCache;
import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.converter.Converters;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.InventoryLogData;
import com.stpl.tech.kettle.data.kettle.InventoryUpdateData;
import com.stpl.tech.kettle.data.kettle.InventoryUpdateEvent;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.kettle.data.kettle.UnitProductInventory;
import com.stpl.tech.kettle.data.master.UnitProductMapping;
import com.stpl.tech.kettle.domain.model.InventoryData;
import com.stpl.tech.kettle.domain.model.InventoryEventData;
import com.stpl.tech.kettle.domain.model.InventoryEventType;
import com.stpl.tech.kettle.domain.model.TransitionStatus;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.repository.kettle.InventoryLogDataDao;
import com.stpl.tech.kettle.repository.kettle.InventoryUpdateDataDao;
import com.stpl.tech.kettle.repository.kettle.InventoryUpdateEventDao;
import com.stpl.tech.kettle.repository.kettle.UnitProductInventoryDao;
import com.stpl.tech.kettle.repository.master.UnitProductMappingDao;
import com.stpl.tech.kettle.service.StockEventService;
import com.stpl.tech.kettle.service.UnitInventoryManagementService;
import com.stpl.tech.kettle.service.WebClientService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.Constants.Endpoints;
import com.stpl.tech.kettle.util.adapter.DateDeserializer2;
import com.stpl.tech.master.domain.model.ProductInventory;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;

import jakarta.persistence.NoResultException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class UnitInventoryManagementServiceImpl implements UnitInventoryManagementService {
	@Autowired
	private UnitProductInventoryDao unitProductInventoryDao;

	@Autowired
	private InventoryLogDataDao inventoryLogDataDao;

	@Autowired
	private UnitProductMappingDao unitProductMappingDao;

	@Autowired
	private UnitCacheService unitCacheService;

	@Autowired
	StockEventService stockEventService;

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private ProductCache productCache;

	@Autowired
	private WebClientService webClientService;

	@Autowired
	EmployeeCache employeeCache;

	@Autowired
	InventoryUpdateEventDao updateEventDao;

	@Autowired
	InventoryUpdateDataDao inventoryUpdateDataDao;

	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public List<ProductInventory> getUnitInventoryForProducts(int unitId, List<Integer> productIds)
			throws DataNotFoundException {
		Map<Integer, UnitProductInventory> currentInventory = new HashMap<>();
		List<Integer> units = new ArrayList<>();
		units.add(unitId);
		Map<Integer, Set<Integer>> productsForInventoryTracking = getUnitProductMappings(units);
		try {
			if (unitCacheService.getUnitBasicDetailById(unitId).isLiveInventoryEnabled()) {
				Set<Integer> checkSet = new HashSet<>(productIds);
				List<InventoryData> inventory = getInventory(unitId);
				for (InventoryData i : inventory) {
					if (checkSet.contains(i.getId())) {
						currentInventory.put(i.getId(), convertToProductInventory(i, unitId));
					}
				}
			} else {
				currentInventory = getUnitProductInventoryForProducts(unitId, productIds);
			}

		} catch (NoResultException e) {
			log.info(String.format("Did not find Unit Inventory Details For Unit Id : %d", unitId), e);
		}
		List<ProductInventory> returnData = Converters.getInventoryDetailsForProducts(productCache,
				unitCacheService.getUnitBasicDetailById(unitId), currentInventory,
				productsForInventoryTracking.get(unitId));
		return returnData;
	}

	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateUnitInventory(com.stpl.tech.kettle.domain.model.InventoryUpdateEvent updateData,
			boolean generateEvents, boolean incrementalUpdate, int employeeId, Integer orderId,
			boolean isCancellation) {
		boolean inventoryUpdated = updateUnitInventoryData(updateData, incrementalUpdate, employeeId, orderId,
				isCancellation);
		if (inventoryUpdated && generateEvents) {
			inventoryUpdated = addInventoryEvent(updateData);
		}
		return inventoryUpdated;
	}

	public boolean addInventoryEvent(com.stpl.tech.kettle.domain.model.InventoryUpdateEvent updateData) {
		Date currentTime = AppUtils.getCurrentTimestamp();
		// LOG.info(new Gson().toJson(updateData));
		InventoryUpdateEvent data = new InventoryUpdateEvent();
		data.setBusinessDate(updateData.getBusinessDate());
		data.setEventType(updateData.getType().value());
		data.setRecordsCount(updateData.getUpdatedInventory().size());
		data.setUnitId(updateData.getUnitId());
		data.setUpdateComment(updateData.getComment());
		data.setUpdatedBy(updateData.getUpdatedBy());
		data.setUpdateStatus(TransitionStatus.SUCCESS.name());
		data.setUpdateTime(currentTime);
		updateEventDao.save(data);

		for (InventoryEventData inventory : updateData.getUpdatedInventory()) {
			InventoryUpdateData update = new InventoryUpdateData();
			update.setAddTime(currentTime);
			update.setBusinessDate(updateData.getBusinessDate());
			update.setInventoryUpdateEventId(data.getInventoryUpdateEventId());
			update.setProductId(inventory.getProductId());
			update.setQuantity(inventory.getQuantity());
			update.setThresholdQuantity(inventory.getThresholdQuantity());
			update.setUnitId(inventory.getUnitId());
			update.setExpireQuantity(inventory.getExpireQuantity());
			inventoryUpdateDataDao.save(update);
		}

		return true;
	}

	public boolean updateUnitInventoryData(com.stpl.tech.kettle.domain.model.InventoryUpdateEvent updateData,
			boolean incrementalUpdate, int employeeId, Integer orderId, boolean isCancellation) {

		int unitId = updateData.getUnitId();
		Date businessDate = updateData.getBusinessDate();
		Map<Integer, UnitProductInventory> currentInventory = getUnitProductInventory(unitId);

		if (!incrementalUpdate) {
			logNonIncrementalStockEvent(updateData, employeeId, currentInventory);
		}

		UnitProductsStockEvent unitProductsStockOutEvent = new UnitProductsStockEvent(unitId, StockStatus.STOCK_OUT);
		UnitProductsStockEvent unitProductsStockInEvent = new UnitProductsStockEvent(unitId, StockStatus.STOCK_IN);

		for (ProductInventory product : updateData.getCurrentInventory()) {
			UnitProductInventory inventoryData = lookup(unitId, currentInventory,
					product.getProduct().getDetail().getId());
			Integer currentQty = inventoryData.getNoOfUnits();
			Integer finalQty = null;

			if (incrementalUpdate) {
				finalQty = proccessIncrementalInventoryUpdate(product, inventoryData, employeeId, orderId,
						isCancellation, unitId, businessDate);
			} else {
				setQuantity(inventoryData, product.getQuantity(), product.getExpireQuantity());
				finalQty = product.getQuantity();
			}

			if (currentQty > 0 && finalQty <= 0) {
				unitProductsStockOutEvent.getProductIds()
						.add(Integer.valueOf(product.getProduct().getDetail().getId()).toString());
			} else if (currentQty <= 0 && finalQty > 0) {
				unitProductsStockInEvent.getProductIds()
						.add(Integer.valueOf(product.getProduct().getDetail().getId()).toString());
			}
		}
		publishStockEvents(unitProductsStockOutEvent);
		publishStockEvents(unitProductsStockInEvent);

		return true;
	}

	private UnitProductInventory lookup(int unitId, Map<Integer, UnitProductInventory> currentInventory,
			int productId) {
		return currentInventory == null || currentInventory.get(productId) == null ? create(unitId, productId, 0, 0)
				: currentInventory.get(productId);
	}

	private void publishStockEvents(UnitProductsStockEvent unitProductsStockEvent) {
		if (unitProductsStockEvent != null && unitProductsStockEvent.getProductIds().size() > 0) {
			try {
				stockEventService.publishStockEvent(props.getEnvironmentType().name(), unitProductsStockEvent);
			} catch (JMSException e) {
				log.error("Error publishing STOCK event:::", e);
			} catch (Exception e) {
				log.error("Error publishing STOCK event:::", e);
			}
		}
	}

	private UnitProductInventory create(int unitId, int productId, int quantity, int expireQuantity) {
		UnitProductInventory data = new UnitProductInventory();
		data.setUnitId(unitId);
		data.setProductId(productId);
		data.setNoOfUnits(quantity);
		data.setProductInventoryStatus(AppConstants.ACTIVE);
		data.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
		data.setExpireQuantity(expireQuantity);
		if (quantity == 0) {
			data.setLastStockOutTime(AppUtils.getCurrentTimestamp());
		}
		unitProductInventoryDao.save(data);

		return data;
	}

	private Integer proccessIncrementalInventoryUpdate(ProductInventory product, UnitProductInventory inventoryData,
			int employeeId, Integer orderId, boolean isCancellation, int unitId, Date businessDate) {

		if (!isCancellation && orderId != null && inventoryData.getNoOfUnits() > 0
				&& inventoryData.getNoOfUnits() <= product.getQuantity()) {
			logStockEvent(product.getProduct().getDetail().getId(), unitId, employeeId,
					InventoryEventType.STOCK_OUT.value(), InventoryEventType.ORDER_PUNCH.value(), businessDate,
					orderId);
		} else if (orderId == null && inventoryData.getNoOfUnits() > 0
				&& inventoryData.getNoOfUnits() <= product.getQuantity()) {
			logStockEvent(product.getProduct().getDetail().getId(), unitId, employeeId,
					InventoryEventType.STOCK_OUT.value(), InventoryEventType.CAFE_WASTAGE.value(), businessDate,
					orderId);
		}

		int multiplier = isCancellation ? -1 : 1;
		inventoryData.setNoOfUnits(inventoryData.getNoOfUnits() - (multiplier * product.getQuantity()));

		/**
		 * This is a bit tricky as when there is no expire quantity we cannot increment
		 * on order cancellation as we do not know if the order has expire product
		 * quantity or not, currently we do nothing in such case.
		 *
		 */
		if (inventoryData.getExpireQuantity() > 0 && inventoryData.getExpireQuantity() >= product.getQuantity()) {
			inventoryData
					.setExpireQuantity(inventoryData.getExpireQuantity() - (multiplier * product.getExpireQuantity()));
		} else {
			inventoryData.setExpireQuantity(0);
		}

		Integer finalQty = inventoryData.getNoOfUnits();

		if (inventoryData.getNoOfUnits() <= 1) {
			// stockOutSlack(inventoryData, unitId, employeeId, product);
		}

		inventoryData.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
		return finalQty;
	}

	private void stockOutSlack(UnitProductInventory inventoryData, int unitId, int employeeId,
			ProductInventory product) {
//        try {
//            Unit unit = unitCacheService.getUnitById(unitId);
//            String employee = employeeCache.getEmployeeNameById(employeeId);
//
//            String message = String.format(
//                    "Unit : *%s*\nProduct : *%s*\nTime : %s\nEmployee : %s\nCurrent Stock : *%d*\nLast Update Time : %s",
//                    unit.getName(), product.getProduct().getDetail().getName(), AppUtils.getCurrentTimestamp(),
//                    employee, inventoryData.getNoOfUnits(), inventoryData.getLastUpdateTmstmp());
//            Set<String> channels = new HashSet<>();
//            if (unit.getManagerChannel() != null) {
//                channels.add(unit.getManagerChannel());
//            } else {
//                log.error(" ::: Did not find manager channel while sending stock out notification :::");
//            }
//            if (unit.getCafeManager() != null && unit.getCafeManager().getId() != 0) {
//                channels.add(masterCache.getEmployeeBasicDetail(unit.getCafeManager().getId()).getSlackChannel());
//            }
//
//            for (String channel : channels) {
//                SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "Kettle",
//                        AppUtils.isDev(props.getEnvironmentType()) ? null : channel, null, message);
//            }
//
//            try {
//                StockOutEventObject stockOutEventObject = new StockOutEventObject(unit.getName(), product.getProduct().getDetail().getId(), product.getProduct().getDetail().getName(), inventoryData.getNoOfUnits(), inventoryData.getLastUpdateTmstmp(), unit.getCafeManager().getId(), unit.getManagerId());
//                WebServiceHelper.postWithAuth(props.getKnockBaseUrl() + AppConstants.KNOCK_NOTIFICATION_ENDPOINT + SEND_STOCKOUT_NOTIFICATION, props.getKnockMasterToken(), stockOutEventObject, Boolean.class);
//            }catch (Exception e){
//                log.error("Error while sending stock out notification to knock::" , e);
//            }
//
//            // for publishing in channel when direct users don't exist
//            SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "Kettle", null,
//                    SlackNotification.STOCK_OUT_EVENT.getChannel(props.getEnvironmentType()), message);
//
//        } catch (Exception e) {
//            log.error("Error while sending slack notification", e);
//        }

	}

	private void logStockEvent(int productId, int unitId, int employeeId, String eventType, String reasonCode,
			Date businessDate, Integer orderId) {
		InventoryLogData inventoryLogData = new InventoryLogData();
		inventoryLogData.setEmployeeId(employeeId);
		inventoryLogData.setEventType(eventType);
		inventoryLogData.setProductId(productId);
		inventoryLogData.setReasonCode(reasonCode);
		inventoryLogData.setUnitId(unitId);
		inventoryLogData.setUpdateTime(AppUtils.getCurrentTimestamp());
		inventoryLogData.setBusinessDate(businessDate);
		inventoryLogData.setOrderId(orderId);
		inventoryLogDataDao.save(inventoryLogData);
	}

	public Map<Integer, UnitProductInventory> getUnitProductInventory(int unitId) {
		Map<Integer, UnitProductInventory> map = new HashMap<>();
		try {
			List<UnitProductInventory> list = unitProductInventoryDao.findByUnitIdAndProductInventoryStatus(unitId,
					AppConstants.ACTIVE);
			for (UnitProductInventory data : list) {
				map.put(data.getProductId(), data);
			}
		} catch (NoResultException e) {
			log.info(String.format("Did not find Unit Inventory Details For Unit with ID : %d", unitId), e);
		}
		return map;
	}

	private void setQuantity(UnitProductInventory inventoryData, int quantity, int expireQuantity) {
		if (inventoryData.getNoOfUnits() != quantity || inventoryData.getExpireQuantity() != expireQuantity) {
			inventoryData.setNoOfUnits(quantity);
			inventoryData.setProductInventoryStatus(AppConstants.ACTIVE);
			inventoryData.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
			inventoryData.setExpireQuantity(expireQuantity);
			if (quantity == 0) {
				inventoryData.setLastStockOutTime(AppUtils.getCurrentTimestamp());
			}
			unitProductInventoryDao.save(inventoryData);
		}
	}

	private void logNonIncrementalStockEvent(com.stpl.tech.kettle.domain.model.InventoryUpdateEvent updateData,
			int employeeId, Map<Integer, UnitProductInventory> currentInventory) {
		int unitId = updateData.getUnitId();
		for (InventoryEventData productInventory : updateData.getUpdatedInventory()) {
			UnitProductInventory inventoryData = lookup(unitId, currentInventory, productInventory.getProductId());
			if (updateData.getType() == InventoryEventType.STOCK_IN && inventoryData.getNoOfUnits() <= 0
					&& productInventory.getQuantity() > (0 - inventoryData.getNoOfUnits())) {
				logStockEvent(productInventory.getProductId(), unitId, employeeId, InventoryEventType.STOCK_IN.value(),
						InventoryEventType.STOCK_IN.value(), updateData.getBusinessDate(), null);
			}
			if ((updateData.getType() == InventoryEventType.TRANSFER_OUT
					|| updateData.getType() == InventoryEventType.WASTAGE)
					&& (inventoryData.getNoOfUnits() <= productInventory.getQuantity())) {
				logStockEvent(productInventory.getProductId(), unitId, employeeId, InventoryEventType.STOCK_OUT.value(),
						updateData.getType().value(), updateData.getBusinessDate(), null);
			}
			if (updateData.getType() == InventoryEventType.UPDATE) {
				if (productInventory.getQuantity() == 0) {
					logStockEvent(productInventory.getProductId(), unitId, employeeId,
							InventoryEventType.STOCK_OUT.value(), updateData.getType().value(),
							updateData.getBusinessDate(), null);
				} else if (inventoryData.getNoOfUnits() <= 0) {
					logStockEvent(productInventory.getProductId(), unitId, employeeId,
							InventoryEventType.STOCK_IN.value(), updateData.getType().value(),
							updateData.getBusinessDate(), null);
				}
			}
		}
	}

	public List<ProductInventory> getUnitProductsInventory(int unitId, List<Integer> productIds)
			throws DataNotFoundException {

		Map<Integer, UnitProductInventory> currentInventory = new HashMap<>();
		List<Integer> units = new ArrayList<>();
		units.add(unitId);
		Map<Integer, Set<Integer>> productsForInventoryTracking = getUnitProductMappings(units);
		try {
			if (unitCacheService.getUnitBasicDetailById(unitId).isLiveInventoryEnabled()) {
				Set<Integer> checkSet = new HashSet<>(productIds);
				List<InventoryData> inventory = getInventory(unitId);
				for (InventoryData i : inventory) {
					if (checkSet.contains(i.getId())) {
						currentInventory.put(i.getId(), convertToProductInventory(i, unitId));
					}
				}
			} else {
				currentInventory = getUnitProductInventoryForProducts(unitId, productIds);
			}

		} catch (NoResultException e) {
			log.info(String.format("Did not find Unit Inventory Details For Unit Id : %d", unitId), e);
		}
		List<ProductInventory> returnData = Converters.getInventoryDetailsForProducts(productCache,
				unitCacheService.getUnitBasicDetailById(unitId), currentInventory,
				productsForInventoryTracking.get(unitId));
		return returnData;

	}

	private UnitProductInventory convertToProductInventory(InventoryData data, int unitId) {
		UnitProductInventory i = new UnitProductInventory();
		i.setUnitId(unitId);
		i.setProductId(data.getId());
		i.setNoOfUnits(data.getQty() != null ? data.getQty().intValue() : 0);
		return i;
	}

	private List<InventoryData> getInventory(int unitId) {
		long startTime = System.currentTimeMillis();
		String unitZone = unitCacheService.getUnitBasicDetailById(unitId).getUnitZone();
		String endPoint = props.getInventoryServiceBasePath() + Endpoints.INVENTORY_SERVICE_ENTRY_POINT
				+ (Objects.nonNull(unitZone) ? unitZone.toLowerCase() : AppConstants.DEFAULT_UNIT_ZONE)
				+ Endpoints.INVENTORY_SERVICE_VERSION + Endpoints.GET_CAFE_INVENTORY;
		// String token = props.getInventoryClientToken();
		Map<String, String> params = new HashMap<>();
		params.put("unitId", String.valueOf(unitId));
		List<InventoryData> data = new ArrayList<>();
		try {
			String response = webClientService.getRequest(endPoint, null, null, params);
			List<?> list = webClientService.parseResponse(response, List.class);
			GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
			if (list != null) {
				list.forEach(p -> {
					Gson gson = gSonBuilder.create();
					String str = gson.toJson(p);
					InventoryData cat = gson.fromJson(str, InventoryData.class);
					if (cat != null) {
						data.add(cat);
					}
				});
			} else {
				log.info("No Inventory Available for cafe {}", unitId);
			}
		} catch (Exception e) {
			log.error("Error while creating request for inventory for unit Id {}", unitId, e);
		}
		log.info("Downloaded cafe Inventory Data in {} miliseconds", System.currentTimeMillis() - startTime);
		return data;

	}

	public Map<Integer, UnitProductInventory> getUnitProductInventoryForProducts(int unitId, List<Integer> productIds) {
		Map<Integer, UnitProductInventory> map = new HashMap<>();
		List<UnitProductInventory> list = getInventoryForProducts(unitId, productIds);
		for (UnitProductInventory data : list) {
			map.put(data.getProductId(), data);
		}
		return map;
	}

	private List<UnitProductInventory> getInventoryForProducts(int unitId, List<Integer> productIds) {
		return unitProductInventoryDao.findByUnitIdAndProductId(unitId, productIds);
	}

	public Map<Integer, Set<Integer>> getUnitProductMappings(List<Integer> unitId) {

		Map<Integer, Set<Integer>> map = new HashMap<>();
		List<UnitProductMapping> list = unitProductMappingDao
				.findByUnitIdAndIsInventoryTrackedAndProductDetailProductStatusAndProductStatus(unitId,
						AppConstants.YES, ProductStatus.ACTIVE.name(), AppConstants.ACTIVE);
		for (UnitProductMapping data : list) {
			if (!map.containsKey(data.getUnitDetail().getUnitId())) {
				map.put(data.getUnitDetail().getUnitId(), new HashSet<>());
			}
			map.get(data.getUnitDetail().getUnitId()).add(data.getProductDetail().getProductId());
		}
		return map;
	}

}
