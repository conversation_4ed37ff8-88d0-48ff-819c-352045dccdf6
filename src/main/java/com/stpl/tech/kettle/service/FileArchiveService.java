package com.stpl.tech.kettle.service;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Date;

import org.springframework.web.multipart.MultipartFile;

import com.stpl.tech.kettle.domain.FileDetail;
import com.stpl.tech.kettle.exceptions.FileArchiveServiceException;

public interface FileArchiveService {

	FileDetail saveFileToS3(String bucket, String baseDir, String fileName, MultipartFile multipartFile,
			boolean sameName) throws FileArchiveServiceException;

	FileDetail saveFileToS3(String bucket, String baseDir, String fileName, File file, boolean sameName)
			throws FileArchiveServiceException;

	FileDetail saveFileToS3(String bucket, String baseDir, String fileName, MultipartFile multipartFile)
			throws FileArchiveServiceException;

	File getFileFromS3(String baseDir, FileDetail detail) throws FileArchiveServiceException;

	FileDetail saveFileToS3(String bucket, String baseDir, File fileToUpload, boolean sameName, int expiryDays)
			throws FileArchiveServiceException;

	FileDetail saveFileToS3(String bucket, String baseDir, File fileToUpload, boolean sameName)
			throws FileArchiveServiceException;
	
	URL getSignedUrl(String bucket, String key, int expiryDays);

	URL getSignedUrl(String bucket, String key);
	
	FileDetail saveFileToS3(String bucket, String baseDir, File fileToUpload)
			throws FileArchiveServiceException;
	
	Date getExpirationDate(int daysToExpire);
	
	void deleteImageFromS3(String bucket, FileDetail fileDetail);

	File convertFromMultiPart(String fileName, MultipartFile multipartFile) throws IOException;

	boolean saveFileToDestinationPath(String resourceFileName,String destinationDirectory,String fileName);
}
