package com.stpl.tech.kettle.service;

import java.util.List;

import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.data.kettle.OrderItem;
import com.stpl.tech.kettle.data.kettle.SubscriptionPlan;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.SubscriptionProduct;
import com.stpl.tech.kettle.domain.model.SubscriptionRequest;

public interface SubscriptionPlanService {

	SubscriptionPlan getActiveSubscription(Integer customerId, String code);

	SubscriptionPlan addSubscription(SubscriptionRequest request, String source, Integer campaignId);

	SubscriptionPlan updateSubscriptionData(SubscriptionRequest request, String source, Integer campaignId);

	SubscriptionProduct getSubscriptionProduct(Order order);

	List<String> validateSubscriptionRequest(Order order, Customer customer, List<String> errors);

	List<String> validateSubscriptionOrderRequest(Order order, Customer customer, List<String> errors);

	List<String> validateCustomerSubscriptionRequest(Customer customer, List<String> errors);
	
	SubscriptionPlan createSubscription(OrderDetail orderDetail, SubscriptionProduct subscriptionProduct ,Customer customer);

	OrderItem getSubscriptionProductItem(OrderDetail order, int productId);

	SubscriptionPlan createSubscription(SubscriptionProduct subscriptionProduct, Integer orderId, Integer orderItemId,
			Customer customer, Integer campaignId, String source, Integer lagDays);
}
