package com.stpl.tech.kettle.service;

import com.stpl.tech.kettle.data.kettle.DroolsDecisionTableData;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface DroolService {

    boolean addNewDroolFile(MultipartFile file, String droolFileType, boolean persist);

    public void downloadRecipeMedia(HttpServletResponse response, String fileName, String droolFileType, String version) throws IOException;

    boolean activateVersion(String fileName, String droolFileType, String version) throws IOException;

    List<DroolsDecisionTableData> fetchAllFileByType(String fileType);
    public void initailizeDroolContainer(String droolFileType, String version);

    public boolean isDroolContainerInitializeForWallerDecision(String version);

    public boolean isDroolContainerInitializeForDenominationPercentage(String version);

    boolean setDefaultDroolSheet(String droolFileType, String version, String fileName);
    boolean inactivateVersion(String droolFileType, String version, String fileName);
}
