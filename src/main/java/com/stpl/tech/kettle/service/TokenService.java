package com.stpl.tech.kettle.service;

import com.stpl.tech.master.core.external.acl.service.TokenDao;
import jakarta.servlet.http.HttpServletRequest;

public interface TokenService<T extends TokenDao> {

	String createToken(T object, long ttlMillis);

	String createToken(T object, long ttlMillis, String passPhraseKey);

	void parseToken(T object, String text);

	void parseToken(T object, String jwt, String key);
	public Integer getLoggedInUnit(HttpServletRequest httpServletRequest);
}
