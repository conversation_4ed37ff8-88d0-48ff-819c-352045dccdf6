package com.stpl.tech.kettle.service;

import com.stpl.tech.kettle.data.kettle.CashCardDetail;
import com.stpl.tech.kettle.exceptions.CardValidationException;

import java.util.List;

public interface CardService {

    public CashCardDetail getCardDetail(int customerId, String cardNumber, boolean runValidations) throws CardValidationException;

    public List<CashCardDetail> getActiveCashCards(int customerId);


}
