package com.stpl.tech.kettle.service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.domain.model.SMSConfiguration;
import com.stpl.tech.master.notification.ShortUrlData;

import io.micrometer.common.util.StringUtils;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class AbstractSMSClient implements SMSWebServiceClient {
	protected final SMSConfiguration config;
	protected final SMSConfiguration shortURLConfig;

	@Autowired
	private WebClientService webClientService;

	protected AbstractSMSClient(SMSConfiguration config, SMSConfiguration shortURLConfig) {
		this.config = config;
		this.shortURLConfig = shortURLConfig;
	}

	public boolean sendMessage(String message, String contactNumber) throws IOException {
		if (StringUtils.isEmpty(message) || StringUtils.isEmpty(contactNumber)) {
			return false;
		}
		log.info(String.format("Message : %s\nContact Number : %s", message, contactNumber));
		String data = getSMSRequest(message, contactNumber);
		String response = callService(config.getUrl(), data);
		// LOG.info("Service Response : ", response);
		return checkSuccess(response);
	}

	public String callService(String urlString, String payLoad) throws IOException {
		URL url = new URL(urlString + payLoad);
		log.info("URL : " + urlString + payLoad);
		HttpURLConnection conn = (HttpURLConnection) url.openConnection();
		conn.setRequestMethod("GET");
		conn.setDoOutput(true);
		conn.setDoInput(true);
		conn.setUseCaches(false);
		conn.connect();
		BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
		String line;
		StringBuffer buffer = new StringBuffer();
		while ((line = rd.readLine()) != null) {
			buffer.append(line).append("\n");
		}
		log.info(buffer.toString());
		rd.close();
		conn.disconnect();
		return buffer.toString();
	}

	public boolean sendOTPRequestViaIVR(String token, String contact, Integer ivrId) {
		if (StringUtils.isEmpty(contact)) {
			return false;
		}
		StringBuilder sb = new StringBuilder();
		String baseUrl = config.getUrl();
		try {
			String s = getOTPRequestViaIVR(token, contact, ivrId.toString());
			sb.append(baseUrl);
			sb.append(s);
		} catch (IOException e) {
			log.error("Error while generating url to send request for OTP Via IVR:", e);
		}
		try {
			return createPostRequest(sb.toString());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return false;
	}

	public boolean createPostRequest(String url) throws IOException {

		try {
			webClientService.createPostRequestWithTimeout(url, null, 25, 25);// throws exception if status code !=200
			return true;
		} catch (Exception e) {
			log.error("Error in sending post request for otp via ivr :", e);
			return false;
		}
	}

	public boolean checkSuccess(String response) {
		return false;
	}

	@Override
	public String getSMSRequest(String message, String contactNumber) throws UnsupportedEncodingException {
		return null;
	}

	@Override
	public ShortUrlData getShortUrl(String url) throws IOException {
		return null;
	}

	@Override
	public void updateShortUrl(ShortUrlData url) throws IOException {

	}

	@Override
	public String getOTPRequestViaIVR(String token, String contactNumber, String ivrId) throws IOException {
		return null;
	}
}
