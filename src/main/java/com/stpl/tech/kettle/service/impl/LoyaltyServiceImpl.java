package com.stpl.tech.kettle.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import com.stpl.tech.kettle.data.kettle.LoyaltyLogHistory;
import com.stpl.tech.kettle.domain.model.LoyaltyEventStatus;
import com.stpl.tech.kettle.domain.model.LoyaltyFailedReason;
import com.stpl.tech.kettle.repository.kettle.CustomerDao;
import com.stpl.tech.kettle.repository.kettle.LoyaltyLogHistoryDao;
import com.stpl.tech.kettle.util.Constants.TransactionConstants;
import com.stpl.tech.master.core.CacheReferenceType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.LoyaltyEvents;
import com.stpl.tech.kettle.data.kettle.LoyaltyScore;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.LoyaltyEventTransactionType;
import com.stpl.tech.kettle.domain.model.LoyaltyEventType;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.kettle.domain.model.TransactionType;
import com.stpl.tech.kettle.repository.kettle.LoyaltyEventsDao;
import com.stpl.tech.kettle.repository.kettle.LoyaltyScoreDao;
import com.stpl.tech.kettle.repository.kettle.OrderDetailDao;
import com.stpl.tech.kettle.service.LoyaltyService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadataType;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class LoyaltyServiceImpl implements LoyaltyService {
	private static final String LOYALTY_EVENT_SUCCESS_STATUS = "SUCCESS";
	private static final String LOYALTY_EVENT_FAILED_STATUS = "FAILED";

	@Autowired
	private LoyaltyEventsDao loyaltyEventsDao;

	@Autowired
	private LoyaltyScoreDao loyaltyScoreDao;

	@Autowired
	private OrderDetailDao orderDetailDao;

	@Autowired
	private UnitCacheService unitCacheService;

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private LoyaltyLogHistoryDao loyaltyLogHistoryDao;

	@Autowired
	private CustomerDao customerDao;

	public boolean updateScore(int customerId, LoyaltyEventType type, int points, int cumulativePoints, Integer orderId,
			boolean hasSignupOffer, boolean updateLastOrderId) {
		return createEventAndUpdateScore(customerId, type, points, cumulativePoints, true, orderId, hasSignupOffer,
				updateLastOrderId);
	}

	public boolean updateScore(int customerId, LoyaltyEventType type, int points, Integer orderId,
			boolean hasSignupOffer, boolean updateLastOrderId) {
		return createEventAndUpdateScore(customerId, type, points, points, true, orderId, hasSignupOffer,
				updateLastOrderId);
	}

	private boolean createEventAndUpdateScore(int customerId, LoyaltyEventType type, int points, int cumulativePoints,
			boolean updateScore, Integer orderId, boolean hasSignupOffer, boolean updateLastOrderId) {

		LoyaltyScore score = getScore(customerId);
		if (score != null) {
			if (LoyaltyEventType.OUTLET_VISIT.equals(type)) {
				if (score.getCumulativePoints().compareTo(Integer.valueOf(0)) > 0
				&& TransactionUtils.checkWithinTripThresholdTime(score.getLastOrderTime(),unitCacheService)) {
					createLoyaltyEvent(customerId, points, type, LOYALTY_EVENT_FAILED_STATUS, orderId,
							score.getAcquiredPoints(), score.getAcquiredPoints(), LoyaltyFailedReason.WITHIN_TRIP_THRESHOLD_TIME.name());
					return false;
				}
			}
			int finalAcquiredPoints = score.getAcquiredPoints() + points;
			if (finalAcquiredPoints >= 0) {
				createLoyaltyEvent(customerId, points, type, LOYALTY_EVENT_SUCCESS_STATUS, orderId, finalAcquiredPoints,
						score.getAcquiredPoints(),null);
				if (updateScore) {
					score.setAcquiredPoints(finalAcquiredPoints);
					if(points < 0){
						Integer redeemedPoints = Math.abs(points);
						score.setTotalRedeemedPoints(score.getTotalRedeemedPoints()!=null?
								score.getTotalRedeemedPoints() +redeemedPoints: 0+redeemedPoints);
					}
					if (LoyaltyEventType.OUTLET_VISIT.equals(type) && updateLastOrderId) {
						score.setLastOrderId(orderId);
						score.setLastOrderTime(AppUtils.getCurrentTimestamp());
						score.setOrderCount(score.getOrderCount() == null ? 1 : score.getOrderCount() + 1);
						// log.info("New order count , " + score.getOrderCount());
						if (hasSignupOffer || score.getOrderCount() >= 2) {
							// log.info("Setting signupOffer availed to true where order count is " +
							// score.getOrderCount() + " and hasSignupOffer is " + hasSignupOffer );
							score.setAvailedSignupOffer(AppConstants.getValue(true));
							score.setSignupOfferStatus(SignupOfferStatus.REDEEMED.name());
							score.setRedemptionOrderId(orderId);
						}
					} else if (LoyaltyEventType.OUTLET_VISIT.equals(type) && !updateLastOrderId) {
						score.setLastOrderTime(AppUtils.getCurrentTimestamp());
					}
					log.info(String.format("Updated loyalty points of the customer %d to %d points", customerId,
							score.getAcquiredPoints()));
					log.info(String.format("Updated cumulative points of the customer %d from %d to %d points",
							customerId, score.getCumulativePoints(), score.getCumulativePoints() + cumulativePoints));
					if (!LoyaltyEventType.REGULAR_REDEMPTION.equals(type) && !LoyaltyEventType.LOYALTY_OFFER.equals(type) ) {
						score.setCumulativePoints(score.getCumulativePoints() + cumulativePoints);
					}

				}
				return true;
			}
		}
		createLoyaltyEvent(customerId, points, type, LOYALTY_EVENT_FAILED_STATUS, orderId,
				Objects.nonNull(score) && Objects.nonNull(score.getAcquiredPoints()) ? score.getAcquiredPoints() : 0,
				0,LoyaltyFailedReason.INVALID_POINTS.name());
		return false;

	}

	private boolean createLoyaltyEvent(int customerId, int points, LoyaltyEventType type, String status,
			Integer orderId, int finalAcquiredPoints, Integer acquiredPoints,String reason) {
		try {
			Integer daysAfterLoyaltyExpire = properties.getDaysAfterLoyaltyExpire();
			LoyaltyEvents event = new LoyaltyEvents(customerId,
					points > 0 ? LoyaltyEventTransactionType.DEBIT.name() : LoyaltyEventTransactionType.CREDIT.name());
			String loyaltyEventStatus = points<0 ? LoyaltyEventStatus.CLAIMED.name():
					(LOYALTY_EVENT_SUCCESS_STATUS.equalsIgnoreCase(status) ? LoyaltyEventStatus.ACTIVE.name() :
							LoyaltyEventStatus.IN_ACTIVE.name());
			event.setTransactionCode(type.name());
			event.setTransactionCodeType(type.getType());
			event.setTransactionStatus(status);
			event.setTransactionPoints(points);
			event.setTransactionTime(AppUtils.getCurrentTimestamp());
			event.setOrderId(orderId);
			event.setOpeningBalance(acquiredPoints);
			event.setClosingBalance(finalAcquiredPoints);
			event.setReason(reason);
			event.setLoyaltyEventStatus(loyaltyEventStatus);
			if(points>0 && LOYALTY_EVENT_SUCCESS_STATUS.equalsIgnoreCase(status)){
				Date expirationDate = AppUtils.getDateAfterDays(AppUtils.getCurrentTimestamp(),daysAfterLoyaltyExpire);
				event.setExpirationTime(AppUtils.getLastDayOfMonth(expirationDate));
				event.setRedeemedPoints(0);
				event.setExpiredPoints(0);
			}
			event = loyaltyEventsDao.save(event);
			log.info("Loyalty Event Created - " + event.toString());
//			Boolean isCustomerAddedIsNew = false;
//			try {
//				String date = properties.getDateToCheckNewCustomer();
//				isCustomerAddedIsNew = customerDao.existsByAddTimeGreaterThanEqualAndCustomerId(
//						AppUtils.getDate(AppUtils.parseDate(date)),customerId);
//				log.info("isCustomerAddedIsNew : {}",isCustomerAddedIsNew);
//			}catch (Exception e){
//				log.info("Error in checking customer add time for customer id : {} and error is : {}",customerId,e);
//			}
			if(points<0 && LOYALTY_EVENT_SUCCESS_STATUS.equalsIgnoreCase(status)){
				updateLoyaltyEvents(customerId,points,orderId,event.getLoyaltyEventsId());
			}
			return true;
		} catch (Exception e) {
			log.error("Encountered error while persisting loyalty event", e);
			return false;
		}
	}

	public boolean createEventAndUpdate(int customerId, LoyaltyEventType type, int points, Integer orderId,
			boolean hasSignupOffer, boolean updateLastOrderId) {
		return createEventAndUpdateScore(customerId, type, points, points, false, orderId, hasSignupOffer,
				updateLastOrderId);
	}

	public boolean createLoyaltyEvent(int customerId, LoyaltyEventType type, int points, Integer orderId) {
		try {
			createLoyaltyEvent(customerId, points, type, LOYALTY_EVENT_SUCCESS_STATUS, orderId);
		} catch (Exception e) {
			log.error("Encountered error while persisting loyalty event", e);
			return false;
		}
		return true;
	}

	public LoyaltyScore getScore(int customerId) {
		LoyaltyScore score = loyaltyScoreDao.findByCustomerId(customerId);
		if (Objects.isNull(score)) {
			log.info("In getScore() Did not find Customer Loyalty Score with Contact ID : {}", customerId);
		}
		return score;
	}

	public boolean createLoyaltyEventAndUpdateLoyaltyScore(Integer loyaltyPoints, Integer customerId, String contactNumber,
														   LoyaltyEventType loyaltyEvent, LoyaltyScore loyaltyScore,Integer orderId){
		if (Objects.nonNull(loyaltyScore)) {
			loyaltyScore.setAcquiredPoints(loyaltyScore.getAcquiredPoints() - loyaltyPoints);
			loyaltyScore.setTotalRedeemedPoints(loyaltyScore.getTotalRedeemedPoints()!=null?
					loyaltyScore.getTotalRedeemedPoints() + loyaltyPoints: 0+loyaltyPoints);
			if(createLoyaltyEvent(customerId, loyaltyEvent, loyaltyPoints * (-1),orderId)){
				loyaltyScoreDao.save(loyaltyScore);
				return true;
			}
			else{
				return false;
			}
		}
		return false;
	}

	public boolean validateLoyaltyPoint(Integer loyaltyPoint, LoyaltyScore loyaltyScore) {
		if (Objects.nonNull(loyaltyScore)) {
			Integer currentLoyaltyScore = loyaltyScore.getAcquiredPoints();
			return currentLoyaltyScore >= loyaltyPoint;
		}
		return false;
	}

	private void createLoyaltyEvent(int customerId, int points, LoyaltyEventType type, String status, Integer orderId) {
		Integer daysAfterLoyaltyExpire = properties.getDaysAfterLoyaltyExpire();
		LoyaltyEvents event = new LoyaltyEvents(customerId,
				points > 0 ? LoyaltyEventTransactionType.DEBIT.name() : LoyaltyEventTransactionType.CREDIT.name());
		String loyaltyEventStatus = points<0 ? LoyaltyEventStatus.CLAIMED.name():
				(LOYALTY_EVENT_SUCCESS_STATUS.equalsIgnoreCase(status) ? LoyaltyEventStatus.ACTIVE.name() :
						LoyaltyEventStatus.IN_ACTIVE.name());
		event.setTransactionCode(type.name());
		event.setTransactionCodeType(type.getType());
		event.setTransactionStatus(status);
		event.setTransactionPoints(points);
		event.setTransactionTime(AppUtils.getCurrentTimestamp());
		event.setOrderId(orderId);
		event.setLoyaltyEventStatus(loyaltyEventStatus);
		if(points>0 && LOYALTY_EVENT_SUCCESS_STATUS.equalsIgnoreCase(status)){
			Date expirationDate = AppUtils.getDateAfterDays(AppUtils.getCurrentTimestamp(),daysAfterLoyaltyExpire);
			event.setExpirationTime(AppUtils.getLastDayOfMonth(expirationDate));
			event.setRedeemedPoints(0);
			event.setExpiredPoints(0);
		}
		event = loyaltyEventsDao.save(event);
		log.info("Loyalty Event Created - " + event.toString());
//		Boolean isCustomerAddedIsNew = false;
//		try {
//			String date = properties.getDateToCheckNewCustomer();
//			isCustomerAddedIsNew = customerDao.existsByAddTimeGreaterThanEqualAndCustomerId(
//					AppUtils.getDate(AppUtils.parseDate(date)),customerId);
//			log.info("isCustomerAddedIsNew : {}",isCustomerAddedIsNew);
//		}catch (Exception e){
//			log.info("Error in checking customer add time for customer id : {} and Error is : {}",customerId,e);
//		}
		if(points<0 && LOYALTY_EVENT_SUCCESS_STATUS.equalsIgnoreCase(status)){
			updateLoyaltyEvents(customerId,points,orderId,event.getLoyaltyEventsId());
		}
		log.info("Loyalty Event Created - " + event.toString());
	}

	@Override
	public boolean isLoyaltyAwarded(int customerId, Integer orderId) {
		LoyaltyEvents event = loyaltyEventsDao
				.findByCustomerIdAndOrderIdAndTransactionCodeTypeAndTransactionCodeAndTransactionStatus(orderId,
						customerId, LoyaltyEventType.OUTLET_VISIT.getType(), LoyaltyEventType.OUTLET_VISIT.name(),
						LOYALTY_EVENT_SUCCESS_STATUS);
		return Objects.nonNull(event);
	}

	@Override
	public void updateCustomerId(int customerId, Integer orderId) {
		orderDetailDao.updateOrderCustomerId(customerId, orderId);
	}

	@Override
	public LoyaltyEvents getTransactionPointsByOrderId(Integer orderId) {
		LoyaltyEvents loyaltyEvents = loyaltyEventsDao
				.findByOrderIdAndTransactionCodeTypeAndTransactionTypeAndTransactionStatus(orderId,
						TransactionType.DEBIT.name(), "Addition", LOYALTY_EVENT_SUCCESS_STATUS);
		if (Objects.isNull(loyaltyEvents)) {
			loyaltyEvents = new LoyaltyEvents();
			loyaltyEvents.setTransactionPoints(0);
		}
		return loyaltyEvents;
	}

	@Override
	public boolean isLoyaltyAwarded(int customerId, LoyaltyEventType emailVerification) {
		List<LoyaltyEvents> events = loyaltyEventsDao.findAllByCustomerIdAndTransactionCodeAndTransactionStatus(
				customerId, emailVerification.name(), LOYALTY_EVENT_SUCCESS_STATUS);
		return !CollectionUtils.isEmpty(events);
	}

	@Override
	public void handleZeroAmountOrder(Order order) {
		try {
			LoyaltyScore score = getCustomerLoyaltyScore(order.getCustomerId());
			if (Objects.nonNull(score)) {
				if (AppConstants.NO.equals(score.getAvailedSignupOffer()) && TransactionUtils.hasSignupOffer(order)
						&& Objects.nonNull(score.getOrderCount()) && score.getOrderCount() >= 1) {
					score.setAvailedSignupOffer(AppConstants.YES);
					score.setSignupOfferStatus(SignupOfferStatus.REDEEMED.name());
					score.setRedemptionOrderId(order.getOrderId());
				}
				score.setLastOrderId(order.getOrderId());
				score.setLastOrderTime(AppUtils.getCurrentTimestamp());
				score.setOrderCount(score.getOrderCount() == null ? 1 : score.getOrderCount() + 1);
				loyaltyScoreDao.save(score);
			}
		} catch (Exception e) {
			log.error("Error while updating status for redemption", e);
		}
	}

	@Override
	public LoyaltyScore getCustomerLoyaltyScore(Integer customerId) {
		return loyaltyScoreDao.findByCustomerId(customerId);
	}

	@Override
	public void awardloyalty(Set<Integer> productIds, Order order, Customer customer, OrderDetail orderDetail, boolean forceAwardLoyalty) {
		if(order.isContainsSignupOffer() || (Objects.nonNull(order.getOfferCode()) &&
				order.getOfferCode().equals(TransactionConstants.SIGNUP_OFFER_CODE))){
			order.setOrderId(orderDetail.getOrderId());
			handleFailedLoyaltyAwardOrder(order,LoyaltyFailedReason.LOYALTY_REDEMMED.name(),!productIds.isEmpty());
			return;
		}
		BigDecimal loyaltyAwardAmountThreshold =null;
		try {
			loyaltyAwardAmountThreshold = BigDecimal.valueOf(Integer.parseInt(unitCacheService.getCacheReferenceMetadata(CacheReferenceType.
					LOYALTY_AWARD_THRESHOLD_AMOUNT)));
		}catch (Exception e){
			loyaltyAwardAmountThreshold = BigDecimal.valueOf(properties.defaultLoyaltyThreshold());
		}

		if (forceAwardLoyalty || Objects.nonNull(order.getTransactionDetail().getPaidAmount())
				&& order.getTransactionDetail().getPaidAmount().compareTo(loyaltyAwardAmountThreshold) >= 0) {
			if (!productIds.isEmpty()) {
				int points = order.getAwardLoyalty() == null || order.getAwardLoyalty()
						? unitCacheService.getUnitPartnerBrandLoyalty(new UnitPartnerBrandKey(order.getUnitId(),
								order.getBrandId(), order.getChannelPartner()))
						: Integer.parseInt(UnitPartnerBrandMappingMetadataType.LOYALTY_POINTS.getDefaultValue());
				boolean awarded = updateScore(customer.getId(), LoyaltyEventType.OUTLET_VISIT, points,
						orderDetail.getOrderId(), TransactionUtils.hasSignupOffer(order), !productIds.isEmpty());
				if (points > 0 && awarded) {
					order.setPointsAcquired(points);
				}
			}
		} else if (Objects.isNull(order.isCombinedOrder()) || (Objects.nonNull(order.isCombinedOrder()) && !order.isCombinedOrder())) {
			order.setOrderId(orderDetail.getOrderId());
			handleFailedLoyaltyAwardOrder(order,LoyaltyFailedReason.ORDER_AMOUNT_WITHIN_THRESHOLD_AMOUNT.name(),!productIds.isEmpty());
		}

	}


    @Override
	public void handleFailedLoyaltyAwardOrder(Order order , String loyaltyFailedReason,Boolean updateLastOrderId){
		try {
			LoyaltyScore score = getCustomerLoyaltyScore(order.getCustomerId());
			if (Objects.nonNull(score)) {
				if (AppConstants.NO.equals(score.getAvailedSignupOffer()) && TransactionUtils.hasSignupOffer(order)
						&& Objects.nonNull(score.getOrderCount()) && score.getOrderCount() >= 1) {
					score.setAvailedSignupOffer(AppConstants.YES);
					score.setSignupOfferStatus(SignupOfferStatus.REDEEMED.name());
					score.setRedemptionOrderId(order.getOrderId());
					loyaltyScoreDao.save(score);
				}else if(Objects.isNull(score.getOrderCount()) && Boolean.TRUE.equals(updateLastOrderId)){
					score.setLastOrderId(order.getOrderId());
					score.setLastOrderTime(AppUtils.getCurrentTimestamp());
					score.setOrderCount(1);
					loyaltyScoreDao.save(score);
				}

				int points = order.getAwardLoyalty() == null || order.getAwardLoyalty()
						? unitCacheService.getUnitPartnerBrandLoyalty(new UnitPartnerBrandKey(order.getUnitId(),
						order.getBrandId(), order.getChannelPartner()))
						: Integer.parseInt(UnitPartnerBrandMappingMetadataType.LOYALTY_POINTS.getDefaultValue());
				createLoyaltyEvent(order.getCustomerId(), points, LoyaltyEventType.OUTLET_VISIT, LOYALTY_EVENT_FAILED_STATUS,
						order.getOrderId(), score.getAcquiredPoints(), score.getAcquiredPoints(),
						loyaltyFailedReason);
			}
		} catch (Exception e) {
			log.error("Error while updating status for redemption", e);
		}
	}

	public void updateLoyaltyEvents(int customerId,int points,Integer orderId,Integer transactionEventId){
		try {
			List<LoyaltyEvents> loyaltyEventsList = loyaltyEventsDao.
					findAllByCustomerIdAndLoyaltyEventStatusAndTransactionStatus(customerId, LoyaltyEventStatus.ACTIVE.name(),
							LOYALTY_EVENT_SUCCESS_STATUS);
			int pointsToBeRedeemed = Math.abs(points);
			if (!CollectionUtils.isEmpty(loyaltyEventsList)) {
				for (LoyaltyEvents event : loyaltyEventsList) {
					boolean isMaintainAuditEventLog = false;
					Integer availablePoints = Math.abs(Math.abs(event.getTransactionPoints()) - Math.abs(event.getRedeemedPoints()));
					Integer redeemedPoints;
					Integer redeemedPointsInEvent;
					if (availablePoints > pointsToBeRedeemed) {
						redeemedPoints = event.getRedeemedPoints() + pointsToBeRedeemed;
						event.setRedeemedPoints(redeemedPoints);
						availablePoints = Math.abs(Math.abs(event.getTransactionPoints()) - Math.abs(redeemedPoints));
						if (availablePoints == 0) {
							event.setLoyaltyEventStatus(LoyaltyEventStatus.REDEEMED.name());
						}
						redeemedPointsInEvent = pointsToBeRedeemed;
						pointsToBeRedeemed = 0;
						isMaintainAuditEventLog = true;
					} else {
						if (Objects.nonNull(event.getRedeemedPoints()) && event.getRedeemedPoints() != 0) {
							isMaintainAuditEventLog = true;
						}
						redeemedPoints = event.getRedeemedPoints() + availablePoints;
						if (redeemedPoints.equals(event.getTransactionPoints())) {
							event.setLoyaltyEventStatus(LoyaltyEventStatus.REDEEMED.name());
						}
						event.setRedeemedPoints(redeemedPoints);
						redeemedPointsInEvent = availablePoints;
						pointsToBeRedeemed = Math.abs(pointsToBeRedeemed - availablePoints);
					}
					event.setRedemptionTime(AppUtils.getCurrentTimestamp());
					event = loyaltyEventsDao.save(event);
					if (isMaintainAuditEventLog) {
						saveLoyaltyEventLog(event.getLoyaltyEventsId(), -1 * redeemedPointsInEvent,
								LoyaltyEventStatus.REDEEMED.name(), TransactionType.CREDIT.name(), event.getTransactionStatus(), orderId, transactionEventId);
					}
					if (pointsToBeRedeemed == 0) {
						break;
					}
				}
			}
		}catch (Exception e){
			log.info("Error in Updating loyalty events for customer id : {} and exception is : {}",customerId,e);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void expireLoyaltyPoints(){
		log.info("CRON RUNNING TO EXPIRE LOYALTY AT : {}",AppUtils.getCurrentTimestamp());
		Date currentdate = AppUtils.getCurrentDate();
		List<LoyaltyEvents> loyaltyEventsList = loyaltyEventsDao.findAllByExpirationTimeLessThanEqualAndLoyaltyEventStatus(
				currentdate,LoyaltyEventStatus.ACTIVE.name());
		if(!CollectionUtils.isEmpty(loyaltyEventsList)){
			for(LoyaltyEvents event : loyaltyEventsList){
				Integer expiredPoints = Math.abs(Math.abs(event.getTransactionPoints())-Math.abs(event.getRedeemedPoints()));
				event.setExpiredPoints(expiredPoints);
				event.setLoyaltyEventStatus(LoyaltyEventStatus.EXPIRED.name());
				LoyaltyScore score = loyaltyScoreDao.findByCustomerId(event.getCustomerId());
				Integer acquiredPoints = score.getAcquiredPoints();
				Integer finalAcquiredPoints = Math.max(0,Math.abs(acquiredPoints - expiredPoints));
				LoyaltyEvents expirationEvent = getExpiredLoyaltyEvent(event.getCustomerId(),expiredPoints,acquiredPoints,finalAcquiredPoints);
				score.setAcquiredPoints(finalAcquiredPoints);
				score.setTotalExpiredPoints(score.getTotalExpiredPoints()!=null?
						score.getTotalExpiredPoints() +expiredPoints: 0+expiredPoints);
				loyaltyScoreDao.save(score);
				event = loyaltyEventsDao.save(event);
				expirationEvent = loyaltyEventsDao.save(expirationEvent);
				saveLoyaltyEventLog(event.getLoyaltyEventsId(),-1*expiredPoints,
						LoyaltyEventStatus.EXPIRED.name(),TransactionType.CREDIT.name(), event.getTransactionStatus(),-1,
						expirationEvent.getLoyaltyEventsId());


			}
		}
	}

	private LoyaltyEvents getExpiredLoyaltyEvent(Integer customerId,Integer expiredPoints,Integer acquiredPoints,Integer finalAcquiredPoints){
		LoyaltyEvents expirationEvent = new LoyaltyEvents(customerId, LoyaltyEventTransactionType.CREDIT.name());
		expirationEvent.setTransactionCode(LoyaltyEventType.EXPIRATION.name());
		expirationEvent.setTransactionCodeType(LoyaltyEventType.EXPIRATION.getType());
		expirationEvent.setTransactionStatus(LOYALTY_EVENT_SUCCESS_STATUS);
		expirationEvent.setTransactionPoints((-1*expiredPoints));
		expirationEvent.setTransactionTime(AppUtils.getCurrentTimestamp());
		expirationEvent.setOrderId(-1);
		expirationEvent.setOpeningBalance(acquiredPoints);
		expirationEvent.setClosingBalance(finalAcquiredPoints);
		expirationEvent.setLoyaltyEventStatus(LoyaltyEventStatus.EXPIRED.name());
		return expirationEvent;
	}

	public void saveLoyaltyEventLog(LoyaltyEvents event,String transactionCodeType,String transactionType,Integer points){
		LoyaltyLogHistory logHistory = new LoyaltyLogHistory();
		logHistory.setLoyaltyEventId(event.getLoyaltyEventsId());
		logHistory.setTransactionStatus(event.getTransactionStatus());
		logHistory.setTransactionPoints(points);
		logHistory.setTransactionType(StringUtils.isEmpty(transactionType) ? event.getTransactionType() : transactionType);
		logHistory.setOrderId(event.getOrderId());
		logHistory.setTransactionCodeType(StringUtils.isEmpty(transactionCodeType) ? event.getTransactionCodeType() : transactionCodeType);
		logHistory.setTransactionTime(AppUtils.getCurrentTimestamp());
		loyaltyLogHistoryDao.save(logHistory);
	}

	public void saveLoyaltyEventLog(Integer loyaltyEventId,Integer points,String transactionCodeType,
									String transactionType, String status,Integer orderId,Integer transactionEventId){
		LoyaltyLogHistory logHistory = new LoyaltyLogHistory();
		logHistory.setLoyaltyEventId(loyaltyEventId);
		logHistory.setTransactionStatus(status);
		logHistory.setTransactionPoints(points);
		logHistory.setTransactionType(transactionType);
		logHistory.setOrderId(orderId);
		logHistory.setTransactionCodeType(transactionCodeType);
		logHistory.setTransactionTime(AppUtils.getCurrentTimestamp());
		logHistory.setTransactionEventId(transactionEventId);
		loyaltyLogHistoryDao.save(logHistory);
	}



}
