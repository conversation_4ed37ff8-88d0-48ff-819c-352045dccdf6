package com.stpl.tech.kettle.service;

import java.util.List;

import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignDetailResponse;
import com.stpl.tech.master.domain.model.CouponDetail;

public interface CampiagnManagementService {
	CouponDetail searchCoupon(String couponCode, boolean applyLimit);

	List<Integer> getLinkedCampaignIds(Integer campaignId);

	CampaignDetailResponse getCampaignByTokenAndStatus(String campaignToken, String status);

	CampaignDetail getActiveCampaignForUnitId(Integer unitId, String strategy);

	CampaignDetail getCampaignById(Integer campaignId);

}
