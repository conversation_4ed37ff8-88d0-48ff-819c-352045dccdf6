package com.stpl.tech.kettle.service;

import java.util.Set;

import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.data.kettle.OrderPaymentDetail;
import com.stpl.tech.kettle.domain.model.Order;

public interface PaymentGatewayService {

	OrderPaymentDetail getActivePaymentDetail(String externalOrderId);

	void processOrderPayment(Order order, Set<Integer> paymentIdSet,OrderDetail orderDetail,StringBuilder sb);

	boolean isGiftCardPayment(Order order);
	
}
