package com.stpl.tech.kettle.service.impl;

import java.security.Key;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

import javax.crypto.spec.SecretKeySpec;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;

import com.stpl.tech.master.core.external.acl.service.TokenDao;
import com.stpl.tech.kettle.service.TokenService;
import com.stpl.tech.kettle.util.Constants.AppConstants;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import jakarta.xml.bind.DatatypeConverter;

/**
 * Created by <PERSON><PERSON> on 24-05-2016.
 */
@Service
public class TokenServiceImpl<T extends TokenDao> implements TokenService<T> {


	@Override
	public String createToken(T object, long ttlMillis) {
		return createToken(object, ttlMillis, AppConstants.PASSPHRASE_KEY);
	}

	@Override
	public String createToken(T object, long ttlMillis, String passPhraseKey) {

		// The JWT signature algorithm used to sign the token
		SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;

		long nowMillis = System.currentTimeMillis();
		Date now = new Date(nowMillis);

		// Signing JWT with our ApiKey secret
		byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(passPhraseKey);
		Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());
		Map<String, Object> authClaims = object.createClaims();

		JwtBuilder builder = Jwts.builder().setClaims(authClaims).setIssuedAt(now).signWith(signatureAlgorithm,
				signingKey);
		// if it has been specified, add the expiration
		if (ttlMillis >= 0) {
			long expMillis = nowMillis + ttlMillis;
			Date exp = new Date(expMillis);
			builder.setExpiration(exp);
		}
		// Builds the JWT and serializes it to a compact, URL-safe string
		return builder.compact();
	}

	@Override
	public void parseToken(T object, String jwt) {
		parseToken(object, jwt, AppConstants.PASSPHRASE_KEY);
	}

	@Override
	public void parseToken(T object, String jwt, String passPhraseKey) {
		Claims claims = Jwts.parser().setSigningKey(DatatypeConverter.parseBase64Binary(passPhraseKey))
				.parseClaimsJws(jwt).getBody();
		object.parseClaims(claims);
	}

	@Override
	public Integer getLoggedInUnit(HttpServletRequest httpServletRequest) {
		try {
			String authHeader = Objects.nonNull(httpServletRequest.getHeader("auth"))
					? httpServletRequest.getHeader("auth").trim()
					: httpServletRequest.getHeader("auth");
			if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
				JWTToken jwtToken = new JWTToken();
				parseToken((T) jwtToken, authHeader);
				return Integer.valueOf(jwtToken.getUnitId());
			}
		} catch (Exception e) {
			return null;
		}
		return null;
	}
}
