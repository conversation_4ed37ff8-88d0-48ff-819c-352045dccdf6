package com.stpl.tech.kettle.service;

import java.util.Map;

import com.stpl.tech.kettle.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;

public interface ClevertapAttributesService {

	public Map<String, Object> getProfileAttributes(Customer customer);

	public Map<String, Object> getEventAttributes(OrderDetail order, CustomerInfo customer);

	public Map<String, Object> getSubscriptionAttributes(OrderDetail order, CustomerInfo customer);

}
