package com.stpl.tech.kettle.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.master.core.external.cache.ACLCache;
import com.stpl.tech.master.core.external.cache.PreAuthenticatedApiCache;
import com.stpl.tech.kettle.service.ACLService;
import com.stpl.tech.kettle.util.ACLUtil;

@Service
public class ACLServiceImpl implements ACLService {

	@Autowired
	private ACLCache aclCache;

	@Autowired
	private PreAuthenticatedApiCache preAuthenticatedApiCache;
	
	@Autowired
	private ACLUtil aclUtil;

	@Override
	public Boolean checkPermission(String requestURI, String requestMethod, String sessionKey) {
		return aclUtil.checkPermission(aclCache.getPermissions(sessionKey).get(sessionKey), requestURI,
				requestMethod);
	}

	@Override
	public boolean isPreAuthenticated(String requestUrl) {
		return aclUtil.hasPermission(preAuthenticatedApiCache.getPreAuthenticatedAPIs(),
				requestUrl);
	}

}
