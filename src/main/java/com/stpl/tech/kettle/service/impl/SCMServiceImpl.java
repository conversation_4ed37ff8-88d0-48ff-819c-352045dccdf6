package com.stpl.tech.kettle.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.gson.internal.LinkedTreeMap;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.data.kettle.HouseCostConsumableData;
import com.stpl.tech.kettle.data.kettle.HouseCostEvent;
import com.stpl.tech.kettle.data.kettle.HouseCostItemData;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.scm.WastageData;
import com.stpl.tech.kettle.domain.scm.WastageEvent;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.repository.kettle.CustomerDao;
import com.stpl.tech.kettle.repository.kettle.HouseCostConsumableDao;
import com.stpl.tech.kettle.repository.kettle.HouseCostEventDao;
import com.stpl.tech.kettle.repository.kettle.HouseCostItemDao;
import com.stpl.tech.kettle.repository.kettle.OrderDetailDao;
import com.stpl.tech.kettle.service.SCMService;
import com.stpl.tech.kettle.service.WebClientService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.adapter.JSONSerializer;
import com.stpl.tech.kettle.util.consumptionHelper.ItemConsumptionHelper;
import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.IdCodeName;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class SCMServiceImpl implements SCMService {

	private static final int WASTAGE_IN_SOCKET_TIMEOUT = 3000;
	private static final int WASTAGE_IN_CONNECTION_TIMEOUT = 3000;

	@Autowired
	private UnitCacheService unitCacheService;

	@Autowired
	private ItemConsumptionHelper itemConsumptionHelper;

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private WebClientService webClientService;

	@Autowired
	private HouseCostItemDao houseCostItemDao;

	@Autowired
	private OrderDetailDao orderDetailDao;

	@Autowired
	private CustomerDao customerDao;

	@Autowired
	private HouseCostConsumableDao houseCostConsumableDao;

	@Autowired
	private HouseCostEventDao houseCostEventDao;

	@Override
	public void checkBookWastage(Order o) throws DataUpdationException, DataNotFoundException {
		Map<Integer, Consumable> map = new HashMap<>();
		try {
			/*
			 * int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(),
			 * TransactionUtils
			 * .isPartnetOrder(getMasterDataCache().getChannelPartner(o.getChannelPartner())
			 * .getType()));
			 */
			int deliveryUnitId = unitCacheService.getDeliveryUnit(o.getUnitId(), o.getChannelPartner(), o.getBrandId(),
					TransactionUtils.isCODOrder(o.getSource()));
			itemConsumptionHelper.calculateConsumption(o, map, deliveryUnitId);
		} catch (Exception e) {
			log.error("Error while calculating Consumption", e);
			throw e;
		}
		if (Objects.nonNull(o) && map.values().size() > 0) {
			WastageEvent event = getWastageEvent(o, map.values(), true);
			List<String> errors = verifyWastageBookingInSumo(event);
			if (errors != null && errors.size() > 0) {
				String errorMsg = String.join("<br/>", errors);
				throw new DataUpdationException("Error while verifying the inventory price data for unit "
						+ o.getUnitId() + "<br/>" + errorMsg);
			}
		}
	}

	@Override
	public WastageEvent getWastageEvent(Order o, Collection<Consumable> values, boolean forVerification)
			throws DataNotFoundException {
		WastageEvent event = new WastageEvent();
		event.setBusinessDate(AppUtils.getBusinessDate());
		String reason = null;
		if (TransactionUtils.isSpecialOrder(o)) {
			event.setGeneratedBy(o.getEmployeeId());
			event.setGenerationTime(o.getBillingServerTime());
			Integer reasonId = -1;
			for (OrderItem item : o.getOrders()) {
				if (item.getComplimentaryDetail().getReasonCode() != AppConstants.COMPLEMENTARY_CODE_COMBO) {
					reasonId = item.getComplimentaryDetail().getReasonCode();
					break;
				}
			}
			if (reasonId != -1) {
				for (IdCodeName code : unitCacheService.getAllComplementaryCodes(AppConstants.ACTIVE)) {
					if (code.getId() == reasonId) {
						reason = code.getCode();
						break;
					}
				}
			}
		} else if (!TransactionUtils.isSpecialOrder(o) && o.getCancellationDetails() != null) {
			if (!forVerification) {
				event.setGeneratedBy(o.getCancellationDetails().getGeneratedBy());
				event.setGenerationTime(o.getCancellationDetails().getActionTime());
			}
			reason = AppConstants.ORDER_CANCELLATION;
		} else {
			return null;
		}
		event.setKettleReason(reason);
		event.setLinkedKettleIdType("HouseCostEventId");
		event.setStatus("SETTLED");
		event.setType("PRODUCT");
		event.setUnitId(o.getUnitId());
		for (Consumable c : values) {
			WastageData d = new WastageData();
			d.setComment(reason);
			d.setProductId(c.getProductId());
			d.setQuantity(c.getQuantity());
			event.getItems().add(d);
		}

		return event;
	}

	@Override
	public List<String> verifyWastageBookingInSumo(WastageEvent event) throws DataUpdationException {
		try {
			List<WastageEvent> events = Arrays.asList(event);
			List<?> results = webClientService.callWebServiceWithTimeout(List.class,
					properties.verifyPriceDataURL(), events, WASTAGE_IN_SOCKET_TIMEOUT, WASTAGE_IN_CONNECTION_TIMEOUT);
			LinkedTreeMap<String, Object> linkedTreeMap = (LinkedTreeMap<String, Object>) results.get(0);
			Object data = linkedTreeMap.get("errors");
			return data == null ? null : ((List<String>) data);
		} catch (Exception e) {
			String message = "Error while adding verifying order wastage in sumo for wastage event :"
					+ JSONSerializer.toJSON(event);
			log.error(message, e);
//            SlackNotificationService.getInstance().sendNotification(getEnvironmentProperties().getEnvironmentType(),
//                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.SYSTEM_ERRORS, message);
			throw new DataUpdationException("Error while Verifying Wastage", e);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void bookWastage(Order o) throws DataNotFoundException {
		Map<Integer, Consumable> map = new HashMap<>();
		try {
			int deliveryUnitId = unitCacheService.getDeliveryUnit(o.getUnitId(), o.getChannelPartner(), o.getBrandId(),
					TransactionUtils.isCODOrder(o.getSource()));
			itemConsumptionHelper.calculateConsumption(o, map, deliveryUnitId);
		} catch (Exception e) {
			log.error("Error while calculating Consumption", e);
			throw e;
		}
		if (Objects.nonNull(o) && map.values().size() > 0) {
			int wastageEventId = addCost(o, map.values());
			WastageEvent event = getWastageEvent(o, map.values(), false);
			persistWastageToSumo(event, wastageEventId);
		}
	}

	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	private int addCost(Order o, Collection<Consumable> values) {
		HouseCostEvent event = new HouseCostEvent();
		event.setKettleOrderId(o.getOrderId());
		if (TransactionUtils.isSpecialOrder(o)) {
			Integer reasonId = -1;
			for (OrderItem item : o.getOrders()) {
				if (item.getComplimentaryDetail().getReasonCode() != AppConstants.COMPLEMENTARY_CODE_COMBO) {
					reasonId = item.getComplimentaryDetail().getReasonCode();
					break;
				}
			}
			event.setComplimentaryReasonId(reasonId);
			event.setWastageType("COMPLETE_WASTAGE");
			if (Objects.isNull(o.getLinkedOrderId())) {
				try {
					if (Objects.nonNull(o.getLinkedOrderId())) {
						OrderDetail oldOrder = orderDetailDao.findById(o.getLinkedOrderId()).get();
						event.setCustomerId(oldOrder.getCustomerId());
						if (oldOrder.getCustomerId() <= 5) {
							event.setCustomerName(oldOrder.getCustomerName());
						} else {
							if (Objects.nonNull(oldOrder.getCustomerId())) {
								CustomerInfo customer = customerDao.findById(oldOrder.getCustomerId()).get();
								event.setCustomerName(customer.getFirstName());
							}
						}
					}
				} catch (Exception e) {
				}
			}
		} else if (!TransactionUtils.isSpecialOrder(o) && Objects.nonNull(o.getCancellationDetails())) {
			event.setCancelApprovedBy(o.getCancellationDetails().getApprovedBy());
			event.setCancelationReason(o.getCancellationDetails().getReason());
			event.setCancellationReasonId(o.getCancellationDetails().getReasonId());
			event.setCancelledBy(o.getCancellationDetails().getGeneratedBy());
			event.setWastageType(o.getCancellationDetails().getBookedWastage());
			event.setCustomerId(o.getCustomerId());
			event.setCustomerName(o.getCustomerName());
		} else {
			return -1;
		}
		event.setEmpId(o.getEmployeeId());
		event.setGenerationTime(AppUtils.getCurrentTimestamp());
		event.setOrderSource(o.getSource());
		event.setTotalAmount(o.getTransactionDetail().getTotalAmount());
		event.setUnitId(o.getUnitId());
		event = houseCostEventDao.save(event);
		if (Objects.nonNull(o.getOrderId())) {
			OrderDetail order = orderDetailDao.findById(o.getOrderId()).get();
			order.setWastageKettleId(event.getOrderId());
		}
		List<HouseCostItemData> items = new ArrayList<>();
		for (OrderItem i : o.getOrders()) {
			HouseCostItemData item = new HouseCostItemData();
			// TODO add cost as and when the order is punched, This needs to be
			// changed with price calculation module
			item.setDimension(i.getDimension());
			item.setLinkedOrderItemId(i.getItemId());
			item.setOrderDetail(event);
			item.setPrice(i.getPrice());
			item.setProductId(i.getProductId());
			item.setProductName(i.getProductName());
			item.setQuantity(new BigDecimal(i.getQuantity()));
			item.setRecipeId(i.getRecipeId());
			item.setTotalAmount(i.getTotalAmount());
			item = houseCostItemDao.save(item);
			items.add(item);
		}
		event.setOrderItems(items);
		List<HouseCostConsumableData> consumables = new ArrayList<>();
		for (Consumable i : values) {
			HouseCostConsumableData item = new HouseCostConsumableData();
			// TODO add cost as and when the order is punched, This needs to be
			// changed with price calculation module
			item.setOrderDetail(event);
			item.setUom(i.getUom());
			item.setProductId(i.getProductId());
			item.setProductName(i.getName());
			item.setQuantity(i.getQuantity());
			item = houseCostConsumableDao.save(item);
			consumables.add(item);
		}
		event.setConsumableItems(consumables);
		return event.getOrderId();
	}

	@Override
	public void persistWastageToSumo(WastageEvent event, int wastageEventId) {
		if (Objects.isNull(event)) {
			return;
		}
		event.setLinkedKettleId(wastageEventId);
		try {
			List<WastageEvent> events = new ArrayList<>();
			events.add(event);
			List<?> results = webClientService.callWebServiceWithTimeout(List.class, properties.addWastageURL(), events,
					WASTAGE_IN_SOCKET_TIMEOUT, WASTAGE_IN_CONNECTION_TIMEOUT);
			@SuppressWarnings("unchecked")
			LinkedTreeMap<String, Object> linkedTreeMap = (LinkedTreeMap<String, Object>) results.get(0);
			setWastageSumoId(wastageEventId, ((Double) linkedTreeMap.get("wastageId")).intValue());
		} catch (Exception e) {
			String message = "Error while adding cancelled order wastage to sumo for wastage event id :"
					+ wastageEventId;
			log.error(message, e);
			/*
			 * SlackNotificationService.getInstance().sendNotification(
			 * getEnvironmentProperties().getEnvironmentType(),
			 * ApplicationName.KETTLE_SERVICE.name(), SlackNotification.SYSTEM_ERRORS,
			 * message);
			 */
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void setWastageSumoId(int orderId, int sumoId) {
		HouseCostEvent event = houseCostEventDao.findById(orderId).get();
		event.setWastageSumoId(sumoId);
		houseCostEventDao.save(event);
	}

}
