package com.stpl.tech.kettle.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.util.Constants.AppConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.cache.DeliveryPartnerPriorityMappingCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.cache.impl.DeliveryServiceCacheImpl;
import com.stpl.tech.kettle.cache.impl.UnitDeliverySelectionCache;
import com.stpl.tech.kettle.converter.Converters;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.data.kettle.DeliveryDetail;
import com.stpl.tech.kettle.data.kettle.DeliveryStatusEvent;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.delivery.strategy.DeliveryRouter;
import com.stpl.tech.kettle.delivery.strategy.SelectionStrategy;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.domain.model.DeliveryStatus;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.TransitionStatus;
import com.stpl.tech.kettle.repository.kettle.DeliveryDetailDao;
import com.stpl.tech.kettle.repository.kettle.DeliveryStatusEventDao;
import com.stpl.tech.kettle.repository.kettle.OrderDetailDao;
import com.stpl.tech.kettle.service.DeliveryRequestService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Unit;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class DeliveryRequestServiceImpl implements DeliveryRequestService {

    @Autowired
    private UnitCacheService unitCacheService;
    @Autowired
    private DeliveryPartnerPriorityMappingCache unitToDeliveryMappingsCache;

    @Autowired
    private SelectionStrategy<UnitDeliverySelectionCache, Integer> selectionStrategy;

    @Autowired
    private DeliveryDetailDao deliveryDetailDao;

    @Autowired
    private OrderDetailDao orderDetailDao;

    @Autowired
    private DeliveryServiceCacheImpl deliveryServiceCache;

    @Autowired
    private DeliveryStatusEventDao deliveryStatusEventDao;

    @Autowired
    private DeliveryRouter router;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderInfo addDeliveryDetails(OrderInfo info, Unit unit) {
        DeliveryResponse deliveryResponse = createDeliveryRequest(info, unit);
        return updateOrderWithDelivery(info, deliveryResponse, convert(deliveryResponse));
    }




    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public DeliveryResponse createDeliveryRequest(OrderInfo orderInfo, Unit unit) {
        orderInfo.setUnit(unit);
        log.info("unit productId for orderInfo is {} ", orderInfo.getUnit().getId());
        DeliveryResponse response = createDeliveryPartnerRequest(orderInfo, 0);
        saveDeliveryDetails(response, orderInfo);
        return response;
    }

    private DeliveryResponse createDeliveryPartnerRequest(OrderInfo orderObject, Integer count) {
        int unitId = orderObject.getOrder().getUnitId();
        Order order = orderObject.getOrder();
        log.info("Unit is {} and orderId is {}", unitId, order.getOrderId());
        int partnersForUnit = unitToDeliveryMappingsCache.getRetriesForUnit(unitId);
        if (count >= partnersForUnit) {
            return null;
        } else {
            Integer partnerId = getNextPartnerFromCache(unitId);
            DeliveryResponse deliveryResponse;
            try {
                if (checkIfAutomated(partnerId) && order.getSettlements() != null
                        && checkCashOrderEligibility(order.getSettlements(), partnerId)) {

                    deliveryResponse = router.createDeliveryInSystem(orderObject, getMappingForPartner(partnerId));
                    if (deliveryResponse == null || (deliveryResponse
                            .getDeliveryStatus() == DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus())) {
                        return createDeliveryPartnerRequest(orderObject, count + 1);
                    } else {
                        refreshDeliveryCache(unitId);
                        return deliveryResponse;
                    }
                } else {
                    log.info("Delivery Partner {} is currently not automated via API", partnerId);
                    return createDeliveryPartnerRequest(orderObject, count + 1);
                }
            } catch (CloneNotSupportedException e) {
                log.error("Cannot create clone of object", e);
            }
            return null;
        }
    }

    private void refreshDeliveryCache(int unitId) {
        selectionStrategy.refreshDeliveryCache(unitToDeliveryMappingsCache.getSelectionCacheForUnit(unitId));
    }

    private Map<String, String> getMappingForPartner(Integer partnerId) {
        return deliveryServiceCache.getApiProfiles().get(partnerId);
    }

    private void saveDeliveryDetails(DeliveryResponse response, OrderInfo orderInfo) {
        if (response != null && response.getDeliveryTaskId() != null) {
            response.setOrderId(orderInfo.getOrder().getOrderId());
            saveDeliveryDetails(orderInfo.getOrder().getUnitId(), response);
        }
    }

    @Override
    public synchronized DeliveryResponse saveDeliveryDetails(int unitId, DeliveryResponse response) {
        List<DeliveryDetail> details =  deliveryDetailDao.findByGeneratedOrderIdAndDeliveryStatusOrderByStatusUpdateTimeDesc(response.getGeneratedOrderId(), AppConstants.CANCELLED);
        DeliveryDetail detail = null;
        if (details != null && details.size() > 0) {
            markAdditionalDetailsAsCancelled(details);
            detail = details.get(0);
            updateDetails(detail, response);
        } else {
            detail = Converters.convert(response);
            generateDeliveryStatusEvent(response, DeliveryStatus.valueOf(detail.getDeliveryStatus()),
                    AppUtils.getCurrentTimestamp(), TransitionStatus.SUCCESS);
            log.info("deliveryStatus is {} and value of enum is {}", response.getDeliveryStatus(),
                    DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus());
            if (response.getDeliveryStatus() != DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus()) {
                deliveryDetailDao.save(detail);

            }
        }

        return Converters.convert(unitId, detail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderInfo addDeliveryDetails(OrderInfo info, Unit unit, IdCodeName deliveryPartner) {
        DeliveryResponse delivery = createDeliveryRequest(info, unit, deliveryPartner.getId());
        return updateOrderWithDelivery(info, delivery, deliveryPartner);
    }

    private DeliveryResponse createDeliveryRequest(OrderInfo info, Unit unit, int deliveryPartnerId) {
        info.setUnit(unit);
        log.info("unit productId for orderInfo is {} ", info.getUnit().getId());
        try {
            if (checkCashOrderEligibility(info.getOrder().getSettlements(), deliveryPartnerId)) {
                DeliveryResponse response = router.createDeliveryInSystem(info,
                        getMappingForPartner(deliveryPartnerId));
                saveDeliveryDetails(response, info);
                return response;
            }
        } catch (CloneNotSupportedException e) {
            log.error("Unable to create delivery request since cloning is not supported");
        }
        return null;
    }

    private void generateDeliveryStatusEvent(DeliveryResponse response, DeliveryStatus previousStatus,
                                             Date previousUpdateTime, TransitionStatus transition) {
        DeliveryStatusEvent deliveryStatusEvent = new DeliveryStatusEvent();
        deliveryStatusEvent.setDeliveryPartnerId(response.getDeliveryPartnerId());
        deliveryStatusEvent.setFromStatus(DeliveryStatus.get(previousStatus.getDeliveryStatus()).toString());
        deliveryStatusEvent.setDeliveryTaskId(response.getDeliveryTaskId());
        deliveryStatusEvent.setToStatus(DeliveryStatus.get(response.getDeliveryStatus()).toString());
        deliveryStatusEvent.setUpdateTimeStamp(AppUtils.getCurrentTimestamp());
        deliveryStatusEvent.setStatusStartTime(previousUpdateTime);
        deliveryStatusEvent.setOrderId(response.getOrderId());
        deliveryStatusEvent.setTransitionStatus(transition.name());

    }

    private void markAdditionalDetailsAsCancelled(List<DeliveryDetail> details) {
        if (details != null && details.size() > 1) {
            for (int i = 1; i < details.size(); i++) {
                DeliveryDetail d = details.get(i);
                d.setDeliveryStatus(DeliveryStatus.CANCELLED.name());
            }
        }
    }

    private void updateDetails(DeliveryDetail details, DeliveryResponse updateObject) {

        details.setDeliveryBoyName(updateObject.getDeliveryBoyName());
        details.setDeliveryBoyPhoneNum(updateObject.getDeliveryBoyPhoneNum());
        details.setDeliveryBoyId(updateObject.getDeliveryBoyId());
        details.setStatusUpdateTime(updateObject.getStatusUpdateTime());
        details.setDeliveryStatus(DeliveryStatus.get(updateObject.getDeliveryStatus()).name());
        details.setAllotedNo(updateObject.getAllotedNo());
    }

    private Integer getNextPartnerFromCache(Integer unitId) {
        Integer selection = selectionStrategy
                .getSelectionForTicket(unitToDeliveryMappingsCache.getSelectionCacheForUnit(unitId));
        return selection;
    }

    private boolean checkCashOrderEligibility(List<Settlement> orderSettlements, Integer partnerId) {
        boolean flag = true;
        boolean isCashOrder = (!orderSettlements.isEmpty() && orderSettlements.get(0).getMode() == 1);
        if (isCashOrder) {
            String partnerStatus = deliveryServiceCache.getCashEligibleStatus(partnerId);
            flag = (partnerStatus == null || partnerStatus.equalsIgnoreCase("Y"));
        }
        return flag;
    }

    @Override
    public Boolean checkIfAutomated(Integer deliveryPartnerId) {
        if (deliveryServiceCache.getAutomatedPartnerMap().containsKey(deliveryPartnerId)) {
            return deliveryServiceCache.getAutomatedPartnerMap().get(deliveryPartnerId).equals("Y");
        }
        return false;
    }

    private OrderInfo updateOrderWithDelivery(OrderInfo info, DeliveryResponse delivery, IdCodeName deliveryPartner) {
        DeliveryDetail deliveryDetails = Converters.convert(delivery);
        // update delivery details in cache & database
        info = updateDeliveryDetails(info, deliveryDetails, deliveryPartner);
        return info;
    }

    private OrderInfo updateDeliveryDetails(OrderInfo info, DeliveryDetail deliveryDetails,
                                            IdCodeName deliveryPartner) {
        if (deliveryDetails != null && deliveryDetails.getDeliveryStatus() != null && !DeliveryStatus
                .valueOf(deliveryDetails.getDeliveryStatus()).equals(DeliveryStatus.REQUEST_DECLINED)) {

            info.setDeliveryPartner(deliveryPartner);
            info.setDeliveryDetails(Converters.convert(info.getOrder().getUnitId(), deliveryDetails));
        } else {
            info.setDeliveryPartner(unitCacheService.getDeliveryPartnerById(1));
            info.setDeliveryDetails(null);
        }
        Boolean updateInDB = updateOrderInfo(info); // in database
        log.info("Order updation status in database after creation :::: {}", updateInDB);

        return info;
    }

    private boolean updateOrderInfo(OrderInfo info) {
        try {
            int deliveryPartnerId = info.getDeliveryPartner().getId();
            Order order = info.getOrder();
            order.setDeliveryPartner(deliveryPartnerId);
            return updateOrderForDeliveryPartner(order);
        } catch (Exception e) {
            log.error("error in updating delivery partner productId for orderId :::: {}",
                    info.getOrder().getGenerateOrderId());
        }
        return false;
    }

    private Boolean updateOrderForDeliveryPartner(Order order) {
        OrderDetail orderDetail = orderDetailDao.findByGeneratedOrderId(order.getGenerateOrderId());
        orderDetail.setDeliveryPartnerId(order.getDeliveryPartner());
        orderDetailDao.save(orderDetail);
        return orderDetail.getDeliveryPartnerId() == order.getDeliveryPartner();
    }

    public IdCodeName convert(DeliveryResponse delivery) {
        if (delivery != null && delivery.getDeliveryPartnerId() != 0) {
            return unitCacheService.getDeliveryPartnerById(delivery.getDeliveryPartnerId());
        } else {
            return unitCacheService.getDeliveryPartnerById(1);
        }
    }
}
