package com.stpl.tech.kettle.service;


import com.stpl.tech.kettle.data.kettle.CashBackOfferData;
import com.stpl.tech.kettle.domain.model.CashBackOfferDTO;

import java.util.Map;

public interface CashBackOfferCache {
    public CashBackOfferDTO getOfferDataForUnit(int unitId);

    public void clearCashBackOfferCache();

    public Map<Integer, CashBackOfferDTO> getCashBackOfferCache();

    public void updateCashBackOfferCache(Map<Integer, CashBackOfferDTO> newMap);

    public CashBackOfferDTO convertToCahBackOfferDTO(CashBackOfferData data);
}
