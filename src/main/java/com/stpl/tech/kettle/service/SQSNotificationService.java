package com.stpl.tech.kettle.service;

import java.io.Serializable;

import javax.jms.JMSException;

import com.amazonaws.regions.Regions;

public interface SQSNotificationService {
	<T extends Serializable> void publishToSQS(String env, T response, String queueNameSuffix)
			throws JMSException;

	<T extends Serializable> void publishToSQS(String env, T response, String queueNameSuffix, Regions region)
			throws JMSException;

	<T extends Serializable> void publishToSQSFifo(String env, T event, String queueNameSuffix, Regions region)
			throws JMSException;

	<T extends Serializable> void publishToSQSFifo(String env, T event, String queueNameSuffix, Regions region,String messageGrpId)
			throws JMSException;
}
