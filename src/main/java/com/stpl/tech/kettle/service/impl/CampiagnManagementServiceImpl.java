package com.stpl.tech.kettle.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.converter.Converters;
import com.stpl.tech.kettle.data.master.CampaignDetailData;
import com.stpl.tech.kettle.data.master.CouponDetailData;
import com.stpl.tech.kettle.data.master.CouponDetailMappingData;
import com.stpl.tech.kettle.domain.model.CampaignReachType;
import com.stpl.tech.kettle.mapper.CampaignDetailMapper;
import com.stpl.tech.kettle.repository.master.CampaignDetailDataDao;
import com.stpl.tech.kettle.repository.master.CouponDetailDataDao;
import com.stpl.tech.kettle.service.CampiagnManagementService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignDetailResponse;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class CampiagnManagementServiceImpl implements CampiagnManagementService {

	private static final int MAX_MAPPING_COUNT = 10;

	@Autowired
	private UnitCacheService unitCacheService;

	@Autowired
	private CampaignDetailMapper campaignDetailMapper;

	@Autowired
	private CampaignDetailDataDao campaignDetailDataDao;

	@Autowired
	private CouponDetailDataDao couponDetailDataDao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public CampaignDetail getCampaignById(Integer campaignId) {
		CampaignDetailData campaignDetailData = campaignDetailDataDao.findByCampaignIdAndCampaignStatus(campaignId,
				AppConstants.ACTIVE);
		return campaignDetailMapper.toDomain(campaignDetailData);
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public CouponDetail searchCoupon(String couponCode, boolean applyLimit) {
		try {
			CouponDetailData o = couponDetailDataDao.findByCouponCode(couponCode);
			if (applyLimit) {
				int lastIndex = o.getMappings().size();
				int startIndex = o.getMappings().size() > MAX_MAPPING_COUNT ? lastIndex - MAX_MAPPING_COUNT : 0;
				List<CouponDetailMappingData> newMappingData = o.getMappings().subList(startIndex, lastIndex);
				o.setMappings(newMappingData);
			}
			return Objects.isNull(o) ? null : Converters.convert(o, true, true);
		} catch (Exception e) {
			log.info("Error while getting Coupon Code: {} , {}", couponCode, e.getMessage());
		}
		return null;
	}

	@Override
	public List<Integer> getLinkedCampaignIds(Integer campaignId) {
		Date currentDate = AppUtils.getCurrentTimestamp();
		return campaignDetailDataDao.getLinkedCampaignIds(campaignId, AppConstants.ACTIVE, currentDate, currentDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public CampaignDetailResponse getCampaignByTokenAndStatus(String campaignToken, String status) {
		CampaignDetailData campaignDetailData = campaignDetailDataDao
				.findByCampaignTokenAndCampaignStatus(campaignToken, status);
		CampaignDetail detail = campaignDetailMapper.toDomain(campaignDetailData);
		if (Objects.nonNull(detail)) {
			CampaignDetailResponse response = new CampaignDetailResponse();
			response.setCampaignId(detail.getCampaignId());
			response.setUtmHeading(detail.getUtmHeading());
			response.setUtmDesc(detail.getUtmDesc());
			response.setUtmImageUrl(detail.getUtmImageUrl());
			response.setImage1(detail.getImage1());
			response.setImage2(detail.getImage2());
			response.setImage3(detail.getImage3());
			response.setStartDate(detail.getStartDate());
			response.setEndDate(detail.getEndDate());
			response.setUnitId(detail.getLaunchUnitId());
			response.setUnitName(Objects.nonNull(detail.getLaunchUnitId())
					? unitCacheService.getUnitById(detail.getLaunchUnitId()).getName()
					: null);
			response.setOfferStartDate(detail.getCafeLaunchDate());
			if (Objects.nonNull(detail.getLandingPageDesc()) && detail.getLandingPageDesc().length() > 0) {
				response.setLandingPageDescription(detail.getLandingPageDesc());
			} else {
				response.setLandingPageDescription("Signup for relaxing free chai");
			}
			return response;
		} else {
			log.info("NEO :: No campaign detail found for campaign token :: {} and status :: {}", campaignToken,
					status);
			return null;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public CampaignDetail getActiveCampaignForUnitId(Integer unitId, String strategy) {
		CampaignDetail campaignByUnitId = campaignDetailMapper.toDomain(campaignDetailDataDao
				.getActiveCampaignByUnitId(unitId, AppConstants.ACTIVE, strategy, AppConstants.YES));
		if (campaignByUnitId == null) {
			UnitBasicDetail detail = unitCacheService.getUnitBasicDetailById(unitId);
			campaignByUnitId = campaignDetailMapper
					.toDomain(campaignDetailDataDao.getActiveCampaignByUnitRegion(detail.getRegion(),
							AppConstants.ACTIVE, strategy, AppConstants.YES, CampaignReachType.REGION_SPECIFIC.name()));
			if (campaignByUnitId == null) {
				campaignByUnitId = campaignDetailMapper.toDomain(campaignDetailDataDao.getActiveCampaignBySystem(
						strategy, AppConstants.ACTIVE, AppConstants.YES, CampaignReachType.SYSTEM_SPECIFIC.name()));
			}
		}
		return campaignByUnitId;
	}
}
