package com.stpl.tech.kettle.service.impl;

import com.stpl.tech.kettle.data.kettle.CashBackOfferData;
import com.stpl.tech.kettle.domain.model.CashBackOfferDTO;
import com.stpl.tech.kettle.repository.kettle.CashBackOfferDao;
import com.stpl.tech.kettle.service.CashBackOfferCache;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.KeyMutexFactory;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class CashBackOfferCacheImpl implements CashBackOfferCache {

    private Map<Integer, CashBackOfferDTO> cashBackOfferDTOMap = new HashMap<>();

    @Autowired
    private CashBackOfferDao cashBackOfferDao;

    @Autowired
    private KeyMutexFactory keyMutexFactory;

    @Override
    public CashBackOfferDTO getOfferDataForUnit(int unitId) {
        try {
            if(!cashBackOfferDTOMap.containsKey(unitId)){
                synchronized (keyMutexFactory.getMutex("CASH_BACK_OFFER_"+unitId)){
                    if(!cashBackOfferDTOMap.containsKey(unitId)){
                        CashBackOfferDTO data = searchForOfferInUnitScope(unitId);
                        cashBackOfferDTOMap.put(unitId, data);
                    }
                }
            }
            return cashBackOfferDTOMap.get(unitId);
        }catch (Exception e){
            log.info("Error while getting cashback offer detail for unit id : {}",unitId);
            return null;
        }
    }

    @PostConstruct
    @Scheduled(cron = "0 31 05 * * *", zone = "GMT+05:30")
    @Override
    public void clearCashBackOfferCache() {
        cashBackOfferDTOMap = new HashMap<>();
    }

    @Override
    public Map<Integer, CashBackOfferDTO> getCashBackOfferCache() {
        return cashBackOfferDTOMap;
    }

    @Override
    public void updateCashBackOfferCache(Map<Integer, CashBackOfferDTO> newMap) {
        cashBackOfferDTOMap = newMap;
    }

    private CashBackOfferDTO searchForOfferInUnitScope(int unitId){
        return convertToCahBackOfferDTO(
                cashBackOfferDao.findAllByOfferScope(
                        unitId,
                        AppConstants.ACTIVE,
                        AppUtils.getBusinessDate()));
    }

    @Override
    public CashBackOfferDTO convertToCahBackOfferDTO(CashBackOfferData data){
        if(Objects.isNull(data)){
            return null;
        }
        return CashBackOfferDTO.builder()
                .unitId(data.getUnitId())
                .lagDays(data.getLagDays())
                .validityInDays(data.getValidityInDays())
                .offerStartDate(data.getOfferStartDate())
                .offerEndDate(data.getOfferEndDate())
                .cashbackPercentage(data.getCashbackPercentage())
                .maxNumberOfOrder(data.getMaxNumberOfOrder())
                .offerStatus(data.getOfferStatus())
                .build();
    }
}
