package com.stpl.tech.kettle.service;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.data.kettle.OrderMetricDetailData;
import com.stpl.tech.kettle.domain.model.*;
import com.stpl.tech.kettle.exceptions.*;
import com.stpl.tech.master.domain.model.DreamFolksVoucherDetails;
import com.stpl.tech.master.domain.model.Unit;

import javax.jms.JMSException;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface OrderManagementService {

	OrderInfo processPlainOrder(Order order, boolean includeReceipts, boolean addMetadata,
			OrderNotification orderNotification, String randomString, OrderUnitMapping orderUnitMapping)
			throws DataUpdationException, CardValidationException, DataNotFoundException, TemplateRenderingException,
			JMSException;

	OrderInfo processOrder(OrderDomain orderSessionDetail) throws DataNotFoundException, TemplateRenderingException,
			CardValidationException, DataUpdationException, JMSException, OfferValidationException;

	OrderInfo processLoyaltyWalletOrder(String contactNumber,Integer loyaltyPoints,Integer walletAmount,Integer extraAmount);

	OrderInfo getOrderInfo(Order order, boolean includeReceipts, String customerName);

	OrderInfo generateOrderInfo(int orderId, boolean includeRecpushDataToThirdPartyAnalyticseipts, String customerName, Order order)
			throws DataNotFoundException, TemplateRenderingException;

	boolean isSubscriptionOrder(Order order);

	Set<Integer> getProductIds(Order order);

	public Order validateOrder(OrderDomain orderDetail, OrderUnitMapping orderUnitMapping) throws DataUpdationException;

	public OrderInfo createWalletOrder(Order order, WalletOrder wallet, OrderDomain orderDetail, String randomString,
			OrderUnitMapping orderUnitMapping) throws CardValidationException, DataNotFoundException,
			DataUpdationException, TemplateRenderingException, JMSException, OfferValidationException;

	void complimentryValidation(Order order);

	boolean isSubscriptionPresent(Order order);

	boolean hasGiftCard(Order order);

	void validateGiftCardAndSubscription(Order order) throws CardValidationException;

	boolean isRoutedToAssembly(Unit unit, Order order, boolean hasGiftcards, boolean hasSelectOrder);

	Order applyFreeItemOffer(boolean newCustomer, Order order);

	void setDeliveryDetail(OrderInfo orderInfo);

	void updateOfferCode(Order order, Integer orderId, OrderInfo info) throws Exception;

	void addDeliveryPartner(Integer orderId, Order order, OrderInfo info);

	public OrderInfo createSubscrptionOrder(Order order, WalletOrder wallet, OrderDomain orderDetail,
			String randomString, OrderUnitMapping orderUnitMapping)
			throws DataNotFoundException, WebServiceCallException, DataUpdationException, CardValidationException,
			TemplateRenderingException, JMSException, OfferValidationException;


	Order extractWalletOrder(Order order, WalletOrderType type, WalletOrder wallet);

	Order extractSelectOrder(Order order);

	OrderInfo createDirectWalletOrder(Order order, WalletOrder wallet, OrderDomain orderDetail, String randomString,
			OrderUnitMapping orderUnitMapping) throws DataNotFoundException, WebServiceCallException,
			DataUpdationException, CardValidationException, TemplateRenderingException, JMSException;

	OrderInfo createMicroWalletOrder(Order walletOrder, Order plainOrder, OrderDomain orderDetail, String randomString,
			OrderUnitMapping orderUnitMapping, boolean deductFromWallet)
			throws DataNotFoundException, WebServiceCallException, DataUpdationException, CardValidationException,
			TemplateRenderingException, JMSException;

	boolean validateWalletOrder(WalletOrder wallet, Order order) throws OfferValidationException;

	Boolean isPrioritizedOrder(Integer kettleOrderId);

	OrderInfo generateReciept(OrderInfo info);

	public void pushDataToThirdPartyAnalytics(OrderInfo info, Map<Integer, OrderNotification> orderNotificationMap);

    List<LastNOrderResponse> getLastNOrders(Integer size, Integer unitId);

	void publishToOrderInfoCacheSQS(String env, OrderInfo info) throws JMSException;

	public OrderMetricDetailData createOrderMetricData(OrderMetricDomain orderMetricDomain);
	public void publishAllOrders(List<Integer> orderIdList) ;

	public void tableCheckout(TableSettlement settlement) throws DataUpdationException;

	public void createOrderMetricData(WebOrderMetricDomain orderMetricDomain);
	public TableViewOrder createTableOrder(TableViewOrder order) throws DataUpdationException, DataNotFoundException, JMSException, TemplateRenderingException, CardValidationException;

	public TableResponse getTableOrderRecipt(OrderDomain orderDomain,String serviceChargeApplied);

	public void tableCheckoutNew(TableSettlement settlement) throws DataUpdationException;
	public void setFeedbackUrl(TableViewOrder tableViewOrder,TableResponse tableResponse);

    Boolean deductWalletAmountForOrders(List<Integer> orderIds);

	public OrderInfo processDohfulOrder(OrderDomain orderDetail) throws DataNotFoundException, TemplateRenderingException,
			CardValidationException, DataUpdationException, JMSException, OfferValidationException;

	void pushDataToCleverTapForPartner(OrderInfo info, Map<Integer, OrderNotification> orderNotificationMap);

	// DreamFolks transaction detail methods
	void addDreamFolksTransactionDetail(OrderInfo info, DreamFolksVoucherDetails dreamFolksVoucherDetails);

	/**
	 * Updates DreamFolks transaction detail orderId using voucher code
	 * @param voucherCode The voucher code to update
	 * @param orderId The order ID to set
	 * @return true if update was successful, false otherwise
	 */
	boolean updateDreamFolksOrderIdByVoucherCode(String voucherCode, Integer orderId);


}
