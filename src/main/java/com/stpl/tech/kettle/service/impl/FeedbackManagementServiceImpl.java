package com.stpl.tech.kettle.service.impl;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.domain.model.TableResponse;
import com.stpl.tech.kettle.service.SMSWebServiceClient;
import com.stpl.tech.kettle.service.SolsInfiniWebServiceClient;
import com.stpl.tech.master.domain.model.Unit;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.stpl.tech.kettle.cache.BrandMetaDataCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.converter.Converters;
import com.stpl.tech.kettle.converter.FeedbackEventConverter;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.FeedbackDetail;
import com.stpl.tech.kettle.data.kettle.FeedbackEvent;
import com.stpl.tech.kettle.data.kettle.FeedbackRatingType;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.domain.model.CreateOrderResult;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.FeedbackEventInfo;
import com.stpl.tech.kettle.domain.model.FeedbackEventStatus;
import com.stpl.tech.kettle.domain.model.FeedbackEventType;
import com.stpl.tech.kettle.domain.model.FeedbackSource;
import com.stpl.tech.kettle.domain.model.FeedbackStatus;
import com.stpl.tech.kettle.domain.model.FeedbackTokenInfo;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.repository.kettle.FeedbackDetailDao;
import com.stpl.tech.kettle.repository.kettle.FeedbackEventDao;
import com.stpl.tech.kettle.repository.kettle.LoyaltyScoreDao;
import com.stpl.tech.kettle.repository.kettle.OrderDetailDao;
import com.stpl.tech.kettle.service.FeedbackManagementService;
import com.stpl.tech.kettle.service.TokenService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.notification.ShortUrlData;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class FeedbackManagementServiceImpl implements FeedbackManagementService {

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private BrandMetaDataCache brandMetaDataCache;
	@Autowired
	private UnitCacheService unitCacheService;

	@Autowired
	private TokenService tokenService;

	@Autowired
	private FeedbackEventDao feedbackEventDao;

	@Autowired
	private FeedbackDetailDao feedbackDetailDao;

	@Autowired
	private OrderDetailDao orderDetailDao;

	@Autowired
	private LoyaltyScoreDao loyaltyScoreDao;

	@Autowired
	private FeedbackEventConverter feedbackEventConverter;

	@Override
	public void updateFeedbackUrl(int orderId, String feedbackUrl) {
		// TODO Kafka call
	}

	@Override
	public void generateFeedBackEvent(int orderId, String orderSource, Date currentTimestamp,
			FeedbackEventType eventType, FeedbackDetail feedback, FeedbackSource[] eventSource) {
		for (FeedbackSource sources : eventSource) {
			Optional<OrderDetail> orderDetail = orderDetailDao.findById(orderId);
			feedback.getFeedbackEvents().add(createFeedbackEvent(sources, orderId, orderSource, feedback,
					currentTimestamp, eventType, null, orderDetail.get().getBrandId()));
		}

	}

	@Override
	public FeedbackEvent createFeedbackEvent(FeedbackSource source, int orderId, String orderSource,
			FeedbackDetail feedback, Date currentTimestamp, FeedbackEventType eventType, Integer rating, int brandId) {
		FeedbackEvent event = new FeedbackEvent();
		event.setEventSource(source.name());
		event.setFeedbackDetail(feedback);
		event.setEventGenerationTime(currentTimestamp);
		event.setEventType(eventType.name());
		event.setRating(rating);
		Date triggerTime = getTriggerTime(source, orderSource, currentTimestamp, eventType,feedback.getUnitId());
		event.setEventTriggerTime(triggerTime);
		event.setEventStatus(FeedbackEventStatus.CREATED.name());
		event.setBrandId(brandId);
		if (FeedbackEventType.ORDER_FEEDBACK.equals(eventType)) {
			if (properties.getIsShowNpsRating()) {
				event.setRatingType(FeedbackRatingType.NPS_RATING.name());
				event.setMaxRating(10);
			} else {
				event.setRatingType(FeedbackRatingType.ORDER_RATING.name());
				event.setMaxRating(5);
			}
		} else {
			event.setRatingType(FeedbackRatingType.NPS_RATING.name());
			event.setMaxRating(10);
		}
		feedbackEventDao.save(event);
		return event;
	}

	@Override
	public FeedbackEvent createFeedbackEvent(FeedbackSource source, int orderId, String orderSource, int feedbackId,
			Date currentTimestamp, FeedbackEventType eventType, Integer rating, int brandId) {
		Optional<FeedbackDetail> feedback = feedbackDetailDao.findById(feedbackId);
		return createFeedbackEvent(source, orderId, orderSource, feedback.get(), currentTimestamp, eventType, rating,
				brandId);
	}

	@Override
	public FeedbackDetail getFeedback(int orderId, FeedbackSource source, FeedbackEventType eventType) {
		return feedbackDetailDao.findByOrderIdAndEventType(orderId, eventType.name());
	}

	@Override
	public FeedbackDetail generateFeedbackData(int orderId, int unitId, String orderSource, Set<Integer> productIds,
			Customer customer, Date currentTimestamp, FeedbackEventType eventType, FeedbackSource... eventSource) {
		FeedbackDetail feedback = createFeedbackData(orderId, unitId, orderSource, productIds, customer.getId(),
				customer.getEmailId(), customer.getContactNumber(), customer.getFirstName(), currentTimestamp,
				eventType);
		generateFeedBackEvent(orderId, orderSource, currentTimestamp, eventType, feedback, eventSource);
		return feedback;
	}

	@Override
	public void runNpsProc() {
		feedbackDetailDao.runNpsProc(AppUtils.getCurrentBusinessDate());
	}

	@Override
	public Date getTriggerTime(FeedbackSource source, String orderSource, Date currentTimestamp,
			FeedbackEventType eventType,int unitId) {
		Date threshold = AppUtils.getTimeOfDay(AppUtils.getCurrentDate(),
				properties.getThresholdFeedbackMessageDelay());
		Date triggerTime = null;
		if (FeedbackEventType.ELABORATED.equals(eventType)) {
			triggerTime = AppUtils.getTimeOfDay(currentTimestamp, 0);
		} else if (FeedbackEventType.NPS.equals(eventType) || FeedbackEventType.NPS_OFFER.equals(eventType)
				|| FeedbackEventType.ORDER_FEEDBACK.equals(eventType)) {
			if (orderSource.equals(UnitCategory.COD.name())) {
				triggerTime = AppUtils.getTimeOfDay(currentTimestamp, properties.getDeliveryNPSMessageDelay());
			} else {
				Unit unit =  unitCacheService.getUnitById(unitId);
				triggerTime = AppUtils.getTimeOfDay(currentTimestamp,
						unit.getNpsSmsDineinDelayTime() != null ? unit.getNpsSmsDineinDelayTime() : properties.getDineInNPSMessageDelay());
			}
		} else {
			if (source.equals(FeedbackSource.POS)) {
				triggerTime = AppUtils.getTimeOfDay(currentTimestamp, 0);
			} else {
				if (currentTimestamp.before(threshold)) {
					if (orderSource.equals(UnitCategory.COD.name())) {
						triggerTime = AppUtils.getTimeOfDay(currentTimestamp,
								properties.getDeliveryFeedbackMessageDelay());
					} else {
						triggerTime = AppUtils.getTimeOfDay(currentTimestamp,
								properties.getDineInFeedbackMessageDelay());
					}
				} else {
					triggerTime = AppUtils.getTimeOfDay(AppUtils.getNextDate(AppUtils.getCurrentBusinessDate()),
							properties.getNextDayFeedbackMessageDelay());
				}
			}
		}
		return triggerTime;
	}

	@Override
	public boolean cancelFeedBackforOrder(Integer orderId, FeedbackEventType nps) {
		FeedbackDetail feedbackDetail = feedbackDetailDao.findByOrderIdAndEventTypeAndFeedbackStatus(orderId,
				nps.name(), FeedbackStatus.CREATED.name());
		if (Objects.nonNull(feedbackDetail)) {
			return feedbackEventDao.updateFeedbackEventStatus(feedbackDetail.getFeedbackId(), nps.name(),
					FeedbackEventStatus.CANCELLED.name());
		}
		return false;
	}

	@Override
	public boolean availableForNPSEvent(int customerId, Date triggerTime) {
		FeedbackEvent feedbackEvent = feedbackEventDao.availableForNPSEvent(customerId, FeedbackEventType.NPS.name(),
				FeedbackEventStatus.CANCELLED.name());
		if (Objects.isNull(feedbackEvent)) {
			return true;
		}
		return AppUtils.checkNPSApplicable(triggerTime, feedbackEvent.getEventTriggerTime());
	}

	@Override
	public boolean updateCustomerInfoInFeedbackData(int feedbackId, int customerId, String emailId) {
		feedbackDetailDao.updateCustomerInfoInFeedbackData(feedbackId, customerId, emailId);
		return true;
	}

	@Override
	public Integer getOrderId(Integer feedbackId) {
		Optional<FeedbackDetail> feedbackDetail = feedbackDetailDao.findById(feedbackId);
		if (Objects.nonNull(feedbackDetail)) {
			return feedbackDetail.get().getOrderId();
		}
		return null;
	}

	@Override
	public FeedbackDetail getFeedbackForSource(Integer orderId, FeedbackSource source) {
		return feedbackDetailDao.findByOrderIdAndSource(orderId, source.name());
	}

	@Override
	public Optional<OrderDetail> getOrderDetail(Integer orderId) {
		return orderDetailDao.findById(orderId);
	}

	@Override
	public void createNPSFeedBack(Set<Integer> productIds, Customer customer, OrderDetail orderDetail,
			Date currentTimestamp, String orderFeedbackType, CreateOrderResult result, Order order, StringBuilder sb) {
		try {
			FeedbackDetail detail;
			if (orderFeedbackType.equalsIgnoreCase("internal")) {
				detail = createOrderFeedback(productIds, customer, orderDetail, currentTimestamp, sb);
			} else {
				detail = createNPSFeedback(productIds, customer, orderDetail, currentTimestamp, sb);
			}
			if (detail != null) {
				for (FeedbackEvent event : detail.getFeedbackEvents()) {
					log.info("NPS event" + event);
					if (event.getEventSource().equals(FeedbackSource.QR.name())) {
						result.setFeedbackId(detail.getFeedbackId());
						result.setGenerateQRCode(true);
						log.info("NPS QR " + event);
					}
				}
			}

			if (orderEligibleForInAppFeedback(productIds, order)) {
				FeedbackDetail inAppFeedback;
				if (orderFeedbackType.equalsIgnoreCase("internal")) {
					inAppFeedback = createOrderFeedbackInApp(productIds, customer, orderDetail.getOrderId(),
							orderDetail.getUnitId(), orderDetail.getOrderSource(), currentTimestamp, sb);
				} else {
					inAppFeedback = createNPSFeedbackInApp(productIds, customer, orderDetail.getOrderId(),
							orderDetail.getUnitId(), orderDetail.getOrderSource(), currentTimestamp, sb);
				}
				if (inAppFeedback != null) {
					for (FeedbackEvent event : inAppFeedback.getFeedbackEvents()) {
						if (event.getEventSource().equals(FeedbackSource.IN_APP.name())) {
							result.setFeedbackId(inAppFeedback.getFeedbackId());
							result.setGenerateInAppFeedback(true);
						}
					}
				}
			}
		} catch (Exception e) {
			log.error("Error while creating NPS FeedBack", e);
		}
	}

	@Override
	public FeedbackDetail createOrderFeedback(Set<Integer> productIds, Customer customer, OrderDetail orderDetail,
			Date currentTimestamp, StringBuilder sb) {
		if (!customer.isContactNumberVerified() || (!properties.getNPSConsiderationForDelivery()
				&& orderDetail.getOrderSource().equals(AppConstants.COD))) {
//!false && COD && !false
			log.info("Skipping Feedback For COD Order with Order ID {}", orderDetail.getOrderId());
			return null;
		}
		FeedbackDetail feedback = null;
		long time = System.currentTimeMillis();
// check if nps is set or not for brand
		Brand brand = brandMetaDataCache.getBrandMetaData().get(orderDetail.getBrandId());
		if (brand.getSendNPS() && !productIds.isEmpty()) {
			if (properties.getSendFeedbackMessageForSwiggyDelivery()
					&& orderDetail.getChannelPartnerId() == AppConstants.CHANNEL_PARTNER_SWIGGY) {
				time = System.currentTimeMillis();
				feedback = generateFeedbackData(orderDetail.getOrderId(), orderDetail.getUnitId(),
						orderDetail.getOrderSource(), productIds, customer, currentTimestamp,
						FeedbackEventType.ORDER_FEEDBACK, FeedbackSource.QR);
				if (Objects.nonNull(sb)) {
					sb.append("\n########## , STEP F.1, - ,Swiggy NPS Feedback Creation----------,"
							+ (System.currentTimeMillis() - time));
				}

			} else {
// product Id Size checks if order has gift card items only
//Date triggerTime = feedbackManagementDao.getTriggerTime(FeedbackSource.SMS,
//orderDetail.getOrderSource(), currentTimestamp, FeedbackEventType.NPS);
//if (triggerTime == null) {
//LOG.info("NPS Trigger Time is Null");
//}
//boolean npsApplicable = AppUtils.checkNPSApplicable(triggerTime, customer.getLastNPSTime());
				boolean isCustomerAvilable = customer.isSmsSubscriber() && !customer.isBlacklisted()
						&& !customer.getIsDND();

				if (isCustomerAvilable) {
					time = System.currentTimeMillis();
					FeedbackSource source = FeedbackSource.SMS;
					feedback = generateFeedbackData(orderDetail.getOrderId(), orderDetail.getUnitId(),
							orderDetail.getOrderSource(), productIds, customer, currentTimestamp,
							FeedbackEventType.ORDER_FEEDBACK, source);
					if (Objects.nonNull(sb)) {
						sb.append("\n########## , STEP F.2, - , NPS Feedback Creation----------,"
								+ (System.currentTimeMillis() - time));
					}
				}
			}

		}
		return feedback;
	}

	@Override
	public FeedbackDetail createNPSFeedback(Set<Integer> productIds, Customer customer, OrderDetail orderDetail,
			Date currentTimestamp, StringBuilder sb) {
		if (!customer.isContactNumberVerified() || (!properties.getNPSConsiderationForDelivery()
				&& orderDetail.getOrderSource().equals(AppConstants.COD))) {
//!false && COD && !false
			log.info("Skipping Feedback For COD Order with Order ID {}", orderDetail.getOrderId());
			return null;
		}
		FeedbackDetail feedback = null;
		long time = System.currentTimeMillis();
// check if nps is set or not for brand
		Brand brand = brandMetaDataCache.getBrandMetaData().get(orderDetail.getBrandId());
		if (brand.getSendNPS() && !productIds.isEmpty()) {
			if (properties.getSendFeedbackMessageForSwiggyDelivery()
					&& orderDetail.getChannelPartnerId() == AppConstants.CHANNEL_PARTNER_SWIGGY) {
				time = System.currentTimeMillis();
				feedback = generateFeedbackData(orderDetail.getOrderId(), orderDetail.getUnitId(),
						orderDetail.getOrderSource(), productIds, customer, currentTimestamp, FeedbackEventType.NPS,
						FeedbackSource.QR);
				if (Objects.nonNull(sb)) {
					sb.append("\n########## , STEP F.1, - ,Swiggy NPS Feedback Creation----------,"
							+ (System.currentTimeMillis() - time));
				}
			} else {
				// product Id Size checks if order has gift card items only
				Date triggerTime = getTriggerTime(FeedbackSource.SMS, orderDetail.getOrderSource(), currentTimestamp,
						FeedbackEventType.NPS, orderDetail.getUnitId());
				if (triggerTime == null) {
					log.info("NPS Trigger Time is Null");
				}
				boolean npsApplicable = AppUtils.checkNPSApplicable(triggerTime, customer.getLastNPSTime());
				boolean isCustomerAvilable = customer.isSmsSubscriber() && !customer.isBlacklisted()
						&& !customer.getIsDND();

				if (npsApplicable && isCustomerAvilable) {
					time = System.currentTimeMillis();
					FeedbackSource source = FeedbackSource.SMS;
					if (availableForNPSEvent(customer.getId(), triggerTime)) {
						feedback = generateFeedbackData(orderDetail.getOrderId(), orderDetail.getUnitId(),
								orderDetail.getOrderSource(), productIds, customer, currentTimestamp,
								FeedbackEventType.NPS, source);
					}
					if (Objects.nonNull(sb)) {
						sb.append("\n########## , STEP F.2, - , NPS Feedback Creation----------,"
								+ (System.currentTimeMillis() - time));
					}
				}
			}

		}
		return feedback;
	}

	@Override
	public boolean orderEligibleForInAppFeedback(Set<Integer> productIds, Order order) {
		if (order.getCustomerId() > 5 && AppUtils.isAppOrder(order.getChannelPartner()) && productIds.size() > 0) {
			return true;
		} else {
			return false;
		}
	}

	@Override
	public FeedbackDetail createOrderFeedbackInApp(Set<Integer> productIds, Customer customer, Integer orderId,
			int unitId, String orderSource, Date currentTimestamp, StringBuilder sb) {
		FeedbackDetail feedback = null;
		FeedbackEventType eventType = FeedbackEventType.ORDER_FEEDBACK;
		long time = System.currentTimeMillis();
		feedback = getFeedback(orderId, FeedbackSource.IN_APP, eventType);
		if (feedback == null) {
			feedback = getFeedback(orderId, FeedbackSource.IN_APP, FeedbackEventType.NPS);
		}
		FeedbackSource source = null;
//		if (Objects.nonNull(customer.getOptWhatsapp())) {
//			source = AppUtils.getStatus(customer.getOptWhatsapp()) ? FeedbackSource.WHATS_APP : FeedbackSource.SMS;
//		} else {
			source = FeedbackSource.SMS;
//		}
		if (feedback != null) {
			boolean notFoundInApp = true;
			boolean notFoundSms = true;
			for (FeedbackEvent event : feedback.getFeedbackEvents()) {
				if (event.getEventSource().equals(FeedbackSource.IN_APP.name())) {
					notFoundInApp = false;
				}
				if (event.getEventSource().equals(source.name())) {
					notFoundSms = false;
				}
			}
			if (notFoundInApp && notFoundSms) {
				FeedbackSource[] feedbackSources = { FeedbackSource.IN_APP, source };
				generateFeedBackEvent(orderId, orderSource, currentTimestamp, eventType, feedback, feedbackSources);
			} else if (notFoundInApp) {
				FeedbackSource[] feedbackSources = { FeedbackSource.IN_APP };
				generateFeedBackEvent(orderId, orderSource, currentTimestamp, eventType, feedback, feedbackSources);
			} else if (notFoundSms) {
				FeedbackSource[] feedbackSources = { source };
				generateFeedBackEvent(orderId, orderSource, currentTimestamp, eventType, feedback, feedbackSources);
			}
		} else {
			FeedbackSource[] feedbackSources = { FeedbackSource.IN_APP, source };
			feedback = generateFeedbackData(orderId, unitId, orderSource, productIds, customer, currentTimestamp,
					eventType, feedbackSources);
		}
		if (Objects.nonNull(sb)) {
			sb.append("\n########## , STEP F.3, - , NPS In APP Feedback Creation----------,"
					+ (System.currentTimeMillis() - time));
		}
		return feedback;

	}

	@Override
	public FeedbackDetail createNPSFeedbackInApp(Set<Integer> productIds, Customer customer, Integer orderId,
			int unitId, String orderSource, Date currentTimestamp, StringBuilder sb) {
		FeedbackDetail feedback = null;
		FeedbackEventType eventType = FeedbackEventType.NPS;
		FeedbackSource eventSource = FeedbackSource.IN_APP;
		long time = System.currentTimeMillis();
		feedback = getFeedback(orderId, eventSource, eventType);
		if (feedback == null) {
			feedback = getFeedback(orderId, FeedbackSource.IN_APP, FeedbackEventType.ORDER_FEEDBACK);
		}
		FeedbackSource source = null;
//		if (Objects.nonNull(customer.getOptWhatsapp())) {
//			source = AppUtils.getStatus(customer.getOptWhatsapp()) ? FeedbackSource.WHATS_APP : FeedbackSource.SMS;
//		} else {
			source = FeedbackSource.SMS;
//		}
		if (feedback != null) {
			boolean notFound = true;
			for (FeedbackEvent event : feedback.getFeedbackEvents()) {
				if (event.getEventSource().equals(eventSource.name())) {
					notFound = false;
				}
			}
			if (notFound) {
				FeedbackSource[] feedbackSources = { eventSource, source };
				generateFeedBackEvent(orderId, orderSource, currentTimestamp, eventType, feedback, feedbackSources);
			}
		} else {
			FeedbackSource[] feedbackSources = { eventSource, source };
			feedback = generateFeedbackData(orderId, unitId, orderSource, productIds, customer, currentTimestamp,
					eventType, feedbackSources);
		}

		if (Objects.nonNull(sb)) {
			sb.append("\n########## , STEP F.3, - , NPS In APP Feedback Creation----------,"
					+ (System.currentTimeMillis() - time));
		}
		return feedback;
	}

	@Override
	public Pair<String, String> createFeedbackUrl(CreateOrderResult result) {
		Pair<String, String> feedbackUrl = null;
		if (result.isGenerateQRCode()) {
			try {
				feedbackUrl = getFeedbackLinkForQRCode(result.getFeedbackId(), FeedbackSource.QR);
				updateFeedbackUrl(result.getOrderId(), feedbackUrl.getSecond());
			} catch (Exception e) {
				log.error("Error in setting QR link", e);
//				new ErrorNotification("Error in setting QR link", "Error in setting QR link", e,
//						getEnvironmentProperties().getEnvironmentType()).sendEmail();
			}
		} else if (result.isGenerateInAppFeedback()) {
			try {
				feedbackUrl = getFeedbackLinkForQRCode(result.getFeedbackId(), FeedbackSource.IN_APP);
			} catch (Exception e) {
				log.error("Error in setting IN APP Feedback link");
//				new ErrorNotification("Error in setting IN APP Feedback  link",
//						"Error in setting IN APP Feedback  link", e, getEnvironmentProperties().getEnvironmentType())
//						.sendEmail();
			}
		}
		return feedbackUrl;
	}

	@Override
	public Pair<String, String> getFeedbackLinkForQRCode(int feedbackId, FeedbackSource feedbackSource) {
		return getFeedbackLinkForSource(feedbackId, feedbackSource);// TODO
//		return Pair.of("", "");
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public Pair<String, String> getFeedbackLinkForSource(int feedbackId, FeedbackSource feedbackSource) {
		FeedbackEventInfo event = getFeedbackEventInfo(feedbackId, feedbackSource);
		ShortUrlData shortUrl = null;
		String feedbackUrl = null;
		if (event == null) {
			return null;
		}
		try {
			String unitName = unitCacheService.getUnitBasicDetailById(event.getUnitId()).getName();
			FeedbackTokenInfo token = new FeedbackTokenInfo(event.getContactNumber(), unitName, event.getCustomerName(),
					event.getOrderId(), event.getOrderSource(), event.getEventSource(), event.getFeedbackId(),
					event.getFeedbackEventId());
			String jwtToken = tokenService.createToken(token, -1L);
			// feedback.endpoint.nps.delivery.only.order
			if (FeedbackSource.IN_APP.equals(feedbackSource)) {
				if (properties.getOrderFeedbackType().equals("internal")) {
					feedbackUrl = event.getBrand().getInternalOrderFeedbackUrl();
				} else {
					feedbackUrl = (UnitCategory.COD.name().equals(event.getOrderSource())
							? event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointDelivery()
							: event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointDinein());
				}
				URIBuilder builder = new URIBuilder(feedbackUrl);
				builder.addParameter("name", event.getCustomerName());
				builder.addParameter("number", event.getContactNumber());
				builder.addParameter("unit", unitName);
				builder.addParameter("token", jwtToken);
				feedbackUrl = builder.build().toURL().toString();
				shortUrl = new ShortUrlData(null, null);
			} else {
				if (properties.getOrderFeedbackType().equals("internal")) {
					feedbackUrl = event.getBrand().getInternalOrderFeedbackUrl();
				} else {
					feedbackUrl = event.getBrand().getFeedBackUrl()
							+ event.getBrand().getFeedBackEndpointNPSDeliveryOnlyOrder();
				}
				URIBuilder builder = new URIBuilder(feedbackUrl);
				builder.addParameter("unit", unitName);
				builder.addParameter("token", jwtToken);
				feedbackUrl = builder.build().toURL().toString();
//				shortUrl = SolsInfiniWebServiceClient.getTransactionalClient(event.getBrand()).getShortUrl(feedbackUrl);//TODO
			}

			event.setEventLongUrl(feedbackUrl);
			event.setEventShortUrl(shortUrl.getUrl());
			updateFeedbackEventStatus(event.getFeedbackId(), event.getFeedbackEventId(), shortUrl, feedbackUrl,
					FeedbackEventStatus.NOTIFIED, FeedbackStatus.CREATED);
		} catch (IOException | URISyntaxException e) {
			log.error("Error while generating the feedback message to " + event.getContactNumber(), e);
			updateFeedbackEventStatus(event.getFeedbackId(), event.getFeedbackEventId(), null, null,
					FeedbackEventStatus.FAILED, FeedbackStatus.FAILED);
		}
		return Pair.of(feedbackUrl, shortUrl.getUrl());
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public FeedbackEventInfo getFeedbackEventInfo(int feedbackId, FeedbackSource qr) {
		List<FeedbackEvent> feedbackEventInfo = feedbackEventDao.getFeedbackEventInfo(feedbackId, qr.name());
		if (!CollectionUtils.isEmpty(feedbackEventInfo)) {
			return Converters.convert(
					unitCacheService.getUnitBasicDetailById(feedbackEventInfo.get(0).getFeedbackDetail().getUnitId()),
					brandMetaDataCache.getBrandMetaData().get(feedbackEventInfo.get(0).getBrandId()),
					feedbackEventInfo.get(0));
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public Date updateFeedbackEventStatus(int feedbackId, int eventId, ShortUrlData shortUrl, String longUrl,
			FeedbackEventStatus status, FeedbackStatus feedbackStatus) {
		feedbackDetailDao.updateFeedbackDetail(feedbackId, feedbackStatus.name());
		Optional<FeedbackEvent> feedbackEvent = feedbackEventDao.findById(eventId);
		if (Objects.nonNull(feedbackEvent.get())) {
			feedbackEvent.get().setEventStatus(status.name());
			feedbackEvent.get().setEventShortUrl(null);
			feedbackEvent.get().setEventShortUrlId(null);
			feedbackEvent.get().setEventLongUrl(longUrl);
			feedbackEvent.get().setEventNotificationTime(AppUtils.getCurrentTimestamp());
			feedbackEventDao.save(feedbackEvent.get());
		}
		return AppUtils.getCurrentTimestamp();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateLastNPSTime(Date updateTime, int customerId) {
		loyaltyScoreDao.updateLastNPSTime(updateTime, customerId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public FeedbackEventInfo getPendingNPSForCustomer(FeedbackSource source, Integer orderId, Integer customerId) {
		FeedbackEvent feedbackEvent = feedbackEventDao.getPendingNPSEventsForCustomer(
				FeedbackStatus.CREATED.name(), source.name(), orderId, Arrays.asList(FeedbackEventType.NPS.name(),
						FeedbackEventType.NPS_OFFER.name(), FeedbackEventType.ORDER_FEEDBACK.name()),
				FeedbackEventStatus.CREATED.name(), customerId);
		if (Objects.isNull(feedbackEvent)) {
			return null;
		}
		return feedbackEventConverter.convert(feedbackEvent);
	}

	public FeedbackDetail createFeedbackData(int orderId, int unitId, String orderSource, Set<Integer> productIds,
			int customerId, String emailId, String contactNumber, String name, Date currentTimestamp,
			FeedbackEventType eventType) {
		FeedbackDetail feedback = new FeedbackDetail();
		feedback.setCustomerId(customerId);
		feedback.setFeedbackStatus(FeedbackStatus.CREATED.name());
		feedback.setOrderId(orderId);
		feedback.setUnitId(unitId);
		feedback.setOrderSource(orderSource);
		feedback.setEmailId(emailId);
		feedback.setContactNumber(contactNumber);
		feedback.setCustomerName(name);
		feedback.setFeedbackCreationTime(currentTimestamp);
		feedback.setProductIds(StringUtils.join(productIds, ","));
		feedback.setEventType(eventType.name());
		if (FeedbackEventType.ORDER_FEEDBACK.equals(eventType)) {
			if (properties.getIsShowNpsRating()) {
				feedback.setRatingType(FeedbackRatingType.NPS_RATING.name());
				feedback.setMaxRating(10);
			} else {
				feedback.setRatingType(FeedbackRatingType.ORDER_RATING.name());
				feedback.setMaxRating(5);
			}
		} else {
			feedback.setRatingType(FeedbackRatingType.NPS_RATING.name());
			feedback.setMaxRating(10);
		}
		feedbackDetailDao.save(feedback);
		return feedback;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public String getFeedbackUrl(OrderInfo info, TableResponse tableResponse) throws URISyntaxException, MalformedURLException {
		FeedbackEventInfo event = getPendingNPSForCustomer
				(FeedbackSource.SMS, info.getOrder().getOrderId(), info.getCustomer().getId());
		String feedbackUrl = null;
		if (event == null) {
			return null;
		}
		try {
			String unitName = unitCacheService.getUnitBasicDetailById(event.getUnitId()).getName();
			FeedbackTokenInfo token = new FeedbackTokenInfo(event.getContactNumber(), unitName, event.getCustomerName(),
					event.getOrderId(), event.getOrderSource(), event.getEventSource(), event.getFeedbackId(),
					event.getFeedbackEventId());
			String jwtToken = tokenService.createToken(token, -1L);
			// feedback.endpoint.nps.delivery.only.order
			if (properties.getOrderFeedbackType().equals("internal")) {
				feedbackUrl = event.getBrand().getInternalOrderFeedbackUrl();
			} else {
				feedbackUrl = event.getBrand().getFeedBackUrl()
						+ event.getBrand().getFeedBackEndpointNPSDeliveryOnlyOrder();
			}
			URIBuilder builder = new URIBuilder(feedbackUrl);
			builder.addParameter("name", event.getCustomerName());
			builder.addParameter("unit", unitName);
			builder.addParameter("token", jwtToken);
			feedbackUrl = builder.build().toURL().toString();
			tableResponse.setFeedbackUrl(feedbackUrl);
			SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(event.getBrand());
			ShortUrlData shortUrl = smsWebServiceClient.getShortUrl(builder.build().toURL().toString());
			return shortUrl.getUrl();
		} catch (Exception e) {
			log.info("Error in generating feedback url : {}",e.getMessage());
		}
		return null;
	}

}
