package com.stpl.tech.kettle.service.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.stpl.tech.kettle.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.repository.kettle.CustomerDao;
import com.stpl.tech.kettle.service.ClevertapAttributesService;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class ClevertapAttributesServiceImpl implements ClevertapAttributesService {

	@Autowired
	private CustomerDao customerDao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, Object> getProfileAttributes(Customer customer) {
		long startTime = System.currentTimeMillis();
		log.info("Query for Clevertap Profile Attributes ");
		List<Object[]> resultList = customerDao.clevertapUserAttributes(customer.getId());
		Map<String, Object> resultMap = new HashMap<>();
		Object[] result = resultList.get(0);
		if (!CollectionUtils.isEmpty(resultList)) {
			resultMap.put("nboAvailableFlag", null == result[0] ? BigInteger.ZERO : result[0]);
			resultMap.put("dnboAvailableFlag", null == result[1] ? BigInteger.ZERO : result[1]);
			resultMap.put("gcBalance", null == result[2] ? BigDecimal.ZERO : result[2]);
			resultMap.put("subscriptionActiveFlag", null == result[3] ? BigInteger.ZERO : result[3]);
			resultMap.put("chyCashBalance", null == result[4] ? BigDecimal.ZERO : result[4]);
			resultMap.put("loyaltyPointsBalance", null == result[5] ? 0 : result[5]);
			resultMap.put("loyaltyRedeemedCount", null == result[6] ? BigDecimal.ZERO : result[6]);
			resultMap.put("totalSpent", null == result[7] ? BigDecimal.ZERO : result[7]);
		}
		log.info("Query for Clevertap Profile Attributes : took {} ms", System.currentTimeMillis() - startTime);

		return resultMap;
	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, Object> getEventAttributes(OrderDetail order, CustomerInfo customer) {
		long startTime = System.currentTimeMillis();
		log.info("Query for Clevertap Event Attributes ");
		List<Object[]> resultList = customerDao.clevertapChargedEventAttributes(order.getOfferCode(),
				customer.getCustomerId(), order.getOrderId(), order.getOrderSource(),order.getBrandId());
		Map<String, Object> resultMap = new HashMap<>();
		Object[] result = resultList.get(0);
		if (!CollectionUtils.isEmpty(resultList)) {
			resultMap.put("offerDetailId", Objects.isNull(result[0]) ? 0 : result[0]);
			resultMap.put("isNewCustomer", Objects.isNull(result[1]) ? 'N' : result[1]);
			resultMap.put("previousOrderId", result[2]);
			resultMap.put("previousSourceOrderId", result[3]);
			resultMap.put("previousBillingServerTime", result[4]);
			resultMap.put("previousOrderTimeDiff", result[5]);
			resultMap.put("previousSourceBillingServerTime", result[6]);
			resultMap.put("previousSourceOrderTimeDiff", result[7]);
			resultMap.put("overallOrderCount", null == result[8] ? BigInteger.ZERO : result[8]);
			resultMap.put("orderSourceOrderCount", null == result[9] ? BigInteger.ZERO : result[9]);
			resultMap.put("offerClass", result[10]);
			resultMap.put("walletPendingAmount",null==result[11] ? BigDecimal.ZERO : result[11]);
		}
		log.info("Query for Clevertap Event Attributes : took {} ms", System.currentTimeMillis() - startTime);

		return resultMap;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, Object> getSubscriptionAttributes(OrderDetail order, CustomerInfo customer) {
		long startTime = System.currentTimeMillis();
		log.info("Query for Clevertap Subscription Event Attributes ");
		List<Object[]> resultList = customerDao.clevertapSubscriptionEventAttributes(customer.getCustomerId(),
				order.getOrderId());
		Map<String, Object> resultMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(resultList)) {
			Object[] result = resultList.get(0);
			resultMap.put("isNewCustomer", result[0]);
			resultMap.put("planCode", result[1]);
			resultMap.put("planStartDate", result[2]);
			resultMap.put("planEndDate", result[3]);
			resultMap.put("eventType", result[4]);
		}

		log.info("Query for Clevertap Subscription Event Attributes : took {} ms",
				System.currentTimeMillis() - startTime);
		return resultMap;
	}

}
