
package com.stpl.tech.kettle.service.impl;

import com.stpl.tech.kettle.cache.DreamFolksVoucherUsageCache;
import com.stpl.tech.kettle.cache.OrderInfoCache;
import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.cache.impl.OrderInfoCacheServiceImpl;
import com.stpl.tech.kettle.converter.OrderConverter;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.data.kettle.OrderItemMetaDataDetail;
import com.stpl.tech.kettle.data.kettle.OrderItemStatus;
import com.stpl.tech.kettle.data.kettle.OrderSettlement;
import com.stpl.tech.kettle.data.kettle.TableOrderMappingDetail;
import com.stpl.tech.kettle.data.kettle.UnitTableMappingDetail;
import com.stpl.tech.kettle.domain.kettle.CustomerType;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDomain;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.domain.model.PreferenceDetail;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.TableOrderItemStatus;
import com.stpl.tech.kettle.domain.model.TableOrderItemStatusRequest;
import com.stpl.tech.kettle.domain.model.TableResponse;
import com.stpl.tech.kettle.domain.model.TableStatus;
import com.stpl.tech.kettle.domain.model.TableViewOrder;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.domain.model.UnitOrderRequest;
import com.stpl.tech.kettle.domain.model.UnitTableMapping;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.exceptions.TemplateRenderingException;
import com.stpl.tech.kettle.repository.clm.CustomerInfoDataDao;
import com.stpl.tech.kettle.repository.kettle.OrderItemStatusDao;
import com.stpl.tech.kettle.repository.kettle.OrderItemMetaDataDetailDao;
import com.stpl.tech.kettle.repository.kettle.OrderItemStatusDao;
import com.stpl.tech.kettle.repository.kettle.OrderDetailDao;
import com.stpl.tech.kettle.repository.kettle.TableOrderMappingDetailDao;
import com.stpl.tech.kettle.repository.kettle.UnitTableMappingDetailDao;
import com.stpl.tech.kettle.service.CustomerDataService;
import com.stpl.tech.kettle.service.TableOrderManagementService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.PrintType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TablOrderManagementServiceImpl implements TableOrderManagementService {

	@Autowired
	private TableOrderMappingDetailDao mappingDetailDao;

	@Autowired
	private UnitTableMappingDetailDao tableMappingDetailDao;

	@Autowired
	private UnitCacheService unitCacheService;

	@Autowired
	private OrderConverter orderConverter;

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private CustomerInfoDataDao customerInfoDataDao;

	@Autowired
	private OrderItemStatusDao orderItemStatusDao;

	@Autowired
	private OrderDetailDao orderDetailDao;

	@Autowired
	private CustomerDataService customerDataService;

	@Autowired
	private OrderInfoCache orderInfoCacheService;

	@Autowired
	private ProductCache productCache;

	@Autowired
	private DreamFolksVoucherUsageCache dreamFolksVoucherUsageCache;

	@Autowired
	private OrderItemMetaDataDetailDao orderItemMetaDataDetailDao;

	@Override
	public void addOrderTableMapping(OrderDetail orderDetail, Integer tableRequestId,
									 Customer customer , Boolean toBeRedeemed) throws DataUpdationException {
		log.info("Add order table mapping for Table Request id :{} and order id : {} ",tableRequestId,orderDetail.getOrderId());
		Optional<UnitTableMappingDetail> data = tableMappingDetailDao.findById(tableRequestId);

		if (data.isEmpty()) {
			return;
		} else {
			UnitTableMappingDetail table = data.get();
			if (TableStatus.CLOSED.name().equals(table.getTableStatus())) {
				throw new DataUpdationException("Table already closed.");
			}
			if (TableStatus.OPEN.name().equals(table.getTableStatus())) {
				table.setTableStatus(TableStatus.OCCUPIED.name());
			}
			if (orderDetail.getCustomerId() > 5 && table.getCustomerId() != null && table.getCustomerId() > 5
					&& !orderDetail.getCustomerId().equals(table.getCustomerId())) {
				throw new DataUpdationException(
						"Table already booked with other customer please check table allotments.");
			}

			// if there is no customer associated with table
			if ((table.getCustomerId() == null || table.getCustomerId() <= 5) && orderDetail.getCustomerId() > 5) {
				table.setCustomerId(orderDetail.getCustomerId());
				if (customer != null) {
					table.setContact(customer.getContactNumber());
				}
				table.setCustomerName(orderDetail.getCustomerName());
				if (orderDetail.getCustomerName() == null && customer != null) {
					table.setCustomerName(customer.getFirstName());
				}
				// set customer Id to all Orders
				for (TableOrderMappingDetail to : table.getOrders()) {
					if (to.getOrder().getCustomerId() <= 5) {
						to.getOrder().setCustomerId(customer.getId());
					}
				}
			}

			if (table.getCustomerName() == null) {
				table.setCustomerName(orderDetail.getCustomerName());
			}

			if(Boolean.TRUE.equals(toBeRedeemed) && table.getLoyalteaToBeRedeemed() == null ){
				table.setLoyalteaToBeRedeemed(AppUtils.setStatus(toBeRedeemed));
			}
			if(Objects.isNull(table.getOrderStartTime())){
					table.setOrderStartTime(AppUtils.getCurrentTimestamp());
			}
			if(Objects.isNull(table.getBusinessDate())){
				table.setBusinessDate(AppUtils.getBusinessDate());
			}

			table.setTotalAmount(
					AppUtils.add(new BigDecimal(table.getTotalAmount()), orderDetail.getSettledAmount()).intValue());
			table.setTotalOrders(AppUtils.add(new BigDecimal(table.getTotalOrders()), BigDecimal.ONE).intValue());
			if(orderDetail.getCustomerId() > 5 && Objects.nonNull(table.getCustomerId()) &&
					table.getCustomerId() > 5 &&  Objects.isNull(table.getCustomerType())){
				table =  setCustomerType(table,orderDetail.getCustomerId(),orderDetail.getBrandId());
			}
			TableOrderMappingDetail tom = new TableOrderMappingDetail(tableRequestId, orderDetail);
			mappingDetailDao.save(tom);
		}

	}

	@Override
	public void initiateOrderItemStatus(OrderDetail orderDetail, Integer tableRequestId) throws DataUpdationException {
		log.info("initiate order item status for Table Request id :{} and order id : {} ",tableRequestId,orderDetail.getOrderId());
		try {
			List<OrderItemStatus> orderItemStatusList = new ArrayList<>();
			for(com.stpl.tech.kettle.data.kettle.OrderItem orderItem :  orderDetail.getOrderItems()){
				Product product = productCache.getProductById(orderItem.getProductId());
				if(!AppConstants.SERVICE_CHARGE_SUBTYPE.equals(product.getSubType())
				  && !AppUtils.isGiftCard(orderItem.getTaxCode()) &&
				!Objects.nonNull(productCache.getSubscriptionProductDetail(orderItem.getProductId()))){
					String orderItemStatus = OrderStatus.CREATED.value();
					if(AppConstants.YES.equalsIgnoreCase(orderItem.getIsHoldOn())){
						orderItemStatus = OrderStatus.ON_HOLD.value();
					}
					orderItemStatusList.add(OrderItemStatus.builder().tableRequestId(tableRequestId).orderId(orderDetail.getOrderId()).orderItemId(orderItem)
							.itemCreationTime(AppUtils.getCurrentTimestamp()).status(orderItemStatus).build());
				}

			}
			orderItemStatusDao.saveAll(orderItemStatusList);
		}catch (Exception e){
			log.error("Error while initiating order Item Statuses : {} ", e);
		}
	}

	@Override
	public boolean processItemOnHold(List<Integer> orderItemIds){
		try {
			List<OrderItemStatus> orderItemStatusList = orderItemStatusDao.findAllByOrderItemId_orderItemIdIn(orderItemIds);
			if (!CollectionUtils.isEmpty(orderItemStatusList)) {
				for(OrderItemStatus oi : orderItemStatusList){
					if(OrderStatus.ON_HOLD.value().equalsIgnoreCase(oi.getStatus())) {
						oi.setStatus(OrderStatus.CREATED.value());
						long timeDiff = oi.getItemCreationTime().getTime() - AppUtils.getCurrentTimestamp().getTime();
						oi.setTotalHoldTime(new BigDecimal(timeDiff).abs().intValue());
					}
				}
				orderItemStatusDao.saveAll(orderItemStatusList);
			}
			orderInfoCacheService.updateOrderInOrderInfoCache(orderItemStatusList);
			return true;
		}catch (Exception e){
			log.info("Error in process item on hold for further process : {}",e);
		}
		return false;
	}

	private UnitTableMappingDetail setCustomerType(UnitTableMappingDetail table , Integer customerId , Integer brandId){
	    table.setCustomerType(customerDataService.getCustomerType(customerId,brandId));
		return  table;
	}

	private UnitTableMapping createOpenTable(int unitId, int tableNumber) {
		return new UnitTableMapping(unitId, tableNumber, TableStatus.OPEN);
	}

	private UnitTableMapping convert(UnitTableMappingDetail t) {
		TableStatus status = TableStatus.OCCUPIED;
		if(TableStatus.SETTLEMENT_PENDING.name().equals(t.getTableStatus())){
			status = TableStatus.SETTLEMENT_PENDING;
		}
		UnitTableMapping unitTableMapping   = new UnitTableMapping(t.getTableRequestId(), t.getUnitId(), t.getTableNumber(), t.getCustomerId(),
				t.getCustomerName(), t.getTotalOrders(), t.getTotalAmount(), status, t.getContact(),
				t.getNoOfPax(),t.getCustomerType(),t.getSettledOrderId(),t.getServiceChargeApplied() == null || AppUtils.getStatus(t.getServiceChargeApplied()),t.getBillPrintAllowed() == null || AppUtils.getStatus(t.getBillPrintAllowed()),t.getOfferCode(),t.getPointsRedeemed());
		if (t.getOrders() != null) {
			for (TableOrderMappingDetail map : t.getOrders()) {
				if(!map.getOrder().getOrderStatus().equalsIgnoreCase(OrderStatus.CANCELLED_REQUESTED.value())
				 && !map.getOrder().getOrderStatus().equalsIgnoreCase(OrderStatus.CANCELLED.value())){
					unitTableMapping.getGeneratedOrderIds().add(map.getOrder().getGeneratedOrderId());
				}
			}
		}
		return unitTableMapping;
	}
	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitTableMapping> getTablesForUnit(Integer unitId) {
		List<UnitTableMappingDetail> tablesList = tableMappingDetailDao.findByUnitIdAndTableStatusNot(unitId,TableStatus.CLOSED.name());
		List<UnitTableMapping> tables = new ArrayList<>();
		Map<Integer, UnitTableMapping> tablesMap = new HashMap<>();
		for (UnitTableMappingDetail tableMappingDetail : tablesList) {
			UnitTableMapping unitTableMapping = convert(tableMappingDetail);
			tablesMap.put(unitTableMapping.getTableNumber(), unitTableMapping);
		}
		Unit unit = unitCacheService.getUnitById(unitId);
		for (int i = 1; i <= unit.getNoOfTables(); i++) {
			UnitTableMapping utm = tablesMap.get(i);
			if (utm == null) {
				tables.add(createOpenTable(unitId, i));
			} else {
				tables.add(utm);
			}
		}
		Collections.sort(tables);
		return tables;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public UnitTableMapping reserveTableForUnit(Integer unitId, Integer tableNumber , Integer noOfPax) throws DataNotFoundException {
		if (unitId == 0 || tableNumber == 0) {
			throw new DataNotFoundException("Invalid Unit and Table");
		}
		UnitTableMappingDetail table = tableMappingDetailDao.findByUnitIdAndTableNumberAndTableStatusNot(unitId, tableNumber,TableStatus.CLOSED.name());
		if (table != null) {
			throw new DataNotFoundException("Table Already Reserved");
		}

		UnitTableMappingDetail unitTableMappingDetail = new UnitTableMappingDetail();
		unitTableMappingDetail.setUnitId(unitId);
		unitTableMappingDetail.setTableNumber(tableNumber);
		unitTableMappingDetail.setTableStatus(TableStatus.OCCUPIED.name());
		unitTableMappingDetail.setNoOfPax(noOfPax);
		unitTableMappingDetail.setTableStartTime(AppUtils.getCurrentTimestamp());
		table = tableMappingDetailDao.save(unitTableMappingDetail);
		return orderConverter. convert(table);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean closeTableForUnit(Integer tableRequestId) {
		if (tableRequestId == 0) {
			return false;
		}
		UnitTableMappingDetail table = tableMappingDetailDao.findByTableRequestId(tableRequestId);
		if ((table.getOrders().isEmpty() && !TableStatus.CLOSED.name().equals(table.getTableStatus())) ||
				(getTableOrders(tableRequestId).isEmpty())) {
			table.setTableStatus(TableStatus.CLOSED.name());
		}
		else {
			return false;
		}
		tableMappingDetailDao.save(table);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public UnitTableMapping updatePaxForTableOrder(Integer tableRequestId , Integer noOfPax) throws DataUpdationException {
		UnitTableMappingDetail table = tableMappingDetailDao.findByTableRequestId(tableRequestId);
		if(TableStatus.CLOSED.name().equalsIgnoreCase(table.getTableStatus())){
			throw new DataUpdationException("Table is Closed");
		}
		log.info("Current Pax Number For Table Request Number  : {} is {} ",tableRequestId,table.getNoOfPax());
		table.setNoOfPax(noOfPax);
		tableMappingDetailDao.save(table);
		List<OrderItemStatus> orderItemStatusList = orderItemStatusDao.findAllByTableRequestId(tableRequestId);
		Map<Integer,OrderItemStatus> orderItemStatusMapByOrderItemId = orderItemStatusList.stream().collect(Collectors.toMap(
				status -> status.getOrderItemId().getOrderItemId(), Function.identity(),(o1,o2)->{
					if(o1.getOrderItemStatusId() > o2.getOrderItemStatusId()){
						return o1;
					}else {
						return o2;
					}
				}
		));
		return orderConverter.convert(table,orderItemStatusMapByOrderItemId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitTableMapping getTableSummary(Integer tableRequestId) {
		if (tableRequestId == 0) {
			return null;
		}
		UnitTableMappingDetail table = tableMappingDetailDao.findByTableRequestId(tableRequestId);
		List<OrderItemStatus> orderItemStatusList = orderItemStatusDao.findAllByTableRequestId(tableRequestId);
		Map<Integer,OrderItemStatus> orderItemStatusMapByOrderItemId = orderItemStatusList.stream().collect(Collectors.toMap(
				status -> status.getOrderItemId().getOrderItemId(), Function.identity(),(o1,o2)->{
					if(o1.getOrderItemStatusId() > o2.getOrderItemStatusId()){
						return o1;
					}else{
						return o2;
					}
				}
		));
		return table == null ? null : orderConverter.convert(table,orderItemStatusMapByOrderItemId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public Boolean saveTableOrderItemStatuses(TableOrderItemStatusRequest tableOrderItemStatusRequest) {
		UnitTableMappingDetail unitTableMapping = null;
		List<OrderItemStatus> initiatedOrderItemStatusList = new ArrayList<>();
		if(Objects.nonNull(tableOrderItemStatusRequest.getTableRequestId())){
			unitTableMapping = tableMappingDetailDao.findByTableRequestId(tableOrderItemStatusRequest.getTableRequestId());
			initiatedOrderItemStatusList = orderItemStatusDao.findAllByTableRequestId(tableOrderItemStatusRequest.getTableRequestId());
		}else if(Objects.nonNull(tableOrderItemStatusRequest.getOrderItemStatusList()) && Objects.nonNull(tableOrderItemStatusRequest.getOrderItemStatusList().get(0).getOrderId())){
			initiatedOrderItemStatusList = orderItemStatusDao.findAllByOrderId(tableOrderItemStatusRequest.getOrderItemStatusList().get(0).getOrderId());
		}

		Map<Integer,OrderItemStatus> orderItemStatusMap = new HashMap<>();
		for(OrderItemStatus initiatedOrderItemStatus : initiatedOrderItemStatusList){
			orderItemStatusMap.put(initiatedOrderItemStatus.getOrderItemId().getOrderItemId(),initiatedOrderItemStatus);
		}

		List<OrderItemStatus> orderItemStatusList = new ArrayList<>();
		for(TableOrderItemStatus tableOrderItemStatus : tableOrderItemStatusRequest.getOrderItemStatusList()){
			if(orderItemStatusMap.containsKey(tableOrderItemStatus.getOrderItemId())){
				OrderItemStatus initiatedOrderItemStatus = orderItemStatusMap.get(tableOrderItemStatus.getOrderItemId());
				if(!initiatedOrderItemStatus.getStatus().equalsIgnoreCase(OrderStatus.CANCELLED.value())){
					initiatedOrderItemStatus.setStatus(tableOrderItemStatus.getStatus());
					initiatedOrderItemStatus.setUpdationTime(AppUtils.getCurrentTimestamp());
				}
				initiatedOrderItemStatus.setSettlementOrderId(Objects.nonNull(unitTableMapping) ? unitTableMapping.getSettledOrderId() : tableOrderItemStatus.getOrderId());
				initiatedOrderItemStatus.setServedBy(tableOrderItemStatus.getServedBy());
				initiatedOrderItemStatus.setItemServeTime(!StringUtils.isEmpty(tableOrderItemStatus.getServeTime()) ?
						new Date(tableOrderItemStatus.getServeTime()) : null);
				initiatedOrderItemStatus.setItemCompletionTime(	!StringUtils.isEmpty(tableOrderItemStatus.getCompletionTime()) ?
						new Date(tableOrderItemStatus.getCompletionTime()) : null);
				initiatedOrderItemStatus.setItemInProcessTime(!StringUtils.isEmpty(tableOrderItemStatus.getInProcessTime())?
						new Date(tableOrderItemStatus.getInProcessTime()) : null);
				initiatedOrderItemStatus.setTotalProcessingTime(tableOrderItemStatus.getTotalProcessingTime());
				orderItemStatusList.add(initiatedOrderItemStatus);
			}else{
				com.stpl.tech.kettle.data.kettle.OrderItem orderItem = new com.stpl.tech.kettle.data.kettle.OrderItem();
				orderItem.setOrderItemId(tableOrderItemStatus.getOrderItemId());
				OrderItemStatus orderItemStatus = OrderItemStatus.builder().tableRequestId(tableOrderItemStatusRequest.getTableRequestId())
						.orderId(tableOrderItemStatus.getOrderId()).orderItemId(
								orderItem)
						.status(tableOrderItemStatus.getStatus()).itemCreationTime(
								!StringUtils.isEmpty(tableOrderItemStatus.getItemCreationTime()) ?
										new Date(tableOrderItemStatus.getItemCreationTime()) : null)
						.itemInProcessTime(!StringUtils.isEmpty(tableOrderItemStatus.getInProcessTime())?
								new Date(tableOrderItemStatus.getInProcessTime()) : null).itemCompletionTime(
								!StringUtils.isEmpty(tableOrderItemStatus.getCompletionTime()) ?
										new Date(tableOrderItemStatus.getCompletionTime()) : null)
						.itemServeTime(!StringUtils.isEmpty(tableOrderItemStatus.getServeTime()) ?
								new Date(tableOrderItemStatus.getServeTime()) : null)
						.totalProcessingTime(tableOrderItemStatus.getTotalProcessingTime())
						.servedBy(tableOrderItemStatus.getServedBy()).updationTime(AppUtils.getCurrentTimestamp())
						.settlementOrderId(Objects.nonNull(unitTableMapping)  ? unitTableMapping.getSettledOrderId() : tableOrderItemStatus.getOrderId())
						.build();
				orderItemStatusList.add(orderItemStatus);
			}
		}
		if(!CollectionUtils.isEmpty(orderItemStatusList)){
			orderItemStatusDao.saveAll(orderItemStatusList);
		}
		return true;
	}



	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public UnitTableMapping changeTable(int tableRequestId, int tableNumber) throws DataUpdationException {
		if (tableRequestId == 0 || tableNumber == 0) {
			throw new DataUpdationException("Incorrect Table");
		}
		UnitTableMappingDetail utm1 = tableMappingDetailDao.findByTableRequestId(tableRequestId);
		UnitTableMappingDetail utm2 = tableMappingDetailDao.findByUnitIdAndTableNumberAndTableStatusNot(utm1.getUnitId(), tableNumber,TableStatus.CLOSED.name());

		if (TableStatus.CLOSED.name().equals(utm1.getTableStatus())) {
			throw new DataUpdationException("Table already closed");
		}

		if (utm2 != null) {
			throw new DataUpdationException("Table already occupied");
		}

		utm1.setTableNumber(tableNumber);
		tableMappingDetailDao.save(utm1);
		List<OrderItemStatus> orderItemStatusList = orderItemStatusDao.findAllByTableRequestId(utm1.getTableRequestId());
		Map<Integer,OrderItemStatus> orderItemStatusMapByOrderItemId = orderItemStatusList.stream().collect(Collectors.toMap(
				status -> status.getOrderItemId().getOrderItemId(), Function.identity(),(o1,o2)->{
					if(o1.getOrderItemStatusId() > o2.getOrderItemStatusId()){
						return o1;
					}else{
						return o2;
					}
				}
		));
		return orderConverter.convert(utm1, orderItemStatusMapByOrderItemId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public TableResponse generateSettlementReceipt(int tableRequestId) throws TemplateRenderingException {
		if (tableRequestId <= 0) {
			return null;
		}
		UnitTableMappingDetail table = tableMappingDetailDao.findByTableRequestId(tableRequestId);
		Map<Integer, BigDecimal> settlementMap = new HashMap<>();
		for (TableOrderMappingDetail map : table.getOrders()) {
			OrderDetail od = map.getOrder();
			if (TransactionUtils.isCancelled(map.getOrder().getOrderStatus())) {
				continue;
			}
			for (OrderSettlement os : od.getOrderSettlements()) {
				if (!settlementMap.containsKey(os.getPaymentModeId())) {
					settlementMap.put(os.getPaymentModeId(), BigDecimal.ZERO);
				}
				;
				settlementMap.put(os.getPaymentModeId(),
						settlementMap.get(os.getPaymentModeId()).add(os.getAmountPaid()));
			}
		}
		String receipt = TransactionUtils.getTableSettlementReceipt(settlementMap, unitCacheService.getAllPaymentMode(),
				table, properties.getBasePath());
		TableResponse tr = new TableResponse();
		tr.setPrintType(PrintType.RAW);
		tr.setSettlementReceipt(receipt);
		return tr;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Integer> getTableOrders(int tableRequestId) {
		List<Integer> list = new ArrayList<Integer>();
		UnitTableMappingDetail utm = tableMappingDetailDao.findByTableRequestId(tableRequestId);
		for (TableOrderMappingDetail map : utm.getOrders()) {
			OrderDetail od = map.getOrder();
			if (TransactionUtils.isCancelled(map.getOrder().getOrderStatus())) {
				continue;
			}
			list.add(od.getOrderId());
		}
		return list;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateTableSettlementOrder(int tableRequestId,Integer settledOrderId){
		UnitTableMappingDetail utm = tableMappingDetailDao.findByTableRequestId(tableRequestId);
		utm.setSettledOrderId(settledOrderId);
		if(Objects.nonNull(utm.getSettledOrderId()) && Objects.nonNull(utm.getOrderStartTime())){
			utm.setTableSettlementTime(AppUtils.getCurrentTimestamp());
			utm.setTotalOrderTimeInMin(AppUtils.
					getMinDiffernce(utm.getOrderStartTime(),utm.getTableStartTime()));
		}
		tableMappingDetailDao.save(utm);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public TableViewOrder consolidateOrders(Integer tableRequestId){
		TableViewOrder tableViewOrder = new TableViewOrder();
		Integer customerId = null;
		try {
			List<Order> orderList = getOrders(tableRequestId);
			if (!CollectionUtils.isEmpty(orderList)) {
				List<Integer> orderIds = new ArrayList<>();
				Order combinedOrder = new Order();
				for (Order o : orderList) {
					orderIds.add(o.getOrderId());
					if(Objects.isNull(customerId) && Objects.nonNull(o.getCustomerId())){
						customerId = o.getCustomerId();
					}
					for (OrderItem oi : o.getOrders()) {
						if (Objects.isNull(combinedOrder.getOrders())) {
							combinedOrder.setOrders(new ArrayList<>());
						}
						oi.setTableOrderId(o.getOrderId());
						combinedOrder.getOrders().add(oi);
					}
//					calculateTransactionDetail(combinedOrder, o.getTransactionDetail());
					calculateSettlements(combinedOrder, o.getSettlements());
					tableViewOrder.setLoyalteaToBeRedeemed(o.getLoyalteaToBeRedeemed());
				}
				combinedOrder.setTransactionDetail(createTransactionDetail(combinedOrder.getOrders(),tableRequestId));
				updateSettlement(combinedOrder);
				addOrderBasicDetails(combinedOrder, orderList.get(0),customerId);
				tableViewOrder.setOrder(combinedOrder);
				tableViewOrder.setTableOrderIds(orderIds);
				return tableViewOrder;
			}
		}catch (Exception e){
			log.info("Error in combining orders for table request id : {} , error is : {}",tableRequestId,e);
		}
		return null;
	}

	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Order> getOrders(Integer tableRequestId){
		try {
			UnitTableMappingDetail utm = tableMappingDetailDao.findByTableRequestId(tableRequestId);
			List<Order> orderList = new ArrayList<>();
			if(Objects.nonNull(utm.getSettledOrderId())){
				OrderDetail orderDetail = orderDetailDao.findByOrderId(utm.getSettledOrderId());
				Order order = orderConverter.convert(orderDetail, null);
				order.setLoyalteaToBeRedeemed(AppUtils.getStatus(utm.getLoyalteaToBeRedeemed()));
				orderList.add(order);
			}else {
				List<OrderItemStatus> orderItemStatusList = orderItemStatusDao.findAllByTableRequestId(tableRequestId);
				for(OrderItemStatus ois : orderItemStatusList){
					if(OrderStatus.ON_HOLD.value().equalsIgnoreCase(ois.getStatus())){
						return null;
					}
				}
				for (TableOrderMappingDetail data : utm.getOrders()) {
					Order order = orderConverter.convert(data.getOrder(), null);
					order.setLoyalteaToBeRedeemed(AppUtils.getStatus(utm.getLoyalteaToBeRedeemed()));
					if (!OrderStatus.CANCELLED.equals(order.getStatus()) &&
							!OrderStatus.CANCELLED_REQUESTED.equals(order.getStatus())) {
						orderList.add(order);
					}
				}
			}
			
			// Set rule numbers for order items from metadata
			setRuleNumbersForOrderItems(orderList);
			
			return orderList;
		}catch (Exception e){
			log.info("Error in fetching Orders for table request id : {} , Error is : {}",tableRequestId,e);
		}
		return null;
	}

	private void addOrderBasicDetails(Order combinedOrder,Order baseOrder,Integer customerId){
		combinedOrder.setUnitId(baseOrder.getUnitId());
		combinedOrder.setSource(baseOrder.getSource());
		combinedOrder.setEmployeeId(baseOrder.getEmployeeId());
		combinedOrder.setHasParcel(baseOrder.isHasParcel());
		combinedOrder.setSettlementType(baseOrder.getSettlementType());
		combinedOrder.setChannelPartner(baseOrder.getChannelPartner());
		combinedOrder.setPointsRedeemed(baseOrder.getPointsRedeemed());
		combinedOrder.setTerminalId(baseOrder.getTerminalId());
		combinedOrder.setTableNumber(baseOrder.getTableNumber());
		combinedOrder.setTableRequestId(baseOrder.getTableRequestId());
		combinedOrder.setContainsSignupOffer(baseOrder.isContainsSignupOffer());
		combinedOrder.setNewCustomer(baseOrder.isNewCustomer());
		combinedOrder.setOrderType(baseOrder.getOrderType());
		combinedOrder.setCashRedeemed(baseOrder.getCashRedeemed());
		combinedOrder.setGiftCardOrder(baseOrder.isGiftCardOrder());
		combinedOrder.setBypassLoyateaAward(baseOrder.isBypassLoyateaAward());
		combinedOrder.setUnitName(baseOrder.getUnitName());
		combinedOrder.setBrandId(baseOrder.getBrandId());
		combinedOrder.setNewCustomer(baseOrder.isNewCustomer());
		combinedOrder.setCustomerName(baseOrder.getCustomerName());
		if(Objects.nonNull(customerId)){
			combinedOrder.setCustomerId(customerId);
		}else {
			combinedOrder.setCustomerId(baseOrder.getCustomerId());
		}
		combinedOrder.setUnitOrderId(baseOrder.getUnitOrderId());
		combinedOrder.setStatus(OrderStatus.SETTLED);
		// TODO Add bill timings
	}

	private void calculateTransactionDetail(Order order, TransactionDetail transactionDetail){
		if(Objects.isNull(order.getTransactionDetail()) || Objects.isNull(order.getTransactionDetail().getTotalAmount())){
			TransactionDetail td = new TransactionDetail();
			td.setTotalAmount(transactionDetail.getTotalAmount());
			td.setTaxableAmount(transactionDetail.getTaxableAmount());
			td.setSavings(transactionDetail.getSavings());
			td.setDiscountDetail(transactionDetail.getDiscountDetail());
			td.setPaidAmount(transactionDetail.getPaidAmount());
			td.setRoundOffValue(transactionDetail.getRoundOffValue());
			td.setTax(transactionDetail.getTax());
			td.setTaxes(transactionDetail.getTaxes());
			order.setTransactionDetail(td);
		}else{
			TransactionDetail td = order.getTransactionDetail();
			td.setTotalAmount(AppUtils.add(td.getTotalAmount(), transactionDetail.getTotalAmount()));
			td.setTaxableAmount(AppUtils.add(td.getTaxableAmount(), transactionDetail.getTaxableAmount()));
			td.setSavings(AppUtils.add(td.getSavings(), transactionDetail.getSavings()));
			td.setPaidAmount(AppUtils.add(td.getPaidAmount(), transactionDetail.getPaidAmount()));
			td.setRoundOffValue(AppUtils.add(td.getRoundOffValue(), transactionDetail.getRoundOffValue()));
			td.setTax(AppUtils.add(td.getTax(), transactionDetail.getTax()));
			List<TaxDetail> taxes = new ArrayList<>();
			for(int i=0;i<td.getTaxes().size();i++){
				TaxDetail taxDetail1 = td.getTaxes().get(i);
				TaxDetail taxDetail2 = transactionDetail.getTaxes().get(i);
				taxDetail1.setTotal(AppUtils.add(taxDetail1.getTotal(),taxDetail2.getTotal()));
				taxDetail1.setTaxable(AppUtils.add(taxDetail1.getTaxable(),taxDetail2.getTaxable()));
				taxDetail1.setValue(AppUtils.add(taxDetail1.getValue(),taxDetail2.getValue()));
				taxes.add(taxDetail1);
			}
			td.setTaxes(taxes);
			order.setTransactionDetail(td);
		}
	}

	private void calculateSettlements(Order order, List<Settlement> settlement){
		if(CollectionUtils.isEmpty(order.getSettlements())){
			List<Settlement> settlementList = new ArrayList<>();
			for(Settlement s : settlement){
				Settlement st = new Settlement();
				st.setMode(s.getMode());
				st.setAmount(s.getAmount());
				st.setExternalSettlements(s.getExternalSettlements());
				settlementList.add(st);
			}
			order.setSettlements(settlementList);
		}else{
			if(!CollectionUtils.isEmpty(settlement)) {
				List<Settlement> settlementList = order.getSettlements();
				for (Settlement s : settlementList) {
					s.setAmount(AppUtils.add(s.getAmount(), settlement.get(0).getAmount()));
				}
				order.setSettlements(settlementList);
			}
		}
	}

	private void updateSettlement(Order order){
		List<Settlement> settlementList = order.getSettlements();
		for(Settlement s : settlementList){
			s.setAmount(order.getTransactionDetail().getPaidAmount());
		}
		order.setSettlements(settlementList);
	}

	private TransactionDetail createTransactionDetail(List<OrderItem> cartItems,Integer tableRequestId){
		try {
			TransactionDetail td = createTransactionalObject();
			BigDecimal promotionalDiscount = BigDecimal.ZERO;
			BigDecimal discountValue = BigDecimal.ZERO;
			BigDecimal totalDiscount = BigDecimal.ZERO;
			//BigDecimal nonDiscountedPayable = BigDecimal.ZERO;
			for (OrderItem item : cartItems) {
				td.setTotalAmount(td.getTotalAmount().add(item.getPrice().multiply(new BigDecimal(item.getQuantity()))));
				td.setTaxableAmount(td.getTaxableAmount().add(item.getAmount()));
				td.setTax(td.getTax().add(item.getTax()));
				if(Objects.nonNull(item.getComposition()) &&
						!CollectionUtils.isEmpty(item.getComposition().getMenuProducts())){
					for(OrderItem menuProduct : item.getComposition().getMenuProducts()){
						if(Objects.nonNull(menuProduct.getTax())){
							td.setTax(td.getTax().add(menuProduct.getTax()));
						}
						if(!CollectionUtils.isEmpty(menuProduct.getTaxes())){
							for (TaxDetail itemTax : menuProduct.getTaxes()) {
								TaxDetail atax = TaxDetail.builder().taxId(itemTax.getTaxId()).type(itemTax.getType())
										.total(itemTax.getTotal()).code(itemTax.getCode()).taxable(itemTax.getTaxable())
										.percentage(itemTax.getPercentage()).value(itemTax.getValue()).build();
								boolean found = false;
								for (TaxDetail tdx : td.getTaxes()) {
									if (tdx.getCode().equals(atax.getCode()) && tdx.getPercentage().equals(atax.getPercentage())) {
										tdx.setValue(tdx.getValue().add(atax.getValue()));
										found = true;
									}
								}
								if (!found) {
									td.getTaxes().add(atax);
								}
							}
						}
					}

				}
				for (TaxDetail itemTax : item.getTaxes()) {
					TaxDetail atax = TaxDetail.builder().taxId(itemTax.getTaxId()).type(itemTax.getType())
							.total(itemTax.getTotal()).code(itemTax.getCode()).taxable(itemTax.getTaxable())
							.percentage(itemTax.getPercentage()).value(itemTax.getValue()).build();
					boolean found = false;
					for (TaxDetail tdx : td.getTaxes()) {
						if (tdx.getCode().equals(atax.getCode()) && tdx.getPercentage().equals(atax.getPercentage())) {
							tdx.setTaxable(tdx.getTaxable().add(atax.getTaxable()));
							tdx.setTotal(tdx.getTotal().add(atax.getTotal()));
							tdx.setValue(tdx.getValue().add(atax.getValue()));
							found = true;
						}
					}
					if (!found) {
						td.getTaxes().add(atax);
					}
				}
//				promotionalDiscount = promotionalDiscount.add(item.getDiscountDetail().getPromotionalOffer());
//				discountValue = discountValue.add(item.getDiscountDetail().getDiscount().getPercentage().divide(new BigDecimal(100)).multiply(item.getAmount()));
//				totalDiscount = totalDiscount.add(item.getDiscountDetail().getTotalDiscount());
				//nonDiscountedPayable = nonDiscountedPayable.add(item.getTotalAmount());
			}
			td.getDiscountDetail().setPromotionalOffer(promotionalDiscount);
			td.getDiscountDetail().getDiscount().setValue(discountValue);
			td.getDiscountDetail().setTotalDiscount(totalDiscount);
			BigDecimal paidAmount = td.getTaxableAmount().add(td.getTax());
			td.setPaidAmount(paidAmount.setScale(0, BigDecimal.ROUND_HALF_UP));
			td.setRoundOffValue(td.getPaidAmount().subtract(paidAmount));
			// TODO add saving amount
			td.setSavings(BigDecimal.ZERO);
			return td;
		}catch (Exception e){
			log.info("Error in getting transaction detail for table requestId : {}",tableRequestId);
			log.info("error is : {}",e);
			return null;
		}
	}

	protected TransactionDetail createTransactionalObject() {
		TransactionDetail td = new TransactionDetail();
		td.setDiscountDetail(new DiscountDetail());
		td.getDiscountDetail().setDiscount(new PercentageDetail());
		td.getDiscountDetail().getDiscount().setPercentage(BigDecimal.ZERO);
		td.getDiscountDetail().getDiscount().setValue(BigDecimal.ZERO);
		td.getDiscountDetail().setTotalDiscount(BigDecimal.ZERO);
		td.getDiscountDetail().setPromotionalOffer(BigDecimal.ZERO);
		td.setPaidAmount(BigDecimal.ZERO);
		td.setRoundOffValue(BigDecimal.ZERO);
		td.setSaleAmount(BigDecimal.ZERO);
		td.setSavings(BigDecimal.ZERO);
		td.setTaxableAmount(BigDecimal.ZERO);
		td.setTotalAmount(BigDecimal.ZERO);
		td.setTax(BigDecimal.ZERO);
		return td;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean isTableAlreadySettled(int tableRequestId) {
		List<Integer> list = new ArrayList<Integer>();
		UnitTableMappingDetail utm = tableMappingDetailDao.findByTableRequestId(tableRequestId);
		if(Objects.nonNull(utm.getSettledOrderId())){
			return true;
		}
		return false;
	}

	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", propagation = Propagation.REQUIRED)
	public boolean setServiceChargeApplicable(Integer tableRequestId,String flag){
		UnitTableMappingDetail utm = tableMappingDetailDao.findByTableRequestId(tableRequestId);
		utm.setServiceChargeApplied(flag);
		if(utm.getBillPrintCount() <= 1){
			utm.setBillPrintAllowed(AppUtils.YES);
		}
		tableMappingDetailDao.save(utm);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean ValidateServiceCharge(Order order,String serviceChargeApplied) {
		UnitTableMappingDetail utm = tableMappingDetailDao.findByTableRequestId(order.getTableRequestId());
		if(utm.getTotalOrders() == 0 && utm.getTotalAmount() == 0){
			return false;
		}else if(Objects.nonNull(utm.getServiceChargeApplied()) && !utm.getServiceChargeApplied().equals(serviceChargeApplied)){
			return false;
		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", propagation = Propagation.REQUIRED)
	public void updateTableDataForBillPrint(Order order, String serviceChargeApplied, String isGeneratedOrderId){
		UnitTableMappingDetail utm = tableMappingDetailDao.findByTableRequestId(order.getTableRequestId());
		utm.setTableStatus(TableStatus.SETTLEMENT_PENDING.name());
		utm.setServiceChargeApplied(serviceChargeApplied);
		utm.setBillPrintCount(utm.getBillPrintCount()+1);
		utm.setBillPrintAllowed(AppUtils.NO);
		utm.setOfferCode(order.getOfferCode());
		utm.setPointsRedeemed(Math.abs(order.getPointsRedeemed()));
		if(isGeneratedOrderId.equalsIgnoreCase(AppConstants.YES) && Objects.nonNull(order.getGenerateOrderId())){
			utm.setGeneratedOrderId(order.getGenerateOrderId());
		}
		tableMappingDetailDao.save(utm);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean validateBillPrint(OrderDomain orderDomain, String serviceChargeApplied){
		UnitTableMappingDetail utm = tableMappingDetailDao.findByTableRequestId(orderDomain.getOrder().getTableRequestId());
		if(Objects.nonNull(utm) && Objects.nonNull(utm.getGeneratedOrderId())){
			orderDomain.getOrder().setGenerateOrderId(utm.getGeneratedOrderId());
		} else {
			orderDomain.getOrder().setGenerateOrderId(null);
		}
		if(utm.getBillPrintCount() > 1 || AppUtils.NO.equals(utm.getBillPrintAllowed())){
			return false;
		}else if(Objects.nonNull(utm.getServiceChargeApplied()) && !utm.getServiceChargeApplied().equals(serviceChargeApplied)){
			return false;
		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean validateOrderItems(Order order){
		List<Order> orderList = getOrders(order.getTableRequestId());
		Order combinedOrder = new Order();
		for (Order o : orderList) {
			for (OrderItem oi : o.getOrders()) {
				if (Objects.isNull(combinedOrder.getOrders())) {
					combinedOrder.setOrders(new ArrayList<>());

				}
				oi.setTableOrderId(o.getOrderId());
				combinedOrder.getOrders().add(oi);
			}
		}

		return order.getOrders().size() == combinedOrder.getOrders().size();
	}

	@Override
	public List<Order> getCafeOrdersForUnit(UnitOrderRequest request){
		List<Order> unitOrders = orderInfoCacheService.getUnitOrdersFromCache(request.getUnitId());
		List<Order> result = new ArrayList<>();
		for(Order order: unitOrders){
			if(request.getOrderIds().contains(order.getOrderId()) &&
					(order.getStatus() == OrderStatus.CANCELLED || order.getStatus() == OrderStatus.CANCELLED_REQUESTED)){
				result.add(order);
			}else if(!request.getOrderIds().contains(order.getOrderId())){
				result.add(order);
			}
		}

		return result;
	}

	@Override
	public boolean isDreamFolksVoucherCodeUsed(String voucherCode){
		if(Objects.isNull(voucherCode)){
			return false;
		}
		return dreamFolksVoucherUsageCache.getDreamFolksVoucherCodesUsed().contains(voucherCode);
	}
	
	private void setRuleNumbersForOrderItems(List<Order> orderList) {
		if (CollectionUtils.isEmpty(orderList)) {
			return;
		}
		Map<Integer, OrderItemMetaDataDetail> orderItemToRuleMap = getCartRuleMapByOrderId(orderList);
		for (Order order : orderList) {
			if (CollectionUtils.isNotEmpty(order.getOrders())) {
				try {
					for (OrderItem orderItem : order.getOrders()) {
						OrderItemMetaDataDetail metaData = orderItemToRuleMap.get(orderItem.getItemId());
						if (metaData == null) continue;
						
						boolean isRecommended = AppConstants.YES.equalsIgnoreCase(metaData.getIsRecommended());
						orderItem.setRecProd(isRecommended);
						
						boolean isSavedChai = AppConstants.YES.equalsIgnoreCase(metaData.getIsSavedChai());
						Integer preferenceId = metaData.getSavedChaiId();
						if (isSavedChai && preferenceId != null) {
							PreferenceDetail preferenceDetail = new PreferenceDetail();
							preferenceDetail.setPreferenceId(preferenceId);
							orderItem.setPreferenceDetail(preferenceDetail);
						}
						
						if (metaData.getRecomRuleId() != null) {
							orderItem.setRuleNumber(metaData.getRecomRuleId());
						}
					}

				} catch (Exception e) {
					log.warn("Failed to set rule numbers for order id: {}, Error: {}",
							order.getOrderId(), e.getMessage());
				}
			}
		}
	}
	
	private Map<Integer, OrderItemMetaDataDetail> getCartRuleMapByOrderId(List<Order> orderList) {
		Map<Integer, OrderItemMetaDataDetail> orderItemToRuleMap = new HashMap<>();
		List<Integer> orderIds = orderList.stream()
									  .map(Order::getOrderId)
									  .toList();
		try {
			List<OrderItemMetaDataDetail> metaDataList = orderItemMetaDataDetailDao.findByOrderIdIn(orderIds);
			if (CollectionUtils.isNotEmpty(metaDataList)) {
				for (OrderItemMetaDataDetail metaData : metaDataList) {
					orderItemToRuleMap.put(metaData.getOrderItemId(), metaData);
				}
			}
		} catch (Exception e) {
			log.warn("Failed to fetch metadata for order ids: {}, Error: {}", orderIds, e.getMessage());
		}
		
		return orderItemToRuleMap;
	}
}
