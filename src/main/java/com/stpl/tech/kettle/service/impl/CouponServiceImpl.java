package com.stpl.tech.kettle.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.converter.Converters;
import com.stpl.tech.kettle.data.master.CouponDetailData;
import com.stpl.tech.kettle.data.master.CouponDetailMappingData;
import com.stpl.tech.kettle.data.master.DeliveryCouponAllocationDetailData;
import com.stpl.tech.kettle.data.master.DeliveryCouponDetailData;
import com.stpl.tech.kettle.data.master.OfferDetailData;
import com.stpl.tech.kettle.domain.model.DeliveryCouponStatus;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.repository.master.CouponDetailDataDao;
import com.stpl.tech.kettle.repository.master.CouponDetailMappingDataDao;
import com.stpl.tech.kettle.repository.master.DeliveryCouponAllocationDetailDataDao;
import com.stpl.tech.kettle.repository.master.DeliveryCouponDetailDataDao;
import com.stpl.tech.kettle.repository.master.OfferDetailDataDao;
import com.stpl.tech.kettle.service.CouponService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.RandomStringGenerator;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.Constants.TransactionConstants;
import com.stpl.tech.kettle.util.adapter.JSONSerializer;
import com.stpl.tech.master.domain.model.CouponCloneRequest;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.master.domain.model.CouponData;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.master.domain.model.CouponMappingType;
import com.stpl.tech.master.domain.model.IdentifierType;
import com.stpl.tech.master.domain.model.OfferDetail;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class CouponServiceImpl implements CouponService {

	@Autowired
	private CouponDetailDataDao couponDetailDataDao;

	@Autowired
	private OfferDetailDataDao offerDetailDataDao;

	@Autowired
	private CouponDetailMappingDataDao detailMappingDataDao;

	@Autowired
	private DeliveryCouponDetailDataDao deliveryCouponDetailDataDao;

	@Autowired
	private DeliveryCouponAllocationDetailDataDao couponAllocationDetailDataDao;

	private static final int MAX_MAPPING_COUNT = 10;

	@Override
	public void applyLoyaltyCode(Order order) {
		boolean hasLoyaltyRedemption = false;
		for (OrderItem item : order.getOrders()) {
			if (Objects.nonNull(order) && Objects.nonNull(item.getComplimentaryDetail().getReasonCode())
					&& item.getComplimentaryDetail().getReasonCode() == AppConstants.COMPLEMENTARY_CODE_LOYALTY) {
				item.getComplimentaryDetail().setReason(TransactionConstants.SIGNUP_OFFER_CODE);
				hasLoyaltyRedemption = true;
			}
		}
		if (hasLoyaltyRedemption) {
			order.setOfferCode(TransactionConstants.SIGNUP_OFFER_CODE);
		}
	}

	@Override
	public void checkAmex(Order order) throws DataUpdationException {
		if (AppConstants.AMEX_COUPON_CODE.equalsIgnoreCase(order.getOfferCode())) {
			for (Settlement s : order.getSettlements()) {
				if (s.getMode() != AppConstants.PAYMENT_MODE_AMEX) {
					throw new DataUpdationException("Coupon can be used with AMEX payments only");
				}
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public List<String> getValidCoupon(List<String> coupons) {
		try {
			List<CouponDetailData> couponDetailData = couponDetailDataDao.findByCouponCodeIn(coupons);
			if (couponDetailData.size() == 0) {
				return Arrays.asList(coupons.get(0));
			}
			for (CouponDetailData data : couponDetailData) {
				for (String code : coupons) {
					if (!code.equals(data.getCouponCode())) {
						return Arrays.asList(code);
					}
				}
			}
			return new ArrayList<>();
		} catch (Exception e) {
			log.error("Error while fetching generated coupon codes that are already present for coupon code : {}",
					JSONSerializer.toJSON(coupons));
			return null;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public CouponCloneResponse generateCoupon(CouponCloneRequest coupon) throws DataUpdationException {
		log.info("Request for Clone Coupon with code: {}", coupon);
		long startTime = System.currentTimeMillis();
		CouponDetail cloned = getModalCoupon(coupon.getCode());
		CouponCloneResponse response = new CouponCloneResponse();
		response.setCode(coupon.getCode());
		response.setIdentifier(coupon.getIdentifierType());
		List<String> errors = new ArrayList<String>();
		int length = 0;
		Date startDate = null;
		Date endDate = null;
		String startDateString = null;
		String endDateString = null;
		List<String> couponCodes = new ArrayList<>();
		Integer usageCount = coupon.getUsageCount() == null ? 1 : coupon.getUsageCount();
		if (cloned == null) {
			errors.add("Coupon Code Does Not Exist : " + coupon.getCode());
		} else {
//			couponCodes = generateUniqueCoupons(coupon.getCode(), coupon.getPrefix(),
//					coupon.getIdentifier().size());
			couponCodes = new ArrayList<>();
			int maxIteration = 3;
			while (couponCodes.size() == 0 && maxIteration > 0) {
				couponCodes.add(new RandomStringGenerator().getRandomSingleCode(coupon.getPrefix(), 6));
				couponCodes.add(new RandomStringGenerator().getRandomSingleCode(coupon.getPrefix(), 6));
				couponCodes.add(new RandomStringGenerator().getRandomSingleCode(coupon.getPrefix(), 6));
				couponCodes = getValidCoupon(couponCodes);
				maxIteration = maxIteration - 1;
				if (maxIteration == 0 && couponCodes.isEmpty()) {
					errors.add("Maximum iteration reached and no offer generated");
				}
			}
			if (Objects.isNull(couponCodes) && couponCodes.isEmpty()) {
				errors.add("Unable to create Coupon code");
			}

			addCouponDetailData(coupon.getCode(), couponCodes.get(0));
			length = coupon.getIdentifier().size() < couponCodes.size() ? coupon.getIdentifier().size()
					: couponCodes.size();
			OfferDetail offer = null;
			Optional<OfferDetailData> offerDetailData = offerDetailDataDao.findById(cloned.getOffer().getId());
			if (offerDetailData.isPresent()) {
				offer = Converters.convert(offerDetailData.get(), true, false);
			}
			response.setDescription(offer.getDescription());
			if (!AppConstants.ACTIVE.equals(offer.getStatus())) {
				errors.add("Offer is in " + offer.getStatus() + " state");
			}
			if (coupon.getStartDay() != null) {
				startDate = AppUtils.getDate(coupon.getStartDay(), AppConstants.DATE_FORMAT);
			} else {
				startDate = AppUtils.getNextDate(AppUtils.getBusinessDate());
			}
			if (coupon.getValidDays() != null) {
				endDate = AppUtils.getDayBeforeOrAfterDay(startDate, coupon.getValidDays() - 1);
			} else {
				endDate = cloned.getEndDate();
				if (endDate.before(startDate)) {
					errors.add("Coupon End Date " + endDate + " cannot be before startDate  " + startDate
							+ " as it is picked up from cloned coupon end date which is " + endDate);
				}
			}
			startDateString = AppUtils.getDateString(startDate, AppConstants.DATE_FORMAT);
			endDateString = AppUtils.getDateString(endDate, AppConstants.DATE_FORMAT);
			if (endDate.after(offer.getEndDate())) {
				errors.add("Offer expires on " + offer.getEndDate() + " which is prior to coupon end date "
						+ endDateString);
			}

		}
		if (errors != null && errors.size() > 0) {
			response.setErrors(errors);
			return response;
		}
		Map<String, CouponData> map = new HashMap<>();
		for (int i = 0; i < length; i++) {
			String generatedCoupon = couponCodes.get(i);
			String identifier = coupon.getIdentifier().get(i);
			CouponDetail detail = searchCoupon(generatedCoupon, false);
			if (identifier != null) {
				CouponMapping mapping = new CouponMapping();
				mapping.setDataType(String.class.getCanonicalName());
				mapping.setGroup(1);
				mapping.setMinValue("1");
				mapping.setStatus("ACTIVE");
				if (IdentifierType.CONTACT_NUMBER.equals(coupon.getIdentifierType())) {
					mapping.setType(CouponMappingType.CONTACT_NUMBER.name());
					mapping.setValue(identifier);
				} else if (IdentifierType.CUSTOMER_ID.equals(coupon.getIdentifierType())) {
					mapping.setType(CouponMappingType.CUSTOMER.name());
					mapping.setValue(identifier);
				}
				detail.getCouponMappingList().add(mapping);
				if (coupon.getApplicableRegion() != null) {
					CouponMapping mapping1 = new CouponMapping();
					mapping1.setDataType(String.class.getCanonicalName());
					mapping1.setGroup(1);
					mapping1.setMinValue("1");
					mapping1.setStatus("ACTIVE");
					mapping1.setType(CouponMappingType.UNIT_REGION.name());
					mapping1.setValue(coupon.getApplicableRegion());
					detail.getCouponMappingList().add(mapping1);
				}
				if (coupon.getApplicableRegion() == null && coupon.getApplicableUnitId() != null) {
					CouponMapping mapping2 = new CouponMapping();
					mapping2.setDataType(String.class.getCanonicalName());
					mapping2.setGroup(1);
					mapping2.setMinValue("1");
					mapping2.setStatus("ACTIVE");
					mapping2.setType(CouponMappingType.UNIT.name());
					mapping2.setValue(coupon.getApplicableUnitId() + "");
					detail.getCouponMappingList().add(mapping2);
				}

			}
			detail.setStartDate(startDate);
			detail.setEndDate(endDate);
			detail.setUsage(0);
			detail.setMaxUsage(usageCount);
			detail.setMaxCustomerUsage(coupon.getMaxCustomerUsage());
			if (usageCount > 1) {
				detail.setReusable(true);
				detail.setReusableByCustomer(true);
			} else {
				detail.setReusable(false);
				detail.setReusableByCustomer(false);
			}
			map.put(identifier, new CouponData(generatedCoupon, startDateString, endDateString, usageCount,
					detail.getId(), detail.getOffer().getId()));
			updateCoupon(detail, true);
		}
		log.info("DINE_IN_POST_OFFER coupon generated in , ---------------- ,{}, milliseconds",
				System.currentTimeMillis() - startTime);
		response.setMappings(map);
		return response;
	}

	@Override
	public CouponDetail getModalCoupon(String modelCouponCode) {
		CouponDetail modelCoupon = getCouponDetail(modelCouponCode, true, false, false);
		if (modelCoupon == null) {
			return null;
		}
		modelCoupon = removeIds(modelCoupon);
		return modelCoupon;
	}

	private CouponDetail removeIds(CouponDetail modelCoupon) {
		modelCoupon.setId(0);
		for (int i = 0; i < modelCoupon.getCouponMappingList().size(); i++) {
			modelCoupon.getCouponMappingList().get(i).setId(0);
		}
		return modelCoupon;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public CouponDetail getCouponDetail(String couponCode, boolean getAll, boolean getParentMapping,
			boolean applyLimit) {
		try {
			CouponDetailData o = couponDetailDataDao.findByCouponCode(couponCode);
			if (applyLimit) {
				int lastIndex = o.getMappings().size();
				int startIndex = o.getMappings().size() > MAX_MAPPING_COUNT ? lastIndex - MAX_MAPPING_COUNT : 0;
				List<CouponDetailMappingData> newMappingData = o.getMappings().subList(startIndex, lastIndex);
				o.setMappings(newMappingData);
			}
			return o == null ? null : Converters.convert(o, getAll, getParentMapping);
		} catch (Exception e) {
			log.info("Error while getting Coupon Code: {} , {}", couponCode, e.getMessage());
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public CouponDetail addCouponDetailData(String code, String newCode) {
		CouponDetail detail = getModalCoupon(code);
		detail.setCode(newCode);
		CouponDetailData couponDetailData = new CouponDetailData();
		setCouponValues(couponDetailData, detail);
		couponDetailData = couponDetailDataDao.save(couponDetailData);
		setCouponMappings(couponDetailData, detail);
		couponDetailData.getOfferDetail().getAccountsCategory().getBudgetCategory();
		CouponDetail couponDetail = Converters.convert(couponDetailData, true, false);
		return couponDetail;
	}

	private void setCouponValues(CouponDetailData couponDetailData, CouponDetail coupon) {
		couponDetailData.setCouponCode(coupon.getCode());
		Optional<OfferDetailData> data = offerDetailDataDao.findById(coupon.getOffer().getId());
		if (data.isPresent()) {
			couponDetailData.setOfferDetail(data.get());
		}
		couponDetailData.setCouponReuse(AppConstants.getValue(coupon.isReusable()));
		couponDetailData.setCustomerReuse(AppConstants.getValue(coupon.isReusableByCustomer()));
		couponDetailData.setStartDate(AppUtils.getDate(coupon.getStartDate()));
		couponDetailData.setEndDate(AppUtils.getDate(coupon.getEndDate()));
		couponDetailData.setCouponStatus(AppUtils.getCorrectStatus(coupon.getStatus()));
		couponDetailData.setMaxUsage(coupon.getMaxUsage());
		couponDetailData.setMaxCustomerUsage(coupon.getMaxCustomerUsage());
		couponDetailData.setUsageCount(coupon.getUsage());
		couponDetailData.setManualOverride(AppConstants.getValue(coupon.isManualOverride()));
	}

	private void setCouponMappings(CouponDetailData couponDetailData, CouponDetail coupon) {
		if (couponDetailData.getMappings() == null) {
			couponDetailData.setMappings(new ArrayList<>());
		}
		coupon.getCouponMappingList()
				.forEach(p -> couponDetailData.getMappings().add(createOrUpdateCouponMappingData(p, couponDetailData)));
	}

	private CouponDetailMappingData createOrUpdateCouponMappingData(CouponMapping couponMapping,
			CouponDetailData couponDetailData) {

		CouponDetailMappingData data = null;

		if (couponMapping.getId() > 0) {
			Optional<CouponDetailMappingData> detailData = detailMappingDataDao.findById(couponMapping.getId());
			if (detailData.isPresent()) {
				data = detailData.get();
			}
		}
		if (data == null) {
			data = new CouponDetailMappingData();
			setValues(couponMapping, couponDetailData, data);
			detailMappingDataDao.save(data);
		} else {
			setValues(couponMapping, couponDetailData, data);
		}
		return data;
	}

	private void setValues(CouponMapping couponMapping, CouponDetailData couponDetailData,
			CouponDetailMappingData data) {
		data.setCouponDetail(couponDetailData);
		data.setMappingType(couponMapping.getType());
		data.setMappingValue(couponMapping.getValue());
		data.setMinValue(couponMapping.getMinValue());
		data.setDimension(couponMapping.getDimension());
		data.setStatus(AppUtils.getCorrectStatus(couponMapping.getStatus()));
		data.setDataType(couponMapping.getDataType());
		data.setMappingGroup(couponMapping.getGroup());
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public CouponDetail searchCoupon(String couponCode, boolean applyLimit) {
		return getCouponDetail(couponCode, true, true, applyLimit);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void addCustomerMappingCouponData(CouponDetailData couponDetail, String contactNumber) {
		CouponDetailMappingData couponMapping = new CouponDetailMappingData();
		couponMapping.setCouponDetail(couponDetail);
		couponMapping.setDataType(String.class.getCanonicalName());
		couponMapping.setMappingGroup(1);
		couponMapping.setMappingType(CouponMappingType.CONTACT_NUMBER.name());
		couponMapping.setMappingValue(contactNumber);
		couponMapping.setMinValue("1");
		couponMapping.setStatus(AppConstants.ACTIVE);
		detailMappingDataDao.save(couponMapping);
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public CouponDetail updateCoupon(CouponDetail coupon, boolean updateMappings) {
		Optional<CouponDetailData> data = couponDetailDataDao.findById(coupon.getId());
		if (data.isPresent()) {
			CouponDetailData couponDetailData = data.get();
			setCouponValues(couponDetailData, coupon);
			if (updateMappings) {
				setCouponMappings(couponDetailData, coupon);
			}
			return Converters.convert(couponDetailData, true, false);
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateAllocationOfCoupon(DeliveryCouponDetailData deliveryCoupon, Integer customerId,
			String contactNumber, Integer campaignId, int orderId) {
		log.info("Update coupon detail after allocation for Coupon id : {}", deliveryCoupon.getDeliveryCouponId());
		Optional<DeliveryCouponDetailData> data = deliveryCouponDetailDataDao
				.findById(deliveryCoupon.getDeliveryCouponId());
		DeliveryCouponDetailData couponDetailData = null;
		if (data.isPresent()) {
			couponDetailData = data.get();
		}
		Date allotmentTime = AppUtils.getCurrentTimestamp();
		couponDetailData.setNoOfAllocations(couponDetailData.getNoOfAllocations() + 1);
		couponDetailData.setLastAllocationTime(allotmentTime);
		couponDetailData.setDeliveryCouponStatus(DeliveryCouponStatus.AVAILABLE.name());
		if (couponDetailData.getNoOfAllocations().equals(couponDetailData.getMaxNoOfDistributions())) {
			couponDetailData.setIsExhausted(AppConstants.getValue(true));
			couponDetailData.setDeliveryCouponStatus(DeliveryCouponStatus.ALLOTTED.name());
		}
		DeliveryCouponAllocationDetailData allocationData = new DeliveryCouponAllocationDetailData();
		allocationData.setDeliveryCouponId(couponDetailData.getDeliveryCouponId());
		allocationData.setCustomerId(customerId);
		allocationData.setAllotmentTime(allotmentTime);
		allocationData.setCampaignId(campaignId);
		allocationData.setContactNumber(contactNumber);
		allocationData.setAllotmentOrderId(orderId);
		couponAllocationDetailDataDao.save(allocationData);
		deliveryCouponDetailDataDao.save(couponDetailData);
	}

}
