package com.stpl.tech.kettle.service.impl;

import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.data.kettle.EmployeeMealData;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.repository.kettle.EmployeeMealDataDao;
import com.stpl.tech.kettle.service.OrderSearchService;
import com.stpl.tech.kettle.util.AppUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class OrderSearchServiceImpl implements OrderSearchService {

	@Autowired
	EmployeeMealDataDao employeeMealDataDao;

	@Autowired
	UnitCacheService unitCacheService;

	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public List<EmployeeMealData> getEmployeeMealData(int employeeId) {
		return employeeMealDataDao.findByOrderStatusEmployeeIdAndBusinessDate(OrderStatus.CANCELLED.name(),
				employeeId, AppUtils.getCurrentBusinessDate());
	}

}
