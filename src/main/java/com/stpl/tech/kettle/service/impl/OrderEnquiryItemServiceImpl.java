package com.stpl.tech.kettle.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.data.kettle.OrderEnquiryItem;
import com.stpl.tech.kettle.domain.model.EnquiryItem;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.repository.kettle.OrderEnquiryItemDao;
import com.stpl.tech.kettle.service.OrderEnquiryItemService;
import com.stpl.tech.kettle.util.AppUtils;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class OrderEnquiryItemServiceImpl implements OrderEnquiryItemService {

	@Autowired
	private OrderEnquiryItemDao enquiryItemDao;

	@Override
	public boolean addOrderEnquiryItems(Order order, Integer orderId) {
		try {
			List<EnquiryItem> enquiryItems = order.getEnquiryItems();
			Date time = AppUtils.getCurrentTimestamp();
			for (EnquiryItem enquiryItem : enquiryItems) {
				OrderEnquiryItem orderEnquiryItem = new OrderEnquiryItem();
				orderEnquiryItem.setOrderId(orderId);
				orderEnquiryItem.setProductId(enquiryItem.getId());
				orderEnquiryItem.setCustomerId(enquiryItem.getLinkedCustomerId());
				orderEnquiryItem.setUnitId(enquiryItem.getLinkedUnitId());
				orderEnquiryItem.setAvailableQuantity(enquiryItem.getAvailableQuantity());
				orderEnquiryItem.setOrderderedQuantity(enquiryItem.getOrderedQuantity());
				orderEnquiryItem.setReplacementServed(enquiryItem.isReplacementServed());
				orderEnquiryItem.setEnquiryTime(time);
				orderEnquiryItem.setEnquiryOrderId(Long.valueOf(time.getTime()).toString());
				enquiryItemDao.save(orderEnquiryItem);
			}
			return true;
		} catch (Exception e) {
			log.info("Failed to save enquiry items for orderId: {} ", orderId);
			return false;
		}
	}

}
