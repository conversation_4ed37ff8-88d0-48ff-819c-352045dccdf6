package com.stpl.tech.kettle.service.impl;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import javax.jms.JMSException;
import javax.jms.MessageProducer;
import javax.jms.Session;

import org.springframework.beans.factory.annotation.Autowired;

import com.amazon.sqs.javamessaging.SQSQueueDestination;
import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.MessageAttributeValue;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.amazonaws.services.sqs.model.SendMessageResult;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.service.SQSNotificationService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.kettle.util.RandomStringGenerator;
import com.stpl.tech.master.notification.SQSNotification;

import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class SQSNotificationServiceImpl implements SQSNotificationService {

	    private AmazonSQS sqs;
	    private Map<String, MessageProducer> messageProducers = new HashMap<>();
	    private SQSSession session;
	    private SQSSession fifoQueueSession;
	    private Map<String, MessageProducer> fifoMessageProducers = new HashMap<>();
	    private RandomStringGenerator randomStringGenerator = new RandomStringGenerator();

	    @Autowired
	    private EnvironmentProperties env;

	    @PostConstruct
	    public void setQueueSessions() throws JMSException {
	    	log.info("POST-CONSTRUCT SQSNotificationServiceImpl - STARTED");
	    	EnvType envType = env.getEnvironmentType();
	        Regions region = AppUtils.getRegion(envType);
	        sqs = SQSNotification.getInstance().getSqsConnection(region).getAmazonSQSClient();
	        session = SQSNotification.getInstance().getSession(region, Session.AUTO_ACKNOWLEDGE);
	        fifoQueueSession = SQSNotification.getInstance().getSession(Regions.EU_WEST_1, Session.AUTO_ACKNOWLEDGE);
	    }

		@Override
		public <T extends Serializable> void publishToSQS(String env, T response, String queueNameSuffix)
				throws JMSException {
			if (!messageProducers.containsKey(queueNameSuffix)) {
				messageProducers.put(queueNameSuffix,
						SQSNotification.getInstance().getProducer(session, env, queueNameSuffix));
			}
			messageProducers.get(queueNameSuffix).send(session.createObjectMessage(response));
		}

		@Override
		public <T extends Serializable> void publishToSQS(String env, T response, String queueNameSuffix, Regions region)
				throws JMSException {
	        SQSSession session = SQSNotification.getInstance().getSession(region, Session.AUTO_ACKNOWLEDGE);
			if (!messageProducers.containsKey(queueNameSuffix)) {
				messageProducers.put(queueNameSuffix,
						SQSNotification.getInstance().getProducer(session, env, queueNameSuffix));
			}
			messageProducers.get(queueNameSuffix).send(session.createObjectMessage(response));
		}

		@Override
	    public <T extends Serializable> void publishToSQSFifo(String env, T event, String queueNameSuffix, Regions region) throws JMSException {
	        publishToSQSFifo(env, event, queueNameSuffix, "messageGroup1", region);
	    }

	@Override
	public <T extends Serializable> void publishToSQSFifo(String env, T event, String queueNameSuffix, Regions region,String messageGrpId) throws JMSException {
		publishToSQSFifo(env, event, queueNameSuffix, messageGrpId, region);
	}

	    private <T extends Serializable> void publishToSQSFifo(String env, T event, String queueNameSuffix, String messageGroupId, Regions region) throws JMSException {
	        /*String dedupId = AppUtils.getCurrentTimeISTStringWithNoColons() + randomStringGenerator.getRandomCode(6);
	        if (!fifoMessageProducers.containsKey(queueNameSuffix)) {
	            fifoMessageProducers.put(queueNameSuffix,
	                SQSNotification.getInstance().getProducer(fifoQueueSession, env, queueNameSuffix));
	        }
	        TextMessage textMessage = fifoQueueSession.createTextMessage(AppUtils.serialize(event));
	        textMessage.setStringProperty("JMSXGroupID", messageGroupId);
	        textMessage.setStringProperty("JMS_SQS_DeduplicationId", dedupId);
	        fifoMessageProducers.get(queueNameSuffix).send(textMessage);
	        log.info("EVENT SENT:::: messageId " + textMessage.getJMSMessageID() + ", sequence number " + textMessage.getStringProperty("JMS_SQS_SequenceNumber")
	            + "\n" + new Gson().toJson(event) + "dedupId::: " + dedupId);*/
	        String dedupId = AppUtils.getCurrentTimeISTStringWithNoColons() + randomStringGenerator.getRandomCode(6);
	        final Map<String, MessageAttributeValue> attributes = new HashMap<>();
	        attributes.put("FifoQueue", new MessageAttributeValue().withDataType("String").withStringValue("true"));
	        attributes.put("ContentBasedDeduplication", new MessageAttributeValue().withDataType("String").withStringValue("false"));
	        SQSQueueDestination queue = (SQSQueueDestination) SQSNotification.getInstance().
	                getQueue(SQSNotification.getInstance().getSession(region, Session.AUTO_ACKNOWLEDGE), env, queueNameSuffix);
	        final SendMessageRequest sendMessageRequest = new SendMessageRequest(queue.getQueueUrl(), AppUtils.serialize(event));
	        sendMessageRequest.setMessageGroupId(messageGroupId);
	        sendMessageRequest.setMessageDeduplicationId(dedupId);
	        sendMessageRequest.setMessageAttributes(attributes);
	        final SendMessageResult sendMessageResult = sqs.sendMessage(sendMessageRequest);
	        final String sequenceNumber = sendMessageResult.getSequenceNumber();
	        final String messageId = sendMessageResult.getMessageId();
	        log.info("EVENT SENT:::: messageId " + messageId + ", sequence number " + sequenceNumber  + "dedupId::: " + dedupId);
	    }

}
