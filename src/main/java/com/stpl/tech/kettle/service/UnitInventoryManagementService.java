package com.stpl.tech.kettle.service;

import java.util.List;

import com.stpl.tech.kettle.domain.model.InventoryUpdateEvent;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.master.domain.model.ProductInventory;

public interface UnitInventoryManagementService {

	List<ProductInventory> getUnitInventoryForProducts(int unitId, List<Integer> productIds)
			throws DataNotFoundException;

	public boolean updateUnitInventory(InventoryUpdateEvent updateData, boolean generateEvents,
			boolean incrementalUpdate, int employeeId, Integer orderId, boolean isCancellation);
}
