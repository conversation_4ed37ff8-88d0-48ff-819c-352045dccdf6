package com.stpl.tech.kettle.service.impl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import com.google.gson.GsonBuilder;
import com.stpl.tech.kettle.util.adapter.DateAdapter;
import com.stpl.tech.kettle.util.adapter.DateDeserializer2;
import com.stpl.tech.kettle.util.adapter.JSONSerializer;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ClientHttpConnector;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import com.google.gson.Gson;
import com.stpl.tech.kettle.exceptions.WebServiceCallException;
import com.stpl.tech.kettle.service.WebClientService;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import lombok.extern.log4j.Log4j2;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

@Log4j2
@Service
public class WebClientServiceImpl implements WebClientService {

	@Override
	public String getRequest(String url) {
		WebClient client = WebClient.create();
		WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.GET);
		WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
		return getFinalResponse(bodySpec);
	}

	@Override
	public String postRequest(String url, Map<String, String> bodyMap) {
		WebClient client = WebClient.create();
		WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.POST);
		WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
		WebClient.RequestHeadersSpec<?> headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON).body(BodyInserters.fromValue(bodyMap));
		return getFinalResponse(headersSpec);
	}

	public String postRequest(String url, Object object) {
		WebClient client = WebClient.create();
		WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.POST);
		WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
		WebClient.RequestHeadersSpec<?> headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON).body(BodyInserters.fromValue(object));
		return getFinalResponse(headersSpec);
	}

	@Override
	public String postRequestWithAuthInternal(String url, Map<String, String> bodyMap, String authInternal) {
		WebClient client = WebClient.create();
		WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.POST);
		WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
		WebClient.RequestHeadersSpec<?> headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON).header("auth-internal", authInternal)
				.body(BodyInserters.fromValue(bodyMap));
		return getFinalResponse(headersSpec);
	}

	@Override
	public String getRequest(String url, Object body, String authInternal, Map<String, String> uriVariables) {
		WebClient client = WebClient.create();
		WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.GET);
		if (uriVariables != null) {
			url += "?";
			for (String key : uriVariables.keySet()) {
				url = url + key + "=" + uriVariables.get(key).toString() + "&";
			}
			url = url.substring(0, url.length() - 1);
		}
		WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
		WebClient.RequestHeadersSpec<?> headersSpec;
		if (Objects.nonNull(body)) {
			headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON).contentType(MediaType.APPLICATION_JSON)
					.header("auth-internal", authInternal).body(BodyInserters.fromValue(body));
		} else {
			headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON).contentType(MediaType.APPLICATION_JSON)
					.header("auth-internal", authInternal);
		}

		return getFinalResponse(headersSpec);

	}

	@Override
	public String postRequest(String url, Object body, String authInternal, Map<String, String> uriVariables) {
		WebClient client = WebClient.create();
		WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.POST);
		if (uriVariables != null) {
			url += "?";
			for (String key : uriVariables.keySet()) {
				url = url + key + "=" + uriVariables.get(key).toString() + "&";
			}
			url = url.substring(0, url.length() - 1);
		}
		WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
		WebClient.RequestHeadersSpec<?> headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON).header("auth-internal", authInternal)
				.body(BodyInserters.fromValue(body));
		return getFinalResponse(headersSpec);
	}

	private String getFinalResponse(WebClient.RequestHeadersSpec<?> headersSpec) {

		Mono<String> finalResponse = headersSpec.exchangeToMono(response -> {
			if (response.statusCode().equals(HttpStatus.OK)) {
				return response.bodyToMono(String.class);
			} else if (response.statusCode().isError()) {
				return response.bodyToMono(String.class).flatMap(errorBody -> Mono.error(new Exception(errorBody)));
			} else {
				return response.createException().flatMap(Mono::error);
			}
		});
		try {
			return finalResponse.block();
		} catch (Exception e) {
			log.error("::Error in webclient request", e);
			throw e;
		}
	}

	@Override
	public String getRequestWithAuthInternal(String url, String authInternal) {
		WebClient client = WebClient.create();
		WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.GET);
		WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
		WebClient.RequestHeadersSpec<?> headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON).header("auth-internal", authInternal);
		return getFinalResponse(headersSpec);
	}

	@Override
	public String getRequestWithAuth(String url, String bodyText, String auth, Map<String, String> uriVariables) {
		WebClient client = WebClient.create();
		WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.GET);
		if (uriVariables != null) {
			url += "?";
			for (String key : uriVariables.keySet()) {
				url = url + key + "=" + uriVariables.get(key).toString() + "&";
			}
			url = url.substring(0, url.length() - 1);
		}
		WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
		WebClient.RequestHeadersSpec<?> headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.TEXT_PLAIN).header("auth", auth)
				.body(BodyInserters.fromPublisher(Mono.just(bodyText), String.class));
		return getFinalResponse(headersSpec);
	}

	@Override
	public <T> T parseResponse(String response, Class<T> c) {
		return JSONSerializer.toJSON(response,c);
	}

	<T> T convertResponse(HttpResponse response, Class<T> responseClazz, boolean usejsonSerializer)
			throws IllegalStateException, IOException {
		if (response != null) {
			BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
			StringBuffer result = new StringBuffer();
			String line = "";
			while ((line = reader.readLine()) != null) {
				result.append(line);
			}
			// LOG.info("recorded response :::: {}", result.toString());
			EntityUtils.consume(response.getEntity());
			if (usejsonSerializer) {
				return parseResponse(result.toString(), responseClazz);

			} else {
				return parseResponse(result.toString(), responseClazz);

			}
		}

		return null;

	}

	@Override
	public <T> T callWebServiceWithTimeout(Class<T> clazz, String endPoint, Object object, int socketTimeOut,
			int connectionTimeout) throws WebServiceCallException {
		String response = createPostRequestWithTimeout(endPoint, object, socketTimeOut, connectionTimeout);
		return parseResponse(response, clazz);
	}

	@Override
	public String createPostRequestWithTimeout(String url, Object object, int readTimeOut, int connectionTimeout) {
		WebClient client = createWebClientWithConnectAndReadTimeOuts(connectionTimeout, readTimeOut);
		WebClient.UriSpec<WebClient.RequestBodySpec> uriSpec = client.method(HttpMethod.POST);
		WebClient.RequestBodySpec bodySpec = uriSpec.uri(url);
		WebClient.RequestHeadersSpec<?> headersSpec = bodySpec.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON).body(BodyInserters.fromValue(object));
		return getFinalResponse(headersSpec);
	}

	private WebClient createWebClientWithConnectAndReadTimeOuts(int connectTimeOut, long readTimeOut) {
		HttpClient httpClient = HttpClient.create().option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectTimeOut)
				.doOnConnected(conn -> conn.addHandlerLast(new ReadTimeoutHandler(readTimeOut, TimeUnit.MILLISECONDS))
						.addHandlerLast(new ReadTimeoutHandler(readTimeOut, TimeUnit.MILLISECONDS)));
		ClientHttpConnector connector = new ReactorClientHttpConnector(httpClient);
		return WebClient.builder().clientConnector(connector).build();
	}
}
