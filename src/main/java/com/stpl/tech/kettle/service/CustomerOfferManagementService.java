package com.stpl.tech.kettle.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

import com.stpl.tech.kettle.data.kettle.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.master.DeliveryCouponDetailData;
import com.stpl.tech.kettle.domain.model.*;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CouponData;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.master.domain.model.CouponMappingType;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;

public interface CustomerOfferManagementService {
	OfferOrder applyCoupoun(OfferOrder offerOrder, BigDecimal offerValue)
			throws DataNotFoundException, OfferValidationException;

	void checkCouponMapping(Set<CouponMapping> mappingList, List<CouponMapping> values, CouponMappingType type)
			throws OfferValidationException, DataNotFoundException;

	Boolean updateOfferApplicationDetails(String couponCode, Integer customerId, Integer orderId, BigDecimal savings);

	SubscriptionInfoDetail getSubscriptionInfoDetail(int customerId);

	boolean hasCustomerReceivedPostOrderOffer(int customerId, Integer lastNDays, Integer campaignId);

	CustomerDineInView getCustomerDineInView(int customerId, Integer brandId, List<Integer> excludeOrderIds);

	CustomerCampaignOfferDetail getActiveCustomerOffer(int customerId, String strategy);

	public CustomerCampaignOfferDetail createPostOrderOffer(Integer brandId, Integer unitId, Integer campaignId,
			int customerId, Integer orderId, String contactNumber, String countryCode, CouponData response,
			String firstName, String description, String cloneCopunCode, CampaignDetail campaignDetail,
			CustomerRepeatType type, Integer journeyNumber, CreateNextOfferRequest request);

	boolean hasCustomerReceivedDNBO(int customerId, Integer dinePostOrderOfferCheckLastNDaysValue, Integer campaignId);

	DeliveryCouponDetailData getDeliveryCloneCode(String code, Integer brandId, Boolean getClonedCoupon);

	void addSubscriptionSaving(int customerId, Order overAllSaving, Pair<CouponDetail, Product> subscriptionObj);


	CustomerCampaignOfferDetail createDeliveryPostOrderOffer(Integer brandId, Integer unitId, Integer campaignId,
			Customer customer, Integer orderId, String contactNumber, String countryCode, CouponData response,
			String firstName, String description, String cloneCouponCode, DeliveryCouponDetailData deliveryCoupon,
			Boolean isCloneCoupon, CampaignDetail campaignDetail, CustomerRepeatType type, Integer journeyNumber,
			CreateNextOfferRequest request);

	CustomerEmailData getCustomerEmailData(int customerId, Integer brandId);

	DenominationValueData getDenominationOffer(Integer unitId);

	DenominationValueData getDirectWalletOffers(Integer unitId);


	WalletRecommendationDetail getSuggestWalletOfferExtraAmount(WalletSuggestionCustomerInfo customerData, Integer unitId) throws Exception ;

	void refreshDenominationOfferValue();
	void refreshUnitDenominationOfferValue(Integer unitId);

	public Integer getLastOrderedSavedChai(Integer customerId);

	public SavedChaiOrderedDomain getSavedChaiOrderDetail(Integer customerId);

}
