package com.stpl.tech.kettle.service.impl;

import com.stpl.tech.kettle.data.kettle.CustomerOfferDetail;
import com.stpl.tech.kettle.data.master.CouponDetailData;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.exceptions.WebErrorCode;
import com.stpl.tech.kettle.lock.service.SyncLock;
import com.stpl.tech.kettle.repository.kettle.CustomerOfferDao;
import com.stpl.tech.kettle.repository.kettle.OfferManagementDao;
import com.stpl.tech.kettle.service.CustomerService;
import com.stpl.tech.kettle.service.RevalidationService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.cache.OfferUsageCache;
import com.stpl.tech.master.domain.model.CustomerAppliedCouponDetail;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Log4j2
public class RevalidationServiceImpl implements RevalidationService {

    @Autowired
    private OfferManagementDao offerManagementDao;

    @Autowired
    private CustomerOfferDao customerOfferDetailDao;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private SyncLock lock;

    @Autowired
    private OfferUsageCache offerUsageCache;


    @Override
    public String revalidateCouponMaxUsageAndForCustomer(Order order) {
        String result = AppConstants.REVALIDATION_SUCCESSFUL;
        if (Objects.nonNull(order.getCustomerId()) && Objects.nonNull(order.getOfferCode())) {
            String key = order.getOfferCode() + "_" + order.getCustomerId();
            String offerKey = order.getOfferCode();
            result = lock.syncLock(offerKey, () -> {
                try {
                    if (offerUsageCache.getCustomerAppliedCouponDetailMap().containsKey(key) && offerUsageCache.getCustomerAppliedCouponDetailMap().containsKey(offerKey)) {
                        CustomerAppliedCouponDetail appliedCouponDetail = offerUsageCache.getCustomerAppliedCouponDetailMap().get(key);
                        CustomerAppliedCouponDetail customerAppliedCouponDetail = offerUsageCache.getCustomerAppliedCouponDetailMap().get(offerKey);
                        if (appliedCouponDetail.getUsageCount() + 1 <= appliedCouponDetail.getMaxUsage() && customerAppliedCouponDetail.getUsageCount() + 1 <= customerAppliedCouponDetail.getMaxUsage()) {
                            appliedCouponDetail.setUsageCount(appliedCouponDetail.getUsageCount() + 1);
                            customerAppliedCouponDetail.setUsageCount(customerAppliedCouponDetail.getUsageCount() + 1);
                            offerUsageCache.getCustomerAppliedCouponDetailMap().put(key, appliedCouponDetail);
                            offerUsageCache.getCustomerAppliedCouponDetailMap().put(offerKey, customerAppliedCouponDetail);
                            return AppConstants.REVALIDATION_SUCCESSFUL;
                        } else {
                            return WebErrorCode.COUPON_REUSABILITY_FOR_CUSTOMER_FAILED.getReason();
                        }
                    }
                    return revalidateFromDatabase(order, offerKey, offerUsageCache);
                }catch (Exception e){
                    log.info("Error in getting lock : {}",e);
                    return null;
                }
            });
        }
        return result;
    }

    private String revalidateFromDatabase(Order order, String key, OfferUsageCache offerUsageCache) {
        CouponDetailData coupon = offerManagementDao.getCouponWithoutMappings(order.getOfferCode());
        if (Objects.nonNull(coupon)) {
            log.info("Revalidating coupon usage coupon from database for key :::::: {}",key);
            if (!AppUtils.getStatus(coupon.getCouponReuse()) && coupon.getUsageCount() > 0) {
                return WebErrorCode.COUPON_REUSABILITY_FAILED.getReason();
            }
            CustomerOfferDetail customerOfferDetail = customerOfferDetailDao.findByOfferCodeAndCustomerId(coupon.getCouponCode(), order.getCustomerId());
            if (Objects.nonNull(customerOfferDetail)) {
                if (!AppUtils.getStatus(coupon.getCustomerReuse()) && AppConstants.YES.equalsIgnoreCase(customerOfferDetail.getAvailed())) {
                    if (!customerService.getOfferDetail(order.getCustomerId(), order.getOfferCode()).isEmpty()) {
                        return WebErrorCode.COUPON_REUSABILITY_FOR_CUSTOMER_FAILED.getReason();
                    }
                }
            }
            addingToCacheAfterSuccessfulValidation(offerUsageCache, key, coupon);
        }
        return AppConstants.REVALIDATION_SUCCESSFUL;
    }

    private void addingToCacheAfterSuccessfulValidation(OfferUsageCache offerUsageCache, String key, CouponDetailData coupon) {
        CustomerAppliedCouponDetail appliedCouponDetail = offerUsageCache.getCustomerAppliedCouponDetailMap().get(key);
        if (Objects.nonNull(appliedCouponDetail)) {
            appliedCouponDetail.setUsageCount(appliedCouponDetail.getUsageCount() + 1);
        } else {
            appliedCouponDetail = new CustomerAppliedCouponDetail(
                    AppUtils.getStatus(coupon.getCouponReuse()),
                    AppUtils.getStatus(coupon.getCustomerReuse()),
                    coupon.getMaxUsage(),
                    coupon.getUsageCount() + 1
            );
        }
        offerUsageCache.getCustomerAppliedCouponDetailMap().put(key, appliedCouponDetail);
    }

}
