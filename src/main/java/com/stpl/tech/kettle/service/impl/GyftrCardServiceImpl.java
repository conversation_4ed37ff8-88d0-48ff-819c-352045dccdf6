package com.stpl.tech.kettle.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectOutput;
import java.io.ObjectOutputStream;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.ExternalPartnerCardDetail;
import com.stpl.tech.kettle.data.kettle.PartnerCardStatus;
import com.stpl.tech.kettle.domain.model.CashCardType;
import com.stpl.tech.kettle.domain.model.ExternalSettlement;
import com.stpl.tech.kettle.domain.model.GyftrQueryVoucher;
import com.stpl.tech.kettle.domain.model.GyftrVBatchConsumeResult;
import com.stpl.tech.kettle.domain.model.GyftrVoucher;
import com.stpl.tech.kettle.domain.model.GyftrVoucherAction;
import com.stpl.tech.kettle.domain.model.GyftrVoucherQueryResult;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.SettlementType;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.exceptions.CardValidationException;
import com.stpl.tech.kettle.repository.kettle.ExternalPartnerCardDao;
import com.stpl.tech.kettle.service.WebClientService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.master.data.model.ExternalPartnerDetail;
import com.stpl.tech.master.domain.model.PartnerCardRequestStatus;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service("GyftrService")
public class GyftrCardServiceImpl extends PartnerCardServiceImpl {

	private final String GYFTR_CODE = CashCardType.GYFTR.name();

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private ExternalPartnerCardDao partnerCardDao;

	@Autowired
	private WebClientService webClientService;

	@Override
	public List<String> verifyVoucher(Order order, boolean consume) throws CardValidationException {
		List<String> voucherList = new ArrayList<>();
		Map<String, OrderItem> voucherItemMap = new HashMap<>();
		StringBuilder voucherString = new StringBuilder();
		String requestJobNumber = getUniquePartnerBillNumber();
		for (OrderItem item : order.getOrders()) {
			validateVoucherCode(item.getItemCode());
			GyftrVoucherQueryResult queryResult = queryGyftrVoucherDetail(item.getItemCode(), requestJobNumber, order);
			item.setValidUpto(AppUtils.parseDate(queryResult.getEndDate(), new SimpleDateFormat("dd-MMM-yyyy")));
			voucherItemMap.put(item.getItemCode(), item);
			voucherList.add(item.getItemCode());
			voucherString.append(item.getItemCode());
			if (voucherList.size() < order.getOrders().size()) {
				voucherString.append(",");
			}
		}
		GyftrVBatchConsumeResult consumeResponse = getGytrCardDetail(order, voucherString.toString(), requestJobNumber,
				voucherList);
		if (consumeResponse != null) {
			Map<String, GyftrVoucherAction> voucherMap = getVoucherMap(consumeResponse);
			BigDecimal amount = BigDecimal.ZERO;
			for (Map.Entry<String, OrderItem> voucher : voucherItemMap.entrySet()) {
				OrderItem item = voucher.getValue();
				BigDecimal value = new BigDecimal(voucherMap.get(voucher.getKey()).getVALUE());
				item.setPrice(value);
				item.setAmount(value);
				item.setTotalAmount(value);
				amount = AppUtils.add(amount, value);
			}
			updateTransactionDetail(amount, order.getTransactionDetail());
			order.setSettlementType(SettlementType.CREDIT);
			order.setSettlements(getSettlementDetail(amount));
			return voucherList;
		}
		return null;
	}

	private void validateVoucherCode(String voucherCode) throws CardValidationException {
		List<ExternalPartnerCardDetail> cardDetails = getPartnerCardDetail(voucherCode, GYFTR_CODE,
				PartnerCardRequestStatus.REDEEMED.name());
		if (cardDetails.size() > 0) {
			throw new CardValidationException("Gyftr Voucher Code : " + voucherCode + " is already CONSUMED.");
		}
	}

	private GyftrVoucherQueryResult queryGyftrVoucherDetail(String voucherCode, String requestJobNumber, Order order)
			throws CardValidationException {
		ExternalPartnerDetail partnerInfo = getExternalPartner(GYFTR_CODE);
		String url = null;
		GyftrVoucherQueryResult queryResult = null;
		try {
			url = getBuilderUrl(partnerInfo, voucherCode, "/QueryVoucher", requestJobNumber, order.getUnitId());
		} catch (URISyntaxException e) {
			log.error("Error in building GYFTR query URI ", e);
			throw new CardValidationException("Error in building GYFTR query URI :  " + e.getMessage());
		}
		try {
			GyftrQueryVoucher voucher = webClientService.parseResponse(webClientService.getRequest(url),
					GyftrQueryVoucher.class);
			if (voucher == null) {
				throw new CardValidationException(
						"Gytr Service unavailable right now for query.Please Try Again Later.");
			} else {
				log.info("GyftrQueryVoucher : " + voucher);
				queryResult = voucher.getVQueryVoucherResult().get(0);
				if (queryResult.getResultType().equalsIgnoreCase(PartnerCardRequestStatus.FAILED.name())) {
					throw new CardValidationException(queryResult.getErroMsg());
				} else if (queryResult.getResultType().equalsIgnoreCase(PartnerCardRequestStatus.SUCCESS.name())) {
					if (!queryResult.getStatus().equals(PartnerCardStatus.VALID.name())) {
						String msg = null;
						if (queryResult.getStatus().equalsIgnoreCase(PartnerCardStatus.INVALID.name())) {
							msg = "Gyftr Voucher Code : " + voucherCode + " is INVALID.";
						} else if (queryResult.getStatus().equalsIgnoreCase(PartnerCardStatus.EXPIRED.name())) {
							msg = "Gyftr Voucher Code : " + voucherCode + " is EXPIRED.";
						} else if (queryResult.getStatus().equalsIgnoreCase(PartnerCardStatus.CONSUMED.name())) {
							msg = "Gyftr Voucher Code : " + voucherCode + " is already CONSUMED.";
						} else {
							msg = "Gyftr Voucher Code : " + voucherCode + " is INVALID.";
						}
						throw new CardValidationException(msg);
					}
					return queryResult;
				}
			}
		} catch (Exception e) {
			log.error("Error while querying Gyftr Voucher Details :  ", e);
			throw new CardValidationException("Error while getting Gyftr Voucher Details :  " + e.getMessage());
		}
		return queryResult;
	}

	private GyftrVBatchConsumeResult getGytrCardDetail(Order order, String voucherCode, String requestJobNumber,
			List<String> voucherList) throws CardValidationException {
		ExternalPartnerDetail partnerInfo = getExternalPartner(GYFTR_CODE);
		List<ExternalPartnerCardDetail> cardDetails = addRequestLog(order, requestJobNumber, voucherList);
		String url = null;
		GyftrVBatchConsumeResult consumeResult = null;
		try {
			url = getBuilderUrl(partnerInfo, voucherCode, "/BatchConsume", requestJobNumber, order.getUnitId());
		} catch (URISyntaxException e) {
			log.error("Error in building GYFTR Consume URI ", e);
			throw new CardValidationException("Error in building GYFTR Consume URI :  " + e.getMessage());
		}
		try {
			ByteArrayOutputStream baos = null;
			GyftrVoucher voucher = webClientService.parseResponse(webClientService.getRequest(url), GyftrVoucher.class);
			if (voucher == null) {
				failureRequest("Gytr Service unavailable right now.Please Try Again Later.", cardDetails, "", baos);
			} else {
				log.info("GyftrVoucher : " + voucher);
				baos = new ByteArrayOutputStream();
				ObjectOutput out = null;
				try {
					out = new ObjectOutputStream(baos);
					out.writeObject(voucher);
					out.flush();
				} finally {
					try {
						baos.close();
					} catch (IOException e) {
						log.error("Error while serializing repsonse json :  ", e);
						throw new CardValidationException("Error while consuming Gyftr Voucher :  " + e.getMessage());
					}
				}
				consumeResult = voucher.getVBatchConsumeResult().get(0);
				if (consumeResult.getResultType().equalsIgnoreCase(PartnerCardRequestStatus.FAILED.name())) {
					failureRequest(consumeResult.getErrorMsg(), cardDetails, consumeResult.getAuthorizationCode(),
							baos);
				} else if (consumeResult.getResultType().equalsIgnoreCase(PartnerCardRequestStatus.SUCCESS.name())) {
					Map<String, GyftrVoucherAction> voucherValueMap = getVoucherMap(consumeResult);
					updateRequestLog(cardDetails, PartnerCardRequestStatus.SUCCESS.name(),
							consumeResult.getAuthorizationCode(), voucherValueMap, baos);
					return consumeResult;
				} else {
					failureRequest("Gytr Service unavailable right now for redemption.Please Try Again Later.",
							cardDetails, "", null);
				}
			}

		} catch (Exception e) {
			log.error("Error while getting Gyftr Voucher Details :  ", e);
		}
		return consumeResult;
	}

	private Map<String, GyftrVoucherAction> getVoucherMap(GyftrVBatchConsumeResult result) {
		HashMap<String, GyftrVoucherAction> voucherValueMap = new HashMap<>();
		for (GyftrVoucherAction action : result.getVOUCHERACTION()) {
			voucherValueMap.put(action.getVOUCHERNUMBER(), action);
		}
		return voucherValueMap;
	}

	private void updateTransactionDetail(BigDecimal amount, TransactionDetail transactionDetail) {
		transactionDetail.setPaidAmount(amount);
		transactionDetail.setTaxableAmount(amount);
		transactionDetail.setTotalAmount(amount);
	}

	private List<Settlement> getSettlementDetail(BigDecimal amount) {
		Settlement settlement = new Settlement();
		settlement.setMode(getExternalPartner(GYFTR_CODE).getLinkedPaymentModeId());
		settlement.setAmount(amount);
		settlement.setExternalSettlements(getExternalSettlement(amount));
		List<Settlement> list = new ArrayList<>();
		list.add(settlement);
		return list;
	}

	private List<ExternalSettlement> getExternalSettlement(BigDecimal amount) {
		ExternalSettlement externalSettlement = new ExternalSettlement();
		externalSettlement.setAmount(amount);
		externalSettlement
				.setExternalTransactionId(getExternalPartner(GYFTR_CODE).getLinkedCreditAccountId().toString());
		List<ExternalSettlement> external = new ArrayList<>();
		external.add(externalSettlement);
		return external;
	}

	private String getBuilderUrl(ExternalPartnerDetail partnerInfo, String voucherCode, String query,
			String requestJobNumber, int unitId) throws URISyntaxException {
		URIBuilder builder = new URIBuilder(partnerInfo.getEndPoint().concat(query));
		builder.setParameter("deviceCode", "p");
		log.info("merchantUid : " + partnerInfo.getUsername());
		builder.setParameter("merchantUid", partnerInfo.getUsername());
		if (AppUtils.isProd(properties.getEnvironmentType())) {
			builder.setParameter("ShopCode", String.valueOf(unitId));
		} else {
			builder.setParameter("ShopCode", "WEBTEST");
		}
		builder.setParameter("voucherNumber", voucherCode);
		log.info("Password : " + partnerInfo.getPassCode());
		builder.setParameter("Password", partnerInfo.getPassCode());
		builder.setParameter("requestJobNumber", requestJobNumber);
		builder.setParameter("BillValue", "0");
		String uri = builder.toString().replaceAll("%2C", ",");
		;
		log.info("Gyftr URL : " + uri);
		return uri;
	}

	private List<ExternalPartnerCardDetail> addRequestLog(Order order, String billNumber, List<String> voucherList) {
		List<ExternalPartnerCardDetail> cardDetails = new ArrayList<>();
		Date date = AppUtils.getCurrentTimestamp();
		for (String voucherCode : voucherList) {
			ExternalPartnerCardDetail cardDetail = new ExternalPartnerCardDetail();
			cardDetail.setExternalOrderId(billNumber);
			cardDetail.setPartnerCardNumber(voucherCode);
			cardDetail.setPartnerCode(GYFTR_CODE);
			cardDetail.setRequestSource(order.getSource());
			cardDetail.setRequestTime(date);
			cardDetail.setCustomerId(order.getCustomerId());
			cardDetail.setCustomerName(order.getCustomerName());
			cardDetail.setRequestStatus(PartnerCardRequestStatus.INITIATED.name());
			cardDetail = (ExternalPartnerCardDetail) partnerCardDao.save(cardDetail);
			cardDetails.add(cardDetail);
		}
		return cardDetails;
	}

	private void failureRequest(String msg, List<ExternalPartnerCardDetail> cardDetails, String authCode,
			ByteArrayOutputStream baos) throws CardValidationException {
		updateRequestLog(cardDetails, PartnerCardRequestStatus.FAILED.name(), authCode, null, baos);
		throw new CardValidationException(msg);
	}

	private void updateRequestLog(List<ExternalPartnerCardDetail> cardDetails, String reqStatus, String tarnsId,
			Map<String, GyftrVoucherAction> voucherMap, ByteArrayOutputStream baos) throws CardValidationException {
		Date date = AppUtils.getCurrentTimestamp();
		for (ExternalPartnerCardDetail detail : cardDetails) {
			Optional<ExternalPartnerCardDetail> cardDetail = partnerCardDao.findById(detail.getCardDetailId());
			if (cardDetail.isPresent()) {
				cardDetail.get().setRequestStatus(reqStatus);
				cardDetail.get().setResponseTime(date);
				cardDetail.get().setPartnerTransactionId(tarnsId);
				cardDetail.get()
						.setTransactionAmount(voucherMap != null
								? new BigDecimal(voucherMap.get(cardDetail.get().getPartnerCardNumber()).getVALUE())
								: BigDecimal.ZERO);
				if (baos != null) {
					cardDetail.get().setResponse(baos.toByteArray());
				}
				partnerCardDao.save(cardDetail.get());
			}
		}
	}

	@Override
	public void updateVoucher(String voucherCode, String cardNumber, String partnerCode) {
		ExternalPartnerCardDetail cardDetail = getPartnerCardDetail(voucherCode, partnerCode,
				PartnerCardRequestStatus.SUCCESS.name()).get(0);
		cardDetail.setCardNumber(cardNumber);
		cardDetail.setRequestStatus(PartnerCardRequestStatus.REDEEMED.name());
		partnerCardDao.save(cardDetail);
	}
}
