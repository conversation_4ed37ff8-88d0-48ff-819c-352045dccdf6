package com.stpl.tech.kettle.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.stpl.tech.master.domain.model.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.converter.OrderConverter;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.data.kettle.SubscriptionPlan;
import com.stpl.tech.kettle.data.kettle.SubscriptionPlanEvent;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.SubscriptionEventType;
import com.stpl.tech.kettle.domain.model.SubscriptionProduct;
import com.stpl.tech.kettle.domain.model.SubscriptionRequest;
import com.stpl.tech.kettle.repository.kettle.SubscriptionPlanDao;
import com.stpl.tech.kettle.repository.kettle.SubscriptionPlanEventDao;
import com.stpl.tech.kettle.service.SubscriptionPlanService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.FrequencyOfferType;
import com.stpl.tech.master.domain.model.Product;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class SubscriptionPlanServiceImpl implements SubscriptionPlanService {

	@Autowired
	private SubscriptionPlanDao subscriptionPlanDao;

	@Autowired
	private SubscriptionPlanEventDao subscriptionPlanEventDao;

	@Autowired
	private ProductCache productCache;

	@Autowired
	private OrderConverter orderConverter;

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private UnitCacheService unitCacheService;

	@Override
	public SubscriptionPlan getActiveSubscription(Integer customerId, String code) {
		return subscriptionPlanDao.findByCustomerIdAndSubscriptionPlanCodeAndStatus(customerId, code,AppConstants.ACTIVE);
	}

	@Override
	public SubscriptionPlan addSubscription(SubscriptionRequest request, String source, Integer campaignId) {
		Date currentTime = AppUtils.getCurrentTimestamp();
		SubscriptionPlan plan = new SubscriptionPlan();
		plan.setCustomerId(request.getCustomerId());
		setPlan(request, plan, currentTime);
		subscriptionPlanDao.save(plan);
		Date startDate = plan.getPlanStartDate();
		SubscriptionPlanEvent event = createEvent(request, currentTime, plan.getSubscriptionPlanId(), startDate, source,
				campaignId);
		plan.setLastRenewalEventId(event.getSubscriptionPlanEventId());
		return plan;
	}

	@Override
	public SubscriptionPlan updateSubscriptionData(SubscriptionRequest request, String source, Integer campaignId) {
		Date currentTime = AppUtils.getCurrentTimestamp();
		Optional<SubscriptionPlan> data = subscriptionPlanDao.findById(request.getPlanId());
		SubscriptionPlan plan = null;
		if (data.isPresent()) {
			plan = data.get();
			Date endDate = plan.getPlanEndDate();
			setPlan(request, plan, currentTime);
			subscriptionPlanDao.flush();
			SubscriptionPlanEvent event = createEvent(request, currentTime, plan.getSubscriptionPlanId(),
					AppUtils.getNextDate(endDate), source, campaignId);
			plan.setLastRenewalEventId(event.getSubscriptionPlanEventId());
			subscriptionPlanDao.flush();
		}
		return plan;
	}

	private void setPlan(SubscriptionRequest request, SubscriptionPlan plan, Date currentTime) {
		plan.setPlanStartDate(request.getStartDate());
		plan.setPlanEndDate(request.getEndDate());
		plan.setRenewalTime(currentTime);
		plan.setStatus(AppConstants.ACTIVE);
		plan.setSubscriptionPlanCode(request.getOfferDescription());
		plan.setEventType(request.getType().name());
		plan.setLastRenewalEventId(-1);
		plan.setFrequencyStrategy(request.getFrequencyStrategy());
		plan.setOverAllFrequency(request.getOverAllFrequency());
		plan.setFrequencyLimit(request.getFrequencyLimit());
	}

	private SubscriptionPlanEvent createEvent(SubscriptionRequest request, Date currentTime, Integer planId,
			Date startDate, String source, Integer campaignId) {
		SubscriptionPlanEvent event = new SubscriptionPlanEvent();
		event.setSubscriptionPlanId(planId);
		event.setCustomerId(request.getCustomerId());
		event.setPlanStartDate(startDate);
		event.setPlanEndDate(request.getEndDate());
		event.setRenewalTime(currentTime);
		event.setStatus(AppConstants.ACTIVE);
		event.setSubscriptionPlanCode(request.getOfferDescription());
		event.setEventType(request.getType().name());
		event.setDimensionCode(request.getDimension());
		event.setOrderId(request.getOrderId());
		event.setOrderItemId(request.getOrderItemId());
		event.setProductId(request.getProductId());
		event.setPrice(request.getPrice());
		event.setValidityInDays(request.getValidityInDays());
		event.setSubscriptionSource(source);
		event.setCampaignId(campaignId);
		subscriptionPlanEventDao.save(event);
		return event;
	}

	@Override
	public SubscriptionProduct getSubscriptionProduct(Order order) {
		for (OrderItem item : order.getOrders()) {
			if (productCache.getSubscriptionProductDetails().containsKey(item.getProductId())) {
				Optional<Product> productData = productCache.getSubscriptionProductDetailsById(item.getProductId());
				if (productData.isPresent()) {
					return orderConverter.convert(item, productData.get());
				}
			}
		}
		return null;
	}

	@Override
	public List<String> validateSubscriptionRequest(Order order, Customer customer, List<String> errors) {
		if (Objects.isNull(errors)) {
			errors = new ArrayList<>();
		}
		validateSubscriptionOrderRequest(order, customer, errors);
		validateCustomerSubscriptionRequest(customer, errors);
		return errors;
	}

	@Override
	public List<String> validateSubscriptionOrderRequest(Order order, Customer customer, List<String> errors) {
		if (Objects.isNull(errors)) {
			errors = new ArrayList<>();
		}
		int count = 0;
		for (OrderItem item : order.getOrders()) {
			if (productCache.getSubscriptionProductDetailsById(item.getProductId()).isPresent()) {
				count++;
				if (item.getQuantity() != 1) {
					errors.add(String.format("Subscription Item cannot have quantity other than 1 : %d",
							item.getQuantity()));
				}
			}
		}
		if (count != 1) {
			errors.add("Cannot Add More than 1 items of subscription in order");
		}
		if (TransactionUtils.isCODOrder(order.getSource())
				&& order.getChannelPartner() != AppConstants.BAZAAR_PARTNER_ID) {
			errors.add("Subscription Cannot be added in COD Order");
		}
//		if (order.getOfferCode() != null && order.getOfferCode().length() > 0) {
//			errors.add("Subscription Cannot be added in Orders with Offers");
//		}
		if (TransactionUtils.isSpecialOrder(order)) {
			errors.add("Subscription Cannot be added through special orders");
		}
		if (AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId())) {
			errors.add("Subscription Cannot be added for the customer Id " + order.getCustomerId());
		}
		return errors;
	}

	@Override
	public List<String> validateCustomerSubscriptionRequest(Customer customer, List<String> errors) {
		if (Objects.isNull(errors)) {
			errors = new ArrayList<>();
		}
		try {
			if (customer == null) {
				errors.add("Subscription Cannot be added for unavailable customer Id ");
			}
			if (customer != null && !customer.isContactNumberVerified()) {
				errors.add("Subscription Cannot be added for unverified customer Id " + customer.getId());
			}
			if (AppConstants.EXCLUDE_CUSTOMER_IDS.contains(customer.getId())) {
				errors.add("Subscription Cannot be added for excluded customer Id " + customer.getId());
			}
			log.info("Fetching Subscription Info for Customer {}", customer.getId());
			SubscriptionPlan plan = subscriptionPlanDao.getSubscriptionByCustomerId(customer.getId(),
					SubscriptionEventType.SUBSCRIPTION_CANCELLED.name());
			if (Objects.nonNull(plan) && Objects.nonNull(plan.getPlanStartDate())
					&& Objects.nonNull(plan.getPlanEndDate()) && plan.getStatus().equals(AppConstants.ACTIVE)) {
//                Integer subscriptionValidity = AppUtils.getActualDayDifference(plan.getPlanStartDate(),plan.getPlanEndDate());
				Integer remainingDays = AppUtils.getActualDayDifference(AppUtils.getCurrentDate(),
						plan.getPlanEndDate());
				if (properties.getSubscriptionValidBuyingDay() <= remainingDays) {
					errors.add("Subscription Cannot be purchased as customer has reached the validity limit");
				} else if (remainingDays < 0) {
					plan.setStatus(AppConstants.IN_ACTIVE);
					subscriptionPlanDao.save(plan);
				}
//                if (subscriptionValidity > 92 && remainingDays > 92) {
//                    errors.add("Subscription Cannot be purchased as customer has reached the validity limit");
//                }
//                if (Objects.nonNull(couponCode) && !plan.getSubscriptionPlanCode().equals(couponCode)){
//                    errors.add("Subscription Cannot be purchased as customer has different subscription offer");
//                }
			}
		} catch (Exception e) {
			log.error("Exception Faced While Fetching Subscription {}", customer.getId());
		}
		return errors;
	}

	@Override
	public SubscriptionPlan createSubscription(OrderDetail orderDetail, SubscriptionProduct subscriptionProduct,
			Customer customer) {

		com.stpl.tech.kettle.data.kettle.OrderItem subscriptionProductItem = getSubscriptionProductItem(orderDetail, subscriptionProduct.getProductId());
		String channelPartnerName = unitCacheService.getChannelPartnerById(orderDetail.getChannelPartnerId()).getName();
		SubscriptionPlan plan = createSubscription(subscriptionProduct, orderDetail.getOrderId(),
				subscriptionProductItem.getOrderItemId(), customer, null, channelPartnerName, 0);

		return plan;
	}

	@Override
	public com.stpl.tech.kettle.data.kettle.OrderItem getSubscriptionProductItem(OrderDetail order, int productId) {
		for (com.stpl.tech.kettle.data.kettle.OrderItem item : order.getOrderItems()) {
			if (item.getProductId() == productId)
				return item;
		}
		return null;
	}

	@Override
	public SubscriptionPlan createSubscription(SubscriptionProduct subscriptionProduct, Integer orderId,
			Integer orderItemId, Customer customer, Integer campaignId, String source, Integer lagDays) {
		Pair<CouponDetail, Product> subscriptionObj = productCache.getSubscriptionSkuCodeDetail().get(
				productCache.getSubscriptionProductDetailsById(subscriptionProduct.getProductId()).get().getSkuCode());
		SubscriptionRequest request = new SubscriptionRequest();
		request.setOrderId(orderId);
		request.setOfferDescription(subscriptionObj.getValue().getSkuCode());
		request.setOrderItemId(orderItemId);
		request.setProductId(subscriptionProduct.getProductId());
		request.setPrice(subscriptionProduct.getPrice());
		request.setDimension(subscriptionProduct.getDimensionCode());
		request.setValidityInDays(subscriptionProduct.getValidityInDays());
		request.setCustomerId(customer.getId());
		SubscriptionPlan plan = getActiveSubscription(customer.getId(),
				request.getOfferDescription());
		if (plan == null) {
			Date today = AppUtils.addDays(AppUtils.getCurrentDate(), lagDays);
			Date finalEndDate = AppUtils.addDays(today, subscriptionProduct.getValidityInDays());
			request.setStartDate(today);
			request.setEndDate(finalEndDate);
			request.setFrequencyStrategy(subscriptionObj.getKey().getOffer().getFrequencyStrategy());
			request.setOverAllFrequency(BigDecimal.ZERO);
			request.setType(SubscriptionEventType.NEW_SUBSCRIPTION);
			if (FrequencyOfferType.QUANTITY_BASED.name()
					.equals(subscriptionObj.getKey().getOffer().getFrequencyStrategy()) || FrequencyOfferType.TIME_QUANTITY_BASED.name().equals(subscriptionObj.getKey().getOffer().getFrequencyStrategy())) {
				request.setFrequencyLimit(BigDecimal.valueOf(subscriptionObj.getKey().getOffer().getMaxQuantity()));
			} else {
				request.setFrequencyLimit(BigDecimal.valueOf(Integer.MAX_VALUE));
			}
			return addSubscription(request, source, campaignId);
		} else {
			request.setPlanId(plan.getSubscriptionPlanId());
			// Case 1 : Current Subscription is active and the customer needs to extend it.
			if (plan.getPlanEndDate().compareTo(AppUtils.getBusinessDate()) >= 0) {
				Date finalEndDate = AppUtils.addDays(plan.getPlanEndDate(), subscriptionProduct.getValidityInDays());
				request.setStartDate(plan.getPlanStartDate());
				request.setEndDate(finalEndDate);
				request.setType(SubscriptionEventType.RENEW_BEFORE_EXPIRY);
				request.setFrequencyStrategy(subscriptionObj.getKey().getOffer().getFrequencyStrategy());
				request.setOverAllFrequency(plan.getOverAllFrequency());
				if (FrequencyOfferType.QUANTITY_BASED.name()
						.equals(subscriptionObj.getKey().getOffer().getFrequencyStrategy()) || FrequencyOfferType.TIME_QUANTITY_BASED.name().equals(subscriptionObj.getKey().getOffer().getFrequencyStrategy())) {
					request.setFrequencyLimit(AppUtils.add(plan.getFrequencyLimit(),
							BigDecimal.valueOf(subscriptionObj.getKey().getOffer().getMaxQuantity())));
				} else {
					request.setFrequencyLimit(BigDecimal.valueOf(Integer.MAX_VALUE));
				}
				return updateSubscriptionData(request, source, campaignId);
			} else {
				Date today = AppUtils.getCurrentDate();
				Date finalEndDate = AppUtils.addDays(today, subscriptionProduct.getValidityInDays());
				request.setStartDate(today);
				request.setEndDate(finalEndDate);
				request.setType(SubscriptionEventType.RENEW_AFTER_EXPIRY);
				return updateSubscriptionData(request, source, campaignId);
			}
		}
	}
}
