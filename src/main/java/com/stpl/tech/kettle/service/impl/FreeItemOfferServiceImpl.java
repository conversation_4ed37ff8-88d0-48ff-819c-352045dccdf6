package com.stpl.tech.kettle.service.impl;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.cache.TaxDataCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.data.master.CouponDetailData;
import com.stpl.tech.kettle.data.master.CouponDetailMappingData;
import com.stpl.tech.kettle.data.master.OfferDetailData;
import com.stpl.tech.kettle.converter.Converters;
import com.stpl.tech.kettle.domain.model.ComplimentaryDetail;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionData;
import com.stpl.tech.kettle.repository.master.CouponDetailDataDao;
import com.stpl.tech.kettle.repository.master.OfferDetailDataDao;
import com.stpl.tech.kettle.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.service.FreeItemOfferService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.adapter.JSONSerializer;
import com.stpl.tech.master.domain.model.AdditionalTax;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferCategoryType;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.TaxData;
import com.stpl.tech.master.domain.model.Unit;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Log4j2
public class FreeItemOfferServiceImpl implements FreeItemOfferService {

    @Autowired
    private UnitCacheService unitCacheService;

    @Autowired
    private ProductCache productCache;

    @Autowired
    private CustomerOfferManagementService customerOfferManagementService;

    @Autowired
    TaxDataCache taxCache;

    @Autowired
    OfferDetailDataDao detailDataDao;

    @Autowired
    CouponDetailDataDao couponDetailDataDao;


    private Map<Date, CouponDetail> offerData = new HashMap<>();
    private Map<Integer, Product> productData = new HashMap<>();
    private Map<Date, Boolean> offerAvailable = new HashMap<>();

    @Override
    public int freeItemOfferProductId(Date businessDate) {
        return offerData.get(businessDate).getOffer().getOfferWithFreeItem().getProductId();
    }

    @Override
    public boolean hasFreeItemOffer(Date businessDate) {
        if (!offerAvailable.containsKey(businessDate)) {
            productData.clear();
            CouponDetail coupon = null;
            try {
                coupon = getFreeItemOffer(businessDate);
            } catch (Exception e) {
               log.error("Did not find an offer with free item strategy with mass offer", e);
            }
            if (coupon == null) {
                offerAvailable.put(businessDate, false);
            } else {
                offerAvailable.put(businessDate, true);
                offerData.put(businessDate, coupon);
            }
        }
        return offerAvailable.get(businessDate);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
    public CouponDetail getFreeItemOffer(Date businessDate) {
        List<OfferDetailData> offers = detailDataDao.findByOfferTypeAndOfferStatusAndOfferScopeAndStartDateAndEndDate(OfferCategoryType.OFFER_WITH_FREE_ITEM_STRATEGY.name(), AppConstants.ACTIVE,"MASS", businessDate);
        if (offers != null && offers.size() > 0){
            OfferDetailData offer = offers.get(0);
            List<CouponDetail> coupons = getOfferCoupons(offer.getOfferDetailId(), false);
            if(Objects.nonNull(coupons) && coupons.size()>0){
                return coupons.get(0);
            }
        }
        return null;
    }

    public List<CouponDetail> getOfferCoupons(int offerId, boolean applyLimit){
        List<CouponDetail> coupons = new ArrayList<>();
        List<CouponDetailData> dataList = null;
        dataList = (List<CouponDetailData>) couponDetailDataDao.findByOfferDetail(offerId, PageRequest.of(0,10, Sort.Direction.ASC));
        if (dataList != null) {
            for(CouponDetailData data : dataList){
                if(applyLimit){
                    int lastIndex = data.getMappings().size();
                    int startIndex = data.getMappings().size() > 10 ? lastIndex-10 : 0;
                    List<CouponDetailMappingData> newMappingData = data.getMappings().subList(startIndex, lastIndex);
                    data.setMappings(newMappingData);
                }
                coupons.add(Converters.convert(data, true, true));
            }
        }
         return coupons;
    }

    @Override
    public Order applyFreeItemOffer(Date businessDate, boolean newCustomer, Order order) {
        try {

            boolean applicable = isOfferApplicable(businessDate, order);
            if (applicable) {
                Order clone = clone(order);
                TransactionData transactionData = addFreeItemOffer(businessDate, clone);
                OfferOrder offer = new OfferOrder();
                offer.setOrder(clone);
                offer.setNewCustomer(newCustomer);
                offer.setCouponCode(clone.getOfferCode());
             OfferOrder finalOrder = customerOfferManagementService.applyCoupoun(offer, null);
             Order finalObject = finalOrder.getOrder();
               applyDiscount(finalObject, transactionData);
               return finalObject;

            }
        } catch (Exception e) {
           String message = "Error while applying free item offer for the unit " + order.getUnitId() + "\n"
                   + org.apache.commons.lang3.exception.ExceptionUtils.getStackTrace(e);
        }
        return order;
    }


    private boolean isOfferApplicable(Date businessDate, Order order) {
        if (offerAvailable.containsKey(businessDate)) {
            CouponDetail coupon = offerData.get(businessDate);
            if (order.getTransactionDetail().getPaidAmount()
                    .compareTo(new BigDecimal(coupon.getOffer().getMinValue())) >= 0) {
                return true;
            }
        }
        return false;
    }

    private Order clone(Order order) {
        try {
            String json = JSONSerializer.toJSON(order);
            return JSONSerializer.toJSON(json, Order.class);
        } catch (Exception e) {
            log.error("Error in cloning Order Object ", e);
        }
        return order;
    }

    private TransactionData addFreeItemOffer(Date businessDate, Order order) {
        TransactionData transactionData = null;
        if (offerAvailable.containsKey(businessDate)) {
            CouponDetail coupon = offerData.get(businessDate);
            if (order.getTransactionDetail().getPaidAmount()
                    .compareTo(new BigDecimal(coupon.getOffer().getMinValue())) >= 0) {
                order.setOfferCode(coupon.getCode());
                if (coupon.getOffer().getOfferWithFreeItem() != null) {
                    int productId = coupon.getOffer().getOfferWithFreeItem().getProductId();
                    int quantity = coupon.getOffer().getOfferWithFreeItem().getQuantity();
                    boolean found = false;
                    for (OrderItem item : order.getOrders()) {
                        if (item.getProductId() == productId && item.getQuantity() >= quantity) {
                            List<Pair<String, BigDecimal>> taxes = new ArrayList<>();
                            for (TaxDetail t : item.getTaxes()) {
                                taxes.add(new Pair<String, BigDecimal>(t.getCode(), t.getPercentage()));
                            }
                            transactionData = new TransactionData(quantity, item.getPrice(),
                                    AppUtils.multiply(new BigDecimal(quantity), item.getPrice()),
                                    AppUtils.multiply(AppUtils.divideWithScale10(new BigDecimal(quantity),
                                            new BigDecimal(item.getQuantity())), item.getTax()),
                                    taxes);
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        Product p = getProduct(order.getUnitId(), productId);
                        Unit unit = unitCacheService.getUnitById(order.getUnitId());
                        if (p != null) {
                            OrderItem item = createItem(p, quantity, unit.getLocation().getState().getId());
                            order.getOrders().add(item);
                            transactionData = calculateTransactionalData(order, item);
                        }
                    }
                }
            }
        }
        return transactionData;
    }

    private Product getProduct(int unitId, int productId) {
        if (!productData.containsKey(unitId)) {
            Collection<Product> products = productCache.getProductByUnitId(unitId);
            for (Product product : products) {
                if (product.getId() == productId) {
                    productData.put(unitId, product);
                    break;
                }
            }
        }
        return productData.get(unitId);
    }

    private OrderItem createItem(Product p, int quantity, int stateId) {
        OrderItem i = new OrderItem();
        ProductPrice price = p.getPrices().get(0);
        i.setBillType(p.getBillType());
        i.setAmount(AppUtils.multiply(new BigDecimal(quantity), price.getPrice()));
        i.setCode(p.getTaxCode());
        i.setComplimentaryDetail(new ComplimentaryDetail());
        i.setComposition(new OrderItemComposition());
        i.setDimension(price.getDimension());
        i.setDiscountDetail(new DiscountDetail());
        i.setPrice(price.getPrice());
        i.setProductId(p.getId());
        i.setProductName(p.getName());
        i.setQuantity(quantity);
        TaxData tax = taxCache.getTaxData(stateId, p.getTaxCode());
        i.getTaxes().add(getTax(tax.getState().getSgst(), "SGST/UTGST", i.getAmount()));
        i.getTaxes().add(getTax(tax.getState().getCgst(), "CGST", i.getAmount()));
        for (AdditionalTax addonTax : tax.getOthers()) {
            i.getTaxes().add(getTax(addonTax.getTax(), addonTax.getType(), i.getAmount()));
        }
        BigDecimal totalTax = BigDecimal.ZERO;
        for (TaxDetail t : i.getTaxes()) {
            totalTax = AppUtils.add(totalTax, t.getValue());
        }
        i.setTax(totalTax);
        i.setTotalAmount(i.getAmount());
        return i;
    }

    private TaxDetail getTax(BigDecimal percentage, String code, BigDecimal taxable) {
        TaxDetail t = new TaxDetail();
        t.setCode(code);
        t.setPercentage(percentage);
        t.setTaxable(taxable);
        BigDecimal tax = AppUtils.percentageOf(percentage, taxable);
        t.setValue(tax);
        t.setTotal(taxable);
        t.setType("GST");
        return t;
    }

    private TransactionData calculateTransactionalData(Order order, OrderItem item) {
        List<Pair<String, BigDecimal>> taxes = new ArrayList<>();
        for (TaxDetail t : item.getTaxes()) {
            taxes.add(new Pair<String, BigDecimal>(t.getCode(), t.getPercentage()));
        }
        TransactionData transactionData = new TransactionData(item.getQuantity(), item.getPrice(),
                item.getTotalAmount(), item.getTax(), taxes);
        order.getTransactionDetail().setTotalAmount(
                AppUtils.add(order.getTransactionDetail().getTotalAmount(), transactionData.getAmount()));
        order.getTransactionDetail().setTaxableAmount(
                AppUtils.add(order.getTransactionDetail().getTaxableAmount(), transactionData.getAmount()));
        order.getTransactionDetail().setPaidAmount(AppUtils.add(order.getTransactionDetail().getPaidAmount(),
                AppUtils.add(transactionData.getAmount(), transactionData.getTax())));
        order.getTransactionDetail()
                .setTax(AppUtils.add(order.getTransactionDetail().getTax(), transactionData.getTax()));
        return transactionData;
    }

    private void applyDiscount(Order order, TransactionData transaction) {
        order.getTransactionDetail().setTaxableAmount(
                AppUtils.subtract(order.getTransactionDetail().getTaxableAmount(), transaction.getAmount()));
        order.getTransactionDetail().setPaidAmount(AppUtils.subtract(order.getTransactionDetail().getPaidAmount(),
                AppUtils.add(transaction.getAmount(), transaction.getTax())));
        order.getTransactionDetail()
                .setTax(AppUtils.subtract(order.getTransactionDetail().getTax(), transaction.getTax()));
        order.getTransactionDetail().setSavings(AppUtils.add(transaction.getAmount(), transaction.getTax()));
        DiscountDetail discount = new DiscountDetail();
        discount.setPromotionalOffer(transaction.getAmount());
        discount.setTotalDiscount(transaction.getAmount());
        PercentageDetail p = new PercentageDetail();
        p.setPercentage(BigDecimal.ZERO);
        p.setValue(BigDecimal.ZERO);
        discount.setDiscount(p);
        order.getTransactionDetail().setDiscountDetail(discount);
        for (OrderItem item : order.getOrders()) {
            if (item.getDiscountDetail() != null && item.getDiscountDetail().getDiscountReason() != null
                    && item.getDiscountDetail().getDiscountReason().equals(order.getOfferCode())) {
                int remainingQuantity = item.getQuantity() - transaction.getQuantity();
                item.setTax(AppUtils.multiply(
                        AppUtils.divide(new BigDecimal(remainingQuantity), new BigDecimal(item.getQuantity())),
                        item.getTax()));
                for (TaxDetail tax : item.getTaxes()) {
                    tax.setValue(AppUtils.multiply(AppUtils.divideWithScale10(new BigDecimal(remainingQuantity),
                            new BigDecimal(item.getQuantity())), tax.getValue()));
                    tax.setTaxable(AppUtils.multiply(
                            AppUtils.divide(new BigDecimal(remainingQuantity), new BigDecimal(item.getQuantity())),
                            tax.getTaxable()));
                }

                break;
            }
        }

        for (Pair<String, BigDecimal> t : transaction.getTaxes()) {
            for (TaxDetail td : order.getTransactionDetail().getTaxes()) {
                if (t.getKey().equals(td.getCode()) && t.getValue().equals(td.getPercentage())) {
                    td.setTotal(AppUtils.add(td.getTotal(), transaction.getAmount()));
                }
            }
        }

    }
}
