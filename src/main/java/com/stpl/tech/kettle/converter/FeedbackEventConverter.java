package com.stpl.tech.kettle.converter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.cache.BrandMetaDataCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.data.kettle.FeedbackEvent;
import com.stpl.tech.kettle.domain.model.FeedbackEventInfo;
import com.stpl.tech.kettle.domain.model.FeedbackEventType;

@Service
public class FeedbackEventConverter {

	@Autowired
	private UnitCacheService unitCacheService;

	@Autowired
	private BrandMetaDataCache brandMetaDataCache;

	public FeedbackEventInfo convert(FeedbackEvent event) {
		FeedbackEventInfo info = new FeedbackEventInfo();
		info.setContactNumber(event.getFeedbackDetail().getContactNumber());
		info.setCustomerId(event.getFeedbackDetail().getCustomerId());
		info.setEmailId(event.getFeedbackDetail().getEmailId());
		info.setEventCompletionTime(event.getEventCompletionTime());
		info.setEventGenerationTime(event.getEventGenerationTime());
		info.setEventLongUrl(event.getEventLongUrl());
		info.setEventNotificationTime(event.getEventNotificationTime());
		info.setEventShortUrl(event.getEventShortUrl());
		info.setEventShortUrlId(event.getEventShortUrlId());
		info.setEventSource(event.getEventSource());
		info.setEventStatus(event.getEventStatus());
		info.setEventTriggerTime(event.getEventTriggerTime());
		info.setFeedbackEventId(event.getFeedbackEventId());
		info.setFeedbackId(event.getFeedbackDetail().getFeedbackId());
		info.setOrderSource(event.getFeedbackDetail().getOrderSource());
		info.setUnitId(event.getFeedbackDetail().getUnitId());
		info.setCustomerName(event.getFeedbackDetail().getCustomerName());
		info.setUnitName(unitCacheService.getUnitBasicDetailById(event.getFeedbackDetail().getUnitId()).getName());
		info.setType(FeedbackEventType.valueOf(event.getEventType()));
		info.setRating(event.getFeedbackDetail().getRating());
		info.setBrand(brandMetaDataCache.getBrandMetaData().get(event.getBrandId()));
		return info;
	}
}
