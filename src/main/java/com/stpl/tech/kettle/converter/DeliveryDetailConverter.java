package com.stpl.tech.kettle.converter;

import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.data.kettle.DeliveryDetail;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.domain.model.DeliveryStatus;

@Service
public class DeliveryDetailConverter {
	
	public  DeliveryResponse convert(int unitId, DeliveryDetail deliveryDetail) {
		DeliveryResponse detail = new DeliveryResponse();

		if (deliveryDetail != null) {
			detail.setUnitId(unitId);
			detail.setDeliveryBoyName(deliveryDetail.getDeliveryBoyName());
			detail.setDeliveryBoyPhoneNum(deliveryDetail.getDeliveryBoyPhoneNum());
			detail.setDeliveryBoyId(deliveryDetail.getDeliveryBoyId());
			detail.setDeliveryDetailId(deliveryDetail.getId());
			detail.setOrderId(deliveryDetail.getOrderId());
			detail.setGeneratedOrderId(deliveryDetail.getGeneratedOrderId());
			detail.setDeliveryTaskId(deliveryDetail.getDeliveryTaskId());
			detail.setStatusUpdateTime(deliveryDetail.getStatusUpdateTime());
			detail.setDeliveryStatus(DeliveryStatus.valueOf(deliveryDetail.getDeliveryStatus()).getDeliveryStatus());
			detail.setDeliveryPartnerId(deliveryDetail.getDeliveryPartnerId());
			detail.setAllotedNo(deliveryDetail.getAllotedNo());
		}

		return detail;
	}
}
