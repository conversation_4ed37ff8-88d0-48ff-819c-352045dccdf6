package com.stpl.tech.kettle.converter;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.stpl.tech.kettle.data.kettle.FeedbackEvent;
import com.stpl.tech.kettle.data.master.*;
import com.stpl.tech.kettle.domain.enums.MenuType;
import com.stpl.tech.kettle.domain.model.FeedbackEventInfo;
import com.stpl.tech.kettle.domain.model.FeedbackEventType;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.SuperUEvent;
import com.stpl.tech.master.domain.model.Brand;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.data.kettle.ComplimentaryCode;
import com.stpl.tech.kettle.data.kettle.CustomerAddressInfo;
import com.stpl.tech.kettle.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.data.kettle.DeliveryDetail;
import com.stpl.tech.kettle.data.kettle.DeliveryPartner;
import com.stpl.tech.kettle.data.kettle.LoyaltyScore;
import com.stpl.tech.kettle.data.kettle.UnitProductInventory;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.domain.model.DeliveryStatus;
import com.stpl.tech.kettle.mapper.AddressMapper;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.ComplimentaryReason;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.master.domain.model.CouponMappingType;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.MappingIdCodeName;
import com.stpl.tech.master.domain.model.OfferCategoryType;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.OfferValueType;
import com.stpl.tech.master.domain.model.OfferWithFreeItemData;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.ProductInventory;
import com.stpl.tech.master.domain.model.UnitBasicDetail;

public class Converters {
	public static IdCodeName convert(DeliveryPartner type) {

		IdCodeName detail = new IdCodeName();
		detail.setId(type.getPartnerId());
		detail.setName(type.getPartnerDisplayName());
		detail.setCode(type.getPartnerCode());
		detail.setStatus(type.getPartnerStatus());
		detail.setType(type.getPartnerType());
		return detail;
	}

	public static ListData convertToListData(List<ComplimentaryCode> codes) {
		ListData listData = new ListData();
		listData.setDetail(dummyIdCodeName(AppConstants.RTL_CODE_COMPLIMENTARY_CODE));
		if (codes != null) {
			for (ComplimentaryCode code : codes) {
				listData.getContent().add(convert(code));
			}
		}
		return listData;
	}

	public static IdCodeName dummyIdCodeName(String type) {

		IdCodeName details = new IdCodeName();
		details.setId(1);
		details.setCode(type);
		details.setName(type);
		return details;
	}

	public static ComplimentaryReason convert(ComplimentaryCode compCode) {
		ComplimentaryReason idCodeName = new ComplimentaryReason();
		if (compCode != null) {
			idCodeName.setId(compCode.getId());
			idCodeName.setName(compCode.getName());
			idCodeName.setCode(compCode.getCode());
			idCodeName.setType(AppConstants.YES.equals(compCode.getIsAccountable()) ? AppConstants.ACCOUNTABLE
					: AppConstants.NOT_ACCOUNTABLE);
			idCodeName.setCategory(compCode.getCategory());
		}
		return idCodeName;
	}

	public static CouponDetail convert(CouponDetailData o, boolean getAll, boolean getParentMapping) {
		CouponDetail detail = new CouponDetail();
		detail.setCode(o.getCouponCode());
		detail.setEndDate(o.getEndDate());
		detail.setId(o.getCouponDetailId());
		detail.setMaxUsage(o.getMaxUsage());
		detail.setMaxCustomerUsage(o.getMaxCustomerUsage());
		detail.setReusable(AppConstants.getValue(o.getCouponReuse()));
		detail.setReusableByCustomer(AppConstants.getValue(o.getCustomerReuse()));
		detail.setStartDate(o.getStartDate());
		detail.setStatus(o.getCouponStatus());
		detail.setUsage(o.getUsageCount());
		detail.setManualOverride(AppConstants.getValue(o.getManualOverride()));
		detail.setOffer(convert(o.getOfferDetail(), getAll, false));
		addMappings(detail, o.getMappings(), getAll);
		if (getParentMapping) {
			addMappings(detail, o.getOfferDetail().getMappings());
		}
		return detail;
	}

	public static OfferDetail convert(OfferDetailData detail, boolean getAll, boolean trimmed) {
		OfferDetail offer = new OfferDetail();
		offer.setId(detail.getOfferDetailId());
		offer.setCategory(detail.getOfferCategory());
		offer.setType(detail.getOfferType());
		offer.setText(detail.getOfferText());
		offer.setDescription(detail.getOfferDescription());
		offer.setStartDate(detail.getStartDate());
		offer.setEndDate(detail.getEndDate());
		offer.setMinValue(detail.getMinValue());
		offer.setStatus(detail.getOfferStatus());
		offer.setOfferValue(detail.getValue());
		offer.setOfferScope(detail.getOfferScope());
		offer.setAccountsCategory(convertToIdName(detail.getAccountsCategory()));
		if (!trimmed) {
			offer.setValidateCustomer(AppConstants.getValue(detail.getValidateCustomer()));
			offer.setIncludeTaxes(AppConstants.getValue(detail.getIncludeTaxes()));
			offer.setRemoveLoyalty(AppConstants.getValue(detail.getRemoveLoyaltyReward()));
			offer.setMinQuantity(detail.getMinQuantity());
			offer.setMinLoyalty(detail.getMinLoyalty());
			offer.setMinItemCount(detail.getMinItemCount());
			offer.setEmailDomain(detail.getEmailDomain());
			if (detail.getFreeItemProductId() != null && detail.getFreeItemProductId() > 0
					&& detail.getOfferType().equals(OfferCategoryType.OFFER_WITH_FREE_ITEM_STRATEGY.name())) {
				OfferWithFreeItemData data = new OfferWithFreeItemData();
				data.setProductId(detail.getFreeItemProductId());
				data.setQuantity(detail.getFreeItemQuantity());
				data.setType(OfferValueType.valueOf(detail.getFreeItemOfferType()));
				data.setValue(detail.getFreeItemOfferValue());
				data.setDimension(detail.getFreeItemDimension());
				offer.setOfferWithFreeItem(data);
			}
			offer.setPrepaid(AppConstants.YES.equalsIgnoreCase(detail.getPrepaid()));
			offer.setPrepaidAmount(detail.getPrepaidAmount());
			offer.setMaxBillValue(detail.getMaxBillValue());
			offer.setOtpRequired(AppConstants.getValue(detail.getOtpRequired()));
			offer.setMaxDiscountAmount(detail.getMaxDiscountAmount());
			addOfferPartners(offer, detail.getPartners(), getAll);
			addMetaDataMappings(offer, detail.getMetaDataMappings(), getAll);
			addMappings(offer, detail.getMappings(), getAll);
		}
		if (detail.getFrequencyApplicable() != null && AppConstants.getValue(detail.getFrequencyApplicable())) {
			offer.setFrequencyApplicable(AppConstants.getValue(detail.getFrequencyApplicable()));
			offer.setFrequencyCount(detail.getFrequencyCount());
			offer.setFrequencyStrategy(detail.getFrequencyStrategy());
			offer.setMaxQuantity(detail.getMaxQuantity());
		} else {
			offer.setFrequencyApplicable(null);
			offer.setFrequencyCount(null);
			offer.setFrequencyStrategy(null);
			offer.setMaxQuantity(null);
		}
		offer.setDailyFrequencyCount(
				Objects.nonNull(detail.getDailyFrequencyCount()) ? detail.getDailyFrequencyCount() : null);
		offer.setApplicableHour(Objects.nonNull(detail.getApplicableHour()) ? detail.getApplicableHour() : null);
		offer.setAutoApplicableforUnit(Objects.nonNull(detail.getAutoApplicableforUnit())
				? AppConstants.getValue(detail.getAutoApplicableforUnit())
				: null);
		offer.setTermsAndConditions(detail.getTermsAndConditions());
		return offer;
	}

	public static IdName convertToIdName(OfferAccountCategory e) {
		return e != null ? new IdName(e.getId(), e.getName()) : null;
	}

	private static void addOfferPartners(OfferDetail offer, List<OfferPartner> partners, boolean getAll) {
		for (OfferPartner partner : partners) {
			if (AppUtils.isActive(partner.getStatus()) || getAll) {
				offer.getPartners().add(convert(partner));
			}
		}
	}

	private static MappingIdCodeName convert(OfferPartner partner) {
		MappingIdCodeName code = new MappingIdCodeName();
		code.setMappingId(partner.getMappingId());
		code.setId(partner.getPartner().getPartnerId());
		code.setName(partner.getPartner().getPartnerName());
		code.setType(partner.getPartner().getPartnerType());
		code.setStatus(partner.getStatus());
		return code;
	}

	private static void addMetaDataMappings(OfferDetail offer, List<OfferMetadata> metaDataMappings, boolean getAll) {
		for (OfferMetadata metaDataMapping : metaDataMappings) {
			if (AppUtils.isActive(metaDataMapping.getStatus()) || getAll) {
				offer.getMetaDataMappings().add(convert(metaDataMapping));
			}
		}
	}

	private static IdCodeName convert(OfferMetadata metaDataMapping) {
		IdCodeName mapping = new IdCodeName();
		mapping.setId(metaDataMapping.getId());
		mapping.setName(metaDataMapping.getMappingType());
		mapping.setCode(metaDataMapping.getMappingValue());
		mapping.setStatus(metaDataMapping.getStatus());
		mapping.setType(metaDataMapping.getMappingClass());
		return mapping;
	}

	private static void addMappings(CouponDetail detail, List<OfferDetailMappingData> mappings) {
		for (OfferDetailMappingData mapping : mappings) {
			if (AppUtils.isActive(mapping.getStatus())) {
				addMapping(detail, mapping, false);
			}
		}
	}

	private static void addMappings(OfferDetail detail, List<OfferDetailMappingData> mappings, boolean getAll) {
		for (OfferDetailMappingData mapping : mappings) {
			if (AppUtils.isActive(mapping.getStatus()) || getAll) {
				addMapping(detail, mapping, getAll);
			}
		}
	}

	private static void addMapping(OfferDetail detail, OfferDetailMappingData mapping, boolean isAdmin) {
		CouponMapping data = createMapping(mapping);
		if (isAdmin) {
			detail.getCouponMappingList().add(data);
		}
	}

	private static void addMappings(CouponDetail detail, List<CouponDetailMappingData> mappings, boolean getAll) {
		for (CouponDetailMappingData mapping : mappings) {
			if (AppUtils.isActive(mapping.getStatus()) || getAll) {
				addMapping(detail, mapping, getAll);
			}
		}
	}

	private static void addMapping(CouponDetail detail, CouponDetailMappingData mapping, boolean isAdmin) {
		CouponMapping data = new CouponMapping();
		data.setType(mapping.getMappingType());
		data.setId(mapping.getCouponDetailMappingId());
		data.setValue(mapping.getMappingValue());
		data.setDataType(mapping.getDataType());
		data.setMinValue(mapping.getMinValue());
		data.setGroup(mapping.getMappingGroup());
		data.setDimension(mapping.getDimension());
		data.setStatus(mapping.getStatus());
		data.setSource(AppConstants.MAPPING_SOURCE_COUPON);
		if (isAdmin) {
			detail.getCouponMappingList().add(data);
		}
		addToMap(detail, CouponMappingType.valueOf(mapping.getMappingType()).name(), data);
	}

	private static void addMapping(CouponDetail detail, OfferDetailMappingData mapping, boolean isAdmin) {
		CouponMapping data = createMapping(mapping);
		if (isAdmin) {
			detail.getCouponMappingList().add(data);
		}
		addToMap(detail, CouponMappingType.valueOf(mapping.getMappingType()).name(), data);
	}

	private static void addToMap(CouponDetail couponDetail, String key, CouponMapping value) {
		if (!couponDetail.getMappings().containsKey(key)) {
			couponDetail.getMappings().put(key, new HashSet<CouponMapping>());
		}
		couponDetail.getMappings().get(key).add(value);
	}

	private static CouponMapping createMapping(OfferDetailMappingData mapping) {
		CouponMapping data = new CouponMapping();
		data.setType(mapping.getMappingType());
		data.setId(mapping.getOfferDetailMappingId());
		data.setValue(mapping.getMappingValue());
		data.setDataType(mapping.getDataType());
		data.setMinValue(mapping.getMinValue());
		data.setGroup(mapping.getMappingGroup());
		data.setDimension(mapping.getDimension());
		data.setStatus(mapping.getStatus());
		data.setSource(AppConstants.MAPPING_SOURCE_OFFER);
		return data;
	}

	public static Customer convert(CustomerInfo obj, LoyaltyScore score, BigDecimal chaayosCash) {
		if (obj == null) {
			return null;
		}
		Customer data = new Customer();

        data.setContactNumberVerified(
                convert(AppConstants.getValue(obj.getIsNumberVerified(), AppConstants.DEFAULT_IS_NUMBER_VERIFIED)));
        data.setContactNumber(obj.getContactNumber());
        data.setCountryCode(obj.getCountryCode());
        data.setEmailId(obj.getEmailId());
        data.setFirstName(obj.getFirstName());
        data.setMiddleName(obj.getMiddleName());
        data.setLastName(obj.getLastName());
        data.setId(obj.getCustomerId());
        data.setInternal(AppUtils.getStatus(obj.getIsInternal()));
        data.setRegistrationUnitId(obj.getRegistrationUnitId());
        data.setAcquisitionSource(obj.getAcquisitionSource());
        data.setAcquisitionToken(obj.getAcquisitionToken());
        data.setSmsSubscriber(AppUtils.getStatus(obj.getIsSmsSubscriber()));
        data.setEmailSubscriber(AppUtils.getStatus(obj.getIsEmailSubscriber()));
        data.setLoyaltySubscriber(AppUtils.getStatus(obj.getIsLoyaltySubscriber()));
        data.setBlacklisted(AppUtils.getStatus(obj.getIsBlacklisted()));
        data.setIsDND(AppUtils.getStatus(obj.getIsDnd()));
        data.setOptOutOfFaceIt(AppUtils.getStatus(obj.getOptOutFaceIt()));
        data.setOptOutTime(obj.getOptOutTime());
        if (obj.getFaceId() != null) {
            data.setFaceId(obj.getFaceId());
        }
        data.setEmailVerified(
                convert(AppConstants.getValue(obj.getIsEmailVerified(), AppConstants.DEFAULT_IS_EMAIL_VERIFIED)));
        for (CustomerAddressInfo address : obj.getCustomerAddressInfos()) {
            data.getAddresses().add(AddressMapper.INSTANCE.toDomain(address));
        }
        if (score != null) {
            data.setLoyaltyPoints(score.getAcquiredPoints());
            data.setLastOrderId(score.getLastOrderId());
            data.setLastOrderTime(score.getLastOrderTime());
            data.setOrderCount(score.getOrderCount());
            data.setAvailedSignupOffer(AppConstants.getValue(score.getAvailedSignupOffer()));
            data.setLastNPSTime(score.getLastNPSTime());
            data.setSignUpOfferExpired(AppConstants.getValue(score.getSignupOfferExpired()));
            data.setSignupOfferExpiryTime(score.getSignupOfferExpiryTime());
        }
        if (obj.getAddTime() != null) {
            data.setAddTime(obj.getAddTime());
        }
        if (obj.getNumberVerificationTime() != null) {
            data.setNumberVerificationTime(obj.getNumberVerificationTime());
        }
        if (AppConstants.getValue(obj.getIsRefSubscriber())) {
            data.setRefCode(obj.getRefCode());
        }
        data.setIsRefSubscriber(obj.getIsRefSubscriber());
        data.setRefAcquisitionSource(obj.getRefAcquisitionSource());
        data.setReferredOn(obj.getReferredOn());
        data.setReferralDataId(obj.getReferralDataId());
        data.setReferrerId(obj.getReferrerId());
        data.setReferrerAwarded(AppUtils.getStatus(obj.getIsReferrerAwarded()));
        data.setChaayosCash(chaayosCash);
        data.setAcquisitionBrandId(obj.getAcquisitionBrandId());
        data.setChaayosCustomer(AppUtils.getStatus(obj.getIsChaayosCustomer()));
        if (obj.getGender() != null) {
            data.setGender(obj.getGender());
        }
        if (obj.getDateOfBirth() != null) {
            data.setDateOfBirth(obj.getDateOfBirth());
        }
        if (obj.getAnniversary() != null) {
            data.setAnniversary(obj.getAnniversary());
        }
        data.setCustomerAppId(obj.getCustomerAppId());
        if (Objects.nonNull(obj.getOptInWhatsapp())) {
            data.setOptWhatsapp(obj.getOptInWhatsapp());
        }
        return data;
    }

    public static boolean convert(String yesOrNo) {
        return yesOrNo.toUpperCase().equals(AppConstants.YES);
    }



    public static List<ProductInventory> getInventoryDetailsForProducts(ProductCache productCache, UnitBasicDetail unit,
                                                                        Map<Integer, UnitProductInventory> currentInventory, Set<Integer> productsToBeTracked) {
        List<ProductInventory> list = new ArrayList<ProductInventory>();
        Set<Integer> productIds = currentInventory.keySet();
        for (ProductBasicDetail product : productCache.getAllProductBasicDetail()) {
            if (product.isInventoryTracked() && productsToBeTracked != null
                    && productsToBeTracked.contains(product.getDetail().getId())
                    && productIds.contains(product.getDetail().getId())) {
                list.add(convertToInventory(unit, product, currentInventory));
            }
        }
        return list;
    }

    public static ProductInventory convertToInventory(UnitBasicDetail unit, ProductBasicDetail product,
                                                      Map<Integer, UnitProductInventory> currentNumberOfUnits) {
        return convertToInventory(unit, product, currentNumberOfUnits.get(product.getDetail().getId()));
    }
    public static ProductInventory convertToInventory(UnitBasicDetail unit, ProductBasicDetail product,
                                                      UnitProductInventory data) {
        ProductInventory inventory = new ProductInventory();
        inventory.setProduct(product);
        if (data != null) {
            inventory.setQuantity(data.getNoOfUnits());
            inventory.setLastStockOutTime(data.getLastStockOutTime());
            inventory.setLastUpdatedTime(data.getLastUpdateTmstmp());
            inventory.setExpireQuantity(data.getExpireQuantity());
        }
        inventory.setUnit(unit);
        return inventory;
    }

    public static DeliveryResponse convert(int unitId, DeliveryDetail deliveryDetail) {
        DeliveryResponse detail = new DeliveryResponse();

        if (deliveryDetail != null) {
            detail.setUnitId(unitId);
            detail.setDeliveryBoyName(deliveryDetail.getDeliveryBoyName());
            detail.setDeliveryBoyPhoneNum(deliveryDetail.getDeliveryBoyPhoneNum());
            detail.setDeliveryBoyId(deliveryDetail.getDeliveryBoyId());
            detail.setDeliveryDetailId(deliveryDetail.getId());
            detail.setOrderId(deliveryDetail.getOrderId());
            detail.setGeneratedOrderId(deliveryDetail.getGeneratedOrderId());
            detail.setDeliveryTaskId(deliveryDetail.getDeliveryTaskId());
            detail.setStatusUpdateTime(deliveryDetail.getStatusUpdateTime());
            detail.setDeliveryStatus(DeliveryStatus.valueOf(deliveryDetail.getDeliveryStatus()).getDeliveryStatus());
            detail.setDeliveryPartnerId(deliveryDetail.getDeliveryPartnerId());
            detail.setAllotedNo(deliveryDetail.getAllotedNo());
        }

        return detail;
    }

    public static DeliveryDetail convert(DeliveryResponse deliveryReponse) {
        DeliveryDetail detail = new DeliveryDetail();
        if (deliveryReponse != null) {
            detail.setDeliveryBoyName(deliveryReponse.getDeliveryBoyName());
            detail.setOrderId(deliveryReponse.getOrderId());
            detail.setGeneratedOrderId(deliveryReponse.getGeneratedOrderId());
            detail.setDeliveryBoyPhoneNum(deliveryReponse.getDeliveryBoyPhoneNum());
            detail.setDeliveryBoyId(deliveryReponse.getDeliveryBoyId());
            detail.setAllotedNo(deliveryReponse.getAllotedNo());
            if (deliveryReponse.getDeliveryDetailId() != null) {
                detail.setId(deliveryReponse.getDeliveryDetailId());
            }
            detail.setStatusUpdateTime(deliveryReponse.getStatusUpdateTime());
            detail.setDeliveryPartnerId(deliveryReponse.getDeliveryPartnerId());
            detail.setDeliveryStatus(DeliveryStatus.get(deliveryReponse.getDeliveryStatus()).toString());
            detail.setDeliveryTaskId(deliveryReponse.getDeliveryTaskId());
        }
        return detail;
    }

	public static FeedbackEventInfo convert(UnitBasicDetail unitDetail, Brand brand, FeedbackEvent event) {
		FeedbackEventInfo info = new FeedbackEventInfo();
		info.setContactNumber(event.getFeedbackDetail().getContactNumber());
		info.setCustomerId(event.getFeedbackDetail().getCustomerId());
		info.setEmailId(event.getFeedbackDetail().getEmailId());
		info.setEventCompletionTime(event.getEventCompletionTime());
		info.setEventGenerationTime(event.getEventGenerationTime());
		info.setEventLongUrl(event.getEventLongUrl());
		info.setEventNotificationTime(event.getEventNotificationTime());
		info.setEventShortUrl(event.getEventShortUrl());
		info.setEventShortUrlId(event.getEventShortUrlId());
		info.setEventSource(event.getEventSource());
		info.setEventStatus(event.getEventStatus());
		info.setEventTriggerTime(event.getEventTriggerTime());
		info.setFeedbackEventId(event.getFeedbackEventId());
		info.setFeedbackId(event.getFeedbackDetail().getFeedbackId());
		info.setOrderSource(event.getFeedbackDetail().getOrderSource());
		info.setUnitId(event.getFeedbackDetail().getUnitId());
		info.setCustomerName(event.getFeedbackDetail().getCustomerName());
		info.setUnitName(unitDetail.getName());
		info.setType(FeedbackEventType.valueOf(event.getEventType()));
		info.setRating(event.getFeedbackDetail().getRating());
		info.setBrand(brand);
		return info;
	}



	public static ListData convert(RefLookupType type, boolean getAll) {

		ListData data = new ListData();
		if (type == null) {
			return data;
		}
		IdCodeName details = new IdCodeName();
		details.setId(type.getRtlId());
		details.setCode(type.getRtlCode());
		details.setName(type.getRtlName());
		details.setType(type.getRtlGroup());
		details.setStatus(type.getStatus());
		data.setDetail(details);
		for (RefLookup value : type.getRefLookups()) {
			if (getAll || value.getRlStatus().equals("ACTIVE")) {
				data.getContent().add(convert(value));
			}
		}
		return data;
	}

	public static IdCodeName convert(RefLookup type) {

		IdCodeName details = new IdCodeName();
		if (type == null) {
			return details;
		}
		details.setId(type.getRlId());
		details.setName(type.getRlName());
		details.setCode(type.getRlCode());
		details.setShortCode(type.getRlShortCode());
		details.setStatus(type.getRlStatus());
		return details;

	}

	public static SuperUEvent convert(Order order){

		SimpleDateFormat formatter = new SimpleDateFormat(AppConstants.DATE_TIME_STRING);
		SuperUEvent superUEvent = new SuperUEvent();
		superUEvent.setOrderId(order.getOrderId());
		superUEvent.setRevenue(order.getTransactionDetail().getPaidAmount().floatValue());
		superUEvent.setUnitId(order.getUnitId());
		superUEvent.setOrderSource(order.getSource());
		superUEvent.setTerminalNumber(order.getTerminalId());
		superUEvent.setCustomerId(order.getCustomerId());
		superUEvent.setBusinessDate(formatter.format(AppUtils.getBusinessDate(order.getBillingServerTime())));
		superUEvent.setEmpId(order.getEmployeeId());
		superUEvent.setUnitName(order.getUnitName());
		superUEvent.setCustomerName(order.getCustomerName());
		superUEvent.setDaypart(MenuType.getCurrentMenuType(order.getBillingServerTime()).name());
		superUEvent.setBillingServerTime(formatter.format(order.getBillingServerTime()));
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(order.getBillingServerTime());
		calendar.add(Calendar.SECOND, -order.getBillCreationSeconds());
		superUEvent.setOrderStartTime(formatter.format(calendar.getTime()));

		return superUEvent;
	}
}
