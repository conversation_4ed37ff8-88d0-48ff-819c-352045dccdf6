package com.stpl.tech.kettle.mapper;

import com.stpl.tech.kettle.data.kettle.PartnerOrderRiderStatesDetail;
import com.stpl.tech.kettle.domain.model.PartnerOrderRiderStatesDetailData;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface PartnerOrderRiderStatesDetailMapper {

    PartnerOrderRiderStatesDetailMapper INSTANCE = Mappers.getMapper(PartnerOrderRiderStatesDetailMapper.class);

    @Mapping(source = "reasonsForDelay", target = "delayReason")
    PartnerOrderRiderStatesDetailData mapToDetailData(PartnerOrderRiderStatesDetail partnerOrderRiderStatesDetail);
}
