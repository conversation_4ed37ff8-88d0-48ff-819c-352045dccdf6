package com.stpl.tech.kettle.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;

import com.stpl.tech.kettle.annotation.BoolToStringMapper;
import com.stpl.tech.kettle.data.kettle.OrderNotificationData;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.util.AppUtils;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface OrderNotificationMapper {

	@Mapping(source = "isSubscriptionUsed", target = "isSubscriptionUsed", qualifiedBy = BoolToStringMapper.class)
	@Mapping(source = "smsSubscriber", target = "isSmsSubscriber", qualifiedBy = BoolToStringMapper.class)
	@Mapping(source = "whatsAppOptIn", target = "isWhatsappOptIn", qualifiedBy = BoolToStringMapper.class)
	@Mapping(source = "isSubscriptionPurched", target = "isSubscriptionPurched", qualifiedBy = BoolToStringMapper.class)
	@Mapping(source = "daysLeft", target = "subscriptionValidityInDays")
	@Mapping(source = "generateOrderId",target = "generatedOrderId")
	OrderNotificationData toData(OrderNotification orderNotification);

	@BoolToStringMapper
	public static String stringToBool(boolean status) {
		return AppUtils.setStatus(status);
	}
}
