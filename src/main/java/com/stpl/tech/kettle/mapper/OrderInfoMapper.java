package com.stpl.tech.kettle.mapper;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.notification.OrderInfoDomain;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderInfoMapper {

    OrderInfoMapper INSTANCE = Mappers.getMapper(OrderInfoMapper.class);

    OrderInfoDomain toDomain(OrderInfo orderInfo);

}
