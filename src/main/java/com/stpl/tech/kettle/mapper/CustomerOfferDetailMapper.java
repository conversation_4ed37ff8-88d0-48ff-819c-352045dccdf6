package com.stpl.tech.kettle.mapper;

import com.stpl.tech.kettle.annotation.StringToBoolMapper;
import com.stpl.tech.kettle.data.kettle.CustomerOfferDetail;
import com.stpl.tech.kettle.domain.model.CustomerOffer;
import com.stpl.tech.kettle.util.AppUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface CustomerOfferDetailMapper {
    CustomerOfferDetailMapper INSTANCE = Mappers.getMapper(CustomerOfferDetailMapper.class);


    CustomerOfferDetail toDto(CustomerOffer customerOffer);

    @Mapping(source = "availed", target = "availed", qualifiedBy = StringToBoolMapper.class)
    CustomerOffer toDomain(CustomerOfferDetail customerOfferDetail);


    List<CustomerOffer> toDomainList(List<CustomerOfferDetail> customerOfferDetailList);

    @StringToBoolMapper
    public static boolean stringToBool(String status){
        return AppUtils.getStatus(status);
    }
}
