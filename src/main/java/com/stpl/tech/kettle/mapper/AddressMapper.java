package com.stpl.tech.kettle.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import com.stpl.tech.kettle.data.kettle.CustomerAddressInfo;
import com.stpl.tech.master.domain.model.Address;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AddressMapper {

    AddressMapper INSTANCE = Mappers.getMapper(AddressMapper.class);

    @Mapping(source = "addressLine1",target = "line1")
    @Mapping(source = "addressLine2",target = "line2")
    @Mapping(source = "addressLine3",target = "line3")
    Address toDomain(CustomerAddressInfo customerAddressInfo);
}
