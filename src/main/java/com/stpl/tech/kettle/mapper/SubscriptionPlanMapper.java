package com.stpl.tech.kettle.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import com.stpl.tech.kettle.data.kettle.SubscriptionPlan;
import com.stpl.tech.kettle.domain.model.SubscriptionPlanDomain;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface SubscriptionPlanMapper {

	SubscriptionPlanMapper INSTANCE = Mappers.getMapper(SubscriptionPlanMapper.class);

	@Mapping(source = "status", target = "status")
	@Mapping(source = "planStartDate", target = "planStartDate")
	@Mapping(source = "planEndDate", target = "planEndDate")
	@Mapping(source = "renewalTime", target = "renewalTime")
	@Mapping(source = "eventType", target = "eventType")
	@Mapping(source = "overAllSaving", target = "overAllSaving")
	@Mapping(source = "offerDescription", target = "offerDescription")
	SubscriptionPlanDomain toDomain(SubscriptionPlan subscriptionPlan);
}
