package com.stpl.tech.kettle.mapper;



import com.stpl.tech.kettle.annotation.StringToBoolMapper;
import com.stpl.tech.kettle.converter.Converters;
import com.stpl.tech.kettle.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.data.kettle.LoyaltyScore;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;

@Mapper
public interface CustomerMapper {

    CustomerMapper INSTANCE = Mappers.getMapper(CustomerMapper.class);


    @Mapping(source = "customerInfo.isNumberVerified", target = "contactNumberVerified", qualifiedBy = StringToBoolMapper.class)
    @Mapping(source = "customerInfo.isInternal",target = "internal" , qualifiedBy = StringToBoolMapper.class)
    @Mapping(source = "customerInfo.isSmsSubscriber",target = "smsSubscriber" , qualifiedBy = StringToBoolMapper.class)
    @Mapping(source = "customerInfo.isEmailSubscriber",target = "emailSubscriber" , qualifiedBy = StringToBoolMapper.class)
    @Mapping(source = "customerInfo.isLoyaltySubscriber ",target = "loyaltySubscriber" , qualifiedBy = StringToBoolMapper.class)
    @Mapping(source = "customerInfo.isBlacklisted",target = "blacklisted" , qualifiedBy = StringToBoolMapper.class)
    @Mapping(source = "customerInfo.optOutFaceIt",target = "optOutOfFaceIt" , qualifiedBy = StringToBoolMapper.class)
    @Mapping(source = "customerInfo.isDnd",target = "isDND",qualifiedBy = StringToBoolMapper.class)
    Customer convertToCustomer(CustomerInfo customerInfo , LoyaltyScore loyaltyScore , BigDecimal chaayosCash);


    @StringToBoolMapper
    public static boolean stringToBool(String status){
        return Converters.convert(AppConstants.getValue(status,AppConstants.DEFAULT_IS_NUMBER_VERIFIED));
    }
}
