package com.stpl.tech.kettle.mapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants.ComponentModel;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

import com.stpl.tech.kettle.annotation.StringToBoolMapper;
import com.stpl.tech.kettle.converter.Converters;
import com.stpl.tech.kettle.data.master.CampaignCouponMapping;
import com.stpl.tech.kettle.data.master.CampaignDetailData;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignMapping;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = ComponentModel.SPRING)
public interface CampaignDetailMapper {

	@Mapping(source = "campaignDetailData.isCouponClone", target = "couponClone", qualifiedBy = StringToBoolMapper.class)
	@Mapping(source = "campaignDetailData.applicableForOrder", target = "applicableForOrder", qualifiedBy = StringToBoolMapper.class)
	@Mapping(source = "couponMapping", target = "mappings", qualifiedByName = "couponMappingTomappings")
	CampaignDetail toDomain(CampaignDetailData campaignDetailData);

	@StringToBoolMapper
	public static boolean stringToBool(String status) {
		return Converters.convert(AppConstants.getValue(status, AppConstants.DEFAULT_IS_NUMBER_VERIFIED));
	}

	@Named("couponMappingTomappings")
	public static Map<String, Map<Integer, CampaignMapping>> inchToCentimeter(
			List<CampaignCouponMapping> couponMapping) {
		Map<String, Map<Integer, CampaignMapping>> campaignMap = new HashMap<>();
		for (CampaignCouponMapping mapping : couponMapping) {
			if (campaignMap.containsKey(mapping.getCustomerType())) {
				campaignMap.get(mapping.getCustomerType()).put(mapping.getJourneyNumber(), getCampaignMapping(mapping));
			} else {
				Map<Integer, CampaignMapping> innerMap = new HashMap<>();
				innerMap.put(mapping.getJourneyNumber(), getCampaignMapping(mapping));
				campaignMap.put(mapping.getCustomerType(), innerMap);
			}
		}
		return campaignMap;
	}

	public static CampaignMapping getCampaignMapping(CampaignCouponMapping mapping) {
		CampaignMapping campaignMapping = new CampaignMapping();
		campaignMapping.setCampaignId(mapping.getCampaignDetailData().getCampaignId());
		campaignMapping.setCode(mapping.getCloneCode());
		campaignMapping.setDesc(mapping.getCloneCodeDesc());
		campaignMapping.setJourney(mapping.getJourneyNumber());
		campaignMapping.setValidityInDays(mapping.getValidityInDays());
		campaignMapping.setReminderDays(mapping.getReminderDays());
		campaignMapping.setCampaignCouponMappingId(mapping.getCampaignCouponMappingId());
		return campaignMapping;
	}
}
