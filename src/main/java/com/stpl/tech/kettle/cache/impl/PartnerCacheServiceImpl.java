package com.stpl.tech.kettle.cache.impl;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.kettle.cache.PartnerCache;
import com.stpl.tech.kettle.exceptions.DataNotFoundInDBException;
import com.stpl.tech.kettle.util.Constants.CacheConstants;
import com.stpl.tech.master.data.model.ExternalPartnerDetail;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

@Service
@Log4j2
public class PartnerCacheServiceImpl implements PartnerCache {

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;

	@Override
	public Map<String, ExternalPartnerDetail> getExternalPartnerMap() {
		Map<String, ExternalPartnerDetail> externalPartnerDetailIgniteMap = instance
				.getMap(CacheConstants.EXTERNAL_PARTNER_CACHE);
		if (Objects.isNull(externalPartnerDetailIgniteMap)) {
			throw new DataNotFoundInDBException("External partner data not found");
		}
		return externalPartnerDetailIgniteMap;
	}
}
