package com.stpl.tech.kettle.cache.impl;

import com.stpl.tech.kettle.cache.DeliveryServiceCache;
import com.stpl.tech.kettle.data.kettle.DeliveryPartner;
import com.stpl.tech.kettle.data.kettle.PartnerAttributes;
import com.stpl.tech.kettle.data.kettle.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.domain.model.PartnerType;
import com.stpl.tech.kettle.repository.kettle.DeliveryPartnerDao;
import com.stpl.tech.kettle.repository.kettle.PartnerAttributesDao;
import com.stpl.tech.kettle.repository.kettle.UnitToDeliveryPartnerMappingsDao;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@Log4j2
public class DeliveryServiceCacheImpl implements DeliveryServiceCache {
    @Autowired
    private DeliveryPartnerDao deliveryPartnerDao;

    @Autowired
    private UnitToDeliveryPartnerMappingsDao unitToDeliveryPartnerMappingsDao;

    @Autowired
    private PartnerAttributesDao partnerAttributesDao;


    @Override
    @Cacheable(value = "unitPartnerMappings")
    public List<UnitToDeliveryPartnerMappings> getUnitPartnerMappings() {
        List<UnitToDeliveryPartnerMappings> deliveryPartners = unitToDeliveryPartnerMappingsDao.getDeliveryPartnerPriorityForUnits();
        return deliveryPartners;
    }

    @Override
    @Cacheable(value = "automatedPartnerMap")
    public Map<Integer,String> getAutomatedPartnerMap() {
        Map<Integer, String> automatedPartnerMap = new HashMap<Integer, String>();
        List<DeliveryPartner> deliveryPartnerList = deliveryPartnerDao.findAllByPartnerType(PartnerType.EXTERNAL.name());
        for(DeliveryPartner partner : deliveryPartnerList){
            automatedPartnerMap.put(partner.getPartnerId(), partner.getAutomated());
        }
        log.info("size of automated partnerMap :::::::::: {}", automatedPartnerMap.size());

        return automatedPartnerMap;
    }

    @Override
    @Cacheable(value = "cashEligibleMap")
    public Map<Integer,String> getCashEligibleMap(){
        Map<Integer, String> cashEligiblePartners = new HashMap<>();
        List<UnitToDeliveryPartnerMappings> deliveryPartners = getUnitPartnerMappings();
        for(UnitToDeliveryPartnerMappings partner : deliveryPartners){
            DeliveryPartner deliveryPartner = partner.getDeliveryPartner();
            if(deliveryPartner!=null && !cashEligiblePartners.containsKey(deliveryPartner.getPartnerId())){
                cashEligiblePartners.put(deliveryPartner.getPartnerId(), deliveryPartner.getEligibleForCash());
            }
        }
        return cashEligiblePartners;
    }
    @Override
    public String getCashEligibleStatus(int partnerId) {
        return  getCashEligibleMap().get(partnerId);
    }


    @Override
    @Cacheable(value = "apiProfile")
    public Map<Integer,Map<String,String>> getApiProfiles() {
        List<PartnerAttributes> partnerAttributeObjects = partnerAttributesDao.findByPartnerType(AppConstants.DELIVERY);
        Map<Integer, Map<String, String>> apiProfiles = new HashMap<Integer, Map<String, String>>();

        if (partnerAttributeObjects != null && partnerAttributeObjects.size() > 0) {
            for (PartnerAttributes mapping : partnerAttributeObjects) {
                log.info("mapping  in post construct for each loop:::: {}", mapping.toString());
                Map<String, String> partnerIdMap = apiProfiles.get(mapping.getPartnerId());
                if (partnerIdMap != null && !partnerIdMap.isEmpty()) {
                    partnerIdMap.put(mapping.getMappingType(), mapping.getMappingValue());
                } else {
                    Map<String, String> keyValueMap = new HashMap<String, String>();
                    keyValueMap.put(mapping.getMappingType(), mapping.getMappingValue());
                    apiProfiles.put(mapping.getPartnerId(), keyValueMap);
                }
                log.info("api Profile for Partner productId is {} is of size :::: {} ", mapping.getPartnerId(),
                        apiProfiles.get(mapping.getPartnerId()).size());
            }
        }
        return apiProfiles;
    }


    @Override
    @CacheEvict(value = "apiProfile")
    public void evictApiProfileMap(){}

    @Override
    @CacheEvict(value = "cashEligibleMap")
    public void evictCashEligibleMap(){}

    @Override
    @CacheEvict(value = "automatedPartnerMap")
    public void evictAutomatedPartnerMap(){}

    @Override
    @CacheEvict(value = "unitPartnerMappings")
    public void evictUnitPartnerMappings(){}




}
