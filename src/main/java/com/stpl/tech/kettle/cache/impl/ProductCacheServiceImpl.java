package com.stpl.tech.kettle.cache.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.hazelcast.map.IMap;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.multimap.MultiMap;
import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.exceptions.DataNotFoundInDBException;
import com.stpl.tech.kettle.util.Constants.CacheConstants;
import com.stpl.tech.kettle.util.Constants.ListTypes;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;

import lombok.extern.log4j.Log4j2;
import org.springframework.util.CollectionUtils;

@Service
@Log4j2
public class ProductCacheServiceImpl implements ProductCache {

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;


	@Autowired
	private EnvironmentProperties environmentProperties;


	@Override
	public List<Product> getAllProducts() {
		Map<Integer, Product> productMap = instance.getMap(CacheConstants.PRODUCT_DETAIL_CACHE);
		if (Objects.isNull(productMap)) {
			throw new DataNotFoundInDBException("Product data not found");
		}
		return (List<Product>) productMap.values();
	}

	@Override
	public Product getProductById(Integer productId) {
		Product product = (Product) instance.getMap(CacheConstants.PRODUCT_DETAIL_CACHE).get(productId);
		if (Objects.isNull(product)) {
			throw new DataNotFoundInDBException("Product data not found for id " + productId);
		}
		return product;
	}

	@Override
	public Collection<Product> getProductByUnitId(Integer unitId) {
		Collection<?> products =  instance.getMultiMap(CacheConstants.UNIT_PRODUCT_DETAIL_CACHE).get(unitId);
		if (CollectionUtils.isEmpty(products)) {
			throw new DataNotFoundInDBException("Products data not found for unit " + unitId);
		}
		return (Collection<Product>) products;
	}

	@Override
	public Map<Integer, Product> getSubscriptionProductDetails() {
		Map<Integer, Product> subscriptionProductDetails = instance.getMap(CacheConstants.SUBSCRIPTION_PRODUCT_CACHE);
		return subscriptionProductDetails;
	}

	@Override
	public Optional<Product> getSubscriptionProductDetailsById(Integer productId) {
		Product subscriptionProduct = (Product) instance.getMap(CacheConstants.SUBSCRIPTION_PRODUCT_CACHE)
				.get(productId);
		return Optional.ofNullable(subscriptionProduct);
	}

	@Override
	public ProductBasicDetail getProductBasicDetailById(int id) {
		return (ProductBasicDetail) instance.getMap(CacheConstants.PRODUCT_BASIC_CACHE).get(id);
	}

	public List<ProductBasicDetail> getAllProductBasicDetail() {
		Map<Integer, ProductBasicDetail> productMap = instance.getMap(CacheConstants.PRODUCT_BASIC_CACHE);
		return (List<ProductBasicDetail>) productMap.values();
	}

	@Override
	public ListData getProductCategory(int id) {
		return (ListData) instance.getMap(CacheConstants.LIST_CATEGORY_CACHE).get(id);
	}

	@Override
	public ListData getDimensionProfile(int id) {
		return (ListData) instance.getMap((CacheConstants.DIMENSION_PROFILE_CACHE)).get(id);
	}

	public Map<Integer, ListData> getListCategoryData() {
		Map<Integer, ListData> listDataMap = instance.getMap(CacheConstants.LIST_CATEGORY_CACHE);
		return listDataMap;
	}

	@Override
	public IdCodeName getProductSubCategory(int id) {
		MultiMap<ListTypes, IdCodeName> listData = instance.getMultiMap(CacheConstants.LIST_DATA_CACHE);
		return get(listData.get(ListTypes.SUB_CATEGORIES), id);
	}

	private IdCodeName get(Collection<IdCodeName> values, int id) {

		for (IdCodeName val : values) {
			if (val.getId() == id) {
				return val;
			}
		}
		return null;

	}

	@Override
	public Map<String, Pair<CouponDetail, Product>> getSubscriptionSkuCodeDetail() {
		Map<String, Pair<CouponDetail, Product>> subscriptionSkuCodeDetail = instance
				.getMap(("MasterDataCache:subscriptionSkuCodeDetail"));
		return subscriptionSkuCodeDetail;
	}

	@Override
	public Map<Integer, Product> getProductDetails() {
		Map<Integer, Product> productDetails = instance.getMap("MasterDataCache:productDetails");// TreeMap
		return productDetails;
	}

    @Override
	public Product getSubscriptionProductDetail(Integer productId) {
		IMap<Integer,Product> subscriptionProduct = instance.getMap("MasterDataCache:subscriptionProduct");
		return subscriptionProduct.get(productId);
	}

	@Override
	public Pair<CouponDetail,Product> getSubscriptionSkuCodeDetail(String skuCode) {
		 IMap<String,Pair<CouponDetail,Product>> subscriptionSkuCodeDetail= instance.getMap("MasterDataCache:subscriptionSkuCodeDetail");
		return subscriptionSkuCodeDetail.get(skuCode);
	}

	@Override
	public Map<Pair<BigDecimal,String>, String> getUnitProductAlias(int unitId, int productId) {
		Map<Integer, Map<Integer,Map<Pair<BigDecimal,String>,String>>> unitProductAlias = instance.getMap("MasterDataCache:unitProductAlias");
		return unitProductAlias.containsKey(unitId) ? unitProductAlias.get(unitId).get(productId) : new HashMap<>();
	}

	@Override
	public Product getProduct(int id) {
		IMap<Integer, Product> productDetails =instance.getMap("MasterDataCache:productDetails");
		return productDetails.get(id);
	}

	@Override
	public ProductBasicDetail getProductBasicDetail(int id) {
		IMap<Integer, ProductBasicDetail> productBasicDetails = instance.getMap("MasterDataCache:productBasicDetails");
		return productBasicDetails.get(id);
	}

	@Override
	public Integer getSpecialMilkVariant(String variantName) {
		if(!StringUtils.isEmpty(variantName)){
			IMap<String, Integer> specialMilkVariantMap = instance.getMap("MasterDataCache:specialMilkVariant");
			return specialMilkVariantMap.get(variantName);
		}
		return null;
	}


}
