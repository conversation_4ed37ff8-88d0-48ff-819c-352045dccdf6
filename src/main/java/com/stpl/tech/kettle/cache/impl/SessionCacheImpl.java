package com.stpl.tech.kettle.cache.impl;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.kettle.cache.SessionCache;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.CacheConstants;
import com.stpl.tech.master.core.external.cache.SessionDetail;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

@Service
@Log4j2
public class SessionCacheImpl implements SessionCache {

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;

	private Map<String, SessionDetail> sessionKeyToDetailMap;

	@PostConstruct
	public void createMaps() {
		log.info("POST-CONSTRUCT SessionCache - STARTED");
		long time = System.currentTimeMillis();
		sessionKeyToDetailMap = instance.getMap(CacheConstants.SESSION_CACHE);
		log.info("POST-CONSTRUCT SessionCache - took {} ms", System.currentTimeMillis() - time);
	}

	@Override
	public boolean validateSession(String sessionKey, int unitId, int userId) throws AuthenticationFailureException {
		SessionDetail detail = sessionKeyToDetailMap.get(sessionKey);
		log.info("---- session detail : {} ", detail);
		if (Objects.nonNull(detail)) {
			detail.setLastAccessTime(AppUtils.getCurrentTimestamp());
			log.info("---- detail userId : {} ", detail.getUserId());
			log.info("---- detail unitId : {} ", detail.getUnitId());
		}
		return Objects.nonNull(detail) && detail.getUserId() == userId && detail.getUnitId() == unitId;
	}

}
