package com.stpl.tech.kettle.cache.impl;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.kettle.cache.EnvironmentPropertiesCache;
import com.stpl.tech.kettle.exceptions.DataNotFoundInHazelCastException;
import com.stpl.tech.kettle.util.Constants.CacheConstants;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.ConfigAttributeValue;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Service
@Log4j2
public class EnvironmentPropertiesCacheImpl implements EnvironmentPropertiesCache {

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;


	private Set<String> internalNosSet;

	private Map<String, Map<String, String>> applicationAttributeValues;


	@Override
	public Map<String, Map<String, String>> getApplicationAttributeValues() {
		log.info("Fetching all Application Attribute Values");
		Map<String, Map<String, String>> applicationAttributeValuesMap = instance.getMap(CacheConstants.APP_ATTRIBUTES);
		if (Objects.isNull(applicationAttributeValuesMap)) {
			throw new DataNotFoundInHazelCastException("Application Attributes Value Map not found");
		}
		return applicationAttributeValuesMap;
	}

	@Override
	public Map<String, Map<String, ConfigAttributeValue>> getConfigAttributeValues() {
		log.info("Fetching all Config Attribute Values");
		Map<String, Map<String, ConfigAttributeValue>> configAttributeValuesMap = instance
				.getMap(CacheConstants.CONFIG_ATTRIBUTES);
		if (Objects.isNull(configAttributeValuesMap)) {
			throw new DataNotFoundInHazelCastException("Application Attributes Value Map not found");
		}
		return configAttributeValuesMap;
	}

	public boolean isGyftrActive() {
		Map<String,Map<String,String>> test = getApplicationAttributeValues();
		String status = getApplicationAttributeValues().get(ApplicationName.KETTLE_SERVICE.name()).get("gyftr.active");
		return (StringUtils.isNotBlank(status) ? status.equalsIgnoreCase("true") : false);
	}


	public Set<String> getInternalNos() {
		return internalNosSet;
	}

	public Map<String, String> getcompanyConfigAttributes(String applicationName) {
		return getApplicationAttributeValues().get(applicationName);
	}


	@Override
	public String getCharityMailIds() {
		return getApplicationAttributeValues().get(ApplicationName.KETTLE_SERVICE.name())
				.get("charity.notification.emailIds");
	}
}
