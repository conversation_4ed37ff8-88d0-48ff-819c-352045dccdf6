package com.stpl.tech.kettle.cache.impl;

import com.stpl.tech.kettle.cache.WalletCacheService;
import com.stpl.tech.kettle.domain.model.DenominationValueData;
import com.stpl.tech.kettle.service.SuggestWalletService;
import org.springframework.cache.annotation.Cacheable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WalletCacheServiceImpl implements WalletCacheService {


    @Autowired
    private SuggestWalletService suggestWalletService;

    @Override
    @Cacheable(value="ApplicationDenominationValues",key = "#unitId")
    public DenominationValueData getAllActiveDenominationValues(Integer unitId){
        log.info("Adding Denomination Percentage values");
        return suggestWalletService.getAllActiveDenominationValues(unitId);
    }

    @Override
    @CacheEvict(value="ApplicationDenominationValues",allEntries = true)
    public void removeAllActiveDenominationValues(){
        log.info("Removing all Denomination Values");
    }

    @CacheEvict(value = "ApplicationDenominationValues", key = "#unitId")
    public void removeUnitActiveDenominationValues(Integer unitId) {
        log.info("Evicting cache for Denomination Percentage values for unitId: {}", unitId);
    }
}