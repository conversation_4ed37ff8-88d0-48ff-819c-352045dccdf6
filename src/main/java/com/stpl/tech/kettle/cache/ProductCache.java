package com.stpl.tech.kettle.cache;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;

public interface ProductCache {

	List<Product> getAllProducts();

	Product getProductById(Integer productId);

	Collection<Product> getProductByUnitId(Integer unitId);

	Map<Integer, Product> getSubscriptionProductDetails();

	Optional<Product> getSubscriptionProductDetailsById(Integer productId);

	ProductBasicDetail getProductBasicDetailById(int id);

	ListData getProductCategory(int id);

	IdCodeName getProductSubCategory(int id);

	ListData getDimensionProfile(int id);

	Map<Integer, ListData> getListCategoryData();

	List<ProductBasicDetail> getAllProductBasicDetail();

	Map<String, Pair<CouponDetail, Product>> getSubscriptionSkuCodeDetail();

	Map<Integer, Product> getProductDetails();

	 Product getSubscriptionProductDetail(Integer productId);

	Pair<CouponDetail,Product> getSubscriptionSkuCodeDetail(String skuCode);

	Map<Pair<BigDecimal,String>, String> getUnitProductAlias(int unitId, int productId);

	Product getProduct(int id);

	public ProductBasicDetail getProductBasicDetail(int id);
	public Integer getSpecialMilkVariant(String variantName);


}
