package com.stpl.tech.kettle.cache.impl;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.multimap.MultiMap;
import com.stpl.tech.kettle.cache.RecipeCache;
import com.stpl.tech.kettle.util.Constants.CacheConstants;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

@Service
@Log4j2
public class RecipeCacheServiceImpl implements RecipeCache {

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;

	private MultiMap<ProductRecipeKey, IngredientProductDetail> mandatoryAddons;

	@PostConstruct
	public void createCache() {
		log.info("POST-CONSTRUCT RecipeCache - STARTED");
		log.info("$$$$$$$$$$$$$$$Creating Recipe Cache$$$$$$$$$$$$$$$");
		mandatoryAddons = instance.getMultiMap("MasterDataCache:mandatoryAddons");

	}

	@Override
	public RecipeDetail getRecipeByProductRecipeKey(ProductRecipeKey productRecipeKey) {
		RecipeDetail recipe = (RecipeDetail) instance.getMap(CacheConstants.RECIPE_CACHE).get(productRecipeKey);
		if (Objects.isNull(recipe)) {
			log.info("Recipe detail not found for product {} ", productRecipeKey.getProductId());
//			throw new DataNotFoundInDBException(
//					"Recipe detail not found for product " + productRecipeKey.getProductId());
		}
		return recipe;
	}

	@Override
	public RecipeDetail getRecipeByRecipeId(Integer recipeId) {
		RecipeDetail recipe = (RecipeDetail) instance.getMap(CacheConstants.RECIPE_MAP_CACHE).get(recipeId);
		if (Objects.isNull(recipe)) {
			log.info("Recipe detail not found for id {} ", recipeId);// TODO handle for micro card and products without
																		// recipe
			// throw new DataNotFoundInDBException("Recipe detail not found");
		}
		return recipe;
	}

	@Override
	public String getUnitProductProfileDetails(int unitId, int productId, String dimension) {
		Map<Integer, Map<String, Pair<String, Integer>>> productProfileDetails = (Map<Integer, Map<String, Pair<String, Integer>>>) instance
				.getMap(CacheConstants.PRODUCT_PROFILE_CACHE).get(unitId);
		String productProfile = productProfileDetails.get(productId).get(dimension).getKey();
		if (Objects.isNull(productProfile)) {
			log.info("Recipe Profile not found for key {}  ", productProfile);
			// throw new DataNotFoundInDBException("Recipe Profile not found");
		}
		return productProfile;
	}

	@Override
	public Integer getUnitProductRecipeId(int unitId, int productId, String dimension) {
		Map<Integer, Map<String, Pair<String, Integer>>> productProfileDetails = (Map<Integer, Map<String, Pair<String, Integer>>>) instance
				.getMap(CacheConstants.PRODUCT_PROFILE_CACHE).get(unitId);
		Integer recipeId = productProfileDetails.get(productId).get(dimension).getValue();
		if (Objects.isNull(recipeId)) {
			log.info("Recipe Id not found for productId {} and dimension {}  ", productId, dimension);
			// throw new DataNotFoundInDBException("Recipe Id not found");
		}
		return recipeId;
	}

	@Override
	public Collection<IngredientProductDetail> getMandatoryAddons(int productId, String dimension, String profile) {
		return mandatoryAddons.get(new ProductRecipeKey(productId, dimension, profile));
	}

}
