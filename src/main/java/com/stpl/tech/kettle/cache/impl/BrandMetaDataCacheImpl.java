package com.stpl.tech.kettle.cache.impl;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.kettle.cache.BrandMetaDataCache;
import com.stpl.tech.kettle.util.Constants.CacheConstants;
import com.stpl.tech.master.domain.model.Brand;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class BrandMetaDataCacheImpl implements BrandMetaDataCache {

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;


	@Override
	public List<Brand> getAllBrands() {
		return new ArrayList<Brand>(getBrandMetaData().values());
	}

	@Override
	public Map<Integer, Brand> getBrandMetaData() {
		return  instance.getMap((CacheConstants.BRAND_METADATA_CACHE));
	}

}
