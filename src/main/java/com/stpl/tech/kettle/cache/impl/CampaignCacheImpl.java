package com.stpl.tech.kettle.cache.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.cache.CampaignCache;
import com.stpl.tech.kettle.domain.model.CampaignStrategy;
import com.stpl.tech.kettle.domain.model.CustomerRepeatType;
import com.stpl.tech.kettle.service.CampiagnManagementService;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignDetailResponse;
import com.stpl.tech.master.domain.model.CampaignMapping;
import com.stpl.tech.master.domain.model.CouponDetail;

import jakarta.annotation.PostConstruct;

@Service
public class CampaignCacheImpl implements CampaignCache {

	@Autowired
	private CampiagnManagementService campiagnManagementService;

	Map<Map<Integer, String>, CampaignDetail> unitWiseCampaigns;

	Map<Integer, CampaignDetail> campaignWiseCampaigns;

	Map<Integer, List<Integer>> linkedCampaignIds;

	Map<String, CouponDetail> couponDetailMap;

	Map<String, CampaignDetailResponse> campaignByToken;

	@PostConstruct
	@Scheduled(cron = "0 31 05 * * *", zone = "GMT+05:30")
	@Override
	public void reloadCache() {
		unitWiseCampaigns = new HashMap<>();
		campaignWiseCampaigns = new HashMap<>();
		linkedCampaignIds = new HashMap<>();
		couponDetailMap = new HashMap<>();
		campaignByToken = new HashMap<>();
	}

	@Override
	public void refreshCache() {
		refreshCache();
	}

	@Override
	public void refreshCache(int unitId, String strategy) {
		try {
			Map<Integer, String> map = new HashMap<>();
			map.put(unitId, strategy);
			unitWiseCampaigns.remove(map);
		} catch (Exception e) {
		}
	}

	@Override
	public void refreshCacheByCampaign(int campaignId) {
		try {
			campaignWiseCampaigns.remove(campaignId);
		} catch (Exception e) {
		}
	}

	@Override
	public boolean hasApplicableDNBOOffer(int unitId) {
		CampaignDetail campaignDetail = getDNBOCampaignDetail(unitId);
		return campaignDetail != null;
	}

	@Override
	public List<Integer> getLinkedCampaigns(Integer campaignId) {
		if (!linkedCampaignIds.containsKey(campaignId)) {
			synchronized ("LINKED_CAMPAIGN_DATA" + campaignId) {
				if (!linkedCampaignIds.containsKey(campaignId)) {
					loadLinkedCampaignIds(campaignId);
				}
			}
		}
		return linkedCampaignIds.get(campaignId);
	}

	@Override
	public CouponDetail getCouponDetail(String couponCode) {
		if (!couponDetailMap.containsKey(couponCode)) {
			synchronized ("COUPON_DETAIL" + couponCode) {
				if (!couponDetailMap.containsKey(couponCode)) {
					loadCouponDetail(couponCode);
				}
			}
		}
		return couponDetailMap.get(couponCode);
	}

	@Override
	public CampaignDetailResponse getCampaignByToken(String token, String status) {
		if (!campaignByToken.containsKey(token)) {
			synchronized ("COUPON_DETAIL" + token) {
				if (!campaignByToken.containsKey(token)) {
					loadCampaignByToken(token, status);
				}
			}
		}
		return campaignByToken.get(token);
	}

	private void loadCouponDetail(String couponCode) {
		CouponDetail couponDetail = campiagnManagementService.searchCoupon(couponCode, false);
		couponDetailMap.put(couponCode, couponDetail);
	}

	private void loadLinkedCampaignIds(Integer campaignId) {
		List<Integer> ids = campiagnManagementService.getLinkedCampaignIds(campaignId);
		if (!ids.isEmpty()) {
			linkedCampaignIds.put(campaignId, ids);
		}
	}

	private void loadCampaignByToken(String token, String status) {
		CampaignDetailResponse response = campiagnManagementService.getCampaignByTokenAndStatus(token, status);
		if (Objects.nonNull(response)) {
			campaignByToken.put(token, response);
		}
	}

	@Override
	public Integer getCurrentNBOCampaign(int unitId) {
		CampaignDetail campaignDetail = getCampaignDetail(unitId);
		return campaignDetail != null ? campaignDetail.getCampaignId() : null;
	}

	@Override
	public Integer getCurrentDNBOCampaign(int unitId) {
		CampaignDetail campaignDetail = getDNBOCampaignDetail(unitId);
		return campaignDetail != null ? campaignDetail.getCampaignId() : null;
	}

	private CampaignDetail getCampaignDetail(int unitId) {
		Map<Integer, String> map = new HashMap<>();
		map.put(unitId, CampaignStrategy.NBO.name());
		if (!unitWiseCampaigns.containsKey(map)) {
			synchronized ("UNIT_CAMPAIGN_DATA" + unitId) {
				if (!unitWiseCampaigns.containsKey(map)) {
					loadCampaign(unitId, CampaignStrategy.NBO.name());
				}
			}
		}
		return unitWiseCampaigns.get(map);
	}

	private CampaignDetail getDNBOCampaignDetail(int unitId) {
		Map<Integer, String> map = new HashMap<>();
		map.put(unitId, CampaignStrategy.DELIVERY_NBO.name());
		if (!unitWiseCampaigns.containsKey(map)) {
			synchronized ("UNIT_CAMPAIGN_DATA_DNBO" + map) {
				if (!unitWiseCampaigns.containsKey(map)) {
					loadCampaign(unitId, CampaignStrategy.DELIVERY_NBO.name());
				}
			}
		}
		return unitWiseCampaigns.get(map);
	}

	private void loadCampaign(int unitId, String strategy) {
		CampaignDetail detail = campiagnManagementService.getActiveCampaignForUnitId(unitId, strategy);
		Map<Integer, String> map = new HashMap<>();
		map.put(unitId, strategy);
		unitWiseCampaigns.put(map, detail);
	}

	private CampaignDetail getCampaignDetailByCampaignId(int campaignId) {
		if (!campaignWiseCampaigns.containsKey(campaignId)) {
			synchronized ("CAMPAIGN_DATA" + campaignId) {
				if (!campaignWiseCampaigns.containsKey(campaignId)) {
					loadCampaignByCampaignId(campaignId);
				}
			}
		}
		return campaignWiseCampaigns.get(campaignId);
	}

	private void loadCampaignByCampaignId(int campaignId) {
		CampaignDetail detail = campiagnManagementService.getCampaignById(campaignId);
		campaignWiseCampaigns.put(campaignId, detail);
	}

	@Override
	public boolean hasApplicableNBOOffer(int unitId) {
		CampaignDetail campaignDetail = getCampaignDetail(unitId);
		return campaignDetail != null;
	}

	@Override
	public CampaignDetail getCampaign(int campaignId) {
		return getCampaignDetailByCampaignId(campaignId);
	}

	@Override
	public CampaignMapping getCloneCode(CustomerRepeatType type, int journeyNo, Integer campaignId) {
		CampaignDetail detail = getCampaignDetailByCampaignId(campaignId);
		if (detail != null && detail.getMappings() != null && detail.getMappings().containsKey(type.name())
				&& detail.getMappings().get(type.name()).containsKey(journeyNo)) {
			return detail.getMappings().get(type.name()).get(journeyNo);
		} else {
			return null;
		}

	}

	private CampaignMapping getMappingForNewCustomer(CustomerRepeatType type, int journeyNo, Integer campaignId) {
		CampaignMapping mapping = new CampaignMapping();
		mapping.setCampaignId(campaignId);
		mapping.setJourney(journeyNo);
		mapping.setCode("LOYAL_TEA");
		mapping.setDesc("A Free Chai");
		mapping.setValidityInDays(type.getValidityInDays());
		return mapping;
	}

}
