package com.stpl.tech.kettle.cache.impl;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.kettle.cache.EmployeeCache;
import com.stpl.tech.kettle.util.Constants.CacheConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Set;

@Service
@Log4j2
public class EmployeeCacheServiceImpl implements EmployeeCache {

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;

	@Override
	public Set<Integer> getEmployeeMealProducts() {
		return instance.getSet(CacheConstants.EMP_MEAL_PRODUCT_CACHE);
	}

	@Override
	public Set<String> getEmployeeMealDimensions() {
		return instance.getSet(CacheConstants.EMP_MEAL_DIM_CACHE);
	}

	@Override
	public String getEmployeeNameById(Integer id) {
		return instance.getMap(CacheConstants.EMP_NAME_DETAIL_CACHE).get(id).toString();
	}

}
