package com.stpl.tech.kettle.cache.impl;

import com.stpl.tech.kettle.cache.OrderSessionCache;
import com.stpl.tech.kettle.domain.model.OrderSessionDetail;
import com.stpl.tech.kettle.domain.model.OrderTerminalDetail;

import java.util.HashMap;
import java.util.Map;

public class OrderSessionCacheImpl implements OrderSessionCache {

    private final Map<OrderTerminalDetail, OrderSessionDetail> unitSessionMapping = new HashMap<OrderTerminalDetail, OrderSessionDetail>();


    @Override
    public OrderSessionDetail generateToken(OrderTerminalDetail orderTerminalDetail) {
//        OrderSessionDetail orderDetail = new UnitSessionDetail(orderTerminalDetail.getUnitId(),
//                orderTerminalDetail.getTerminalId(), AppUtils.generateRandomOrderId());
//        unitSessionMapping.put(unitTerminalDetail, detail);
//        return detail;
        return null;
    }
}
