package com.stpl.tech.kettle.cache.impl;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.kettle.cache.OrderCacheService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
@Log4j2
public class OrderCacheServiceImpl implements OrderCacheService {

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance instance;
}
