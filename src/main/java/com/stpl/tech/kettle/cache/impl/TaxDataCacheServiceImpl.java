package com.stpl.tech.kettle.cache.impl;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.multimap.MultiMap;
import com.stpl.tech.kettle.cache.TaxDataCache;
import com.stpl.tech.kettle.util.Constants.CacheConstants;
import com.stpl.tech.master.domain.model.TaxData;
import com.stpl.tech.master.domain.model.Taxation;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Log4j2
public class TaxDataCacheServiceImpl implements TaxDataCache {

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;


	@Override
	public TaxData getTaxData(int stateId, String hsnCode) {
		TaxData taxData = new TaxData();
		Map<String, Map<Integer, TaxData>> hsnStateTaxDataMap = instance
				.getMap(CacheConstants.STATE_TAX_CACHE);
		if (hsnStateTaxDataMap.containsKey(hsnCode)) {
			taxData = hsnStateTaxDataMap.get(hsnCode).get(stateId);
		}
		return taxData;
	}

	@Override
	public MultiMap<Integer, Taxation> getSaleTaxation() {
		return instance.getMultiMap(CacheConstants.TAXATION_CACHE);
	}
}
