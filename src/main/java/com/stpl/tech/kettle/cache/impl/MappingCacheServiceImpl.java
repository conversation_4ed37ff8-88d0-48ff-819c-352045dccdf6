package com.stpl.tech.kettle.cache.impl;

import com.stpl.tech.kettle.cache.MappingCacheService;
import com.stpl.tech.kettle.data.master.CouponDetailData;
import com.stpl.tech.kettle.data.master.OfferDetailData;
import com.stpl.tech.kettle.domain.model.SubscriptionOfferInfoDetail;
import com.stpl.tech.kettle.repository.master.CouponDetailDataDao;
import com.stpl.tech.kettle.repository.master.OfferDetailDataDao;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;

@Log4j2
@Service
public class MappingCacheServiceImpl implements MappingCacheService {
    @Autowired
    private OfferDetailDataDao dao;

    @Autowired
    private CouponDetailDataDao couponDetailDataDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
    public SubscriptionOfferInfoDetail findSubscriptionDetail(String offerCode){
        OfferDetailData offerDetailData = getOfferDetailDataFromCoupon(offerCode);
        SubscriptionOfferInfoDetail detail = new SubscriptionOfferInfoDetail(offerCode,offerDetailData.getValue());
        detail.setOfferText(offerDetailData.getOfferText());
        return detail;
    }
    public OfferDetailData getOfferDetailDataFromCoupon(String offerCode){
        try {
            CouponDetailData couponDetailData =couponDetailDataDao.findByCouponCode(offerCode);
            if(Objects.nonNull(couponDetailData)) {
               Optional<OfferDetailData> offerDetailData = dao.findById(couponDetailData.getOfferDetail().getOfferDetailId());
               if(offerDetailData.isPresent()) {
                  OfferDetailData offerDetail = offerDetailData.get();
                  return offerDetail;
               }
               else{
                   log.info("No offers found");
               }
            }
        }catch (Exception e){
            log.error("Exception Caught While Retrieving Offer With Coupon Code :: {}",offerCode);
            return null;
        }
        return null;
    }

}
