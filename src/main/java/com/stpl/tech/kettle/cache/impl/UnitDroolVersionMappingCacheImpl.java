package com.stpl.tech.kettle.cache.impl;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.kettle.cache.UnitDroolVersionMappingCache;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Log4j2
public class UnitDroolVersionMappingCacheImpl implements UnitDroolVersionMappingCache {

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance instance;

    public Map<String, DroolVersionDomain> getUnitDroolVersionMapping(Integer unitId) {
        try {
            log.info("Fetching unit drool version mapping details for unit id {}", unitId);
            return (Map<String, DroolVersionDomain>) instance.getMap("MasterDataCache:unitDroolVersionMapping").get(unitId);
        } catch (Exception e) {
            return new HashMap<>();
        }
    }
}
