package com.stpl.tech.kettle.cache.impl;

import com.stpl.tech.kettle.data.kettle.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.util.comparator.UnitDeliveryMappingComparator;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.PriorityQueue;

@Log4j2
@Service
public class UnitDeliverySelectionCache {
    private PriorityQueue<UnitToDeliveryPartnerMappings> currentQueue = new PriorityQueue<>(
            new UnitDeliveryMappingComparator());
    private List<UnitToDeliveryPartnerMappings> unitToDeliveryPartners = new ArrayList<UnitToDeliveryPartnerMappings>();


    public void refreshCurrentCache() {
        this.currentQueue.addAll(this.unitToDeliveryPartners);
        log.info("Inside addtoCurrentCache and its size :::: {}", currentQueue.size());
    }


    public Integer getNextPartnerFromCache() {
        UnitToDeliveryPartnerMappings nextPartner = this.currentQueue.peek();
        if (nextPartner == null) {
            refreshCurrentCache();
        }
        nextPartner = this.currentQueue.poll();
        log.info("returning partner ID ::: {}", nextPartner);
        return nextPartner.getDeliveryPartner().getPartnerId();
    }


    public List<UnitToDeliveryPartnerMappings> getUnitToDeliveryPartners() {
        return unitToDeliveryPartners;
    }

    public void setUnitToDeliveryPartners(List<UnitToDeliveryPartnerMappings> unitToDeliveryPartners) {
        this.unitToDeliveryPartners = unitToDeliveryPartners;
    }

}
