package com.stpl.tech.kettle.cache;

import java.util.List;

import com.stpl.tech.kettle.domain.model.CustomerRepeatType;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignDetailResponse;
import com.stpl.tech.master.domain.model.CampaignMapping;
import com.stpl.tech.master.domain.model.CouponDetail;

public interface CampaignCache {

	Integer getCurrentNBOCampaign(int unitId);

	Integer getCurrentDNBOCampaign(int unitId);

	boolean hasApplicableNBOOffer(int id);

	CampaignDetail getCampaign(int campaignId);

	CampaignMapping getCloneCode(CustomerRepeatType type, int journeyNo, Integer campaignId);

	void refreshCache();

	void reloadCache();

	void refreshCache(int unitId, String strategy);

	void refreshCacheByCampaign(int campaignId);

    boolean hasApplicableDNBOOffer(int unitId);

    List<Integer> getLinkedCampaigns(Integer campaignId);

	public CouponDetail getCouponDetail(String couponCode);

	CampaignDetailResponse getCampaignByToken(String token, String status);

}
