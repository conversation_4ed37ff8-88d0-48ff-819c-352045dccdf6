package com.stpl.tech.kettle.cache.impl;

import com.stpl.tech.kettle.cache.DeliveryPartnerPriorityMappingCache;
import com.stpl.tech.kettle.data.kettle.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.repository.kettle.UnitToDeliveryPartnerMappingsDao;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;


@Log4j2
@Service
 public class DeliveryPartnerPriorityMappingCacheImpl implements DeliveryPartnerPriorityMappingCache {
    @Autowired
    private UnitToDeliveryPartnerMappingsDao unitToDeliveryPartnerMappingsDao;


    @Cacheable(value = "prioritizedDeliveryPartnerCache")
    private Map<Integer,UnitDeliverySelectionCache> getPrioritizedDeliveryPartnerCache(){
        List<UnitToDeliveryPartnerMappings> deliveryPartners = unitToDeliveryPartnerMappingsDao.getDeliveryPartnerPriorityForUnits();
        Map<Integer, UnitDeliverySelectionCache> prioritizedDeliveryPartnerCache = new HashMap<Integer, UnitDeliverySelectionCache>();
        setMappings(deliveryPartners,prioritizedDeliveryPartnerCache);
        return prioritizedDeliveryPartnerCache;
    }

    @Override
    public UnitDeliverySelectionCache getSelectionCacheForUnit(Integer unitId) {
        Map<Integer,UnitDeliverySelectionCache> prioritizedDeliveryPartnerCache = getPrioritizedDeliveryPartnerCache();
        return prioritizedDeliveryPartnerCache.get(unitId);
    }

    @Override
    @Cacheable(value = "retiresPerUnitCache",key = "#unitId")
    public Integer getRetriesForUnit(Integer unitId) {
        return getPrioritizedDeliveryPartnerCache().get(unitId).getUnitToDeliveryPartners().size();
    }

    @CachePut(value = "retiresPerUnitCache" , key = "#unitId")
    public Integer updateRetriesPerUnit(Integer unitId ,Integer retriesCount){
        return  retriesCount;
    }

    private void setPrioritizedCacheForUnit(Integer unitId, List<UnitToDeliveryPartnerMappings> unitToDeliveryMappings,
                                            Map<Integer,UnitDeliverySelectionCache> prioritizedDeliveryPartnerCache) {

        List<UnitToDeliveryPartnerMappings> priorityMappings = unitToDeliveryMappings.stream()
                .filter(new Predicate<UnitToDeliveryPartnerMappings>() {
                    @Override
                    public boolean test(UnitToDeliveryPartnerMappings singleMapping) {
                        return unitId.equals(singleMapping.getUnitId());
                    }
                }).collect(Collectors.toList());
        UnitDeliverySelectionCache selectionCache = new UnitDeliverySelectionCache();
        selectionCache.setUnitToDeliveryPartners(priorityMappings);
        updateRetriesPerUnit(unitId,priorityMappings.size());
        prioritizedDeliveryPartnerCache.put(unitId, selectionCache);
    }




    private void setMappings(List<UnitToDeliveryPartnerMappings> unitToDeliveryMappings,Map<Integer, UnitDeliverySelectionCache>
            prioritizedDeliveryPartnerCache) {

        unitToDeliveryMappings.forEach(new Consumer<UnitToDeliveryPartnerMappings>() {
            @Override
            public void accept(UnitToDeliveryPartnerMappings t) {
                Integer unitId = t.getUnitId();
                setPrioritizedCacheForUnit(unitId, unitToDeliveryMappings,prioritizedDeliveryPartnerCache);
            }
        });
    }

    @Override
    @CacheEvict(value = "prioritizedDeliveryPartnerCache")
    public void evictPrioritizedDeliveryPartnerCache(){}


    @Override
    @CacheEvict(value = "retiresPerUnitCache",allEntries = true)
    public void evictRetriesCache(){};



}
