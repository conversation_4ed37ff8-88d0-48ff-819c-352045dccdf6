package com.stpl.tech.kettle.cache;

import java.util.Collection;

import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;

public interface RecipeCache {

	RecipeDetail getRecipeByProductRecipeKey(ProductRecipeKey productRecipeKey);

	RecipeDetail getRecipeByRecipeId(Integer recipeId);

	String getUnitProductProfileDetails(int unitId, int productId, String dimension);

	Integer getUnitProductRecipeId(int unitId, int productId, String dimension);

	Collection<IngredientProductDetail> getMandatoryAddons(int productId, String dimension, String profile);

}
