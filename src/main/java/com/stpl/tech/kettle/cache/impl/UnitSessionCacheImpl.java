package com.stpl.tech.kettle.cache.impl;

import com.stpl.tech.kettle.cache.UnitSessionCache;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.UnitSessionDetail;
import com.stpl.tech.kettle.domain.model.UnitTerminalDetail;
import com.stpl.tech.kettle.util.AppUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class UnitSessionCacheImpl implements UnitSessionCache {

	private final Map<UnitTerminalDetail, UnitSessionDetail> unitSessionMapping = new HashMap<>();

	@Override
	public UnitSessionDetail generateToken(UnitTerminalDetail unitTerminalDetail) {
		UnitSessionDetail detail = new UnitSessionDetail(unitTerminalDetail.getUnitId(),
				unitTerminalDetail.getTerminalId(), AppUtils.generateRandomOrderId());
		unitSessionMapping.put(unitTerminalDetail, detail);
		return detail;
	}

	@Override
	public void setCustomer(UnitTerminalDetail unitTerminalDetail, Customer customer, boolean newCustomer) {
		UnitSessionDetail detail = unitSessionMapping.get(unitTerminalDetail);
		if (detail == null) {
			detail = generateToken(unitTerminalDetail);
		}
		detail.setCustomer(customer);
		detail.setNewCustomer(newCustomer);
	}

	@Override
	public UnitSessionDetail get(UnitTerminalDetail unitTerminalDetail) {
		UnitSessionDetail detail = unitSessionMapping.get(unitTerminalDetail);
		if (detail == null) {
			detail = generateToken(unitTerminalDetail);
		}
		return detail;
	}

	@Override
	public String toString() {
		return "UnitSessionCache{" + "unitSessionMapping=" + unitSessionMapping.size() + '}';
	}

}
