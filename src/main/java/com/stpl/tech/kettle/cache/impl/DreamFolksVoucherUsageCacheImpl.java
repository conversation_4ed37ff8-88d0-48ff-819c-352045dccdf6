package com.stpl.tech.kettle.cache.impl;

import com.hazelcast.collection.ISet;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.kettle.cache.DreamFolksVoucherUsageCache;
import com.stpl.tech.kettle.repository.kettle.DreamfolksTransactionDetailDao;
import com.stpl.tech.kettle.util.Constants.CacheConstants;
import com.stpl.tech.master.domain.model.CustomerAppliedCouponDetail;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

@Service
public class DreamFolksVoucherUsageCacheImpl implements DreamFolksVoucherUsageCache {

    private static final Logger LOG = LoggerFactory.getLogger(DreamFolksVoucherUsageCacheImpl.class);

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance instance;

    @Autowired
    DreamfolksTransactionDetailDao dreamfolksTransactionDetailDao;

    @PostConstruct
    public void createCache() {
        LOG.info("POST-CONSTRUCT DreamFolks Voucher Codes Used Cache");
        long time = System.currentTimeMillis();
        dreamFolksVoucherCodesUsed = instance.getSet(CacheConstants.DREAM_FOLKS_VOUCHER_CODES_USED);
        dreamFolksVoucherCodesUsed.clear();
        List<String> listOfVoucherCodesUsed = dreamfolksTransactionDetailDao.getAllDistinctVoucherCodes();
        dreamFolksVoucherCodesUsed.addAll(listOfVoucherCodesUsed);
        LOG.info("POST-CONSTRUCT DreamFolks Voucher Codes Used Cache took {} ms", System.currentTimeMillis() - time);
    }

    @Override
    public void refreshCache(){
        createCache();
    }

    @Getter
    private ISet<String> dreamFolksVoucherCodesUsed ;
}
