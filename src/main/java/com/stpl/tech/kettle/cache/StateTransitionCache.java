/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.cache;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;

import com.stpl.tech.kettle.domain.model.StateData;
import com.stpl.tech.kettle.domain.model.StateTransitionObject;
import com.stpl.tech.kettle.domain.model.TransitionData;
import com.stpl.tech.kettle.domain.model.TransitionState;
import com.stpl.tech.kettle.domain.model.TransitionStateData;
import com.stpl.tech.kettle.domain.model.TransitionStatus;

public class StateTransitionCache {

	private static StateTransitionCache INSTANCE;

	private static final class StateCacheData {
		private Map<String, StateData> states = new HashMap<String, StateData>();
		private Map<String, Set<String>> nextStates = new HashMap<String, Set<String>>();

		public void addToCache(StateData data) {
			states.put(data.getStateCode(), data);
		}

		public void addToCache(TransitionState data) {
			nextStates.put(data.getState(), new HashSet<String>(data.getNextStates()));
		}

		public boolean check(String fromState, String toState) {
			Set<String> nextState = nextStates.get(fromState);
			return nextState != null && nextState.contains(toState);
		}
	}

	private StateTransitionCache() throws JAXBException {
		for (StateTransitionObject value : StateTransitionObject.values()) {
			JAXBContext jaxbContext = JAXBContext.newInstance(TransitionStateData.class);

			Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
			TransitionStateData data = (TransitionStateData) jaxbUnmarshaller
					.unmarshal(Thread.currentThread().getContextClassLoader().getResourceAsStream(value.getXmlPath()));
			StateCacheData cache = new StateCacheData();
			for (StateData d : data.getStates()) {
				cache.addToCache(d);
			}
			for (TransitionState d : data.getTransitions()) {
				cache.addToCache(d);
			}
			transitionData.put(value, cache);
		}
	}

	private final Map<StateTransitionObject, StateCacheData> transitionData = new HashMap<StateTransitionObject, StateCacheData>();

	public static StateTransitionCache getInstance() {
		if (INSTANCE == null) {
			try {
				INSTANCE = new StateTransitionCache();
			} catch (JAXBException e) {
				e.printStackTrace();
			}
		}
		return INSTANCE;
	}

	public void setTransitionState(StateTransitionObject type, TransitionData data) {
		StateCacheData cache = transitionData.get(type);
		data.setStatus(
				cache != null && cache.check(data.getFromStateCode(), data.getToStateCode()) ? TransitionStatus.SUCCESS
						: TransitionStatus.FAILURE);
	}

	@Override
	public String toString() {
		return "StateTransitionCache{" + "transitionData=" + transitionData.size() + '}';
	}
}
