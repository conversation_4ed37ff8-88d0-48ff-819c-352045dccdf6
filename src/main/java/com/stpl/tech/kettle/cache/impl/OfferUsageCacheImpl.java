package com.stpl.tech.kettle.cache.impl;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.kettle.cache.OfferUsageCache;
import com.stpl.tech.kettle.util.Constants.CacheConstants;
import com.stpl.tech.master.domain.model.CustomerAppliedCouponDetail;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Service
public class OfferUsageCacheImpl implements OfferUsageCache {
    private static final Logger LOG = LoggerFactory.getLogger(OfferUsageCacheImpl.class);

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance instance;

    @PostConstruct
    public void createCache() {
        customerAppliedCouponDetailMap = instance.getMap(CacheConstants.MASTER_DATA_CACHE_CUSTOMER_APPLIED_COUPON_DETAIL);
    }

    public void clearCache(){
        instance.getMap(CacheConstants.MASTER_DATA_CACHE_CUSTOMER_APPLIED_COUPON_DETAIL).clear();
    }

    @Getter
    private IMap<String, CustomerAppliedCouponDetail> customerAppliedCouponDetailMap ;

}
