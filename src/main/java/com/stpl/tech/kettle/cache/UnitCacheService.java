package com.stpl.tech.kettle.cache;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.data.kettle.ComplimentaryCode;
import com.stpl.tech.kettle.data.kettle.DeliveryPartner;
import com.stpl.tech.kettle.data.master.BrandDetail;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.master.domain.model.*;

public interface UnitCacheService {

	Map<Integer, Unit> getAllUnitCache();

	Unit getUnitById(Integer unitId);

	Map<Integer, PaymentMode> getAllPaymentMode();

	List<PaymentMode> getPaymentModesByUnit(Integer unitId, List<Integer> paymentModeIds,
			Map<Integer, PaymentMode> paymentModeMap);

	PaymentMode getPaymentModeById(Integer id);

	List<BrandDetail> getAllBrandDetail();

	List<DeliveryPartner> getAllDeliveryPartner();

	IdCodeName getDeliveryPartnerById(Integer id);

	List<ChannelPartnerDetail> getAllChannelPartners();

	ChannelPartnerDetail getChannelPartnerById(Integer id);

	Collection<IdCodeName> getAllComplementaryCodes(String status) throws DataNotFoundException;

	List<ComplimentaryCode> getAllComplimentaryCodes();

	List<CancellationReason> getAllCancellationReasons(UnitCategory unitCategory);


	List<Unit> getAllUnitByEmployee(List<Integer> unitIds);

	Integer getDeliveryUnit(int unitId, int partnerId, int branchId, boolean isCod);

	Map<Integer, UnitBasicDetail> getAllUnitBasicDetailCache();

	UnitBasicDetail getUnitBasicDetailById(Integer unitId);


	Integer getUnitPartnerBrandLoyalty(UnitPartnerBrandKey key);

	public IdCodeName getSubcategoryById(Integer subType);

	public  Map<Pair<BigDecimal,String>, String> getUnitProductAlias(Integer unitId , Integer productId);

	public String getCacheReferenceMetadata(CacheReferenceType cacheReferenceType);
}
