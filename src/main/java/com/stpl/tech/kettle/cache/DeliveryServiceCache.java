package com.stpl.tech.kettle.cache;

import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.data.kettle.UnitToDeliveryPartnerMappings;

public interface DeliveryServiceCache {
	List<UnitToDeliveryPartnerMappings> getUnitPartnerMappings();

	Map<Integer, String> getAutomatedPartnerMap();

	Map<Integer, String> getCashEligibleMap();

	String getCashEligibleStatus(int partnerId);

	Map<Integer, Map<String, String>> getApiProfiles();

	void evictApiProfileMap();

	void evictCashEligibleMap();

	void evictAutomatedPartnerMap();

	void evictUnitPartnerMappings();
}
