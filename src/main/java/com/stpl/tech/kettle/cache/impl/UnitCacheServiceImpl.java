package com.stpl.tech.kettle.cache.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

import com.google.common.base.Stopwatch;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.hazelcast.multimap.MultiMap;
import com.stpl.tech.kettle.data.master.RefLookupType;
import com.stpl.tech.kettle.repository.master.RefLookupTypeDao;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.master.domain.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.converter.Converters;
import com.stpl.tech.kettle.data.kettle.ComplimentaryCode;
import com.stpl.tech.kettle.data.kettle.DeliveryPartner;
import com.stpl.tech.kettle.data.master.BrandDetail;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.DataNotFoundInDBException;
import com.stpl.tech.kettle.exceptions.DataNotFoundInHazelCastException;
import com.stpl.tech.kettle.repository.kettle.ComplimentryCodeDao;
import com.stpl.tech.kettle.repository.kettle.DeliveryPartnerDao;
import com.stpl.tech.kettle.repository.master.BrandDetailDao;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.Constants.CacheConstants;
import com.stpl.tech.kettle.util.Constants.ListTypes;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadataType;

import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.NoResultException;

@Service
@Log4j2
public class UnitCacheServiceImpl implements UnitCacheService {

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;

	@Autowired
	private DeliveryPartnerDao deliveryPartnerDao;

	@Autowired
	private BrandDetailDao brandDetailDao;

	@Autowired
	private ComplimentryCodeDao complementaryCodeDao;

	@Autowired
	private RefLookupTypeDao refLookupTypeDao;

	private final Map<ListTypes, Map<Integer, IdCodeName>> listData = new HashMap<ListTypes, Map<Integer, IdCodeName>>();

	private Map<Integer, IdCodeName> subCategoryMap = new HashMap<>() ;



	private Map<UnitPartnerBrandKey, Map<UnitPartnerBrandMappingMetadataType, String>> unitPartnerBrandMetadataMap;

	@PostConstruct
	public void loadCache() throws DataNotFoundException {
		log.info("POST-CONSTRUCT ListData - STARTED");
		long time = System.currentTimeMillis();

		for (ListTypes type : ListTypes.values()) {
			updateListData(type);
		}
		log.info("Inside POSTCONSTRUCT - updateListData : took {} ms", System.currentTimeMillis() - time);
		unitPartnerBrandMetadataMap = instance.getMap(("MasterDataCache:unitPartnerBrandMetadataMap"));
//        watch.reset();
//        watch.start();
//        refreshCreditAccounts();
//        LOG.info("Inside POSTCONSTRUCT - MetadataCache refreshCreditAccounts : took {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
//        watch.reset();
//        watch.start();
		// log.info("Inside POSTCONSTRUCT - MetadataCache OVERALL : took {} ms",
		// watch1.stop().elapsed(TimeUnit.MILLISECONDS));

	}

	@Override
	public IMap<Integer, Unit> getAllUnitCache() {
		log.info("Fetching all units detail");
		Stopwatch watch = Stopwatch.createUnstarted();
		watch.start();
		IMap<Integer, Unit> allUnitTrimDataMap = instance.getMap(CacheConstants.UNITS_CACHE);
		if (Objects.isNull(allUnitTrimDataMap)) {
			throw new DataNotFoundInHazelCastException("Unit detail not found");
		}
		log.info("Loaded Unit Cache in {} ms",watch.stop().elapsed(TimeUnit.MILLISECONDS));
		return allUnitTrimDataMap;
	}

	@Override
	public Unit getUnitById(Integer unitId) {
		log.info("Fetching unit basic details for unit id {}", unitId);
		IMap<Integer,Unit> unitMap = getAllUnitCache();
		Stopwatch watch = Stopwatch.createUnstarted();
		watch.start();
		Unit unitData = unitMap.get(unitId);
		log.info("Loaded Unit Cache For Unit Id : {} in  {} ms",unitId,watch.stop().elapsed(TimeUnit.MILLISECONDS));
		//Unit uniData = (Unit) instance.getMap(CacheConstants.UNITS_CACHE).get(unitId);

		if (Objects.isNull(unitData)) {
			throw new DataNotFoundInHazelCastException("Data not found for unit id " + unitId);
		}
		return unitData;
	}

	@Override
	public Map<Integer, PaymentMode> getAllPaymentMode() {
		log.info("Fetching all payment modes");
		Stopwatch watch = Stopwatch.createUnstarted();
		watch.start();
		Map<Integer, PaymentMode> allPaymentModes = instance.getMap(CacheConstants.PAYMENT_MODES_CACHE);
		if (Objects.isNull(allPaymentModes)) {
			throw new DataNotFoundInHazelCastException("Payment Modes not found");
		}
		log.info("Payment Mode Cache Loaded in {} ms",watch.stop().elapsed(TimeUnit.MILLISECONDS));
		return allPaymentModes;
	}

	@Override
	public List<PaymentMode> getPaymentModesByUnit(Integer unitId, List<Integer> paymentModeIds,
			Map<Integer, PaymentMode> paymentModeMap) {
		log.info("Fetching payment modes for unit id {}", unitId);
		List<PaymentMode> paymentModeList = new ArrayList<>();
		for (Integer paymentModeId : paymentModeIds) {
			if (paymentModeMap.containsKey(paymentModeId)) {
				paymentModeList.add(paymentModeMap.get(paymentModeId));
			}
		}
		return paymentModeList;
	}

	@Override
	public PaymentMode getPaymentModeById(Integer id) {
		log.info("Fetching payment mode");
		Map<Integer, PaymentMode> allPaymentModes = instance.getMap(CacheConstants.PAYMENT_MODES_CACHE);
		if (Objects.isNull(allPaymentModes)) {
			throw new DataNotFoundInHazelCastException("Unit detail not found");
		}
		return allPaymentModes.get(id);
	}

	@Override
	public List<BrandDetail> getAllBrandDetail() {
		List<BrandDetail> brandDetailList = brandDetailDao.findAll();
		if (brandDetailList.isEmpty()) {
			throw new DataNotFoundInDBException("Brand detail not found");
		}
		return brandDetailList;
	}

	@Override
	public List<DeliveryPartner> getAllDeliveryPartner() {
		List<DeliveryPartner> deliveryPartnerList = deliveryPartnerDao.findAll();
		if (deliveryPartnerList.isEmpty()) {
			throw new DataNotFoundInDBException("Delivery partner data not found");
		}
		return deliveryPartnerList;
	}

	@Override
	public IdCodeName getDeliveryPartnerById(Integer id) {
		return listData.get(ListTypes.DELIVERY_PARTNERS).get(id);
	}

	@Override
	public List<ChannelPartnerDetail> getAllChannelPartners() {
		Map<Integer, ChannelPartnerDetail> channelPartnerMap = instance
				.getMap(CacheConstants.CHANNEL_PARTNER_CACHE);
		if (Objects.isNull(channelPartnerMap) || channelPartnerMap.isEmpty()) {
			throw new DataNotFoundInHazelCastException("Unit partner mapping not found for");
		}
		return channelPartnerMap.values().stream().toList();
	}

	@Override
	public ChannelPartnerDetail getChannelPartnerById(Integer id) {
		Map<Integer, ChannelPartnerDetail> channelPartnerMap = instance
				.getMap(CacheConstants.CHANNEL_PARTNER_CACHE);
		if (Objects.isNull(channelPartnerMap) || Objects.isNull(channelPartnerMap.get(id))) {
			throw new DataNotFoundInHazelCastException("Channel partner mapping not found for " + id);
		}
		return channelPartnerMap.get(id);
	}

	@Override
	public Collection<IdCodeName> getAllComplementaryCodes(String status) throws DataNotFoundException {
		if (Objects.isNull(listData)) {
			for (ListTypes type : ListTypes.values()) {
				updateListData(type);
			}
		}
		if (Objects.isNull(listData.isEmpty())) {
			throw new DataNotFoundInDBException("No complimentory code found with status : " + status);
		}
		return listData.get(ListTypes.COMPLIMENTARY_CODES).values();
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterTransactionManager", readOnly = true, propagation = Propagation.REQUIRED)
	public void updateListData(ListTypes type) throws DataNotFoundException {

		switch (type) {
		case DELIVERY_PARTNERS:
			listData.put(ListTypes.DELIVERY_PARTNERS, new TreeMap<Integer, IdCodeName>());
			List<IdCodeName> result = new ArrayList<>();
			List<DeliveryPartner> deliveryPartnerList = deliveryPartnerDao.findAll(); {
			for (DeliveryPartner list : deliveryPartnerList) {
				result.add(Converters.convert(list));
			}
			for (IdCodeName data : result) {
				listData.get(ListTypes.DELIVERY_PARTNERS).put(data.getId(), data);
			}
		}
			break;
		case COMPLIMENTARY_CODES:
			listData.put(ListTypes.COMPLIMENTARY_CODES, new TreeMap<Integer, IdCodeName>());
			List<ComplimentaryCode> complimentaryCodeList = complementaryCodeDao.findAllByStatus(AppConstants.ACTIVE);
			ListData list = Converters.convertToListData(complimentaryCodeList);
			for (IdCodeName data : list.getContent()) {
				listData.get(ListTypes.COMPLIMENTARY_CODES).put(data.getId(), data);
			}
			break;
			case SUB_CATEGORIES:
				for (ListData list1 : getAllListData(ListTypes.SUB_CATEGORIES.getGroup(), true)) {
					for (IdCodeName data : list1.getContent()) {
						subCategoryMap.put(data.getId(),data);
					}
				}
				break;
		default:
			break;
		}
	}

	@Override
	public IdCodeName getSubcategoryById(Integer subType){
		return subCategoryMap.get(subType);
	}

	private List<ListData> getAllListData(String group, boolean getAll) throws DataNotFoundException {
		try {
			List<ListData> addOns = new ArrayList<>();
			List<RefLookupType> types = refLookupTypeDao.findByRtlGroup(group);
			for (RefLookupType type : types) {
				addOns.add(Converters.convert(type, getAll));
			}
			return addOns;
		} catch (NoResultException e) {
			throw new DataNotFoundException(String.format("Did not find RefLookup Data with type %s", group), e);
		}

	}

	@Override
	public List<ComplimentaryCode> getAllComplimentaryCodes() {
		List<ComplimentaryCode> complimentaryCodes = complementaryCodeDao.findAll();
		if (complimentaryCodes.isEmpty()) {
			throw new DataNotFoundInDBException("No complimentary code found");
		}
		return complimentaryCodes;
	}

	@Override
	public List<CancellationReason> getAllCancellationReasons(UnitCategory unitCategory) {
		List<Object> cancellationReasonObject = instance.getMultiMap(CacheConstants.CANCELLATION_REASON_CACHE)
				.get(unitCategory).stream().toList();
		List<CancellationReason> cancellationReasons = new ArrayList<>();
		if (cancellationReasonObject.isEmpty()) {
			throw new DataNotFoundInHazelCastException(
					"Cancellation reasons not found for unit category " + unitCategory.value());
		}
		cancellationReasonObject.forEach(c -> cancellationReasons.add((CancellationReason) c));
		return cancellationReasons;
	}


	@Override
	public List<Unit> getAllUnitByEmployee(List<Integer> unitIds) {
		List<Unit> unitList = new ArrayList<>();
		for (Integer unitId : unitIds) {
			unitList.add(getUnitById(unitId));
		}
		return unitList;
	}

	@Override
	public Integer getDeliveryUnit(int unitId, int partnerId, int brandId, boolean isCod) {
		if (!isCod) {
			return unitId;
		}
		if (partnerId == 0) {
			partnerId = AppConstants.CHAAYOS_DELIVERY_PARTNER_ID;
		}
		Map<UnitPartnerBrandKey, UnitPartnerBrandMappingData> unitPartnerBrandMappingMetaData = instance
				.getMap(CacheConstants.PARTNER_BRAND_MAPPING_CACHE);
		UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, partnerId);
		return unitPartnerBrandMappingMetaData.get(key).getPriceProfileUnitId();
	}

	@Override
	public Map<Integer, UnitBasicDetail> getAllUnitBasicDetailCache() {
		log.info("Fetching all units basic detail");
		Stopwatch watch = Stopwatch.createStarted();
		watch.start();
		Map<Integer, UnitBasicDetail> allUnitBasicDetailMap = instance
				.getMap(CacheConstants.UNIT_BASIC_DETAILS_CACHE);
		if (Objects.isNull(allUnitBasicDetailMap)) {
			throw new DataNotFoundInHazelCastException("Unit basicdetail not found");
		}
		log.info("Loaded Unit Basic Detail Cache in {} ms" , watch.stop().elapsed(TimeUnit.MILLISECONDS));
		return allUnitBasicDetailMap;
	}

	@Override
	public UnitBasicDetail getUnitBasicDetailById(Integer unitId) {
		log.info("Fetching unit basic details for unit id {}", unitId);
		Stopwatch watch = Stopwatch.createUnstarted();
		watch.start();
		UnitBasicDetail unitBasicData = (UnitBasicDetail) instance.getMap(CacheConstants.UNIT_BASIC_DETAILS_CACHE)
				.get(unitId);
		if (Objects.isNull(unitBasicData)) {
			throw new DataNotFoundInHazelCastException("Data not found for unit id " + unitId);
		}
		log.info("Loaded Unit Basic Detail Cache For Unit Id : {} in {} ms",unitId,
				watch.stop().elapsed(TimeUnit.MILLISECONDS));
		return unitBasicData;
	}


	@Override
	public Integer getUnitPartnerBrandLoyalty(UnitPartnerBrandKey key) {
		if (unitPartnerBrandMetadataMap.containsKey(key) && unitPartnerBrandMetadataMap.get(key) != null
				&& unitPartnerBrandMetadataMap.get(key).containsKey(UnitPartnerBrandMappingMetadataType.LOYALTY_POINTS)
				&& unitPartnerBrandMetadataMap.get(key)
						.get(UnitPartnerBrandMappingMetadataType.LOYALTY_POINTS) != null) {
			return Integer.parseInt(
					unitPartnerBrandMetadataMap.get(key).get(UnitPartnerBrandMappingMetadataType.LOYALTY_POINTS));
		}
		return Integer.parseInt(UnitPartnerBrandMappingMetadataType.LOYALTY_POINTS.getDefaultValue());

	}

	@Override
	public  Map<Pair<BigDecimal,String>, String> getUnitProductAlias(Integer unitId , Integer productId){
		Map<Integer, Map<Integer,Map<Pair<BigDecimal,String>,String>>> unitProductAlias =  instance.getMap("MasterDataCache:unitProductAlias");
		return unitProductAlias.containsKey(unitId) ? unitProductAlias.get(unitId).get(productId) : new HashMap<>();
	}

	public String getCacheReferenceMetadata(CacheReferenceType cacheReferenceType){
		Map<CacheReferenceType,String> cacheReferenceValue = instance.getMap("MasterDataCache:cacheReferenceValue");
		return cacheReferenceValue.get(cacheReferenceType);
	}
}
