package com.stpl.tech.kettle.cache;

import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.UnitSessionDetail;
import com.stpl.tech.kettle.domain.model.UnitTerminalDetail;

public interface UnitSessionCache {

	UnitSessionDetail generateToken(UnitTerminalDetail unitTerminalDetail);

	void setCustomer(UnitTerminalDetail unitTerminalDetail, Customer customer, boolean newCustomer);

	UnitSessionDetail get(UnitTerminalDetail unitTerminalDetail);

}
