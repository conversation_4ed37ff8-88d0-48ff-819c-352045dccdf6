package com.stpl.tech.kettle.cache.impl;

import com.stpl.tech.kettle.cache.MappingCache;
import com.stpl.tech.kettle.cache.MappingCacheService;
import com.stpl.tech.kettle.data.kettle.SubscriptionPlan;
import com.stpl.tech.kettle.domain.model.SubscriptionOfferInfoDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class MappingCacheImpl implements MappingCache {
    @Autowired
    private MappingCacheService service;

    private static final String MAPPING_CACHE_OFFER_LOCK = "MAPPING_CACHE_OFFER_LOCK";

    private Map<String, SubscriptionOfferInfoDetail> subscriptionOfferInfoDetailMap= new HashMap<>() ;
    @Override
    public SubscriptionOfferInfoDetail getSubscriptionInfoDetail(SubscriptionPlan subscriptionPlan){
        if (!subscriptionOfferInfoDetailMap.containsKey(subscriptionPlan.getSubscriptionPlanCode())) {
            log.info("Fetching Offer Detail For Coupon Code ::: {}",subscriptionPlan.getSubscriptionPlanCode());
            synchronized (MAPPING_CACHE_OFFER_LOCK) {
                SubscriptionOfferInfoDetail subscriptionOfferInfoDetail = service.findSubscriptionDetail(subscriptionPlan.getSubscriptionPlanCode());
                if(Objects.nonNull(subscriptionOfferInfoDetail)){
                    subscriptionOfferInfoDetailMap.put(subscriptionOfferInfoDetail.getSubscriptionCode(), subscriptionOfferInfoDetail);
                }
                return subscriptionOfferInfoDetail;
            }
        }
        else {
            return subscriptionOfferInfoDetailMap.get(subscriptionPlan.getSubscriptionPlanCode());
        }
    }

}
