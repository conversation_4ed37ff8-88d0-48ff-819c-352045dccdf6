package com.stpl.tech.kettle.cache;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.data.kettle.OrderItemStatus;
import com.stpl.tech.kettle.domain.model.Order;

import java.util.List;

public interface OrderInfoCache {
    Boolean addToUndelivered(OrderInfo order);
    void addToCache(OrderInfo order);

    public void updateOrderInOrderInfoCache(List<OrderItemStatus> orders);

    List<Order> getUnitOrdersFromCache(Integer unitId);

}
