package com.stpl.tech.kettle.cache;

import com.stpl.tech.master.domain.model.ConfigAttributeValue;

import java.util.Map;
import java.util.Set;

public interface EnvironmentPropertiesCache {
	
    Map<String, Map<String, String>> getApplicationAttributeValues();

    Map<String, Map<String, ConfigAttributeValue>> getConfigAttributeValues();

    boolean isGyftrActive();

    
    String getCharityMailIds();
    

    Set<String> getInternalNos();

    Map<String, String> getcompanyConfigAttributes(String applicationName);


}
