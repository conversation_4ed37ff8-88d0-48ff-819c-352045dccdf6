package com.stpl.tech.kettle.cache.impl;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.kettle.cache.OrderInfoCache;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.OrderItemStatus;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderEmailEntryType;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.service.OrderSearchService;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.master.domain.model.UnitCategory;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;

@Service
@Log4j2
public class OrderInfoCacheServiceImpl implements OrderInfoCache {

	@Autowired
	@Qualifier(value = "KettleHazelCastInstance")
	private HazelcastInstance instance;

	@Autowired
	private OrderSearchService orderService;

	@Autowired
	private EnvironmentProperties props;
	private static final List<OrderStatus> orderStatusList = Arrays.asList(OrderStatus.CREATED, OrderStatus.PROCESSING,
			OrderStatus.READY_TO_DISPATCH, OrderStatus.CANCELLED_REQUESTED);
	public static final List<OrderStatus> unsettledOrderStaus = Arrays.asList(OrderStatus.CREATED,
			OrderStatus.PROCESSING, OrderStatus.READY_TO_DISPATCH);

	public static final List<OrderStatus> undeliveredOrderStaus = Arrays.asList(OrderStatus.SETTLED);

	private static final List<UnitCategory> categoryList = Arrays.asList(UnitCategory.TAKE_AWAY, UnitCategory.COD,
			UnitCategory.CAFE);

	private IMap<Integer, OrderInfo> oMap;
	private IMap<Integer, List<Integer>> undeliveredMessages;
	private IMap<Integer, Map<UnitCategory, Set<Integer>>> orderByCategoryCache;
	private IMap<Integer, Set<OrderEmailEntryType>> orderDeliveryEmail;
	/**
	 * riderContactNo :(allotedNo : orderId)
	 */
	private IMap<String, HashMap<String, Integer>> riderMappingCache;
	private IMap<String, Integer> allotmentCountMap;

	private IMap<String, Integer> resendSMSCount;

	@PostConstruct
	public void createOrderInfoCache() {
		log.info("##### Inside PostConstruct Of OrderInfoCache #####");
		log.info("##### Creating Order Info Cache #####");
		oMap = instance.getMap("OrderInfoCache:OrdersCache");
		undeliveredMessages = instance.getMap("OrderInfoCache:UndeliveredMessages");
		orderByCategoryCache = instance.getMap("OrderInfoCache:OrderByCategoryCache");
		orderDeliveryEmail = instance.getMap("OrderInfoCache:OrderDeliveryEmail");
		riderMappingCache = instance.getMap("OrderInfoCache:RiderMappingCache");
		allotmentCountMap = instance.getMap("OrderInfoCache:AllotmentCountMap");
		resendSMSCount = instance.getMap("OrderInfoCache:ResendSMSCount");
	}

	public void clearOrderInfoCache() {
		log.info("##### Clearing OrderInfo Cache #####");
		instance.getMap("OrderInfoCache:OrdersCache").clear();
		;
		instance.getMap("OrderInfoCache:UndeliveredMessages").clear();
		instance.getMap("OrderInfoCache:OrderByCategoryCache").clear();
		instance.getMap("OrderInfoCache:OrderDeliveryEmail").clear();
		instance.getMap("OrderInfoCache:RiderMappingCache").clear();
		instance.getMap("OrderInfoCache:AllotmentCountMap").clear();
		instance.getMap("OrderInfoCache:ResendSMSCount").clear();
	}

	@Override
	public Boolean addToUndelivered(OrderInfo order) {
		Integer unitId = order.getOrder().getUnitId();
		List<Integer> orderInfoList = getOrderInfoList(unitId);
		Boolean flag = orderInfoList.add(order.getOrder().getOrderId());
		oMap.put(order.getOrder().getOrderId(), order);
		if (Boolean.TRUE.equals(flag)) {
			undeliveredMessages.put(unitId, orderInfoList);
		}
		return flag;
	}


	private List<Integer> getOrderInfoList(Integer unitId) {
		List<Integer> orderInfoList = undeliveredMessages.get(unitId);
		if (orderInfoList == null) {
			orderInfoList = new ArrayList<Integer>();
		}
		return orderInfoList;
	}

	@Override
	public synchronized void addToCache(OrderInfo order) {
		Set<Integer> ordersMap = null;
		Map<UnitCategory, Set<Integer>> categoryMap = orderByCategoryCache.get(order.getOrder().getUnitId());
		if (Objects.isNull(categoryMap)) {
			categoryMap = new HashMap<>();
			for (UnitCategory category : categoryList) {
				categoryMap.put(category, new TreeSet<>());
			}
		}
		if (Objects.nonNull(UnitCategory.valueOf(order.getOrder().getSource()))) {
			ordersMap = categoryMap.get(UnitCategory.valueOf(order.getOrder().getSource()));
			if (ordersMap == null || ordersMap.size() == 0) {
				ordersMap = new TreeSet<>();
			}
			ordersMap.add(order.getOrder().getOrderId());
			oMap.put(order.getOrder().getOrderId(), order);
			categoryMap.put(UnitCategory.valueOf(order.getOrder().getSource()), ordersMap);
			orderByCategoryCache.put(order.getOrder().getUnitId(), categoryMap);
		} else {
			ordersMap = new TreeSet<>();
			Collection<Set<Integer>> values = categoryMap.values();
			if (Objects.nonNull(values)) {
				for (Set<Integer> orders : values) {
					ordersMap.addAll(orders);
				}
			}
		}
	}

	@Override
	public synchronized void updateOrderInOrderInfoCache(List<OrderItemStatus> orders){
		Set<Integer> orderItemIds = new HashSet<>();
		for(OrderItemStatus oi : orders){
			orderItemIds.add(oi.getOrderItemId().getOrderItemId());
		}
		for(OrderItemStatus oi : orders){
			Integer orderId = oi.getOrderId();
			OrderInfo orderInfo = oMap.getOrDefault(orderId,null);
			if(Objects.nonNull(orderInfo)) {
				for (OrderItem item : orderInfo.getOrder().getOrders()) {
					if (orderItemIds.contains(item.getItemId())) {
						item.setIsHoldOn(AppConstants.NO);
					}
					if (Objects.nonNull(item.getComposition()) && !CollectionUtils.isEmpty(item.getComposition().getMenuProducts())) {
						for (OrderItem mi : item.getComposition().getMenuProducts()) {
							if (orderItemIds.contains(mi.getItemId())) {
								mi.setIsHoldOn(AppConstants.NO);
							}
						}
					}
				}
				oMap.put(orderId, orderInfo);
			}
		}
	}

	@Override
	public List<Order> getUnitOrdersFromCache(Integer unitId) {
		List<Order> result = new ArrayList<>();
		Map<UnitCategory, Set<Integer>> unitOrders = orderByCategoryCache.get(unitId);
		if(Objects.nonNull(unitOrders)){
			Set<Integer> dineInOrders = unitOrders.get(UnitCategory.CAFE);
			Set<Integer> takeAwayOrders =unitOrders.get(UnitCategory.TAKE_AWAY);

			Iterator<Integer> it = dineInOrders.iterator();

			while(it.hasNext()){
				result.add(oMap.get(it.next()).getOrder());
			}

			it = takeAwayOrders.iterator();

			while (it.hasNext()){
				result.add(oMap.get(it.next()).getOrder());
			}

			return result.stream().sorted(Comparator.comparing(Order::getBillCreationTime)).toList();
		}

		return result;

	}
}