package com.stpl.tech.kettle.core.notification;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.stpl.tech.kettle.core.ReceiptType;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderInvoice;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.SubscriptionPlanDomain;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.PrintType;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
@NoArgsConstructor
public class OrderInfoDomain {
    private static final long serialVersionUID = 8338090534694576702L;
    private EnvType env;//
    private Order order;//
    private Customer customer;//
    private IdCodeName deliveryPartner;//
    private IdCodeName channelPartner;//
    private List<String> receipts;//
    private List<String> additionalReceipts;//
    private List<String> newCards;//
    private Unit unit;//
    private DeliveryResponse deliveryDetails;//
    private boolean billIncludedInReceipts;
    private PrintType printType;//
    private String feedbackUrl;//
    private String qrCode;//
    private Map<ReceiptType, String> androidReceipts;//
    private Brand brand;//
    private NextOffer nextOffer;
    private NextOffer nextDeliveryOffer;
    private OrderInvoice orderInvoice;
    private OrderNotification orderNotification;
    private BigDecimal cashCardPurchaseAmt;
    private BigDecimal cashCardDeductedAmt;
    private BigDecimal cashCardExtraAmt;
    private BigDecimal cashCardPendingAmt;
    private List<Integer> refrenceOrderIds;
    private SubscriptionPlanDomain subscriptionPlan;
    private Map<Integer, OrderNotification> orderNotificationMap;

    @JsonProperty("loyaltyAwarded")
    @SerializedName("loyaltyAwarded")
    private Boolean loyaltyAwarded;

    public OrderInfoDomain(EnvType env, Order order, Customer customer, Unit unit, IdCodeName deliveryPartner,
                     IdCodeName channelPartner) {
        super();
        this.order = order;
        this.customer = customer;
        this.env = env;
        this.deliveryPartner = deliveryPartner;
        this.channelPartner = channelPartner;
        this.unit = unit;
    }

    public OrderInfoDomain(EnvType env, Order order, Customer customer, IdCodeName deliveryPartner, IdCodeName channelPartner,
                     List<String> receipts, Unit unit, DeliveryResponse deliveryDetails, PrintType printType) {
        super();
        this.env = env;
        this.order = order;
        this.customer = customer;
        this.deliveryPartner = deliveryPartner;
        this.channelPartner = channelPartner;
        this.receipts = receipts;
        this.unit = unit;
        this.deliveryDetails = deliveryDetails;
        this.printType = printType;
    }

    public OrderInfoDomain(EnvType env, Order order, Customer customer, IdCodeName deliveryPartner, IdCodeName channelPartner,
                     Map<ReceiptType, String> receipts, Unit unit, DeliveryResponse deliveryDetails, PrintType printType) {
        super();
        this.env = env;
        this.order = order;
        this.customer = customer;
        this.deliveryPartner = deliveryPartner;
        this.channelPartner = channelPartner;
        this.androidReceipts = receipts;
        this.unit = unit;
        this.deliveryDetails = deliveryDetails;
        this.printType = printType;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
    }

    public EnvType getEnv() {
        return env;
    }

    public void setEnv(EnvType env) {
        this.env = env;
    }

    public IdCodeName getDeliveryPartner() {
        return deliveryPartner;
    }

    public void setDeliveryPartner(IdCodeName deliveryPartner) {
        this.deliveryPartner = deliveryPartner;
    }

    public IdCodeName getChannelPartner() {
        return channelPartner;
    }

    public void setChannelPartner(IdCodeName channelPartner) {
        this.channelPartner = channelPartner;
    }

    public List<String> getReceipts() {
        return receipts;
    }

    public void setReceipts(List<String> receipts) {
        this.receipts = receipts;
    }

    public List<String> getAdditionalReceipts() {
        if (additionalReceipts == null) {
            additionalReceipts = new ArrayList<>();
        }
        return additionalReceipts;
    }

    public void setAdditionalReceipts(List<String> additionalReceipts) {
        this.additionalReceipts = additionalReceipts;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OrderInfo orderInfo = (OrderInfo) o;
        return Objects.equals(getOrder().getOrderId(), orderInfo.getOrder().getOrderId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getOrder().getOrderId());
    }

    public Unit getUnit() {
        return unit;
    }

    public void setUnit(Unit unit) {
        this.unit = unit;
    }

    public DeliveryResponse getDeliveryDetails() {
        return deliveryDetails;
    }

    public void setDeliveryDetails(DeliveryResponse deliveryDetails) {
        this.deliveryDetails = deliveryDetails;
    }

    public boolean isBillIncludedInReceipts() {
        return billIncludedInReceipts;
    }

    public void setBillIncludedInReceipts(boolean billIncludedInRecipts) {
        this.billIncludedInReceipts = billIncludedInRecipts;
    }

    public List<String> getNewCards() {
        return newCards;
    }

    public void setNewCards(List<String> newCards) {
        this.newCards = newCards;
    }

    public PrintType getPrintType() {
        return printType;
    }

    public void setPrintType(PrintType printType) {
        this.printType = printType;
    }

    public Map<ReceiptType, String> getAndroidReceipts() {
        if (Objects.isNull(androidReceipts)) {
            androidReceipts = new HashMap<>();
        }
        return androidReceipts;
    }

    public void setAndroidReceipts(Map<ReceiptType, String> androidReceipts) {
        this.androidReceipts = androidReceipts;
    }

    public String getFeedbackUrl() {
        return feedbackUrl;
    }

    public void setFeedbackUrl(String feedbackUrl) {
        this.feedbackUrl = feedbackUrl;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public Brand getBrand() {
        return brand;
    }

    public void setBrand(Brand brand) {
        this.brand = brand;
    }

    public NextOffer getNextOffer() {
        return nextOffer;
    }

    public void setNextOffer(NextOffer nextOffer) {
        this.nextOffer = nextOffer;
    }

    public NextOffer getNextDeliveryOffer() {
        return nextDeliveryOffer;
    }

    public void setNextDeliveryOffer(NextOffer nextDeliveryOffer) {
        this.nextDeliveryOffer = nextDeliveryOffer;
    }

    public OrderInvoice getOrderInvoice() {
        return orderInvoice;
    }

    public void setOrderInvoice(OrderInvoice orderInvoice) {
        this.orderInvoice = orderInvoice;
    }

    public void setOrderNotification(OrderNotification orderNotification) {
        this.orderNotification = orderNotification;
    }

    public OrderNotification getOrderNotification() {
        return orderNotification;
    }

    public BigDecimal getCashCardPurchaseAmt() {
        return cashCardPurchaseAmt;
    }

    public void setCashCardPurchaseAmt(BigDecimal cashCardPurchaseAmt) {
        this.cashCardPurchaseAmt = cashCardPurchaseAmt;
    }

    public BigDecimal getCashCardDeductedAmt() {
        return cashCardDeductedAmt;
    }

    public void setCashCardDeductedAmt(BigDecimal cashCardDeductedAmt) {
        this.cashCardDeductedAmt = cashCardDeductedAmt;
    }

    public BigDecimal getCashCardExtraAmt() {
        return cashCardExtraAmt;
    }

    public void setCashCardExtraAmt(BigDecimal cashCardExtraAmt) {
        this.cashCardExtraAmt = cashCardExtraAmt;
    }

    public BigDecimal getCashCardPendingAmt() {
        return cashCardPendingAmt;
    }

    public void setCashCardPendingAmt(BigDecimal cashCardPendingAmt) {
        this.cashCardPendingAmt = cashCardPendingAmt;
    }

    public List<Integer> getRefrenceOrderIds() {
        if (Objects.isNull(refrenceOrderIds)) {
            refrenceOrderIds = new ArrayList<>();
        }
        return refrenceOrderIds;
    }

    public void setRefrenceOrderIds(List<Integer> refrenceOrderIds) {
        this.refrenceOrderIds = refrenceOrderIds;
    }

	public SubscriptionPlanDomain getSubscriptionPlan() {
		return subscriptionPlan;
	}

	public void setSubscriptionPlan(SubscriptionPlanDomain subscriptionPlan) {
		this.subscriptionPlan = subscriptionPlan;
	}

    public Boolean getLoyaltyAwarded() {
        return loyaltyAwarded;
    }

    public void setLoyaltyAwarded(Boolean loyaltyAwarded) {
        this.loyaltyAwarded = loyaltyAwarded;
    }

    public Map<Integer, OrderNotification> getOrderNotificationMap() {
        return orderNotificationMap;
    }

    public void setOrderNotificationMap(Map<Integer, OrderNotification> orderNotificationMap) {
        this.orderNotificationMap = orderNotificationMap;
    }
}
