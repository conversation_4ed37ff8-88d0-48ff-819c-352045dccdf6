package com.stpl.tech.kettle.core.properties;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


@Configuration
@ConfigurationProperties("hibernate")
@Getter
@Setter
@ToString
public class HibernateConfigProperties {

	private String dialect;
	private String showSql;
	private String hbmToDdlAuto;


}
