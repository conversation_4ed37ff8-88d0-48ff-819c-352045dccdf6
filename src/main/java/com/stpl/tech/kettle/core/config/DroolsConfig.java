package com.stpl.tech.kettle.core.config;

import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import lombok.extern.log4j.Log4j2;
import org.drools.decisiontable.DecisionTableProviderImpl;
import org.kie.api.KieServices;
import org.kie.api.builder.KieBuilder;
import org.kie.api.builder.KieFileSystem;
import org.kie.api.builder.KieModule;
import org.kie.api.io.Resource;
import org.kie.api.runtime.KieContainer;
import org.kie.internal.io.ResourceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Configuration
@Log4j2
public class DroolsConfig {
    public static final KieServices kieServices = KieServices.Factory.get();

    private static final String WALLET_DECISION_DROOL_FILE = "wallet_decision.xls";
    private static final String WALLET_DECISION_DROOL_PATH = "/drools/wallet_decision/";
    private static final String DENOMINATION_DECISION_DROOL_FILE = "denomination_decision.xls";
    private static final String DENOMINATION_DECISION_DROOL_PATH = "/drools/denomination_decision/";
    public Map<String,KieContainer> kieContainerObjectForWalletDecision = new ConcurrentHashMap<>();

    public Map<String,KieContainer> kieContainerObjectForDenominationPercentage = new ConcurrentHashMap<>();
    @Autowired
    private EnvironmentProperties env;

    public KieContainer getKieContainerForWalletDecision(String version){
        return kieContainerObjectForWalletDecision.get(Objects.nonNull(version) ? version : "default");
    }

    public KieContainer getKieContainerObjectForDenominationPercentage(String version){
        return kieContainerObjectForDenominationPercentage.get(Objects.nonNull(version) ? version : "default");
    }


    public void initDroolConfigForWalletDecision(String version){
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file ::::: {}",version);
                initializeWalletDecisionDrool(version);
            }
        } catch (Exception e) {
            log.info("Getting default version");
            initializeWalletDecisionDrool("default");
        }
    }

    public void initializeWalletDecisionDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + WALLET_DECISION_DROOL_PATH + version + "/";
        String filePath = baseDir + WALLET_DECISION_DROOL_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerObjectForWalletDecision.put(version, kieServices.newKieContainer(kieModule.getReleaseId()));
    }

    public void initDroolConfigForDenominationPercentage(String version){
        try {
            if(Objects.isNull(version)){
                throw new Exception();
            }
            else{
                log.info("Version received for getting drools file ::::: {}",version);
                initializeDenominationDrool(version);
            }
        } catch (Exception e) {
            log.info("Getting default version");
            initializeDenominationDrool("default");
        }
    }

    void initializeDenominationDrool(String version){
        String baseDir = "/data/app/kettle/" + System.getProperty("env.type") + DENOMINATION_DECISION_DROOL_PATH + version + "/";
        String filePath = baseDir + DENOMINATION_DECISION_DROOL_FILE;
        Path dirPath = Paths.get(baseDir);
        if (!Files.exists(dirPath)) {
            try {
                Files.createDirectories(dirPath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        Resource dt = ResourceFactory.newUrlResource("file:" + filePath);
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem().write(dt);
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        KieModule kieModule = kieBuilder.getKieModule();
        kieContainerObjectForDenominationPercentage.put(version, kieServices.newKieContainer(kieModule.getReleaseId()));
    }

}