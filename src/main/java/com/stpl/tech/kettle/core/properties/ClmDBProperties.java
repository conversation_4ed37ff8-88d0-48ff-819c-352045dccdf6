package com.stpl.tech.kettle.core.properties;

import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Getter;
import lombok.Setter;

@Configuration
@ConfigurationProperties("clm.jdbc")
@Getter
@Setter
public class ClmDBProperties extends DataSourceProperties {
	private int hikariPoolSize;
}
