package com.stpl.tech.kettle.core.config;

import com.stpl.tech.kettle.core.properties.ClmDBProperties;
import com.stpl.tech.kettle.core.properties.HibernateConfigProperties;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(basePackages = {
		"com.stpl.tech.kettle.repository.clm" }, entityManagerFactoryRef = "CLMDataSourceEMFactory", transactionManagerRef = "CLMDataSourceTM")
public class ClmConfig {

	@Autowired
	private ClmDBProperties clmDBProperties;

	@Autowired
	private HibernateConfigProperties hibernateConfigProperties;

	public ClmConfig() {
		super();
	}

	@Bean(name = "CLMDataSourceEMFactory")
	public LocalContainerEntityManagerFactoryBean clmEntityManagerFactory() {
		LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(clmDataSource());
		em.setPackagesToScan("com.stpl.tech.kettle.repository.clm");
		HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		Map<String, Object> properties = new HashMap<>();
		properties.put("hibernate.dialect", hibernateConfigProperties.getDialect());
		properties.put("hibernate.show-sql", hibernateConfigProperties.getShowSql());
		properties.put("hibernate.hbm2ddl.auto", hibernateConfigProperties.getHbmToDdlAuto());
		em.setJpaPropertyMap(properties);
		em.setPersistenceUnitName("CLMDataSourcePUName");
		return em;
	}

    @Bean(name = "CLMDataSource")
    public DataSource clmDataSource() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName(clmDBProperties.getDriverClassName());
        dataSource.setUrl(clmDBProperties.getUrl());
        dataSource.setUsername(clmDBProperties.getUsername());
        dataSource.setPassword(clmDBProperties.getPassword());
        return dataSource;
    }

//	@Primary
//	@Bean(name = "CLMDataSource")
//	public DataSource clmDataSource() {
//		HikariDataSource dataSource =   clmDBProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
//		dataSource.setMaximumPoolSize(clmDBProperties.getHikariPoolSize());
//		return dataSource;
//	}

	@Bean(name = "CLMDataSourceTM")
	public PlatformTransactionManager clmTransactionManager() {
		JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(clmEntityManagerFactory().getObject());
		return transactionManager;
	}

	@Bean(name = "CLMDataSourceET")
	public PersistenceExceptionTranslationPostProcessor clmExceptionTranslation() {
		return new PersistenceExceptionTranslationPostProcessor();
	}

}