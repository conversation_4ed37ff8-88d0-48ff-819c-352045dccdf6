package com.stpl.tech.kettle.core.config;

import com.stpl.tech.kettle.core.properties.HibernateConfigProperties;
import com.stpl.tech.kettle.core.properties.KettleDBProperties;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableJpaRepositories(basePackages = {
		"com.stpl.tech.kettle.repository.kettle" }, entityManagerFactoryRef = "KettleEntityManager", transactionManagerRef = "KettleTransactionManager")
public class KettleDbConfig {

	@Autowired
	private KettleDBProperties kettleDBProperties;

	@Autowired
	private HibernateConfigProperties hibernateConfigProperties;

	@Bean("KettleEntityManager")
	@Primary
	public LocalContainerEntityManagerFactoryBean kettleEntityManager() {
		LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(kettleDatasource());
		em.setPackagesToScan("com.stpl.tech.kettle.data.kettle");
		em.setPersistenceUnitName("KettleEntityManager");
		HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		Map<String, Object> properties = new HashMap<>();
		properties.put("hibernate.dialect", hibernateConfigProperties.getDialect());
		properties.put("hibernate.show-sql", hibernateConfigProperties.getShowSql());
		properties.put("hibernate.hbm2ddl.auto", hibernateConfigProperties.getHbmToDdlAuto());
		em.setJpaPropertyMap(properties);
		return em;
	}

//	@Primary
//	@Bean("KettleDataSource")
//	public DataSource kettleDatasource() {
//		HikariDataSource dataSource = kettleDBProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
//		dataSource.setMaximumPoolSize(kettleDBProperties.getHikariPoolSize());
//		return dataSource;
//	}

    @Primary
    @Bean
    public DataSource kettleDatasource() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName(kettleDBProperties.getDriverClassName());
        dataSource.setUrl(kettleDBProperties.getUrl());
        dataSource.setUsername(kettleDBProperties.getUsername());
        dataSource.setPassword(kettleDBProperties.getPassword());
        return dataSource;
    }

	@Primary
	@Bean(value = "KettleTransactionManager")
	public PlatformTransactionManager kettleTransactionManager() {
		JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(kettleEntityManager().getObject());
		return transactionManager;
	}
}
