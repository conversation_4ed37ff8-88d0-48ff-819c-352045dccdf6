package com.stpl.tech.kettle.core.config;

import com.stpl.tech.kettle.core.properties.HibernateConfigProperties;
import com.stpl.tech.kettle.core.properties.MasterDBProperties;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableJpaRepositories(basePackages = {
		"com.stpl.tech.kettle.repository.master" }, entityManagerFactoryRef = "MasterEntityManager", transactionManagerRef = "MasterTransactionManager")
public class MasterDbConfig {

	@Autowired
	private MasterDBProperties masterDBProperties;

	@Autowired
	private HibernateConfigProperties hibernateConfigProperties;

	@Bean("MasterEntityManager")
	@Primary
	public LocalContainerEntityManagerFactoryBean masterEntityManager() {
		LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(masterDatasource());
		em.setPackagesToScan("com.stpl.tech.kettle.data.master");
		em.setPersistenceUnitName("MasterEntityManager");
		HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		Map<String, Object> properties = new HashMap<>();
		properties.put("hibernate.dialect", hibernateConfigProperties.getDialect());
		properties.put("hibernate.show-sql", hibernateConfigProperties.getShowSql());
		properties.put("hibernate.hbm2ddl.auto", hibernateConfigProperties.getHbmToDdlAuto());
		em.setJpaPropertyMap(properties);
		return em;
	}

//	@Primary
//	@Bean(name = "MasterDataSource")
//	public DataSource masterDatasource() {
//		HikariDataSource dataSource =  masterDBProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
//		dataSource.setMaximumPoolSize(masterDBProperties.getHikariPoolSize());
//		return dataSource;
//	}

    @Primary
    @Bean
    public DataSource masterDatasource() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName(masterDBProperties.getDriverClassName());
        dataSource.setUrl(masterDBProperties.getUrl());
        dataSource.setUsername(masterDBProperties.getUsername());
        dataSource.setPassword(masterDBProperties.getPassword());
        return dataSource;
    }

	@Primary
	@Bean(value = "MasterTransactionManager")
	public PlatformTransactionManager masterTransactionManager() {
		JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(masterEntityManager().getObject());
		return transactionManager;
	}
}
