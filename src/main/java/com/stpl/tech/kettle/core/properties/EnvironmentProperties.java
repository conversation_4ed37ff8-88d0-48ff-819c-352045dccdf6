package com.stpl.tech.kettle.core.properties;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.util.EnvType;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;

@Configuration
@ConfigurationProperties("env")
@Getter
@Setter
@Log4j2
@RefreshScope
public class EnvironmentProperties {

	@Autowired
	private Environment env;

	public Integer getEmployeeMealMonthlyStartDate() {
		return Integer.valueOf(env.getProperty("employee.meal.monthly.start.date"));
	}

	public Integer getEmployeeMealDayLimit() {
		return Integer.valueOf(env.getProperty("employee.meal.day.limit"));
	}

	public BigDecimal getEmployeeMealPerDayAmountLimit() {
		return new BigDecimal(env.getProperty("employee.meal.amount.limit"));
	}

	public Date getCashBackStartDate() {
		String cashBackStartDate = env.getProperty("cash.back.start.date");
		return AppUtils.getDate(cashBackStartDate, "yyyy-MM-dd");
	}

	public Date getCashBackEndDate() {
		String cashBackEndDate = env.getProperty("cash.back.end.date");
		return AppUtils.getDate(cashBackEndDate, "yyyy-MM-dd");
	}

	public BigDecimal getCashBackPercentage() {
		String cashBackPercentage = env.getProperty("cash.back.percentage");
		return new BigDecimal(cashBackPercentage);
	}

	public EnvType getEnvironmentType() {
		return EnvType.valueOf(env.getProperty("environment.type"));
	}

	public int getDummyCustomerId() {
		return env.getProperty("mail.dummy.customer.id", Integer.class);
	}

	public String verifyPriceDataURL() {
		return env.getProperty("verify.price.data");
	}

	public String getBasePath() {
		return env.getProperty("server.base.dir");
	}

	public String getWalletDecisionDroolPath(){
		return env.getProperty("wallet.decision.path","file:/data/app/kettle/sprod/drools/wallet_decision/wallet_decision.xls");
	}

	public String getDenominationPercentageDroolPath(){
		return env.getProperty("denomination.decision.path","file:/data/app/kettle/sprod/drools/denomination_decision/denomination_decision.xls");
	}
	public Boolean isRevalidationOrderActive(){
		return Boolean.valueOf(env.getProperty("revalidation.order.active","false"));
	}

	public List<Integer> revalidationOrderPartnerIds(){
		String ids = env.getProperty("revalidation.order.partner.ids","");
		if(StringUtils.isEmpty(ids))
		{
			return new ArrayList<>();
		}
		return Arrays.asList(ids.split(",")).stream()
				.map(s -> Integer.parseInt(s))
				.collect(Collectors.toList());
	}

	public boolean getTrackInventory() {
		return Boolean.valueOf(env.getProperty("inventory.track"));
	}

	public String getInventoryServiceBasePath() {
		return env.getProperty("inventory.base.url");
	}

	public String getKettleCreateorderUrl() {
		return env.getProperty("kettle.create.order.url");
	}

	public String getKettleAuthInternal() {
		return env.getProperty("kettle.auth.internal");
	}

	public String getUndeliveredEmail() {
		return env.getProperty("mail.undelivered.email");
	}

	public String getChaayosBaseUrl() {
		return env.getProperty("chaayos.base.url");
	}

	public boolean getRawPrintingSatus() {
		return Boolean.valueOf(env.getProperty("raw.print.enabled"));
	}

	public boolean getRunValidateFilter() {
		return Boolean.valueOf(env.getProperty("run.validate.filter"));
	}

	public String[] getSecuredUrls() {
		return env.getProperty("interceptor.secure.url").split(",");
	}

	public String[] getOpenUrls() {
		return env.getProperty("interceptor.open.url").split(",");
	}

	public String addWastageURL() {
		return env.getProperty("add.wastage.url");
	}

	public Map<Integer,Boolean> publishOrders() {
		Map<Integer,Boolean> orderPublishMap = new HashMap<>();
		orderPublishMap.put(AppConstants.CHAAYOS_BRAND_ID,Boolean.valueOf(env.getProperty("sqs.publish.queue.order")));
		orderPublishMap.put(AppConstants.GNT_BRAND_ID,Boolean.valueOf(env.getProperty("sqs.publish.queue.order")));
		orderPublishMap.put(AppConstants.SWIGGY_CAFE_BRAND_ID,Boolean.valueOf(env.getProperty("sqs.publish.queue.order")));
		orderPublishMap.put(AppConstants.DOHFUL_BRAND_ID,Boolean.valueOf(env.getProperty("sqs.publish.queue.dohful.order","false")));
		return orderPublishMap;
	}

	public int getMicroWalletId() {
		return Integer.valueOf(env.getProperty("micro.wallet.id"));
	}

	public int getDirectWalletId() {
		return Integer.valueOf(env.getProperty("direct.wallet.id"));
	}

	public String getOrderFeedbackType() {
		return env.getProperty("order.feedback.type");
	}

	public Integer getSubscriptionValidBuyingDay() {
		return Integer.valueOf(env.getProperty("subscription.valid.buy.n.days.value"));
	}

	public boolean getNPSConsiderationForDelivery() {
		return Boolean.valueOf(env.getProperty("send.automated.delivery.nps.sms"));
	}

	public boolean getSendFeedbackMessageForSwiggyDelivery() {
		return Boolean.parseBoolean(env.getProperty("send.feedback.message.delivery.swiggy"));
	}

	public boolean isDinePostOrderOfferEnabled() {
		return Boolean.valueOf(env.getProperty("dinein.post.order.offer.enabled"));
	}

	public boolean isDeliveryPostOrderOfferEnabled() {
		return Boolean.valueOf(env.getProperty("delivery.post.order.offer.enabled"));
	}

	public Integer getDinePostOrderOfferCheckLastNDaysValue() {
		return Integer.valueOf(env.getProperty("dine.post.order.offer.check.last.n.days.value"));
	}

	public Date getCashBackCardStartDate() {
		String cashBackCardStartDate = env.getProperty("cash.back.card.start.date");
		return AppUtils.getDate(cashBackCardStartDate, "yyyy-MM-dd");
	}

	public Date getCashBackCardEndDate() {
		String cashBackCardEndDate = env.getProperty("cash.back.card.end.date");
		return AppUtils.getDate(cashBackCardEndDate, "yyyy-MM-dd");
	}

	public Integer getCashCardPaymentModeId() {
		return Integer.valueOf(env.getProperty("cash.card.payment.mode.id"));
	}

	public boolean getCleverTapEnabled() {
		return Boolean.valueOf(env.getProperty("clevertap.push.enable"));
	}

	public boolean getFacebookPushEnabled() {
		return Boolean.valueOf(env.getProperty("facebook.push.enable"));
	}

	public boolean getSendFeedbackMessageForCODOrders() {
		return Boolean.parseBoolean(env.getProperty("send.cod.order.feedback.message"));
	}

	public boolean getSystemGeneratedNotifications() {
		return Boolean.valueOf(env.getProperty("system.generated.notifications"));
	}

	public boolean getAutomatedNPSSMS() {
		return Boolean.valueOf(env.getProperty("send.automated.nps.sms"));
	}

	public String getRecieptEmail() {
		return env.getProperty("mail.receipt.email");
	}

	public String getBillPromotion() {
		try {
			if (Boolean.valueOf(env.getProperty("promotional.offer.active"))) {
				String startDateStr = env.getProperty("promotional.offer.start.date");
				String endDateStr = env.getProperty("promotional.offer.end.date");
				String offerHtml = env.getProperty("promotional.offer.html.text");
				Date startDate = AppUtils.getDate(startDateStr, "yyyy-MM-dd");
				Date endDate = AppUtils.getDate(endDateStr, "yyyy-MM-dd");
				Date currentDate = AppUtils.getCurrentDate();
				if (currentDate.compareTo(startDate) >= 0 && currentDate.compareTo(endDate) <= 0) {
					return offerHtml;
				}
			}
		} catch (Exception e) {
			log.error("Error while fetching the promotional offer", e);
		}
		return null;
	}

	public String getS3Bucket() {
		return env.getProperty("amazon.s3.bucket");
	}

	public String getOrderReceipt() {
		return env.getProperty("order.receipt.cloudfront");
	}

	public Boolean getIsShowNpsRating() {
		return Boolean.valueOf(env.getProperty("is.show.nps.rating"));
	}

	public int getThresholdFeedbackMessageDelay() {
		return Integer.valueOf(env.getProperty("automated.feedback.sms.threshold.time.gap"));
	}

	public int getDeliveryNPSMessageDelay() {
		return Integer.valueOf(env.getProperty("automated.nps.sms.delivery.time.gap"));
	}

	public int getDineInNPSMessageDelay() {
		return Integer.valueOf(env.getProperty("automated.nps.sms.dinein.time.gap"));
	}

	public int getDeliveryFeedbackMessageDelay() {
		return Integer.valueOf(env.getProperty("automated.feedback.sms.delivery.time.gap"));
	}

	public int getDineInFeedbackMessageDelay() {
		return Integer.valueOf(env.getProperty("automated.feedback.sms.dinein.time.gap"));
	}

	public int getNextDayFeedbackMessageDelay() {
		return Integer.valueOf(env.getProperty("automated.feedback.sms.next.day.time.gap"));
	}

	public boolean getSendAutomatedOTPSMS() {
		return Boolean.valueOf(env.getProperty("send.automated.otp.sms"));
	}

	public String getOrderInfoQueueRegion() {
		return String.valueOf(env.getProperty("order.info.queue.region"));
	}

	public boolean getisDineWhatsappNotificationFlag() {
		return Boolean.valueOf(env.getProperty("dinein.order.notification.whatsapp"));
	}

	public boolean getIsSendSmsForCampaignBySystem() {
		return Boolean.valueOf(env.getProperty("send.campaign.sms.by.system"));
	}

	public boolean sendFeedBackSMSFromClevertap() {
		return Boolean.valueOf(env.getProperty("send.feedback.sms.from.clevertap"));
	}
	
	public boolean clevertapLogEnable() {
		return Boolean.valueOf(env.getProperty("clevertap.log.enable","false"));
	}
	
	public int defaultLoyaltyThreshold() {
		return Integer.valueOf(env.getProperty("default.loyalty.threshold","79"));
	}
	public String getLoyaltWalletOfferCode(){
		return env.getProperty("loyalty.wallet.offer.code","WALLETLOYALTY");
	}

	public String getDroolBasePath() {
		return env.getProperty("server.drool.base.dir");
	}

	public String getS3BucketForDrools() {
		return env.getProperty("offer.drools.s3.bucket", "com.chaayos.drool.dev");
	}


	public String getChaayosCashbackCouponCode(){
		return env.getProperty("chaayos.cashback.coupon.code","CHAAYOS_CASH");
	}

	public String getCashBackCouponCodePrefix(){
		return env.getProperty("cashback.coupon.code.prefix","CCFLAT");
	}

	public Integer getMinWalletAmountForOfferHighlight() {
		return Integer.valueOf(env.getProperty("offer.highlight.min.wallet.amt","20"));
	}
	public String getCashBackCouponCodeForNewCustomer(){
		return env.getProperty("cashback.coupon.code.new.customer","");
	}

	public int getValidityForCashBackCoupon(){
		return Integer.valueOf(env.getProperty("cashback.coupon.code.validity","45"));
	}

	public String getCashBackCouponCodeForNewCustomerPrefix(){
		return env.getProperty("cashback.coupon.code.new.customer.prefix","CCNEW");
	}

	public int getExpireSignupOfferDays() {
		return Integer.valueOf(env.getProperty("expire.signup.offer.days", "60"));
	}

	public List<String> getValidUnitList(){
		List<String> list = Arrays.asList(env.getProperty("valid.city.list.cashback","").split(","));
		return list;
	}

	public boolean getLoyaltyFlagForCashBack(){
		return Boolean.valueOf(env.getProperty("loyalty.flag.for.cashback", "true"));
	}

	public String getFeedBackHeader(){
		String defaultHeader = "Scan QR and rate us";
		String header = env.getProperty("feedback.header","");
		if(!StringUtils.isEmpty(header)){
			try{
				defaultHeader = header.replace('_',' ');
			}catch (Exception e){
				log.info("Error in parsing feedback header");
			}
		}
		return defaultHeader;
	}

	public Integer getDaysAfterLoyaltyExpire(){
		return Integer.valueOf(env.getProperty("days.after.loyalty.expire","365"));
	}
	public String getDateToCheckNewCustomer(){
		return (env.getProperty("date.to.check.new.customer","2024-10-17"));
	}

	public String getAssemblyFirestoreUnits() {
		return env.getProperty("units.for.order.delivery.through.fire.store", "");
	}

	public Boolean isAssemblyFirestoreEnabledForAll() {
		return Boolean.valueOf(env.getProperty("assembly.firestore.enabled.all", "false"));
	}
	public Integer getTokenMaxLimit(){
		return Integer.valueOf(env.getProperty("token.max.limit","100"));
	}

    public boolean isPriortizationOfOrdersEnabled() {
        return Boolean.valueOf(env.getProperty("is.priortization.mode.enabled", "true"));
    }
	public String getAppOrderStatusQueueRegion() {
		return env.getProperty("aws.sqs.dine.in.app.status.queue.region", "ap-south-1");
	}
    
    public Boolean allowDohfulCash(){
		return Boolean.valueOf(env.getProperty("allow.dohful.cash","false"));
	}
	
	public String getGstTextDescription() {
		return env.getProperty("gst.text.description", "GST is applicable as per government regulations. For any queries regarding GST, please contact our customer support.");
	}
	
	public Boolean getShowGstText() {
		return Boolean.valueOf(env.getProperty("gst.text.show", "true"));
	}
}
