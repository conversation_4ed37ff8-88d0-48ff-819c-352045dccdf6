package com.stpl.tech.kettle.core.properties;

import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DBProperties extends DataSourceProperties {

	// master.jdbc.driver-class
	private String driverClassName;
	private String url;
	private String userName;
	private String password;

}
