package com.stpl.tech.kettle.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3Client;

@Configuration
public class AWSConfig {

	@Value("${aws.s3.accessKey}")
	private String accessKey;

	@Value("${aws.s3.secretKey}")
	private String secretKey;

	@Bean
	public AmazonS3Client amazonS3Client() {
		/*
		 * AmazonS3Client client = new AmazonS3Client( new
		 * BasicAWSCredentials("********************",
		 * "muUI7le4c2BY7uby0YIVfX3tGffobVAe+5jPrvmS"));
		 */
		AmazonS3Client client = new AmazonS3Client(new BasicAWSCredentials(accessKey, secretKey));

		// client.setRegion(Region.getRegion(Regions.EU_WEST_1));
		return client;
	}
}
