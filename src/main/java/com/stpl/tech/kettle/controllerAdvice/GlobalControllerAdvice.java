package com.stpl.tech.kettle.controllerAdvice;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import com.stpl.tech.kettle.domain.ApiResponse;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.kettle.exceptions.DataNotFoundInHazelCastException;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.exceptions.GlobalException;

import lombok.extern.log4j.Log4j2;

@RestControllerAdvice
@Log4j2
public class GlobalControllerAdvice extends ResponseEntityExceptionHandler {

    @ExceptionHandler(GlobalException.class)
    public ResponseEntity<ApiResponse> customExceptionHandler(GlobalException e){
        log.error("{}",e.getMessage(),e.getE());
        return new ResponseEntity<>(
                new ApiResponse(e.getMessage(),null,HttpStatus.INTERNAL_SERVER_ERROR.value()
                        ,HttpStatus.INTERNAL_SERVER_ERROR), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(DataNotFoundInHazelCastException.class)
    public ResponseEntity<ApiResponse> customExceptionHandler(DataNotFoundInHazelCastException e){
        log.error("{}",e.getMessage(),e);
        return new ResponseEntity<>(
                new ApiResponse(e.getMessage(),null,HttpStatus.NOT_FOUND.value()
                ,HttpStatus.NOT_FOUND), HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(DataUpdationException.class)
    public ResponseEntity<ApiResponse> customExceptionHandler(DataUpdationException e){
        log.error("{}",e.getMessage(),e);
        return new ResponseEntity<>(
                new ApiResponse(e.getMessage(),null,HttpStatus.NOT_MODIFIED.value()
                        ,HttpStatus.NOT_MODIFIED), HttpStatus.NOT_MODIFIED);
    }
    
	@ExceptionHandler(AuthenticationFailureException.class)
	public ResponseEntity<ApiResponse> customExceptionHandler(AuthenticationFailureException e) {
		log.info("{}", e.getMessage(), e);
		return new ResponseEntity<>(
				new ApiResponse(e.getMessage(), null, HttpStatus.UNAUTHORIZED.value(), HttpStatus.UNAUTHORIZED),
				HttpStatus.UNAUTHORIZED);
	}
	
	@ExceptionHandler(Exception.class)
	public ResponseEntity<ApiResponse> customExceptionHandler(Exception e) {
		log.info("{}", e.getMessage(), e);
		return new ResponseEntity<>(
				new ApiResponse(e.getMessage(), null, HttpStatus.INTERNAL_SERVER_ERROR.value(), HttpStatus.INTERNAL_SERVER_ERROR),
				HttpStatus.INTERNAL_SERVER_ERROR);
	}
}
