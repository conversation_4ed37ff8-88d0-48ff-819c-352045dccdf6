package com.stpl.tech.kettle.controller;

import com.google.gson.Gson;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.TableOrderItemStatusRequest;
import com.stpl.tech.kettle.domain.model.OrderDomain;
import com.stpl.tech.kettle.domain.model.TableResponse;
import com.stpl.tech.kettle.domain.model.TableSettlement;
import com.stpl.tech.kettle.domain.model.TableViewOrder;
import com.stpl.tech.kettle.domain.model.UnitOrderRequest;
import com.stpl.tech.kettle.domain.model.UnitTableMapping;
import com.stpl.tech.kettle.exceptions.CardValidationException;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.exceptions.TemplateRenderingException;
import com.stpl.tech.kettle.service.OrderManagementService;
import com.stpl.tech.kettle.service.TableOrderManagementService;
import com.stpl.tech.kettle.service.impl.OrderManagementServiceImpl;
import com.stpl.tech.kettle.util.ApiConstants.CustomerServiceConstants;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.util.adapter.JSONSerializer;
import com.stpl.tech.util.PrintType;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.jms.JMSException;
import javax.ws.rs.GET;
import javax.ws.rs.core.MediaType;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;

@RestController
@RequestMapping(value = CustomerServiceConstants.API_VERSION + CustomerServiceConstants.SEPARATOR
        + CustomerServiceConstants.TABLE_SERVICES_ROOT_CONTEXT)
@Log4j2
public class TableOrderingResource {

    @Autowired
    private TableOrderManagementService tableDataService;

    @Autowired
    private OrderManagementService orderManagementService;

    @Autowired
    @Qualifier("taskExecutor")
    private Executor taskExecutor;

    @Autowired
    private OrderManagementServiceImpl orderManagementServiceImpl;

    @RequestMapping(method = RequestMethod.GET, value = "unit-table-status", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<UnitTableMapping> getTablesForUnit(@RequestParam final int unitId) {
        log.debug("Request to get tables for unitId {}", unitId);
        return tableDataService.getTablesForUnit(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "reserve-table", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitTableMapping reserveTableForUnit(@RequestParam final int unitId, @RequestParam final int tableNumber , @RequestParam(required = false) final Integer noOfPax)
            throws DataNotFoundException {
        log.debug("Request to reserver table for unitId {} and tableNumber {} and No Of Pax : {} ", unitId, tableNumber,noOfPax);
        return tableDataService.reserveTableForUnit(unitId, tableNumber,noOfPax);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "update-pax", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitTableMapping updatePaxForTableOrder(@RequestParam final int tableRequestId , @RequestParam(required = false) final Integer noOfPax)
            throws DataNotFoundException, DataUpdationException {
        log.debug("Request to update pax number  for  table Request Id {} and No Of Pax : {} ", tableRequestId,noOfPax);
        return tableDataService.updatePaxForTableOrder(tableRequestId,noOfPax);
    }

    @RequestMapping(method = RequestMethod.GET, value = "close-table", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean closeTableForUnit(@RequestParam final int tableRequestId) throws DataNotFoundException {
        log.debug("Request to close table for table request Id {}", tableRequestId);
        return tableDataService.closeTableForUnit(tableRequestId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit-table-summary", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitTableMapping getTableSummaryForUnit(@RequestParam final int tableRequestId) {
        log.info("Request to get table Summary for request Id {}", tableRequestId);
        return tableDataService.getTableSummary(tableRequestId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "change-table", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitTableMapping changeTable(@RequestParam final int tableRequestId, @RequestParam final int tableNumber)
            throws DataNotFoundException, DataUpdationException {
        log.info("Request to change table for tableRequestId {} and tableNumber {}", tableRequestId, tableNumber);
        return tableDataService.changeTable(tableRequestId, tableNumber);
    }

    @RequestMapping(method = RequestMethod.POST, value = "table-checkout", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public TableResponse tableCheckout(@RequestBody final TableSettlement settlement) throws Exception {
        log.info("Request to table-checkout for table request Id : {} and Json is : {}",
                settlement.getTableRequestId(),JSONSerializer.toJSON(settlement));
        if(!tableDataService.validateOrderItems(settlement.getOrderInfo().getOrder())){
            throw new Exception("Mismatch in order Items");
        }
        if(tableDataService.isTableAlreadySettled(settlement.getTableRequestId())){
            throw new DataUpdationException("Table Already Closed");
        }
        List<Integer> orderIds = tableDataService.getTableOrders(settlement.getTableRequestId());
        if (!CollectionUtils.isEmpty(settlement.getTableOrderIds()) &&
                orderIds.size() != settlement.getTableOrderIds().size()) {
            throw new Exception("Transaction Mismatch");
        }

        TableResponse t = new TableResponse();
        t.setPrintType(PrintType.RAW);
        t.setTableNumber(settlement.getOrderInfo().getOrder().getTableNumber());
        if(!CollectionUtils.isEmpty(orderIds)) {
            TableViewOrder tableViewOrder = new TableViewOrder();
            tableViewOrder.setOrder(settlement.getOrderInfo().getOrder());
            tableViewOrder.setTableOrderIds(orderIds);
            tableViewOrder = orderManagementService.createTableOrder(tableViewOrder);
        t.setOrderInfo(tableViewOrder.getOrderInfo());
            try {
                orderManagementService.setFeedbackUrl(tableViewOrder,t);
            }catch (Exception e){
                log.info("Error in updateing feedback url in order info : {}",e.getMessage());
            }
            OrderInfo orderInfo = orderManagementService.generateReciept(tableViewOrder.getOrderInfo());
            if (Objects.nonNull(orderInfo) && !CollectionUtils.isEmpty(orderInfo.getReceipts())) {
                t.setSettlementReceipt(orderInfo.getReceipts().get(0));
            }
            log.info("Pushing data to cleverTap");
            try {
                tableViewOrder.getOrderInfo().setOrderNotificationMap(new HashMap<>());
                tableViewOrder.getOrderInfo().getOrderNotificationMap().put(tableViewOrder.getOrder().getOrderId(),
                        tableViewOrder.getOrderInfo().getOrderNotification());
                orderManagementService.pushDataToThirdPartyAnalytics(tableViewOrder.getOrderInfo(), tableViewOrder.getOrderInfo().getOrderNotificationMap());
            orderManagementServiceImpl.publishCafeOrderStatus(tableViewOrder.getOrder().getOrderId(), tableViewOrder.getOrder().getCustomerId(), tableViewOrder.getOrder().getChannelPartner(), OrderStatus.SETTLED);} catch (Exception e) {
                log.info("Error in pushing data to cleverTap : {}", e);
            }
        }
        return t;
    }

    @GetMapping(value = "consolidate-order")
    public TableViewOrder consolidateOrder(@RequestParam final int tableRequestId) {
        log.info("Call to consolidate table orders with table request id : {}", tableRequestId);
        return tableDataService.consolidateOrders(tableRequestId);
    }

    @PostMapping(value = "get-table-order-receipt")
    public TableResponse getTableOrderRecipt(@RequestBody final TableSettlement settlement) throws Exception {
        log.info("Request to fetch table order receipt for table request id : {}",settlement.getOrderInfo().getOrder().getTableRequestId());
        if(!tableDataService.validateOrderItems(settlement.getOrderInfo().getOrder())){
            throw new Exception("No of order Items mismatch");
        }
        if(!tableDataService.validateBillPrint(settlement.getOrderInfo(),settlement.getServiceChargeApplied())){
            throw new Exception("Transaction Mismatch");
        }
        if(Objects.nonNull(settlement.getOrderInfo()) && Objects.nonNull(settlement.getOrderInfo().getOrder()) && Objects.nonNull(settlement.getOrderInfo().getOrder().getDreamFolksVoucherDetails()) &&
                !settlement.getOrderInfo().getOrder().getDreamFolksVoucherDetails().isSkipReuseCheck() &&  tableDataService.isDreamFolksVoucherCodeUsed(settlement.getOrderInfo().getOrder().getDreamFolksVoucherDetails().getVoucherCode())){
            throw new CardValidationException("DreamFolks Voucher Code is already used");
        }
        return orderManagementService.getTableOrderRecipt(settlement.getOrderInfo(),settlement.getServiceChargeApplied());
    }

    @PostMapping(value = "settle-table")
    public TableResponse settleTablePayment(@RequestBody final TableSettlement settlement) throws DataUpdationException, TemplateRenderingException {
        log.info("Request to settle-table for table request Id :{} and Json is : {}",
                settlement.getTableRequestId(),JSONSerializer.toJSON(settlement));
        orderManagementService.tableCheckoutNew(settlement);
        TableResponse t = tableDataService.generateSettlementReceipt(settlement.getTableRequestId());
        List<Integer> orderIds = tableDataService.getTableOrders(settlement.getTableRequestId());
        // orders are published here after setlements are completed
        orderManagementService.publishAllOrders(orderIds);
        return t;
    }

    @PostMapping(value = "save-order-item-statuses")
    public Boolean saveOrderItemStatuses(@RequestBody final TableOrderItemStatusRequest tableOrderItemStatusRequest) throws DataUpdationException, TemplateRenderingException {
        log.info("Request to save order item statuses for table request Id {} , json : {} ",
                tableOrderItemStatusRequest.getTableRequestId(), new Gson().toJson(tableOrderItemStatusRequest));
        try {
         return tableDataService.saveTableOrderItemStatuses(tableOrderItemStatusRequest);
        }catch (Exception e){
           log.error("Error While Saving Order Item Statuses ::::: {} ",e);
        }
        return false;
    }

    @PostMapping(value = "deduct-wallet-amount-for-orders")
    public Boolean deductWalletAmountForOrders(@RequestBody List<Integer> orderIds) {
        log.info(" Request to deduct wallet amount for order {}", Arrays.toString(orderIds.toArray()));
        try {
            return orderManagementService.deductWalletAmountForOrders(orderIds);
        }catch (Exception e){
            log.error("Error While deductWalletAmountForOrders ::::: ",e);
        }
        return false;
    }

    @PostMapping(value = "process-on-hold-items")
    public boolean processOnHoldItems(@RequestBody List<Integer> orderItemsIds){
        log.info("Request to process on hold Items : {}",Arrays.toString(orderItemsIds.toArray()));
        try {
            return tableDataService.processItemOnHold(orderItemsIds);
        }catch (Exception e){
            log.info("Error in processing on hold items :::: {}",e);
        }
        return false;
    }

    @PostMapping(value = "update-service-charge-applicable-flag")
    public boolean setServiceChargeApplicable(@RequestParam final int tableRequestId,@RequestParam final String flag){
        return tableDataService.setServiceChargeApplicable(tableRequestId,flag);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-order-list", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<Order> getCafeOrdersForUnit(@RequestBody UnitOrderRequest request) {
        log.debug("Request to get tables for unitId {}", request.getUnitId());
        return tableDataService.getCafeOrdersForUnit(request);
    }


}


