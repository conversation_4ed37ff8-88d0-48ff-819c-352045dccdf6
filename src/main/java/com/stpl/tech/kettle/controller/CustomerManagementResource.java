package com.stpl.tech.kettle.controller;

import com.stpl.tech.kettle.domain.ApiResponse;
import com.stpl.tech.kettle.domain.BaseHeaders;
import com.stpl.tech.kettle.domain.model.SavedChaiOrderedDomain;
import com.stpl.tech.kettle.domain.model.WalletRecommendationDetail;
import com.stpl.tech.kettle.domain.model.WalletSuggestionCustomerInfo;
import com.stpl.tech.kettle.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.service.CustomerService;
import com.stpl.tech.kettle.service.TokenService;
import com.stpl.tech.kettle.util.ApiConstants.CustomerServiceConstants;
import com.stpl.tech.kettle.util.AppUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = CustomerServiceConstants.API_VERSION + CustomerServiceConstants.SEPARATOR
        + CustomerServiceConstants.CUSTOMER_SERVICES_ROOT_CONTEXT)
@Log4j2
public class CustomerManagementResource {

    @Autowired
    private CustomerOfferManagementService customerOfferManagementService;

    @Autowired
    private TokenService tokenService;

    @GetMapping(value = "denomination-offer")
    public ResponseEntity<ApiResponse> getDenominationOfferData(HttpServletRequest httpRequest){
        BaseHeaders baseHeaders = AppUtils.getBaseHeaders(httpRequest);
        return ResponseEntity.ok(new ApiResponse(customerOfferManagementService.getDenominationOffer(baseHeaders.getUnitId())));
    }

    @GetMapping(value = "direct-wallet-offers")
    public ResponseEntity<ApiResponse> getDirectWalletOffers(HttpServletRequest httpRequest){
        BaseHeaders baseHeaders = AppUtils.getBaseHeaders(httpRequest);
        return ResponseEntity.ok(new ApiResponse(customerOfferManagementService.getDirectWalletOffers(baseHeaders.getUnitId())));
    }


    @PostMapping("suggest-wallet-extra-amount")
    public WalletRecommendationDetail getSuggestWalletExtraAmount(HttpServletRequest request, @RequestBody WalletSuggestionCustomerInfo customerData) throws Exception{
        Integer unitId = tokenService.getLoggedInUnit(request);
        return customerOfferManagementService.getSuggestWalletOfferExtraAmount(customerData,unitId);
    }

    @GetMapping("refresh-denomination-offer-value")
    public void refreshDenominationOfferValue(){
        customerOfferManagementService.refreshDenominationOfferValue();
    }

    @GetMapping(value = "get-last-ordered-saved-chai")
    public Integer getLastOrderedSavedChai(@RequestParam Integer customerId){
        try{
            log.info("Getting last saved chai ordered by customer with id :::::::::{}",customerId);
            return customerOfferManagementService.getLastOrderedSavedChai(customerId);
        }catch(Exception e ){
            log.error("Exception while getting saved chai data:::::::", e);
            return null ;
        }
    }

    @GetMapping(value = "get-saved-chai-ordered-detail")
    public SavedChaiOrderedDomain getSavedChaiOrderDetail(@RequestParam Integer customerId){
        try{
            log.info("Getting last saved chai detail ordered by customer with id :::::::::{}",customerId);
            return customerOfferManagementService.getSavedChaiOrderDetail(customerId);
        }catch(Exception e ){
            log.error("Exception while getting saved chai detail:::::::", e);
            return null ;
        }
    }

    @GetMapping("refresh-unit-denomination-offer-value")
    public void refreshUnitDenominationOfferValue(@RequestParam Integer unitId){
        customerOfferManagementService.refreshUnitDenominationOfferValue(unitId);
    }

}
