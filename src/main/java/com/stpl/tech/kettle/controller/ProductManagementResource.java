package com.stpl.tech.kettle.controller;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.domain.ApiResponse;
import com.stpl.tech.kettle.util.ApiConstants.CustomerServiceConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = CustomerServiceConstants.API_VERSION + CustomerServiceConstants.SEPARATOR
        + CustomerServiceConstants.PRODUCT_SERVICES_ROOT_CONTEXT)
@Log4j2
public class ProductManagementResource {

    @Autowired
    private ProductCache productCache;

    @GetMapping(value = "get-all-subscription-product")
    public ResponseEntity<ApiResponse> getAllSubscriptionProducts() {
        return ResponseEntity.ok(new ApiResponse(productCache.getSubscriptionProductDetails()));
    }
}
