package com.stpl.tech.kettle.controller;

import com.stpl.tech.kettle.domain.model.*;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.cache.UnitSessionCache;
import com.stpl.tech.kettle.converter.OrderConverter;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.domain.ApiResponse;
import com.stpl.tech.kettle.domain.BaseHeaders;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDomain;
import com.stpl.tech.kettle.domain.model.OrderMetricDomain;
import com.stpl.tech.kettle.domain.model.OrderUnitMapping;
import com.stpl.tech.kettle.domain.model.UnitTerminalDetail;
import com.stpl.tech.kettle.domain.model.WalletOrderType;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.kettle.exceptions.CardValidationException;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.kettle.exceptions.TemplateRenderingException;
import com.stpl.tech.kettle.exceptions.WebServiceCallException;
import com.stpl.tech.kettle.service.LoyaltyService;
import com.stpl.tech.kettle.service.OrderManagementService;
import com.stpl.tech.kettle.util.ApiConstants.CustomerServiceConstants;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.util.adapter.JSONSerializer;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.domain.model.UserSessionDetail;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.jms.JMSException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Objects;

@RestController
@RequestMapping(value = CustomerServiceConstants.API_VERSION + CustomerServiceConstants.SEPARATOR
		+ CustomerServiceConstants.ORDER_SERVICES_ROOT_CONTEXT)
@Log4j2
public class OrderManagementResource {

	@Autowired
	private OrderManagementService orderManagementService;

	@Autowired
	private UnitSessionCache unitSessionCache;

	@Autowired
	private UnitCacheService unitCacheService;

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private OrderConverter orderConverter;

	@Autowired
	private LoyaltyService loyaltyService;

	@PostMapping(value = "create-order")
	public ResponseEntity<ApiResponse> createOrder(@RequestBody OrderDomain orderDomain)
			throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException,
			DataNotFoundException, DataIntegrityViolationException, CardValidationException, IOException,
			WebServiceCallException, JMSException, OfferValidationException {
		long time = System.currentTimeMillis();
		OrderInfo info = orderManagementService.processOrder(orderDomain);
		orderManagementService.publishToOrderInfoCacheSQS(properties.getEnvironmentType().name(), info);
		if(!TransactionUtils.isTableOrder(orderDomain.getOrder())) {
			orderManagementService.pushDataToThirdPartyAnalytics(info, info.getOrderNotificationMap());
		}
		log.info("create-order for {} took {} millisecond ", info.getOrder().getGenerateOrderId(),
				System.currentTimeMillis() - time);
		return ResponseEntity.ok(new ApiResponse(info));
	}

	@PostMapping(value = "create-loyalty-wallet-order")
	public ApiResponse createLoyaltyWalletOrder(@RequestParam String contactNumber,@RequestParam Integer loyaltyPoints,@RequestParam Integer walletAmount,@RequestParam Integer extraAmount) throws JMSException {
		long time = System.currentTimeMillis();
		OrderInfo info = orderManagementService.processLoyaltyWalletOrder(contactNumber,loyaltyPoints,walletAmount,extraAmount);
		if(Objects.nonNull(info)) {
			orderManagementService.publishToOrderInfoCacheSQS(properties.getEnvironmentType().name(), info);
			orderManagementService.pushDataToThirdPartyAnalytics(info, info.getOrderNotificationMap());
			log.info("create-order for {} took {} millisecond ", info.getOrder().getGenerateOrderId(),
					System.currentTimeMillis() - time);
			return new ApiResponse(info);
		}
		return new ApiResponse("",null, HttpStatus.INTERNAL_SERVER_ERROR.value(),HttpStatus.INTERNAL_SERVER_ERROR);
	}

	@PostMapping(value = "session")
	public ResponseEntity<ApiResponse> OrderSession(@RequestBody UserSessionDetail userSession)
			throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException,
			DataNotFoundException, DataIntegrityViolationException, CardValidationException, IOException,
			WebServiceCallException {
		return ResponseEntity.ok(new ApiResponse(
				unitSessionCache.get(new UnitTerminalDetail(userSession.getUnitId(), userSession.getTerminalId()))));
	}

	@PostMapping(value = "create-wallet-order")
	public ResponseEntity<ApiResponse> createWalletOrder(@RequestBody OrderDomain orderDomain)
			throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException,
			DataNotFoundException, DataIntegrityViolationException, CardValidationException, IOException,
			WebServiceCallException, JMSException, OfferValidationException {
		String randomString = orderDomain.getOrder().getGenerateOrderId();
		OrderUnitMapping orderUnitMapping = new OrderUnitMapping();
		Order order = orderManagementService.validateOrder(orderDomain, orderUnitMapping);
		if (orderManagementService.validateWalletOrder(orderDomain.getWalletOrder(), order)) {
			OrderInfo info = orderManagementService.createWalletOrder(order, orderDomain.getWalletOrder(), orderDomain,
					randomString, orderUnitMapping);
			if (!CollectionUtils.isEmpty(order.getOrders())) {
				//Micro wallet order
				orderManagementService.publishToOrderInfoCacheSQS(properties.getEnvironmentType().name(), info);
			}
			orderManagementService.pushDataToThirdPartyAnalytics(info, info.getOrderNotificationMap());
			return ResponseEntity.ok(new ApiResponse(info));
		} else {
			throw new OfferValidationException("Invalid wallet  order for customer");
		}
	}

	@PostMapping(value = "create-subscription-order")
	public ResponseEntity<ApiResponse> createSubscriptionOrder(@RequestBody OrderDomain orderDomain)
			throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException,
			DataNotFoundException, DataIntegrityViolationException, CardValidationException, IOException,
			WebServiceCallException, JMSException, OfferValidationException {
		long time = System.currentTimeMillis();
		String randomString = orderDomain.getOrder().getGenerateOrderId();
		OrderUnitMapping orderUnitMapping = new OrderUnitMapping();
		Order order = orderManagementService.validateOrder(orderDomain, orderUnitMapping);
		OrderInfo info = orderManagementService.createSubscrptionOrder(order, orderDomain.getWalletOrder(), orderDomain,
				randomString, orderUnitMapping);
		orderManagementService.publishToOrderInfoCacheSQS(properties.getEnvironmentType().name(), info);
		orderManagementService.pushDataToThirdPartyAnalytics(info, info.getOrderNotificationMap());
		log.info("create-subscription-order for {} took {} millisecond ", info.getOrder().getGenerateOrderId(),
				System.currentTimeMillis() - time);
		return ResponseEntity.ok(new ApiResponse(orderConverter.converToOrderInfoDomain(info)));
	}

	@PostMapping(value = "recharge-wallet")
	public ResponseEntity<ApiResponse> rechargeWallet(@RequestBody OrderDomain orderDomain)
			throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException,
			DataNotFoundException, DataIntegrityViolationException, CardValidationException, IOException,
			WebServiceCallException, JMSException {
		String randomString = orderDomain.getOrder().getGenerateOrderId();
		OrderUnitMapping orderUnitMapping = new OrderUnitMapping();
		Order order = orderManagementService.validateOrder(orderDomain, orderUnitMapping);
		Order walletOrder = orderManagementService.extractWalletOrder(order, WalletOrderType.DIRECT,
				orderDomain.getWalletOrder());
		OrderInfo info = orderManagementService.createDirectWalletOrder(walletOrder, orderDomain.getWalletOrder(),
				orderDomain, randomString, orderUnitMapping);
		orderManagementService.pushDataToThirdPartyAnalytics(info, info.getOrderNotificationMap());
		return ResponseEntity.ok(new ApiResponse(info));
	}

	@PostMapping(value = "purchase-subscription")
	public ResponseEntity<ApiResponse> purchaseSubscription(@RequestBody OrderDomain orderDomain)
			throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException,
			DataNotFoundException, DataIntegrityViolationException, CardValidationException, IOException,
			WebServiceCallException, JMSException {
		long time = System.currentTimeMillis();
		String randomString = orderDomain.getOrder().getGenerateOrderId();
		OrderUnitMapping orderUnitMapping = new OrderUnitMapping();
		Order order = orderManagementService.validateOrder(orderDomain, orderUnitMapping);
		Order selectOrder = orderManagementService.extractSelectOrder(order);
		OrderInfo info = orderManagementService.processPlainOrder(selectOrder, orderDomain.isIncludeReceipts(),
				orderDomain.isAddMetadata(), orderDomain.getOrderNotification(), randomString, orderUnitMapping);
		if (orderDomain.isIncludeReceipts()) {
			if((Objects.nonNull(info.getCashCardPendingAmt()) && info.getCashCardPendingAmt().compareTo(BigDecimal.ZERO)==0) || Objects.isNull(info.getCashCardPendingAmt())){
				info.setCashCardPendingAmt(orderDomain.getOrder().getCurrentWalletAmount());
			}
			OrderInfo receiptInfo = orderManagementService.generateReciept(info);
			info.setReceipts(receiptInfo.getReceipts());
			info.setAdditionalReceipts(receiptInfo.getAdditionalReceipts());
			info.setAndroidReceipts(receiptInfo.getAndroidReceipts());
		}
		if (MapUtils.isEmpty(info.getOrderNotificationMap())) {
			info.setOrderNotificationMap(new HashMap<>());
		}
		info.getOrderNotificationMap().put(info.getOrder().getOrderId(), info.getOrderNotification());
		orderManagementService.pushDataToThirdPartyAnalytics(info, info.getOrderNotificationMap());
		log.info("Full Order Processing Time for {} :: {} millisecond ", info.getOrder().getGenerateOrderId(),
				System.currentTimeMillis() - time);
		orderManagementService.publishToOrderInfoCacheSQS(properties.getEnvironmentType().name(), info);
		return ResponseEntity.ok(new ApiResponse(info));
	}

	@GetMapping(value = "last-n-orders")
	public ResponseEntity<ApiResponse> fetchLastNOrders(@RequestParam Integer size, HttpServletRequest httpRequest) {
		BaseHeaders baseHeaders = AppUtils.getBaseHeaders(httpRequest);
		return ResponseEntity.ok(new ApiResponse(orderManagementService.getLastNOrders(size, baseHeaders.getUnitId())));
	}

	@PostMapping(value = "create-order-metrics-data")
	public ResponseEntity<ApiResponse> createOrderMetricData(@RequestBody OrderMetricDomain orderMetricDomain) {
		return ResponseEntity.ok(new ApiResponse(orderManagementService.createOrderMetricData(orderMetricDomain)));
	}

	@PostMapping(value = "create-web-order-metrics-data")
	public void createWebOrderMetricData(@RequestBody WebOrderMetricDomain orderMetricDomain) {
		orderManagementService.createOrderMetricData(orderMetricDomain);
	}

	@PostMapping(value = "checkout-table-order")
	public TableViewOrder checkoutTableOrder(@RequestBody final TableViewOrder tableViewOrder) throws DataUpdationException {
		log.info("Call to consolidate table orders with table request id : {}",tableViewOrder.getOrder().getTableRequestId());
		try {
			return orderManagementService.createTableOrder(tableViewOrder);
		}catch (Exception e){
			log.info("Error in calling checkout-table-order : {}",e.getMessage());
		}
		return null;
	}

	@PostMapping(value = "expire-loyalty")
	public void expireLoyalty(){
		log.info("API to expire loyalty");
		loyaltyService.expireLoyaltyPoints();
	}

	@PostMapping(value = "create-dohful-order")
	public ResponseEntity<ApiResponse> createDohfulOrder(@RequestBody OrderDomain orderDomain)
			throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException,
			DataNotFoundException, DataIntegrityViolationException, CardValidationException, IOException,
			WebServiceCallException, JMSException, OfferValidationException {
		long time = System.currentTimeMillis();
		OrderInfo info = orderManagementService.processDohfulOrder(orderDomain);
		orderManagementService.publishToOrderInfoCacheSQS(properties.getEnvironmentType().name(), info);
		log.info("create-order for {} took {} millisecond ", info.getOrder().getGenerateOrderId(),
				System.currentTimeMillis() - time);
		return ResponseEntity.ok(new ApiResponse(info));
	}

}
