package com.stpl.tech.kettle.controller;

import lombok.extern.log4j.Log4j2;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class KettleGarbageCleanExecutor {

    @Scheduled(cron = "0 25 0,4,7,11,15,19 * * *", zone = "GMT+05:30")
    public void garbageCleanExecutor() {
        try {
            log.info("KETTLE_SERVICE_GARBAGE_COLLECTOR :::::: Executing Garbage Clean up");
            System.gc();
        } catch (Exception e) {
            log.error("KETTLE_SERVICE_GARBAGE_COLLECTOR :::::: Exception Faced While Executing Garbage Clean up");
        }
    }
}
