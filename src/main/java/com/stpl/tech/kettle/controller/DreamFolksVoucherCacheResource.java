package com.stpl.tech.kettle.controller;

import com.stpl.tech.kettle.cache.DreamFolksVoucherUsageCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;

import static com.stpl.tech.kettle.util.ApiConstants.CustomerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.util.ApiConstants.CustomerServiceConstants.SEPARATOR;
import static com.stpl.tech.kettle.util.ApiConstants.CustomerServiceConstants.DREAMFOLKS_VOUCHER_CACHE_RESOURCE;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + DREAMFOLKS_VOUCHER_CACHE_RESOURCE)
public class DreamFolksVoucherCacheResource {

    private static final Logger LOG = LoggerFactory.getLogger(DreamFolksVoucherCacheResource.class);

    @Autowired
    private DreamFolksVoucherUsageCache dreamFolksVoucherUsageCache;

    @RequestMapping(method = RequestMethod.POST, value = "refresh-dreamfolks-voucher-cache", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void refreshDreamFolksVoucherCache() {
        LOG.info("Refreshing DreamFolks voucher code cache");
        long startTime = System.currentTimeMillis();
        try {
            dreamFolksVoucherUsageCache.refreshCache();
            LOG.info("DreamFolks voucher code cache refreshed successfully in {} ms", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            LOG.error("Error refreshing DreamFolks voucher code cache", e);
            throw e;
        }
    }
} 