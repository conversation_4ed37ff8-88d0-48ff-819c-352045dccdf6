package com.stpl.tech.kettle.controller;

import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.stpl.tech.kettle.converter.OrderConverter;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.domain.ApiResponse;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDomain;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.WalletOrder;
import com.stpl.tech.kettle.exceptions.CardValidationException;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.kettle.exceptions.TemplateRenderingException;
import com.stpl.tech.kettle.exceptions.WebServiceCallException;
import com.stpl.tech.kettle.service.OrderManagementService;
import com.stpl.tech.kettle.util.ApiConstants.CustomerServiceConstants;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.TransactionUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.jms.JMSException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

@RestController
@Log4j2
@RequestMapping(value = CustomerServiceConstants.API_VERSION + CustomerServiceConstants.SEPARATOR
        + CustomerServiceConstants.PARTNER_ORDER_ROOT_CONTEXT)
public class PartnerOrderResource {

    @Autowired
    private OrderManagementService orderManagementService;

    @Autowired
    private EnvironmentProperties properties;

    @Autowired
    private OrderConverter orderConverter;

    @PostMapping(value = "order/create/app")
    public Order createAppOrder(@RequestBody Order orderData) throws DataNotFoundException, DataUpdationException,
            CardValidationException, JMSException, OfferValidationException, TemplateRenderingException, ParseException, WebServiceCallException {
        Stopwatch stopWatch = Stopwatch.createStarted();
        OrderDomain orderDomain = orderConverter.convertToKettleCompatibleOrder(orderData);
        log.info("convertToKettleCompatibleOrder took {}  millisecond ", stopWatch.stop().elapsed(TimeUnit.MILLISECONDS));
        stopWatch.start();
        OrderInfo info  = orderManagementService.processOrder(orderDomain);
        orderManagementService.publishToOrderInfoCacheSQS(properties.getEnvironmentType().name(), info);
        if(!StringUtils.isEmpty(orderData.getPartnerCustomerId()) && AppConstants.COD.equals(orderData.getSource())) {
            info.setPartnerCustomerId(orderData.getPartnerCustomerId());
        }
        if (!TransactionUtils.isTableOrder(orderDomain.getOrder())) {
            orderManagementService.pushDataToThirdPartyAnalytics(info, info.getOrderNotificationMap());
        }
        log.info("create-order for {} took {} millisecond ", info.getOrder().getGenerateOrderId(),
                stopWatch.elapsed(TimeUnit.MILLISECONDS));
        Order order = info.getOrder();
        
        if (Objects.nonNull(info.getNextOffer())) {
            order.setNextOffer(info.getNextOffer());
        }
        return order;
    }


    @PostMapping(value = "order/create/delivery")
    public Order createDeliveryOrder(@RequestBody Order orderData) throws DataNotFoundException, DataUpdationException,
            CardValidationException, JMSException, OfferValidationException, TemplateRenderingException, ParseException, WebServiceCallException {
        Stopwatch stopWatch = Stopwatch.createStarted();
        OrderDomain orderDomain = orderConverter.convertToKettleCompatibleOrder(orderData);
        log.info("convertToKettleCompatibleOrder took {} millisecond ", stopWatch.stop().elapsed(TimeUnit.MILLISECONDS));
        stopWatch.start();
        OrderInfo info = orderManagementService.processOrder(orderDomain);
        orderManagementService.publishToOrderInfoCacheSQS(properties.getEnvironmentType().name(), info);
        if(!StringUtils.isEmpty(orderData.getPartnerCustomerId()) && AppConstants.COD.equals(orderData.getSource())) {
            info.setPartnerCustomerId(orderData.getPartnerCustomerId());
            orderManagementService.pushDataToCleverTapForPartner(info,info.getOrderNotificationMap());
        }
        log.info("create-order-delivery for {} took {} millisecond ", info.getOrder().getGenerateOrderId(),
                stopWatch.elapsed(TimeUnit.MILLISECONDS));
        Order order = info.getOrder();
        if (Objects.nonNull(info.getNextOffer())) {
            order.setNextOffer(info.getNextOffer());
        }
        return order;
    }

}
