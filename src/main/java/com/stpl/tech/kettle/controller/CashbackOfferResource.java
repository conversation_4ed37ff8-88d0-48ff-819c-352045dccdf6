package com.stpl.tech.kettle.controller;


import com.stpl.tech.kettle.service.CashBackOfferCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;

import static com.stpl.tech.kettle.util.ApiConstants.CustomerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.util.ApiConstants.CustomerServiceConstants.CASHBACK_OFFER_RESOURCE;
import static com.stpl.tech.kettle.util.ApiConstants.CustomerServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + CASHBACK_OFFER_RESOURCE)
public class CashbackOfferResource {

    @Autowired
    private CashBackOfferCache cashBackOfferCache;

    @RequestMapping(method = RequestMethod.POST, value = "clear-cache", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void clearCashbackOfferCache(){
        cashBackOfferCache.clearCashBackOfferCache();
    }

}
