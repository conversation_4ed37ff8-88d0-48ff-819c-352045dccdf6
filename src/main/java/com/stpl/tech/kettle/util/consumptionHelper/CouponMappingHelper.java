package com.stpl.tech.kettle.util.consumptionHelper;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.domain.model.OfferOrder;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.WebErrorCode;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.master.domain.model.CouponMappingType;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class CouponMappingHelper {

    public static List<CouponMapping> getValueOf(CouponMappingType type, OfferOrder o, UnitCacheService cache,
                                                 ProductCache productCache)
            throws DataNotFoundException {
        List<CouponMapping> values = new ArrayList<>();
        switch (type) {

            case CUSTOMER:
                values.add(new CouponMapping(
                        Integer.toString(o.getOrder().getCustomerId() == null ? 0 : o.getOrder().getCustomerId())));
                return values;
            case CONTACT_NUMBER:
                values.add(new CouponMapping(o.getContact()));
                return values;
            case NEW_CUSTOMER:
                values.add(new CouponMapping(AppUtils.setStatus(o.isNewCustomer())));
                return values;
            case UNIT:
                values.add(new CouponMapping(Integer.toString(o.getOrder().getUnitId())));
                return values;
            case UNIT_REGION:
                values.add(new CouponMapping(cache.getUnitById(o.getOrder().getUnitId()).getRegion()));
                return values;
            case ORDER_SOURCE:
                values.add(new CouponMapping(o.getOrder().getSource()));
                return values;
            case CHANNEL_PARTNER:
                values.add(new CouponMapping(Integer.toString(o.getOrder().getChannelPartner())));
                return values;
            case PAYMENT_MODE:
                o.getOrder().getSettlements()
                        .forEach(modes -> values.add(new CouponMapping(Integer.toString(modes.getMode()))));
                return values;
            case PRODUCT:
                o.getOrder().getOrders().forEach(
                        item -> values.add(new CouponMapping(Integer.toString(item.getProductId()), item.getDimension())));
                return values;
            case PRODUCT_CATEGORY:
                o.getOrder().getOrders()
                        .forEach(item -> values.add(new CouponMapping(
                                Integer.toString(productCache.getProductBasicDetailById(item.getProductId()).getType()),
                                item.getDimension())));
                return values;
            case PRODUCT_SUB_CATEGORY:
                o.getOrder().getOrders()
                        .forEach(item -> values.add(new CouponMapping(
                                Integer.toString(productCache.getProductBasicDetailById(item.getProductId()).getSubType()),
                                item.getDimension())));
                return values;
            default:
                return values;
        }

    }

    public static String getErrorStatement(CouponMappingType type, Set<CouponMapping> mappingList,
                                           UnitCacheService cache, ProductCache productCache) throws NumberFormatException, DataNotFoundException {
        StringBuilder sb = new StringBuilder();
        switch (type) {
            case CUSTOMER:
                return "Customer is not eligible for the offer. This offer is for selective customers only.";
            case CONTACT_NUMBER:
                return "Contact Number is not eligible for the offer. This offer is for selective customers only.";
            case NEW_CUSTOMER:
                return "This offer is for new customers only.";
            case UNIT:
                for (CouponMapping mapping : mappingList) {
                    sb.append(cache.getUnitById(Integer.parseInt(mapping.getValue())).getName() + ", ");
                }
                return "This offer cannot be availed at this unit. Offer can be availed at "
                        + sb.substring(0, sb.length() - 2) + " only";
            case UNIT_REGION:
                for (CouponMapping mapping : mappingList) {
                    sb.append(mapping.getValue() + ", ");
                }
                return "This offer cannot be availed in this region. Offer can be availed in "
                        + sb.substring(0, sb.length() - 2) + " only";
            case ORDER_SOURCE:
                return "Incorrect order Source. This offer cannot be availed on this unit.";
            case CHANNEL_PARTNER:
                mappingList.forEach(p -> {
                    sb.append(cache.getChannelPartnerById(Integer.parseInt(p.getValue())).getName() + ", ");
                });
                return "Incorrect Channel Partner. Valid for " + sb.substring(0, sb.length() - 2) + " only";
            case PAYMENT_MODE:
                mappingList.forEach(p -> {
                    sb.append(cache.getAllPaymentMode().get(Integer.parseInt(p.getValue())).getName() + ", ");
                });
                return "Incorrect Payment Mode. Valid for " + sb.substring(0, sb.length() - 2) + " only";
            case PRODUCT:
                mappingList.forEach(p -> {
                    sb.append(productCache.getProductBasicDetailById(Integer.parseInt(p.getValue())).getDetail().getName() + " ("
                            + p.getDimension() + "), ");
                });
                return "Order does not contain required Product, Eligible Products: " + sb.substring(0, sb.length() - 2);
            case PRODUCT_CATEGORY:
                mappingList.forEach(p -> {
                    sb.append(productCache.getProductCategory(Integer.parseInt(p.getValue())).getDetail().getName() + " ("
                            + p.getDimension() + "), ");
                });
                return "Order does not contain Product of required Category, Eligible Product Category: "
                        + sb.substring(0, sb.length() - 2);
            case PRODUCT_SUB_CATEGORY:
                mappingList.forEach(p -> {
                    sb.append(cache.getSubcategoryById(Integer.parseInt(p.getValue())).getName() + " ("
                            + p.getDimension() + "), ");
                });
                return "Order does not contain Product of required Sub-Category, Eligible Product Sub-Category: "
                        + sb.substring(0, sb.length() - 2);
            default:
                return null;
        }
    }

    public static WebErrorCode getErrorCode(CouponMappingType type) {
        switch (type) {
            case CUSTOMER:
                return WebErrorCode.CUSTOMER_NOT_FOUND;
            case CONTACT_NUMBER:
                return WebErrorCode.CUSTOMER_NOT_FOUND;
            case UNIT:
                return WebErrorCode.INVALID_UNIT;
            case UNIT_REGION:
                return WebErrorCode.INVALID_REGION;
            case ORDER_SOURCE:
                return WebErrorCode.INVALID_SOURCE;
            case PRODUCT:
                return WebErrorCode.PRODUCT_NOT_FOUND;
            case PRODUCT_CATEGORY:
                return WebErrorCode.PRODUCT_NOT_FOUND;
            case PRODUCT_SUB_CATEGORY:
                return WebErrorCode.PRODUCT_NOT_FOUND;
            default:
                return WebErrorCode.NOT_AVAILABLE;
        }
    }
}
