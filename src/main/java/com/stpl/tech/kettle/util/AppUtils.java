package com.stpl.tech.kettle.util;

import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Time;
import java.text.DateFormat;
import java.text.Normalizer;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Set;
import java.util.TimeZone;
import java.util.function.Consumer;
import java.util.regex.Matcher;

import javax.jms.JMSException;
import javax.jms.MessageFormatException;

import com.stpl.tech.kettle.domain.BaseHeaders;
import com.stpl.tech.util.EnvType;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.Seconds;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.amazonaws.regions.Regions;
import com.amazonaws.util.Base64;
import com.google.gson.Gson;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.WsType;

public class AppUtils {


    private static final Logger LOG = LoggerFactory.getLogger(AppUtils.class);
    public static final String DATE_FORMAT_STRING = "yyyy-MM-dd";
    public static final Set<String> CHAAYOS_CASH_OFFER_CODE;
    public static final Set<Integer> CHAAYOS_DINE_IN_OR_SNP_ORDERS ;
    public static final Set<Integer> CHAAYOS_CAFE_OR_TAKEAWAY_ORDERS ;

    //	public static final Set<String> CHAAYOS_SUBSCRIPTION_OFFER_CODE;
    static {
        CHAAYOS_CASH_OFFER_CODE = new HashSet<String>();
        CHAAYOS_CASH_OFFER_CODE.add(AppConstants.CHAAYOS_CASH_BACK_OFFER_CODE);
        CHAAYOS_CASH_OFFER_CODE.add(AppConstants.CHAAYOS_CASH_OFFER_CODE);
        CHAAYOS_CASH_OFFER_CODE.add("CASH100");
        CHAAYOS_CASH_OFFER_CODE.add(AppConstants.DOHFUL_CASH_OFFER_CODE);
//		CHAAYOS_SUBSCRIPTION_OFFER_CODE = new HashSet<String>();
//		CHAAYOS_SUBSCRIPTION_OFFER_CODE.add(AppConstants.CHAAYOS_SELECT_SUBSCRIPTION_OFFER_CODE);
//		CHAAYOS_SUBSCRIPTION_OFFER_CODE.add(AppConstants.CHAAYOS_PRO_SUBSCRIPTION_OFFER_CODE);
        CHAAYOS_DINE_IN_OR_SNP_ORDERS = new HashSet<>();
        CHAAYOS_DINE_IN_OR_SNP_ORDERS.add(14);
        CHAAYOS_DINE_IN_OR_SNP_ORDERS.add(21);
        CHAAYOS_DINE_IN_OR_SNP_ORDERS.add(23);

        CHAAYOS_CAFE_OR_TAKEAWAY_ORDERS = new HashSet<>();
        CHAAYOS_CAFE_OR_TAKEAWAY_ORDERS.add(1);
        CHAAYOS_CAFE_OR_TAKEAWAY_ORDERS.add(9);
    }

    private static Date INFINITY;

    public static final String YES = "Y";
    public static final String NO = "N";
    public static final String OATS = "oats";

    public static final SimpleDateFormat sdf = new SimpleDateFormat(AppConstants.BILL_PRINT_DATE_FORMAT);
    public static final SimpleDateFormat sdf2 = new SimpleDateFormat(AppConstants.BILL_PRINT_TIME_FORMAT);
    public static final SimpleDateFormat parser = new SimpleDateFormat("HH:mm:ss");
    public static final Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Kolkata"));
    public static final SimpleDateFormat dateStringParser = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
    public static final SimpleDateFormat dateStringParser_ = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:00");

    public static Calendar getCalender() {
        return new GregorianCalendar(TimeZone.getTimeZone("Asia/Kolkata"));
    }

    ;

    public static Date getCurrentTimestamp() {

        return new Date(getCurrentTimeIST().getMillis());
    }

    public static DateTime getCurrentISOTime() {

        return new DateTime(getCurrentTimeIST().toString(AppConstants.DATE_TIME_ISO_FORMATTER));
    }

    public static DateTime getCurrentISOTimeWithSec() {
        return new DateTime(getCurrentTimeIST().toString(AppConstants.DATE_TIME_WITH_NO_MILISECOND_FORMATTER));
    }

    public static DateTime getCurrentTimeIST() {

        return new DateTime(DateTimeZone.forID("Asia/Kolkata"));
    }

    public static String getCurrentTimeISTString() {

        return getCurrentTimeIST().toString(AppConstants.DATE_TIME_FORMATTER);
    }

    public static Date parseDate(String date) {
        try {
            return new Date(AppConstants.DATE_TIME_WITH_NO_MILISECOND_FORMATTER.parseDateTime(date).getMillis());
        } catch (IllegalArgumentException e) {
            return new Date(AppConstants.DATE_FORMATTER.parseDateTime(date).getMillis());
        } catch (NullPointerException e) {
            return null;
        }
    }

    public static String getCurrentTimeISTStringWithoutMS(){
        return getCurrentTimeIST().toString(AppConstants.DATE_TIME_WITH_NO_MILISECOND_FORMATTER);
    }

    public static String getCurrentDateTime() {
        return DateTime.now(DateTimeZone.forID("Asia/Kolkata")).toString("yyyy-MM-dd'T'HH:mm:ss");
    }

    public static String getCurrentUTCDateTime() {
        return DateTime.now(DateTimeZone.UTC).toString("yyyy-MM-dd'T'HH:mm:ss");
    }

    public static DateTime parseDateTime(String date) {
        try {
            return DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss").parseDateTime(date);
        } catch (IllegalArgumentException e) {
            return DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(date);
        } catch (NullPointerException e) {
            return null;
        }
    }

    public static Date parseDate(String date, SimpleDateFormat dateFormat) {
        try {
            return dateFormat.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void formatDateIfPresent(Date originalDate, Consumer<Date> setter, Integer orderId, String fieldName, SimpleDateFormat dateFormat) {
        if (originalDate != null) {
            try {
                String formatted = dateFormat.format(originalDate);
                setter.accept(dateFormat.parse(formatted));
            } catch (Exception e) {
                LOG.info("Error in formatting {} for order id : {}", fieldName, orderId);
            }
        }
    }

    public static Date parseDate(String date, DateTimeFormatter dateFormat) {
        return new Date(dateFormat.parseDateTime(date).getMillis());
    }

    public static Date parseDateSimple(String date) {
        try {
            return new Date(AppConstants.DATE_FORMATTER.parseDateTime(date).getMillis());
        } catch (NullPointerException e) {
            return null;
        }
    }

    public static String getTimeISTString(Date date) {
        if (date == null) {
            return "";
        }

        return new DateTime(date.getTime(), DateTimeZone.forID("Asia/Kolkata"))
                .toString(AppConstants.DATE_TIME_FORMATTER);
    }

    public static String getTimeWithoutMillisISTString(Date date) {
        if (date == null) {
            return "";
        }

        return new DateTime(date.getTime(), DateTimeZone.forID("Asia/Kolkata"))
                .toString(AppConstants.DATE_TIME_WITH_NO_MILISECOND_FORMATTER);
    }

    public static String getOnlyTime(Date date) {
        if (date == null) {
            return "";
        }

        return new DateTime(date.getTime(), DateTimeZone.forID("Asia/Kolkata"))
                .toString(AppConstants.DATE_TIME_WITH_ONLY_TIME_FORMATTER);
    }

    public static String getCurrentTimeISTStringWithNoColons() {

        return getCurrentTimeIST().toString(AppConstants.DATE_TIME_FORMATTER_WITH_NO_CHARACTERS);
    }

    public static Date getCurrentDate() {

        Calendar c = getCalender();
        c.set(Calendar.HOUR_OF_DAY, 0); // anything 0 - 23
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime(); // the midnight, that's the first second of the day.
    }

    public static Date getDate(int day, int month, int year) {

        Calendar c = getCalender();
        c.set(Calendar.YEAR, year); // anything 0 - 23
        c.set(Calendar.MONTH, month - 1); // anything 0 - 23
        c.set(Calendar.DAY_OF_MONTH, day); // anything 0 - 23
        c.set(Calendar.HOUR_OF_DAY, 0); // anything 0 - 23
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime(); // the midnight, that's the first second of the day.
    }

    public static Date getDayBeforeOrAfterCurrentDay(int numberOfDays) {
        Date currentDate = getCurrentTimestamp();
        Calendar c = getCalender();
        c.setTime(currentDate);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        c.add(Calendar.DATE, numberOfDays);
        return c.getTime();
    }

    public static Date getDayBeforeOrAfterDay(Date date, int numberOfDays) {
        Date currentDate = date;
        Calendar c = getCalender();
        c.setTime(currentDate);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        c.add(Calendar.DATE, numberOfDays);
        return c.getTime();
    }

    public static Date getDateBeforeOrAfter(Date date, int numberOfMonths) {
        Calendar c = getCalender();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        c.add(Calendar.MONTH, numberOfMonths);
        return c.getTime();
    }

    public static Date getDateBeforeOrAfterInSeconds(Date date, int numberOfSeconds) {
        Calendar c = getCalender();
        c.setTime(date);
        c.add(Calendar.SECOND, numberOfSeconds);
        return c.getTime();
    }

    public static Date getDate(Date date) {
        Calendar c = getCalender();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 0); // anything 0 - 23
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime(); // the midnight, that's the first second of the day.
    }


    public static Date getDateAfterDays(Date date, int noOFDays) {
        Calendar c = getCalender();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 0); // anything 0 - 23
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        c.add(Calendar.DATE, noOFDays);
        return c.getTime(); // the midnight, that's the first second of the day.
    }


    public static List<String> getListDateAfterDays(Date date, int noOFDays, int limit) {
        List<String> dates = new ArrayList<>();
        int days = noOFDays;
        for (int i = 0; i < limit; i++) {
            Calendar c = getCalender();
            c.setTime(date);
            c.set(Calendar.HOUR_OF_DAY, 0); // anything 0 - 23
            c.set(Calendar.MINUTE, 0);
            c.set(Calendar.SECOND, 0);
            c.set(Calendar.MILLISECOND, 0);
            c.add(Calendar.DATE, days);
            days += noOFDays;
            dates.add(getSQLFormattedDate(c.getTime())); // the midnight, that's the first second of the day.
        }
        return dates;
    }

    public static Date getCurrentDateIST() {
        Calendar c = getCalender();
        c.set(Calendar.HOUR_OF_DAY, 0); // anything 0 - 23
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getCurrentDateTimeIST() {
        Calendar c = getCalender();
        c.set(Calendar.HOUR_OF_DAY, 5); // anything 0 - 23
        c.set(Calendar.MINUTE, 30);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getCurrentDateHourIST() {
        Calendar c = getCalender();
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getPreviousDateHourIST() {
        Calendar c = getCalender();
        c.add(Calendar.HOUR, -1);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static String getCurrentDateISTFormatted() {
        return new DateTime(getCurrentDateIST().getTime()).toString(AppConstants.DATE_FORMATTER);
    }

    public static Seconds getTimeDifference(DateTime time1, DateTime time2) {
        return Seconds.secondsBetween(time2, time1);
    }

    public static int convertToBinary(Integer permission) {
        if (permission != null) {
            return convertToBinary(permission.toString());
        }
        return 0;
    }

    public static int convertToBinary(String permission) {
        char[] charArr = permission.toCharArray();
        double value = 0;
        for (int i = 0; i < charArr.length; i++) {
            double valueOfChar = Character.getNumericValue(charArr[i]);
            double v = valueOfChar * Math.pow(2, charArr.length - 1 - i);
            value += v;
        }
        return (int) value;
    }

    public static Date getPreviousDate() {
        Calendar c = getCalender();
        c.set(Calendar.HOUR_OF_DAY, 0); // anything 0 - 23
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        c.add(Calendar.DATE, -1);
        return c.getTime(); // the midnight, that's the first second of the day.
    }

    public static String getPreviousDateFormatted() {
        return new DateTime(getPreviousDate().getTime()).toString(AppConstants.DATE_FORMATTER);
    }

    public static String getFormattedDate(Date date) {
        return new SimpleDateFormat("MM/dd/yyyy").format(date);
    }

    public static String getClevertapFormattedDate(Date date) {
        return new SimpleDateFormat("dd/MM/yyyy").format(date);
    }

    public static String getSMSTemplateDate(Date date) {
        return new SimpleDateFormat("dd-MMM-yyyy").format(date);
    }

    public static String getSQLFormattedDate(Date date) {
        return new SimpleDateFormat(DATE_FORMAT_STRING).format(date);
    }

    public static String getFormattedTime(Date date) {
        return new SimpleDateFormat("MM/dd/yyyy HH:mm:ss").format(date);
    }

    public static String getFormattedTimeWithHours(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd HH-mm").format(date);
    }

    public static String getBillPrintFormat(Date date) {
        return sdf.format(date);
    }

    public static String getBillPrintTime(Date date) {
        return sdf2.format(date);
    }

    public static String getFormattedTime(Date date, String format) {
        return new SimpleDateFormat(format).format(date);
    }

    public static Date getNextDate(Date date) {

        Calendar c = getCalender();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 0); // anything 0 - 23
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        c.add(Calendar.DATE, 1);
        return c.getTime(); // the midnight, that's the first second of the day.
    }

    public static Date getPreviousDate(Date date) {
        Calendar c = getCalender();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 0); // anything 0 - 23
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        c.add(Calendar.DATE, -1);
        return c.getTime(); // the midnight, that's the first second of the day.
    }

    public static Date getPreviousDateIST() {
        Calendar c = getCalender();
        c.set(Calendar.HOUR_OF_DAY, 0); // anything 0 - 23
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        c.add(Calendar.DATE, -1);
        return c.getTime();
    }

    public static String getPreviousDateISTFormatted() {
        return new DateTime(getPreviousDateIST().getTime()).toString(AppConstants.DATE_FORMATTER);
    }

    public static Date getInfiniteDate() {
        if (INFINITY == null) {
            Calendar c = getCalender();
            c.set(Calendar.MONTH, 0); // anything 0 - 23
            c.set(Calendar.YEAR, 2036);
            c.set(Calendar.DAY_OF_MONTH, 1);
            c.set(Calendar.HOUR_OF_DAY, 0); // anything 0 - 23
            c.set(Calendar.MINUTE, 0);
            c.set(Calendar.SECOND, 0);
            INFINITY = c.getTime(); // the midnight, that's the first second of
            // the day.
        }
        return INFINITY;
    }

    public static Date getMysqlMaxTimestamp() {
        // Returns 2030-12-01 00:00:00 w.r.t product out time stamp
        Calendar c = getCalender();
        c.set(2030, 11, 1, 00, 00, 00);
        return c.getTime();
    }

    public static Date covertDateIST(long timestamp) {
        Calendar istTime = getCalender();
        istTime.setTimeInMillis(timestamp);
        return istTime.getTime();
    }

    public static Date parseDateIST(String date) {
        long timestamp = parseDate(date).getTime();
        Calendar istTime = getCalender();
        istTime.setTimeInMillis(timestamp);
        return istTime.getTime();
    }

    @SuppressWarnings("deprecation")
    public static Date getStartOfDay(Date date) {
        return new Date(date.getYear(), date.getMonth(), date.getDate());
    }

    @SuppressWarnings("deprecation")
    public static Date getEndOfDay(Date date) {
        return new Date(date.getYear(), date.getMonth(), date.getDate(), 23, 59, 59);
    }

    @SuppressWarnings("deprecation")
    public static Date getStartOfDayIST(Date date) {
        return new Date(date.getYear(), date.getMonth(), date.getDate(), 5, 30, 0);
    }

    @SuppressWarnings("deprecation")
    public static Date getEndOfDayIST(Date date) {
        Date tomm = new Date(date.getYear(), date.getMonth(), date.getDate());
        tomm = getNextDate(date);
        return new Date(tomm.getYear(), tomm.getMonth(), tomm.getDate(), 5, 30, 0);
    }

    @SuppressWarnings("deprecation")
    public static Date getStartOfMonth(int year, int month) {
        return new Date(year, month, 1);
    }

    public static Date getStartOfMonth2(int year, int month) {
        Calendar c = getCalender();
        c.set(year, month - 1, 1, 0, 0, 0);
        return c.getTime();
    }

    public static Date getEndOfMonth(int year, int month) {
        return getLastDayOfMonth(getStartOfMonth2(year, month));
    }

    public static boolean isEqual(BigDecimal b1, BigDecimal b2) {
        if (b1 == null || b2 == null) {
            return false;
        }
        return b1.compareTo(b2) == 0;
    }

    public static BigDecimal percentage(Integer x, Integer ofY) {
        return (divide(BigDecimal.valueOf(x), BigDecimal.valueOf(ofY)).multiply(new BigDecimal(100.00d))).setScale(2,
                BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal percentage(BigDecimal x, BigDecimal ofY) {
        return (divide(x, ofY).multiply(new BigDecimal(100.00d))).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal percentageWithScale(BigDecimal x, BigDecimal ofY, int scale) {
        return (divideWithScale(x, ofY, scale).multiply(BigDecimal.TEN.multiply(BigDecimal.TEN))).setScale(scale,
                BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal percentageWithScale10(BigDecimal x, BigDecimal ofY) {
        return (divideWithScale(x, ofY, 10).multiply(BigDecimal.TEN.multiply(BigDecimal.TEN))).setScale(10,
                BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal percentage(BigDecimal x, BigDecimal ofY, int scale) {
        if (x == null || ofY == null || ofY.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(0);
        }
        try {
            return x.divide(ofY, scale, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100.00d));
        } catch (ArithmeticException e) {
            LOG.error("Arithmetic exception while doing the division", e);
        }
        return null;
    }

    public static float percentage(Float x, Float y) {
        return ((x * y) / 100);
    }

    public static BigDecimal percentOf(BigDecimal x, BigDecimal percent) {
        return divide(multiply(x == null ? BigDecimal.ZERO : x, percent == null ? BigDecimal.ZERO : percent),
                new BigDecimal(100));
    }

    public static BigDecimal percentageOf(BigDecimal percent, BigDecimal value) {
        return multiply(percent.divide(BigDecimal.valueOf(100)), value).setScale(2, RoundingMode.HALF_UP);
    }

    public static BigDecimal percentOfWithScale10(BigDecimal x, BigDecimal percent) {
        return divideWithScale10(multiplyWithScale10(x, percent), BigDecimal.TEN.multiply(BigDecimal.TEN));
    }

    public static BigDecimal divide(BigDecimal numerator, BigDecimal denominator) {
        return divideWithScale(numerator, denominator, 2);
    }

    public static BigDecimal divideWithScale10(BigDecimal numerator, BigDecimal denominator) {
        return divideWithScale(numerator, denominator, 10);
    }

    public static BigDecimal divideWithScale(BigDecimal numerator, BigDecimal denominator, int scale) {
        if (numerator == null || denominator == null || denominator.compareTo(BigDecimal.ZERO) == 0 || numerator.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        try {
            return (numerator.divide(denominator, scale, BigDecimal.ROUND_HALF_UP));
        } catch (ArithmeticException e) {
            LOG.error("Arithmetic exception while doing the division", e);
        }
        return null;
    }

    public static BigDecimal divideWithScaleUptoPrecision(BigDecimal numerator, BigDecimal denominator, int scale) {
        if (numerator == null || denominator == null || denominator.compareTo(BigDecimal.ZERO) == 0 || numerator.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        try {
            return (numerator.divide(denominator, scale, BigDecimal.ROUND_HALF_EVEN));
        } catch (ArithmeticException e) {
            LOG.error("Arithmetic exception while doing the division", e);
        }
        return null;
    }

    public static BigDecimal multiply(BigDecimal x, BigDecimal y) {
        return multiplyWithScale(x, y, 2);
    }

    public static BigDecimal multiplyWithScale10(BigDecimal x, BigDecimal y) {
        return multiplyWithScale(x, y, 10);
    }

    public static BigDecimal multiplyWithScale(BigDecimal x, BigDecimal y, int scale) {
        if (x == null) {
            x = BigDecimal.ZERO;
        }
        if (y == null) {
            y = BigDecimal.ZERO;
        }
        try {
            return x.multiply(y).setScale(scale, BigDecimal.ROUND_HALF_UP);
        } catch (ArithmeticException e) {
            LOG.error("Arithmetic exception while doing the multiplication", e);
        }
        return null;
    }

    public static Float multiply(Float x, Float y) {
        if (x == null) {
            x = (float) 0.0;
        }
        if (y == null) {
            y = (float) 0.0;
        }
        try {
            return x * y;
        } catch (ArithmeticException e) {
            LOG.error("Arithmetic exception while doing the multiplication", e);
        }
        return null;
    }

    public static BigDecimal add(BigDecimal x, BigDecimal y) {
        if (x == null) {
            x = BigDecimal.ZERO;
        }
        if (y == null) {
            y = BigDecimal.ZERO;
        }
        try {
            return x.add(y);
        } catch (ArithmeticException e) {
            // LOG.error("Arithmetic exception while doing the multiplication",
            // e);
        }
        return BigDecimal.ZERO;
    }

    public static Float subtract(Float x, Float y) {
        if (x == null) {
            x = (float) 0.0;
        }
        if (y == null) {
            y = (float) 0.0;
        }
        try {
            return x - y;
        } catch (ArithmeticException e) {
            // LOG.error("Arithmetic exception while doing the multiplication",
            // e);
        }
        return (float) 0.0;
    }

    public static Integer roundToInteger(BigDecimal x) {
        if (x == null) {
            x = BigDecimal.ZERO;
        }
        x = x.setScale(0, RoundingMode.HALF_UP);
        return x.intValue();
    }

    public static String getFormattedEmail(String name, String email) {
        return name + "<" + email + ">";
    }

    public static boolean isDev(EnvType envType) {
        return !EnvType.PROD.equals(envType) && !EnvType.SPROD.equals(envType);
//		return EnvType.DEV.equals(envType);
    }

    public static boolean isProd(EnvType envType) {
        return EnvType.PROD.equals(envType) || EnvType.SPROD.equals(envType);
    }

    public static String getPrefix(EnvType envType) {
        return isDev(envType) ? "DEV " : "";
    }

    public static BigDecimal getCollectableAmountforOrder(BigDecimal totalAmount, BigDecimal paidAmount) {
        return totalAmount.subtract(paidAmount);
    }

    public static boolean isAccountable(String type) {
        boolean result = false;
        if (type != null && AppConstants.ACCOUNTABLE.equals(type)) {
            result = true;
        }
        return result;
    }

    public static boolean isActive(String status) {
        return status != null && (AppConstants.ACTIVE.equals(status));
    }

    public static String getCorrectStatus(String status) {
        if (status != null && (AppConstants.ACTIVE.equalsIgnoreCase(status.trim()))) {
            return AppConstants.ACTIVE;
        }
        return AppConstants.IN_ACTIVE;
    }

    public static String removeCountryCode(String contactNumber) {
        String substring = contactNumber;
        if (contactNumber.indexOf("+91-") != -1) {
            substring = contactNumber.substring(contactNumber.indexOf("+91-") + 4);
        }
        return substring;
    }

    public static Date getTimeOfDay(Date currentDate, int minutesToAdd) {
        Calendar c = getCalender();
        c.setTime(currentDate);
        c.add(Calendar.MINUTE, minutesToAdd);
        return c.getTime();
    }

    public static Date getLastFriday(Date date) {

        Calendar cal = getCalender();
        cal.setTime(date);
        int daysBackToSat = cal.get(Calendar.DAY_OF_WEEK);
        cal.add(Calendar.DATE, daysBackToSat * -1);
        cal.add(Calendar.DATE, -1);
        return cal.getTime();
    }

    public static boolean isTodayMonday() {
        Calendar cal = getCalender();
        cal.setTime(getCurrentDate());
        return cal.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY;
    }

    public static boolean isTodayTheFirstDayOfMonth() {
        Calendar cal = getCalender();
        cal.setTime(getCurrentDate());
        return cal.get(Calendar.DAY_OF_MONTH) == 1;
    }

    public static boolean isTodayTheSecondDayOfMonth() {
        Calendar cal = getCalender();
        cal.setTime(getCurrentDate());
        return cal.get(Calendar.DAY_OF_MONTH) == 2;
    }

    public static Date getOldDate(Date date, int numberOfDays) {

        Calendar cal = getCalender();
        cal.setTime(date);
        cal.add(Calendar.DATE, numberOfDays * -1);
        return cal.getTime();
    }

    public static Date getLastDayOfPreviousMonth(Date date) {

        Calendar cal = getCalender();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.add(Calendar.DATE, -1);
        return cal.getTime();
    }

    public static Date getLastDayOfMonth(Date date) {

        Calendar cal = getCalender();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.add(Calendar.MONTH, 1);
        cal.add(Calendar.DATE, -1);
        return cal.getTime();
    }

    public static Date getFirstDayOfPreviousMonth(Date date) {

        Calendar cal = getCalender();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.add(Calendar.DATE, -1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    public static Date getFirstDayOfMonth(Date date) {
        Calendar cal = getCalender();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    public static Date getLastSunday(Date date) {
        Calendar cal = getCalender();
        cal.setTime(date);
        int dayOfTheWeek = Calendar.SUNDAY;
        cal.add(Calendar.DATE, -1);
        while (cal.get(Calendar.DAY_OF_WEEK) != dayOfTheWeek) {
            cal.add(Calendar.DATE, -1);
        }
        return cal.getTime();
    }

    public static Date getLastToLastMonday(Date date) {
        Calendar cal = getCalender();
        cal.setTime(date);
        int dayOfTheWeek = Calendar.SUNDAY;
        cal.add(Calendar.DATE, -1);
        while (cal.get(Calendar.DAY_OF_WEEK) != dayOfTheWeek) {
            cal.add(Calendar.DATE, -1);
        }
        cal.add(Calendar.DATE, -2);
        dayOfTheWeek = Calendar.MONDAY;
        while (cal.get(Calendar.DAY_OF_WEEK) != dayOfTheWeek) {
            cal.add(Calendar.DATE, -1);
        }
        return cal.getTime();
    }

    public static String setStatus(boolean value) {
        return value ? YES : NO;
    }

    public static String setStatus(Boolean value) {
        return value != null && value ? YES : NO;
    }

    public static boolean getStatus(String value) {
        return YES.equalsIgnoreCase(value);
    }

    public static boolean getIsFavChaiSaved(boolean value) {
        return value;
    }

    public static List<Integer> daysOfWeek(Date date, int noOfDays) {
        List<Integer> daysOfWeek = new ArrayList<>();
        Calendar cal = getCalender();
        cal.setTime(date);
        daysOfWeek.add(cal.get(Calendar.DAY_OF_WEEK));
        for (int i = 1; i < noOfDays; i++) {
            cal.add(Calendar.DATE, 1);
            daysOfWeek.add(cal.get(Calendar.DAY_OF_WEEK));
        }
        return daysOfWeek;
    }

    public static List<Integer> daysOfWeek(Date startDate, Date endDate) {
        List<Integer> daysOfWeek = new ArrayList<>();
        Calendar cal = getCalender();
        cal.setTime(startDate);
        while (cal.getTime().before(endDate)) {
            daysOfWeek.add(cal.get(Calendar.DAY_OF_WEEK));
            cal.add(Calendar.DATE, 1);
        }
        return daysOfWeek;
    }

    public static void printHeapSize() {
        // Getting the runtime reference from system
        // Runtime runtime = Runtime.getRuntime();
        // int mb = 1024 * 1024;

        // System.out.println(AppUtils.getSQLFormattedDate("##### Heap utilization
        // statistics [MB] #####");

        // Print used memory
        // System.out.println(AppUtils.getSQLFormattedDate("Used Memory:" +
        // (runtime.totalMemory() -
        // runtime.freeMemory()) / mb);

        // Print free memory
        // System.out.println(AppUtils.getSQLFormattedDate("Free Memory:" +
        // runtime.freeMemory() / mb);

        // Print total available memory
        // System.out.println(AppUtils.getSQLFormattedDate("Total Memory:" +
        // runtime.totalMemory() / mb);

        // Print Maximum available memory
        // System.out.println(AppUtils.getSQLFormattedDate("Max Memory:" +
        // runtime.maxMemory() / mb);
    }

    @Deprecated
    // IMPORTANT this is specifically for crons running at 5 AM
    public static Date getCurrentBusinessDate() {
        Calendar calendar = getCalender();
        int hour = calendar.get(Calendar.HOUR_OF_DAY); // gets hour in 24h
        calendar.setTime(getCurrentDate()); // format
        if (hour <= 5) {
            calendar.add(Calendar.DATE, -1);
        }
        return calendar.getTime();
    }

    public static Date getBusinessDate() {
        return getBusinessDate(getCurrentDate());
    }

    public static Date getBusinessDate(Date date) {
        Calendar calendar = getCalender();
        int hour = calendar.get(Calendar.HOUR_OF_DAY); // gets hour in 24h
        calendar.setTime(date); // format
        if (hour < 5) {
            calendar.add(Calendar.DATE, -1);
        }
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date getDateWithoutTime(Date date) {
        Calendar calendar = getCalender();
        calendar.setTime(date); // format
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static String getDateString(Date date) {
        return getDateString(date, DATE_FORMAT_STRING);
    }

    public static String getDateString(Date date, String format) {
        return new SimpleDateFormat(format).format(date);
    }

    public static String checkBlank(String str) {
        return str != null && str.trim().length() > 0 ? str : null;
    }

    public static int getDaysDiff(Date date1, Date date2) {
        long timeDiff = date1.getTime() - date2.getTime();
        long millisecInDay = 86400000;
        return new BigDecimal(timeDiff / millisecInDay).abs().intValue();
    }

    public static int getActualDayDifference(Date date1, Date date2) {
        return date2.compareTo(date1) < 0 ? -getAbsDaysDiff(date1, date2) : getAbsDaysDiff(date1, date2);
    }

    public static Integer getAbsDaysDiff(Date date1, Date date2) {
        try {
            String format = DATE_FORMAT_STRING;
            SimpleDateFormat formatter = new SimpleDateFormat(format);

            Date date1Formatted = formatter.parse(formatter.format(date1));
            Date date2Formatted = formatter.parse(formatter.format(date2));
            return getDaysDiff(date1Formatted, date2Formatted);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    public static Boolean isBetweenDatesAbs(Date date, Date fromDate, Date toDate) {
        try {
            String format = DATE_FORMAT_STRING;
            SimpleDateFormat formatter = new SimpleDateFormat(format);
            Date dateFormatted = formatter.parse(formatter.format(date));
            Date fromDateFormatted = formatter.parse(formatter.format(fromDate));
            Date toDateFormatted = formatter.parse(formatter.format(toDate));
            return dateFormatted.compareTo(fromDateFormatted) >= 0 && dateFormatted.compareTo(toDateFormatted) <= 0;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Boolean isSameDate(Date date1, Date date2) {
        try {
            String format = DATE_FORMAT_STRING;
            SimpleDateFormat formatter = new SimpleDateFormat(format);
            Date dateFormatted1 = formatter.parse(formatter.format(date1));
            Date dateFormatted2 = formatter.parse(formatter.format(date2));
            return dateFormatted1.compareTo(dateFormatted2) == 0;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date onlyDate(Date date) {
        try {
            String format = DATE_FORMAT_STRING;
            SimpleDateFormat formatter = new SimpleDateFormat(format);
            return formatter.parse(formatter.format(date));

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static int getSecondsDiff(Date date1, Date date2) {
        long timeDiff = date1.getTime() - date2.getTime();
        return new BigDecimal(timeDiff / 1000).abs().intValue();
    }

    public static String getCoveredCustomerContact(String c) {
        return "*******" + c.substring(7, c.length());
    }

    public static String generateWebOrderId(String module) {
        return module + new SimpleDateFormat("yyyyMMddHHmmssSSS").format(getCurrentTimestamp());
    }

    public static String generateTimeString(String module) {
        return module + new SimpleDateFormat("yyyyMMddHHmmssSSS").format(getCurrentTimestamp());
    }

    public static String generateSimpleDateFormat(Date date) {
        return new SimpleDateFormat("yyyyMMdd").format(date);
    }

    public static String generateSimpleTimeFormat(Date date) {
        return new SimpleDateFormat("HH:mm").format(date);
    }

    public static String write(byte[] bytes, String rootPath, String parentDir, String fileName, Logger log) {
        try {
            // Creating the directory to store file
            File dir = new File(rootPath + File.separator + File.separator + parentDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            // Create the file on server
            File serverFile = new File(dir.getAbsolutePath() + File.separator + fileName);
            BufferedOutputStream stream = new BufferedOutputStream(new FileOutputStream(serverFile));
            try {
                stream.write(bytes);
            } catch (Exception e) {
                log.error("Encountered error while parsing file ::::", e);
            } finally {
                stream.close();
            }
            log.info("Server File Location=" + serverFile.getAbsolutePath());
            return serverFile.getAbsolutePath();
        } catch (Exception e) {
            log.error("Encountered error while parsing file ::::", e);
            return null;
        }
    }

    public static BigDecimal subtract(BigDecimal x, BigDecimal y) {
        if (x == null) {
            x = BigDecimal.ZERO;
        }
        if (y == null) {
            y = BigDecimal.ZERO;
        }
        return x.subtract(y);
    }

    public static int getDayOfWeek(Date date) {
        return getCalendar(date).get(Calendar.DAY_OF_WEEK);
    }

    public static Calendar getCalendar(Date date) {
        Calendar cal = getCalender();
        cal.setTime(date);
        return cal;
    }

    public static Date setTimeToDate(Date date, Date time) {
        Calendar cal = getCalendar(date);
        Calendar calTime = getCalendar(time);
        cal.set(Calendar.HOUR_OF_DAY, calTime.get(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, calTime.get(Calendar.MINUTE));
        cal.set(Calendar.SECOND, calTime.get(Calendar.SECOND));
        return cal.getTime();
    }

    public static Date setTimeToDate(Date date, Date time, boolean start) {
        Calendar cal = getCalendar(date);
        Calendar calTime = getCalendar(time);
        cal.set(Calendar.HOUR_OF_DAY, calTime.get(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, calTime.get(Calendar.MINUTE));
        if (start) {
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
        } else {
            cal.set(Calendar.SECOND, 59);
            cal.set(Calendar.MILLISECOND, 999);
        }
        return cal.getTime();
    }

    public static Date getUpdatedTimeInDate(Integer hour, Integer minute, Integer seconds, Date date) {
        Calendar c = getCalender();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, hour);
        c.set(Calendar.MINUTE, minute);
        c.set(Calendar.SECOND, seconds);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date timeToDate(String s) throws ParseException {
        return new SimpleDateFormat("HH:mm:ss").parse(s);
    }

    public static Date convertTimeToDate(String time) throws ParseException {
        Date d = timeToDate(time);
        d = setTimeToDate(getCurrentBusinessDate(), d);
        return d;
    }

    public static String msgTimeFormat(String s) throws ParseException {
        Date d = timeToDate(s);
        return new SimpleDateFormat("hh:mm a").format(d);
    }

    public static Date getCorrectedDateTime(Date date) {
        Calendar cal = getCalendar(date);
        if (cal.get(Calendar.HOUR_OF_DAY) < 5) {
            cal.add(Calendar.DATE, 1);
        }
        return cal.getTime();
    }

    public static boolean isGiftCard(String taxCode) {
        return AppConstants.GIFT_CARD_TAX_CODE.equals(taxCode);
    }

    public static boolean isCombo(String taxCode) {
        return AppConstants.COMBO_TAX_CODE.equals(taxCode);
    }

    public static boolean isZeroTaxProduct(String taxCode) {
        return AppConstants.ZERO_TAX_CODE.equals(taxCode);
    }

    public static BigDecimal getQuantityByYield(BigDecimal quantity, BigDecimal yield) {
        quantity = AppUtils.divideWithScale10(quantity, yield);
        quantity = AppUtils.multiplyWithScale10(quantity, BigDecimal.TEN.multiply(BigDecimal.TEN));
        return quantity;
    }

    public static int isCurrentBusinessDate(Date date) {
        Date date1 = getCurrentDateIST();
        Date date2 = getDate(date);
        return date1.compareTo(date2);
    }

    public static Date getPaidEmployeeMealStartDate(Integer startDate) {
        Calendar cal = getCalender();
        Integer current = cal.get(Calendar.DATE);
        if (startDate > current) {
            cal.add(Calendar.MONTH, -1);
        } else if (startDate == current) {
            if (cal.get(Calendar.HOUR_OF_DAY) < 5) {
                cal.add(Calendar.MONTH, -1);
            }
        }
        cal.set(Calendar.DATE, startDate);
        cal.set(Calendar.HOUR_OF_DAY, 5);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static long numberOfMealDays(Integer startDate) {
        return numberOfMealDaysFromDate(getPaidEmployeeMealStartDate(startDate));
    }

    public static long numberOfMealDaysFromDate(Date startDate) {
        return ChronoUnit.DAYS.between(startDate.toInstant(), getCurrentTimestamp().toInstant()) + 1;
    }

    public static Integer getCurrentDayofMonth() {
        Calendar calendar = getCalender();
        int hour = calendar.get(Calendar.HOUR_OF_DAY); // gets hour in 24h
        calendar.setTime(getCurrentDate()); // format
        if (hour <= 5) {
            calendar.add(Calendar.DATE, -1);
        }
        return calendar.get(Calendar.DATE);
    }

    public static Integer getCurrentMonth() {
        Calendar calendar = getCalender();
        int hour = calendar.get(Calendar.HOUR_OF_DAY); // gets hour in 24h
        calendar.setTime(getCurrentDate()); // format
        if (hour <= 5) {
            calendar.add(Calendar.DATE, -1);
        }
        return calendar.get(Calendar.MONTH) + 1;
    }

    public static Integer getCurrentMonthFromDate() {
        Date d = new Date();
        return d.getMonth();
    }

    public static Integer getCurrentYearFromDate() {
        Date d = new Date();
        return d.getYear();
    }

    public static String getCurrentMonthName() {
        String[] monthName = {"January", "February", "March", "April", "May", "June", "July", "August", "September",
                "October", "November", "December"};
        Calendar calendar = getCalender();
        int hour = calendar.get(Calendar.HOUR_OF_DAY); // gets hour in 24h
        calendar.setTime(getCurrentDate()); // format
        if (hour <= 5) {
            calendar.add(Calendar.DATE, -1);
        }
        return monthName[calendar.get(Calendar.MONTH)];
    }

    public static String getMonthName(int i) {
        // expected that month starts with 1
        String[] monthName = {"January", "February", "March", "April", "May", "June", "July", "August", "September",
                "October", "November", "December"};
        return monthName[i - 1];
    }

    public static Integer getCurrentYear() {
        Calendar calendar = getCalender();
        int hour = calendar.get(Calendar.HOUR_OF_DAY); // gets hour in 24h
        calendar.setTime(getCurrentDate()); // format
        if (hour <= 5) {
            calendar.add(Calendar.DATE, -1);
        }
        return calendar.get(Calendar.YEAR);
    }

    public static Integer getYear(Date date) {
        Calendar calendar = getCalender();
        calendar.setTime(date); // format
        return calendar.get(Calendar.YEAR);
    }

    public static Integer getMonth(Date date) {
        Calendar calendar = getCalender();
        calendar.setTime(date); // format
        return calendar.get(Calendar.MONTH) + 1;
    }

    public static Integer getDay(Date date) {
        Calendar calendar = getCalender();
        calendar.setTime(date); // format
        return calendar.get(Calendar.DATE);
    }


    /**
     * @param hours
     * @return
     */
    public static String dayPart(int hours) {
        switch (hours) {
            case 6:
            case 7:
            case 8:
            case 9:
            case 10:
            case 11:
                return "BREAKFAST";
            case 12:
            case 13:
            case 14:
                return "LUNCH";
            case 15:
            case 16:
            case 17:
            case 18:
            case 19:
                return "EVENING";
            case 20:
            case 21:
                return "DINNER";
            case 22:
            case 23:
                return "POST_DINNER";
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
                return "NIGHT";
            default:
                return null;
        }
    }

    public static Date getStartOfBusinessDay(Date businessDate) {
        Calendar c = getCalender();
        c.setTime(businessDate);
        c.set(Calendar.HOUR_OF_DAY, 5); // anything 0 - 23
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getEndOfBusinessDay(Date businessDate) {
        Calendar c = getCalender();
        c.setTime(businessDate);
        c.set(Calendar.HOUR_OF_DAY, 5); // anything 0 - 23
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);

        c.add(Calendar.DATE, 1);

        return c.getTime();
    }

    public static boolean timeIsBetween(String currentDate, String start, String end) {
        try {
            Date current = parser.parse(currentDate);
            if (current.after(parser.parse(start)) && current.before(parser.parse(end))) {
                return true;
            }
        } catch (ParseException e) {
            LOG.error("Encountered error while parsing Date ::::", e);
            return false;
        }
        return false;
    }

    public static Time getTimeAfterHour(Time time, int hour) {
        Calendar cal = getCalender();
        cal.setTime(time);
        cal.add(Calendar.HOUR, hour);
        return new Time(cal.getTime().getTime());
    }

    /**
     * @param businessDate
     * @return
     */
    public static Date getStartOfQuarter(Date businessDate) {
        int year = businessDate.getYear();
        int month = businessDate.getMonth();
        if (month == 0) {
            year = year - 1;
            month = 10;
        } else if (month >= 1 && month <= 3) {
            month = 1;
        } else if (month >= 4 && month <= 6) {
            month = 4;
        } else if (month >= 7 && month <= 9) {
            month = 7;
        } else if (month >= 10 && month <= 11) {
            month = 10;
        }

        return getStartOfMonth(year, month);
    }

    public static Date getPreviousBusinessDate() {
        Calendar calendar = getCalender();
        int hour = calendar.get(Calendar.HOUR_OF_DAY); // gets hour in 24h
        if (hour < 5) {
            calendar.add(Calendar.DATE, -1);
        }
        calendar.set(Calendar.HOUR_OF_DAY, 0); // gets hour in 24h format
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.add(Calendar.DATE, -1);
        return calendar.getTime();
    }

    public static Date getPreviousBusinessDate(Date date) {
        Calendar calendar = getCalender();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0); // gets hour in 24h format
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.add(Calendar.DATE, -1);
        return calendar.getTime();
    }

    public static boolean isBlank(String str) {
        if (str == null || str.length() < 1) {
            return true;
        }
        return false;
    }

    public static Date getBusinessDate(long timeInMillis) {
        Date date = new Date(timeInMillis);
        return getBusinessDate(date);
    }

    public static Object deepClone(Object object) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos);
            oos.writeObject(object);
            ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
            ObjectInputStream ois = new ObjectInputStream(bais);
            return ois.readObject();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Date createExpiryDate(Date currentTimestamp, int shelfLifeInDays) {
        // Expire date can have only 2 values for hour 01 and 13
        Calendar calendar = getCalender();
        calendar.setTime(currentTimestamp);
        int hour = calendar.get(Calendar.HOUR_OF_DAY); // gets hour in 24h
        if (hour < 11) {
            calendar.set(Calendar.HOUR_OF_DAY, 13);
        } else {
            calendar.set(Calendar.HOUR_OF_DAY, 1);
        }
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.add(Calendar.DATE, shelfLifeInDays + 1);
        return calendar.getTime();
    }

    public static <T, E> T clone(E object, Class<T> clazz) {
        Gson gson = new Gson();
        String str = gson.toJson(object);
        return gson.fromJson(str, clazz);
    }

    public static List<Date> createAvailableExpiryDate(Date currentTimestamp, int shelfLifeInDays) {
        List<Date> dates = new ArrayList<>();
        Date d = createExpiryDate(currentTimestamp, shelfLifeInDays);
        dates.add(d);
        Calendar calendar = getCalender();
        calendar.setTime(d);
        calendar.add(Calendar.HOUR, 12);
        dates.add(calendar.getTime());
        calendar.add(Calendar.HOUR, -24);
        dates.add(calendar.getTime());
        return dates;
    }

    public static Date formatExpiryDate(Date expiryDate) {
        Calendar calendar = getCalender();
        calendar.setTime(expiryDate);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    public static String dateToString(Date expiryDate) {
        return dateStringParser.format(expiryDate);
    }

    public static Date parseCurrentDate() {
//		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currentTime = dateStringParser.format(new Date());
        try {
            Date date = dateStringParser.parse(currentTime);
            return date;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date parseDate(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        String currentTime = simpleDateFormat.format(date);
        return AppUtils.parseDate(currentTime, simpleDateFormat);
    }

    public static List<Date> getDaysBetweenDates(Date startDate, Date endDate, boolean includeEndDate) {
        List<Date> dates = new ArrayList<Date>();
        Calendar calendar = getCalender();
        calendar.setTime(startDate);
        while (calendar.getTime().before(endDate)) {
            Date result = calendar.getTime();
            dates.add(result);
            calendar.add(Calendar.DATE, 1);
        }
        if (includeEndDate) {
            dates.add(endDate);
        }
        return dates;
    }

    public static boolean checkNPSApplicable(Date triggerTime, Date lastNPSTime) {
        if (lastNPSTime == null) {
            return true;
        }
        Calendar cal = getCalendar(lastNPSTime);
        cal.add(Calendar.DATE, 5);
        // trigger time is greater then last NPS + 5 Days
        return cal.getTime().before(triggerTime);
    }

    public static boolean isBefore(Date dateToBeChecked, Date checkedAgainst) {

        Calendar cal = getCalendar(dateToBeChecked);
        return cal.getTime().before(checkedAgainst);
    }


    public static Date getStartOfPreviousHour(Date currentTime) {
        Calendar c = getCalendar(currentTime);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.add(Calendar.HOUR, -1);
        return c.getTime();
    }

    public static Date getEndOfPreviousHour(Date currentTime) {
        Calendar c = getCalendar(currentTime);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.add(Calendar.HOUR, -1);
        return c.getTime();
    }

    public static String getAssemblyChannelName(String envType, int unitId, String relayType) {
        StringBuffer channel = new StringBuffer(envType);
        channel.append(AppConstants.ASSEMBLY_ORDER_CHANNEL).append(unitId).append("_").append(relayType);
        return channel.toString();
    }

    public static BigDecimal getMinimum(BigDecimal firstValue, BigDecimal secondValue) {
        if (firstValue == null || secondValue == null) {
            return firstValue == null ? (secondValue == null ? BigDecimal.ZERO : secondValue) : firstValue;
        }
        return firstValue.compareTo(secondValue) <= 0 ? firstValue : secondValue;

    }

    public static <T> boolean isNonEmptyList(List<T> list) {
        return list != null && !list.isEmpty();
    }

    private static String getSuffix(String contact, int suffixLength) {
        return contact.substring(contact.length() - suffixLength, contact.length());
    }

    public static String cleanUp(String name) {
        return name != null ? name.replaceAll("[^a-zA-Z]+", "") : null;
    }

    public static Date addDays(Date date, int days) {
        Calendar c = getCalender();
        c.setTime(date);
        c.add(Calendar.DATE, days);
        return c.getTime();
    }

    public static String unaccent(String src) {
        return Normalizer
                .normalize(src, Normalizer.Form.NFD)
                .replaceAll("[^\\p{ASCII}]", "");
    }

    public static Date addMonthsInDate(Date date, int month) {
        Calendar c = getCalender();
        c.setTime(date);
        c.add(Calendar.MONTH, month);
        return c.getTime();
    }

    public static boolean isChaayosCashOffer(String couponCode) {
        return CHAAYOS_CASH_OFFER_CODE.contains(couponCode);
    }

    public static Date getDate(String dateString, String format) {
        if (dateString == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.ENGLISH);
        try {
            return sdf.parse(dateString);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String formatDate(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    public static boolean checkForNewDayCloseTimeline(Date currentTime) {
        //Time currentTime = new Time(currentTimeStamp);
        // 5 AM Check
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentTime);
        cal.set(Calendar.HOUR_OF_DAY, 5);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        Date dayStartTime = cal.getTime();

        // 5 PM Check
        cal.set(Calendar.HOUR_OF_DAY, 17);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        Date minDayCloseTime = cal.getTime();
        if (currentTime.after(dayStartTime) && currentTime.before(minDayCloseTime)) {
            return false;
        } else {
            System.out.println("Ready for Day Close " + currentTime);
            return true;
        }
    }

    public static void logMemoryFootprint() {
        Runtime runtime = Runtime.getRuntime();
        int mb = 1024 * 1024;
        LOG.info("Total Memory:" + (runtime.totalMemory()) / mb);
        LOG.info("Free Memory:" + (runtime.freeMemory()) / mb);
        LOG.info("Used Memory:" + (runtime.totalMemory() - runtime.freeMemory()) / mb);
    }

    public static Date getPreviousYear(Date date) {
        Calendar cal = getCalender();
        cal.setTime(date);
        cal.add(Calendar.YEAR, -1);
        return cal.getTime();
    }

    public static boolean isAppOrder(int channelPartner) {
        return AppConstants.CHANNEL_PARTNER_DINE_IN_APP == channelPartner;
    }

    public static String dateInddthMMMFormat(Date date) {
        SimpleDateFormat f = new SimpleDateFormat("MMM");
        SimpleDateFormat f1 = new SimpleDateFormat("d");
        return f1.format(date) + "th " + f.format(date);
    }

    public static Date addMinutesToDate(Date date, int minutes) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minutes);
        return calendar.getTime();
    }

    public static Date addHoursToDate(Date date, int hours) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR, hours);
        return calendar.getTime();
    }

    public static long getDayDifference(Date start, Date end) {
        long difference_In_Time
                = end.getTime() - start.getTime();
        long difference_In_Days
                = (difference_In_Time
                / (1000 * 60 * 60 * 24))
                % 365;
        return difference_In_Days;
    }

    public static boolean sendOrderFeedbackNotification(Date date) {
        int hourOfDay = getHour(date);
        return hourOfDay >= 9 && hourOfDay <= 21 ? true : false;
    }

    public static int getHour(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.HOUR_OF_DAY);
    }

    public static int getMinute(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.MINUTE);
    }

    public static int getSecond(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.SECOND);
    }

    public static int getMinDiffernce(Date start, Date end) {
        int downtime = ((getHour(end) * 60) + getMinute(end)) - ((getHour(start) * 60) + getMinute(start));
        int down = 0;
        if (downtime >= 0) {
            down = downtime;
        } else {
            down = 1440 + downtime;
        }
        return down;
    }

    public static double getTimeDiffernceInMinutes(Date start, Date end) {
        double first = (getHour(end) * 60) + getMinute(end) + (getSecond(end) == 0 ? 0 : (double) getSecond(end) / 60);
        double second = (getHour(start) * 60) + getMinute(start) + (getSecond(start) == 0 ? 0 : (double) getSecond(start) / 60);
        double diff = first - second;
        return diff;
    }

    public static int getWeekDayNumber(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.DAY_OF_WEEK);
    }

    public static Date getCurrentBusinessDayStartTime() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getStartOfDay(new Date()));
        cal.set(Calendar.HOUR_OF_DAY, 5);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    public static boolean checkDayCloseTime(Date time, String start, String end) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("hh:mm:ss", Locale.ENGLISH);
        DateFormat formatter = new SimpleDateFormat("E MMM dd HH:mm:ss Z yyyy");
        Date date = (Date) formatter.parse(time.toString());
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        String instanceDate = cal.get(Calendar.HOUR_OF_DAY) + ":" + cal.get(Calendar.MINUTE) + ":" + cal.get(Calendar.SECOND);
        String startDate = start;
        String endDate = end;
        Date startTimeDate = sdf.parse(startDate);
        Date endTimeDate = sdf.parse(endDate);
        Date instanceTimeDate = sdf.parse(instanceDate);
        Calendar timeToCheck = Calendar.getInstance();
        timeToCheck.setTime(instanceTimeDate);
        timeToCheck.set(Calendar.HOUR_OF_DAY, cal.get(Calendar.HOUR_OF_DAY));
        Calendar startTime = Calendar.getInstance();
        startTime.setTime(startTimeDate);
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(endTimeDate);
        LOG.info(timeToCheck.get(Calendar.SECOND) + "");
        boolean x = (timeToCheck.getTimeInMillis() >= endTime.getTimeInMillis() && timeToCheck.getTimeInMillis() <= startTime.getTimeInMillis());
        boolean y = startTime.getTimeInMillis() >= endTime.getTimeInMillis();

        if (timeToCheck.getTimeInMillis() >= startTime.getTimeInMillis() && timeToCheck.getTimeInMillis() <= endTime.getTimeInMillis()) {
            return true;
        } else if (!(timeToCheck.getTimeInMillis() >= endTime.getTimeInMillis() && timeToCheck.getTimeInMillis() <= startTime.getTimeInMillis()) &&
                startTime.getTimeInMillis() >= endTime.getTimeInMillis()) {
            return true;
        } else {
            return false;
        }
    }

    public static TimeZone getTimeZone() {
        return TimeZone.getTimeZone("Asia/Kolkata");
    }

    public static void main(String[] args) throws ParseException {
        Calendar cal = Calendar.getInstance();
        System.out.println(getDateInMonth(new Date()));
        String startDate = "24/06/2022";
        String endDate = "24/06/2022";
        Date date1 = new SimpleDateFormat("dd/MM/yyyy").parse(startDate);
        Date date2 = new SimpleDateFormat("dd/MM/yyyy").parse(endDate);
        Integer daysLeft = getActualDayDifference(getCurrentDate(), date2);
        Date d = getCurrentTimestamp();
        System.out.println("days left --->" + daysLeft);
    }

    public static String getDateInMonth(Date date) {
        Calendar calendar = getCalendar(date);
        return new SimpleDateFormat("MMM").format(calendar.getTime()) + " " + getDay(date);
    }

    public static String getCalendarDate(Date date) {
        Calendar calendar = getCalendar(date);
        return new SimpleDateFormat("dd MMM yyyy").format(calendar.getTime());
    }

    public static boolean isValidEmail(String emailStr) {
        Matcher matcher = AppConstants.VALID_EMAIL_ADDRESS_REGEX.matcher(emailStr);
        return matcher.find();
    }

    public static String getMonthDayTimeString(Date date) {
        // April 13, 11:33am
        String month = AppUtils.getMonthName(AppUtils.getMonth(date));
        Integer day = AppUtils.getDay(date);
        Integer hour = AppUtils.getHour(date);
        Integer minute = AppUtils.getMinute(date);
        String meridian = (hour >= 12) ? "pm" : "am";
        hour = (hour > 12) ? Math.abs(hour - 12) : hour;
        return month + " " + day + ", " + hour + ":" + minute + meridian;
    }

    public static String getDayMonthYear(String date) {
        return getDayMonthYear(AppUtils.getDate(date, AppUtils.DATE_FORMAT_STRING));
    }

    public static String getDayMonthYear(Date date) {
        return AppUtils.getDay(date) + " " + AppUtils.getMonthName(AppUtils.getMonth(date)).substring(0, 3) + " " + AppUtils.getYear(date);
    }

    public static Date getNextExpireTimeLimit() {
        Calendar calendar = getCalender();
        int hour = calendar.get(Calendar.HOUR_OF_DAY); // gets hour in 24h
        if (hour < 5) {
            calendar.add(Calendar.DATE, -1);
        }
        calendar.add(Calendar.DATE, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 5);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();

    }

    public static boolean isShortExpire(Date systemExpireTime, Date productExpiryDate) {
        if (productExpiryDate == null) {
            return false;
        }
        return productExpiryDate.before(systemExpireTime);
    }

    public static DateTime dateTimeWithSec(String dateTime) {
        return new DateTime(new DateTime(dateTime).toString(AppConstants.DATE_TIME_ISO_FORMATTER_IN_SEC));
    }

    public static boolean isUnitEligibleForDayClosureNotification(String unitName) {
        return AppConstants.IGNORE_UNIT_KEYWORDS.stream().anyMatch(unitName::contains);
    }

    public static int getPreviousMonthFromDate(Date date) {
        Calendar c = getCalendar(date);
        c.add(Calendar.MONTH, -1);
        return c.get(Calendar.MONTH) + 1;
    }

    public static int getPreviousYearIfChanged(Date date) {
        Calendar c = getCalendar(date);
        c.add(Calendar.MONTH, -1);
        return c.get(Calendar.YEAR);
    }

    public static Date getStartDateOfMonth(int year, int month) {
        Calendar c = getCalender();
        c.set(year, month - 1, 1, 0
                , 0, 0);
        return c.getTime();
    }


    public static String generateRandomOrderId(int length) {
        return RandomStringUtils.randomNumeric(length);

    }

    public static Regions getRegion(EnvType env) {
		switch (env) {
		case DEV:
		case PROD:
			return Regions.EU_WEST_1;
		case SPROD:
			return Regions.AP_SOUTH_1;
		default:
			return Regions.EU_WEST_1;
		}
	}

    public static String serialize(Serializable serializable) throws JMSException {
		if (serializable == null) {
			return null;
		} else {
			ObjectOutputStream objectOutputStream = null;

			String serializedString;
			try {
				ByteArrayOutputStream bytesOut = new ByteArrayOutputStream();
				objectOutputStream = new ObjectOutputStream(bytesOut);
				objectOutputStream.writeObject(serializable);
				objectOutputStream.flush();
				serializedString = Base64.encodeAsString(bytesOut.toByteArray());
			} catch (IOException ex) {
				LOG.error("IOException: cannot serialize objectMessage", ex);
				throw convertExceptionToMessageFormatException(ex);
			} finally {
				if (objectOutputStream != null) {
					try {
						objectOutputStream.close();
					} catch (IOException ex) {
						LOG.warn(ex.getMessage());
					}
				}
			}

			return serializedString;
		}
	}

    private static MessageFormatException convertExceptionToMessageFormatException(Exception e) {
		MessageFormatException ex = new MessageFormatException(e.getMessage());
		ex.initCause(e);
		return ex;
	}

    public static String generateRandomOrderId() {
		return RandomStringUtils.randomNumeric(16);
	}

    public static String generateRandomAlphaNumericCode(int value) {
		return RandomStringUtils.randomAlphanumeric(value).toUpperCase();
	}

    public static BaseHeaders getBaseHeaders(HttpServletRequest request){
        return BaseHeaders.builder()
                .unitId(request.getIntHeader("uid"))
                .employeeId(request.getIntHeader("eid"))
                .companyId(request.getIntHeader("compid"))
                .source(request.getHeader("src"))
                .build();
    }

    public static WsType getTypeForProductType(int typeId, int brandId) {
		if (brandId == 3 && typeId != 12) {
			return WsType.GNT;
		}
		switch (typeId) {
		case 5:
			return WsType.HOT;
		case 6:
			return WsType.COLD;
		case 7:
			return WsType.FOOD;
		}
		return WsType.OTHERS;
	}

    public static String getYOrN(boolean value){
        return (Objects.nonNull(value) && value) ? AppConstants.YES : AppConstants.NO;
    }

    public static String getValidContactNUmber(String contactNUmber){
        try{
            if(contactNUmber.length()==10){
                return contactNUmber;
            }
            else{
                contactNUmber = contactNUmber.substring(contactNUmber.length()-10);
                return contactNUmber;
            }
        }
        catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public static int getLoyalTeas(int loyalteaPoints){
        return loyalteaPoints/60;
    }

    public static int getRemainderLoyalTeaPoints(int loyalteaPoints){
        return loyalteaPoints % 60;
    }
    public static String getMilkVariantPaidAddonPrefix(String paidAddonName){
        if(paidAddonName.toLowerCase().contains("oats")){
            return AppConstants.oatMilkPrefix;
        }

        return AppConstants.oatMilkPrefix;

    }

    public static Integer getMilkVariantPaidAddonSCMProduct(String paidAddonName,Integer brandId){
        if(Objects.isNull(brandId)){
            brandId = AppConstants.CHAAYOS_BRAND_ID;
        }
        switch (brandId){
            case 1:
                if(paidAddonName.toLowerCase().contains("oat")){
                    return AppConstants.OAT_MILK_SCM_PRODUCT_ID;
                }
            case 6:
                if(paidAddonName.toLowerCase().contains("oat")){
                    return AppConstants.OAT_MILK_DOHFUL_SCM_PRODUCT_ID;
                }else if(paidAddonName.toLowerCase().contains("almond")){
                    return AppConstants.ALMOND_MILK_DOHFUL_SCM_PRODUCT_ID;
                }
            default:
                return AppConstants.OAT_MILK_SCM_PRODUCT_ID;
        }

    }

    public static String getMilkVariantPaidAddonSCMProductName(String paidAddonName,Integer brandId){
        if(Objects.isNull(brandId)){
            brandId = AppConstants.CHAAYOS_BRAND_ID;
        }
        switch (brandId){
            case 1:
                if(paidAddonName.toLowerCase().contains("oat")){
                    return AppConstants.OAT_MILK_SCM_PRODUCT_NAME;
                }
            case 6:
                if(paidAddonName.toLowerCase().contains("oat")){
                    return AppConstants.OAT_MILK_DOHFUL_SCM_PRODUCT_NAME;
                }else if(paidAddonName.toLowerCase().contains("almond")){
                    return AppConstants.ALMOND_MILK_DOHFUL_SCM_PRODUCT_NAME;
                }
            default:
                return AppConstants.OAT_MILK_SCM_PRODUCT_NAME;
        }

    }

    public static String getOnlyIntegerValueFromString(String str){
        return str.replaceAll("^\\D+", "");
    }

    public static boolean isDineInOrAppOrder(Integer partnerId){
        return CHAAYOS_DINE_IN_OR_SNP_ORDERS.contains(partnerId);
    }

    public static boolean isCafeorTakeAway(Integer partnerId){
        return CHAAYOS_CAFE_OR_TAKEAWAY_ORDERS.contains(partnerId);
    }
}


