package com.stpl.tech.kettle.util.consumptionHelper;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.cache.RecipeCache;
import com.stpl.tech.kettle.domain.model.MilkVariant;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.inventory.model.ProductQuantityData;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Log4j2
public class ItemConsumptionHelper {

	@Autowired
	private RecipeCache recipeCache;
	@Autowired
	private ProductCache productCache;

	public void calculateConsumption(Order o, Map<Integer, Consumable> map, Integer deliveryUnitId) {
		for (OrderItem item : o.getOrders()) {
			String recipeProfile = getRecipeProfile(item.getProductId(), o.getUnitId(), deliveryUnitId, o.getSource(),
					item.getDimension(), o.getBrandId());
			boolean taxable = productCache.getProductById(item.getProductId()).isTaxableCogs();
			parseItem(o, item, o.getSource(), map, o.getUnitId(), deliveryUnitId, recipeProfile, o.getBrandId(),
					taxable);
		}
	}

	private String getRecipeProfile(int productId, int unitId, int deliveryUnitId, String source, String dimension,
			int brandId) {
		String recipeProfile = null;

		Product product = productCache.getProductById(productId);
		if (product.getClassification() == ProductClassification.FREE_ADDON
				|| product.getClassification() == ProductClassification.PAID_ADDON) {
			recipeProfile = AppConstants.DEFAULT_RECIPE_PROFILE;
		} else {
			if (UnitCategory.COD.equals(UnitCategory.valueOf(source))) {
				// TODO check this if it breaks anything
				if (brandId == AppConstants.CHAAYOS_BRAND_ID) {
					try {
						recipeProfile = recipeCache.getUnitProductProfileDetails(unitId, productId, dimension);
					} catch (Exception e) {
						recipeProfile = recipeCache.getUnitProductProfileDetails(deliveryUnitId, productId, dimension);
					}

				} else {
					try {
						recipeProfile = recipeCache.getUnitProductProfileDetails(deliveryUnitId, productId, dimension);
					} catch (Exception e) {
						recipeProfile = recipeCache.getUnitProductProfileDetails(unitId, productId, dimension);
					}
				}
			} else {
				recipeProfile = recipeCache.getUnitProductProfileDetails(unitId, productId, dimension);
			}
		}
		if (Objects.isNull(recipeProfile)) {
			log.error(
					"############# Could Not Find Recipe Profile for Unit Id {}, Delivery Unit Id {}, Product Id {}, Dimension Id {}, Order Source {}, Brand Id {} ",
					unitId, deliveryUnitId, productId, dimension, source, brandId);
			return AppConstants.DEFAULT_RECIPE_PROFILE;
		}
		return recipeProfile;
	}

	private void parseItem(Order o, OrderItem item, String source, Map<Integer, Consumable> map, int unitId,
                           Integer deliveryUnitId, String recipeProfile, int brandId, boolean taxable) {
		parseComposition(o, item, source, map, unitId, deliveryUnitId, recipeProfile, brandId, taxable);
		addSupplements(item.getTakeAway(), item.getProductId(), item.getDimension(), new BigDecimal(item.getQuantity()),
				source, map, recipeProfile, taxable,item.getMilkVariant());
	}

	private void parseComposition(Order o, OrderItem item, String source, Map<Integer, Consumable> map,
                                  int unitId, Integer deliveryUnitId, String recipeProfile, int brandId, boolean taxable) {

		Consumable c = null;
		OrderItemComposition composition = item.getComposition();
		String consumptionHelperProfile = Objects.nonNull(item.getMilkVariant()) ?
				AppUtils.getMilkVariantPaidAddonPrefix(item.getMilkVariant().getProductName()) + recipeProfile : recipeProfile;

		if (Objects.nonNull(composition)) {
			// Variants
			if (!CollectionUtils.isEmpty(composition.getVariants())) {
				if (DesiChaiConsumptionHelper.getInstance(consumptionHelperProfile).getProducts().contains(item.getProductId())) {
					List<Consumable> consumptions = DesiChaiConsumptionHelper.getInstance(consumptionHelperProfile)
							.getConsumption(item, composition.getVariants());
					for (Consumable con : consumptions) {
						add(con, map, taxable);
					}

				} else {
					for (IngredientVariantDetail detail : composition.getVariants()) {
						c = createConsumable(detail, item.getQuantity(), taxable);
						add(c, map, taxable);
					}

				}
			}

			// products
			if (!CollectionUtils.isEmpty(composition.getProducts())) {
				for (IngredientProductDetail detail : composition.getProducts()) {
					c = createConsumable(detail, BigDecimal.valueOf(item.getQuantity()), taxable);
					add(c, map, taxable);
				}
			}

			// Add-ons
			if (!CollectionUtils.isEmpty(composition.getAddons())) {
				for (IngredientProductDetail detail : composition.getAddons()) {
					// Add-ons are menu products
					addSupplements(item.getTakeAway(), detail.getId(),
							detail.getDimension().getCode(),
							detail.getQuantity().multiply(new BigDecimal(item.getQuantity())), source, map,
							recipeProfile, taxable,null);
				}
			}

			// Menu Products
			if (!CollectionUtils.isEmpty(composition.getMenuProducts())) {
				for (OrderItem i : composition.getMenuProducts()) {
					String profile = getRecipeProfile(i.getProductId(), unitId, deliveryUnitId, source,
							i.getDimension(), brandId);
					boolean taxable1 = productCache.getProductById(i.getProductId()).isTaxableCogs();
					parseItem(o, i, source, map, unitId, deliveryUnitId, profile, brandId, taxable1);
				}
			}
		}
	}

	private  Consumable checkForSpecialMilkVariant(Consumable c ,  MilkVariant milkVariant){
		if(Objects.nonNull(milkVariant) && (c.getProductId() == AppConstants.SCM_MILK_PRODUCT_ID ||
				c.getProductId() == AppConstants.DOHFUL_SCM_MILK_PRODUCT_ID)){
			c.setProductId(Objects.nonNull(milkVariant.getScmProductId()) ? milkVariant.getScmProductId() : AppConstants.OAT_MILK_SCM_PRODUCT_ID);
			c.setName(milkVariant.getScmProductName());
		}
		return c;

	}

	private void addSupplements(Boolean takeAway, int id, String dia, BigDecimal qty, String source,
			Map<Integer, Consumable> map, String recipeProfile, boolean taxable , MilkVariant milkVariant) {
		// log.info("Looking Consumption for takeaway {}, id {}, dia {}, qty {}, source
		// {}, profile {}", takeAway, id, dia,
		// qty, source, recipeProfile);
		ProductRecipeKey productRecipeKey = new ProductRecipeKey(id, dia, recipeProfile);
		RecipeDetail r = recipeCache.getRecipeByProductRecipeKey(productRecipeKey);
		if (Objects.isNull(r)) {
			ProductRecipeKey prodRecipeKey = new ProductRecipeKey(id, dia, AppConstants.DEFAULT_RECIPE_PROFILE);
			r = recipeCache.getRecipeByProductRecipeKey(prodRecipeKey);
		}
		if (Objects.nonNull(r)) {
			// log.info("Found Consumption for takeaway {}, id {}, dia {}, qty {}, source
			// {}, profile {}", takeAway, id, dia, qty,
			// source, recipeProfile);
			r.getIngredient().getComponents().stream().
					forEach(p -> add(checkForSpecialMilkVariant(createConsumable(p, qty, taxable),milkVariant), map, taxable));
			switch (UnitCategory.valueOf(source)) {
			case COD:
				addSupplementaryItem(qty, map, r.getDeliveryConsumables(), taxable);
				break;
			case CAFE:
				if (takeAway != null && takeAway) {
					addSupplementaryItem(qty, map, r.getTakeawayConsumables(), taxable);
				} else {
					addSupplementaryItem(qty, map, r.getDineInConsumables(), taxable);
				}
				break;
			case TAKE_AWAY:
				addSupplementaryItem(qty, map, r.getTakeawayConsumables(), taxable);
				break;
			default:
				break;
			}
		}
	}

	private void add(Consumable c, Map<Integer, Consumable> map, boolean taxable) {
		Consumable e = map.get(c.getProductId());
		if (Objects.nonNull(e)) {

			if (taxable) {
				e.setTaxableQuantity(AppUtils.add(e.getTaxableQuantity(), c.getQuantity()));
			}
			e.setQuantity(e.getQuantity().add(c.getQuantity()));
		} else {
			map.put(c.getProductId(), c);
		}
	}

	private Consumable createConsumable(IngredientVariantDetail detail, int qty, boolean taxable) {
		return createConsumable(detail, new BigDecimal(qty), taxable);
	}

	private void addSupplementaryItem(BigDecimal qty, Map<Integer, Consumable> map,
			List<IngredientProductDetail> ingredients, boolean taxable) {

		Consumable c = null;
		for (IngredientProductDetail i : ingredients) {
			c = createConsumable(i, qty, taxable);
			add(c, map, taxable);
		}
	}

	private Consumable createConsumable(IngredientProductDetail detail, BigDecimal qty, boolean taxable) {
		Consumable c = new Consumable();
		c.setProductId(detail.getProduct().getProductId());
		if (Objects.nonNull(detail.getQuantity())) {
		if (taxable) {
			c.setTaxableQuantity(detail.getQuantity().multiply(qty));
		}
			c.setQuantity(detail.getQuantity().multiply(qty));
		}
		c.setUom(detail.getUom() == null ? null : detail.getUom().name());
		if (Objects.nonNull(detail.getProduct())) {
			c.setName(detail.getProduct().getName());
		}
		return c;
	}

	private Consumable createConsumable(IngredientVariantDetail detail, BigDecimal qty, boolean taxable) {
		Consumable c = new Consumable();
		c.setProductId(detail.getId());
		if (Objects.nonNull(detail.getQuantity())) {
			if (taxable) {
				c.setTaxableQuantity(detail.getQuantity().multiply(qty));
			}
			c.setQuantity(detail.getQuantity().multiply(qty));
		}
		c.setUom(detail.getUom() == null ? null : detail.getUom().name());
		c.setName(detail.getAlias());
		return c;
	}

	public Map<Integer, ProductQuantityData> getCriticalConsumption(Order o, Integer deliveryUnitId) {

		if (OrderStatus.CANCELLED.equals(o.getStatus())) {
			return new HashMap<>();
		}
		Map<Integer, ProductQuantityData> finalData = new HashMap<>();
		for (OrderItem item : o.getOrders()) {
			if (item.getRecipeId() == 0) {
				continue;
			}
			String recipeProfile = getRecipeProfile(item.getProductId(), o.getUnitId(), deliveryUnitId, o.getSource(),
					item.getDimension(), o.getBrandId());
			boolean taxable = productCache.getProductById(item.getProductId()).isTaxableCogs();
			Map<Integer, Consumable> map = new HashMap<>();
			parseItem(o, item, o.getSource(), map, o.getUnitId(), deliveryUnitId, recipeProfile, o.getBrandId(),
					taxable);
			if (map != null && map.size() > 0) {
				for (Integer key : map.keySet()) {
					// if (criticalProducts.contains(key)) {
					// let consumption enabled for non critical products also
					Consumable consumable = map.get(key);
					if (!finalData.containsKey(key)) {
						finalData.put(key, new ProductQuantityData(key, consumable.getQuantity(), consumable.getUom()));
					} else {
						finalData.get(key).addQuantity(consumable.getQuantity());
					}
					// }
				}
			}
		}
		return finalData;
	}
}
