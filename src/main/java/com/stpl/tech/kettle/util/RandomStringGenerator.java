package com.stpl.tech.kettle.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class RandomStringGenerator {
	private static final List<Integer> numbers = new ArrayList<Integer>();
	private static final List<Integer> alphabets = new ArrayList<Integer>();

	static {
		for (int i = 0; i < 10; i++) {
			numbers.add(i);
		}
		for (int i = 65; i < 91; i++) {
			if (i == 73 || i == 79) {
				// skip i and o
				continue;
			}
			alphabets.add(i);
		}
	}

	public String getRandonNumber(int length) {
		Collections.shuffle(numbers);
		String result = "";
		for (int i = 0; i < length; i++) {
			result += numbers.get(i).toString();
		}
		return result;
	}

	public Set<String> getRandomCode(String prefix, int length, int setSize, Set<String> exclusionSet) {
		Set<String> codeSet = new HashSet<>();
		String code = "";
		int maxPermutation = 1;
		for (int i = 0; i < length; i++) {
			maxPermutation = maxPermutation * 26;
		}
		while (codeSet.size() < setSize && codeSet.size() <= maxPermutation) {
			code = prefix + getRandomCode(length);
			if (!exclusionSet.contains(code)) {
				codeSet.add(code);
			}
		}
		return codeSet;
	}

	public String getRandomSingleCode(String prefix, int length) {
		String code = "";
		int maxPermutation = 1;
		for (int i = 0; i < length; i++) {
			maxPermutation = maxPermutation * 26;
		}
		code = prefix + getRandomCode(length);
		return code;
	}

	public String getRandomCode(int length) {
		Collections.shuffle(alphabets);
		String result = "";
		for (int i = 0; i < length; i++) {
			if ((int) (Math.random() * 10) < 5) {
				// to skip 0 and 1
				result += String
						.valueOf(alphabets.get(i) % 10 > 1 ? alphabets.get(i) % 10 : (alphabets.get(i) + 2) % 10);
			} else {
				result += Character.toString((char) alphabets.get(i).intValue());
			}
		}
		return result;
	}
}
