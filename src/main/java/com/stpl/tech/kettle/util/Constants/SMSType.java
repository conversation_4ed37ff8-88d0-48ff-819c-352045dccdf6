package com.stpl.tech.kettle.util.Constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@AllArgsConstructor
@Getter
public enum SMSType {
    OTP("OTP", "sms.otp.client"), TRANSACTIONAL("TRANSAC<PERSON>ONAL", "sms.transactional.client"), PROMOTIONAL("PROMOTIONAL",
            "sms.promotional.client"), OPT_VIA_IVR("OPT_VIA_IVR","sms.otp.client");

    private final String key;
    private final String value;


}
