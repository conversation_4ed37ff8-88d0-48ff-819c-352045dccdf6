package com.stpl.tech.kettle.util.comparator;

import com.stpl.tech.kettle.data.kettle.UnitToDeliveryPartnerMappings;

import java.util.Comparator;

public class UnitDeliveryMappingComparator implements Comparator<UnitToDeliveryPartnerMappings> {
    @Override
    public int compare(UnitToDeliveryPartnerMappings o1, UnitToDeliveryPartnerMappings o2) {
        int priorityComparision = o2.getPriority() - o1.getPriority();
        if (priorityComparision == 0) {
            return o2.getId() - o1.getId();
        } else {
            return priorityComparision;
        }
    }
}
