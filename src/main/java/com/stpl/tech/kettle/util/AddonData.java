package com.stpl.tech.kettle.util;

import java.math.BigDecimal;

public interface AddonData {
	public int getProductId();

	public void setProductId(int addonId);

	public String getSource();

	public void setSource(String source);

	public String getDimension();

	public void setDimension(String dimension);

	public BigDecimal getQuantity();

	public void setQuantity(BigDecimal quantity);

	public String getName();

	public void setName(String name);

	public String getType();

	public void setType(String type);

	public String getUom();

	public void setUom(String uom);

	public String getDefaultSetting();

	public void setDefaultSetting(String defaultSetting);
	public Integer getOrderItemAddonId();

}
