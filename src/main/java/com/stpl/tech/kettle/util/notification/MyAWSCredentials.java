package com.stpl.tech.kettle.util.notification;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;

public class MyAWSCredentials implements AWSCredentialsProvider {
	 private String getAWSAccessKeyId() {
	        return "********************";
	    }

	    private String  getAWSSecretKey(){
	        return "oqPiJswD/nuFTlL++KYivNq68D7n+KOYgKxNRpq0";
	    }

	    @Override
	    public AWSCredentials getCredentials() {
	        AWSCredentials awsCredentials = new BasicAWSCredentials(getAWSAccessKeyId(), getAWSSecretKey());
	        return awsCredentials;
	    }

	    @Override
	    public void refresh() {

	    }
}
