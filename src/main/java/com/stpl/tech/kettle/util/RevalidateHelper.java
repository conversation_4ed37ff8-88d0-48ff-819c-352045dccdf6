package com.stpl.tech.kettle.util;

import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.service.RevalidationService;
import com.stpl.tech.kettle.cache.OfferUsageCache;

public class RevalidateHelper {
    public static String revalidate(RevalidationReason reason, Order order, RevalidationService revalidationService) {
        switch (reason) {
            case COUPON_MAX_USAGE_AND_BY_CUSTOMER:
                return revalidationService.revalidateCouponMaxUsageAndForCustomer(order);
            default:
                break;
        }
        return null;
    }
}
