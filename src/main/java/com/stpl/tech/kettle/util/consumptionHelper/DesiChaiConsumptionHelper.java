package com.stpl.tech.kettle.util.consumptionHelper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;

import lombok.extern.log4j.Log4j2;

@Log4j2
public abstract class DesiChaiConsumptionHelper {

	private static final Map<String, DesiChaiConsumptionHelper> INSTANCES = new HashMap<>();

	public static final int HOT_CUP_SCM_PRODUCT_ID = 100184;

	private static final String PROFILE_INSTANCE_CREATOR = "PROFILE_INSTANCE_CREATOR";

	private static final String PROFILE_INSTANCE_CREATOR_DOUBLE_CHECK = "PROFILE_INSTANCE_CREATOR_DOUBLE_CHECK";

	private static final String REGULAR_MILK = "Regular";
	private static final String DASH_MILK = "Dash";
	private static final String DOUBLE_DASH_MILK = "Double Dash";
	private static final String FULL_DOODH_MILK = "Full Doodh";
	private static final String DESI_DOODH_KAM_MILK = "Doodh Kum";
	private static final String DESI_PAANI_KAM_MILK = "Paani Kum";
	/**
	 *
	 */
	protected static final int KULHAD_CHAI = 80;
	/**
	 *
	 */
	protected static final int DESI_PAANI_KAM = 50;
	/**
	 *
	 */
	protected static final int CUTTING_CHAI = 30;
	/**
	 *
	 */
	protected static final int DESI_DOODH_KAM = 12;
	/**
	 *
	 */
	protected static final int FULL_DOODH = 11;
	/**
	 *
	 */
	public static final int DESI_CHAI = 10;
	/**
	 *
	 */
	protected static final int DESI_CHAI_DASH_MILK = 14;
	/**
	 *
	 */
	protected static final int DESI_CHAI_DOUBLE_DASH_MILK = 15;
	/**
	 *
	 */
	protected static final int LEMON_GRASS_GINGER_FULL_DOODH = 1292;
	/**
	 *
	 */
	protected static final int LEMON_GRASS_GINGER_PAANI_KUM = 1293;
	/**
	 *
	 */
	protected static final int LEMON_GRASS_GINGER_DOODH_KUM = 1294;
	protected static final Integer PLACEHOLDER_LEMON_GRASS_GINGER = 1376;
	/**
	 *
	 */
	protected static final int LEMON_GRASS_GINGER = 1282;
	protected static final Integer PLACEHOLDER_DESI_CHAI = 1375;

	/**
	 *
	 */
	protected static final String PATTI = "Patti";
	/**
	 *
	 */
	protected static final String SUGAR = "Sugar";
	/**
	 *
	 */
	protected static final String REGULAR_SUGAR = "Regular Sugar";
	/**
	 *
	 */
	protected static final String REGULAR_PATTI = "Regular Patti";
	/**
	 *
	 */
	protected static final String KADAK = "Kadak";
	/**
	 *
	 */
	protected static final String KADAK_NO_SUGAR = "Kadak - No Sugar";
	/**
	 *
	 */
	protected static final String KADAK_REGULAR_SUGAR = "Kadak - Regular Sugar";
	/**
	 *
	 */
	protected static final String REGULAR_PATTI_NO_SUGAR = "Regular Patti - No Sugar";
	/**
	 *
	 */
	protected static final String REGULAR_PATTI_REGULAR_SUGAR = "Regular Patti - Regular Sugar";
	/**
	 *
	 */
	protected static final String NONE = "None";
	/**
	 *
	 */
	protected static final String BADI_KETLI = "BadiKetli";
	/**
	 *
	 */
	protected static final String CHOTI_KETLI = "ChotiKetli";
	/**
	 *
	 */
	protected static final String MINI_KETLI = "MiniKetli";

	/**
	 *
	 */
	protected static final String FULL = "Full";
	/**
	 *
	 */
	public static final String REGULAR = "Regular";
	public static final String TEA_FOR_2 = "TF2";
	public static final String TEA_FOR_4 = "TF4";


	public static final Integer SCM_PRODUCT_MILK = 100234;

	protected static final Set<Integer> products;
	protected static final Map<Integer, Integer> productsMap;
	protected static final Map<Integer, Integer> placeHolderProducts;
	protected static final Map<Integer, Map<String, Integer>> productsLookupMap;

	static {
		products = new HashSet<>();
		products.add(DESI_CHAI);
		products.add(DESI_CHAI_DASH_MILK);
		products.add(DESI_CHAI_DOUBLE_DASH_MILK);
		products.add(FULL_DOODH);
		products.add(DESI_DOODH_KAM);
		products.add(CUTTING_CHAI);
		products.add(DESI_PAANI_KAM);
		products.add(KULHAD_CHAI);
		products.add(LEMON_GRASS_GINGER);
		products.add(LEMON_GRASS_GINGER_DOODH_KUM);
		products.add(LEMON_GRASS_GINGER_FULL_DOODH);
		products.add(LEMON_GRASS_GINGER_PAANI_KUM);
		productsMap = new HashMap<>();
		productsMap.put(DESI_CHAI, DESI_CHAI);
		productsMap.put(DESI_CHAI_DASH_MILK, DESI_CHAI);
		productsMap.put(DESI_CHAI_DOUBLE_DASH_MILK, DESI_CHAI);
		productsMap.put(FULL_DOODH, DESI_CHAI);
		productsMap.put(DESI_DOODH_KAM, DESI_CHAI);
		productsMap.put(DESI_PAANI_KAM, DESI_CHAI);
		productsMap.put(LEMON_GRASS_GINGER, LEMON_GRASS_GINGER);
		productsMap.put(LEMON_GRASS_GINGER_DOODH_KUM, LEMON_GRASS_GINGER);
		productsMap.put(LEMON_GRASS_GINGER_FULL_DOODH, LEMON_GRASS_GINGER);
		productsMap.put(LEMON_GRASS_GINGER_PAANI_KUM, LEMON_GRASS_GINGER);

		placeHolderProducts = new HashMap<Integer, Integer>();
		placeHolderProducts.put(PLACEHOLDER_DESI_CHAI, SCM_PRODUCT_MILK);
		placeHolderProducts.put(PLACEHOLDER_LEMON_GRASS_GINGER, SCM_PRODUCT_MILK);
		productsLookupMap = new HashMap<>();
		productsLookupMap.put(PLACEHOLDER_DESI_CHAI, new HashMap<>());
		productsLookupMap.put(PLACEHOLDER_LEMON_GRASS_GINGER, new HashMap<>());

		productsLookupMap.get(PLACEHOLDER_DESI_CHAI).put(REGULAR_MILK, DESI_CHAI);
		productsLookupMap.get(PLACEHOLDER_DESI_CHAI).put(DASH_MILK, DESI_CHAI_DASH_MILK);
		productsLookupMap.get(PLACEHOLDER_DESI_CHAI).put(DOUBLE_DASH_MILK, DESI_CHAI_DOUBLE_DASH_MILK);
		productsLookupMap.get(PLACEHOLDER_DESI_CHAI).put(DESI_DOODH_KAM_MILK, DESI_DOODH_KAM);
		productsLookupMap.get(PLACEHOLDER_DESI_CHAI).put(DESI_PAANI_KAM_MILK, DESI_PAANI_KAM);
		productsLookupMap.get(PLACEHOLDER_DESI_CHAI).put(FULL_DOODH_MILK, FULL_DOODH);
		productsLookupMap.get(PLACEHOLDER_LEMON_GRASS_GINGER).put(REGULAR_MILK, LEMON_GRASS_GINGER);
		productsLookupMap.get(PLACEHOLDER_LEMON_GRASS_GINGER).put(DESI_DOODH_KAM_MILK, LEMON_GRASS_GINGER_DOODH_KUM);
		productsLookupMap.get(PLACEHOLDER_LEMON_GRASS_GINGER).put(DESI_PAANI_KAM_MILK, LEMON_GRASS_GINGER_PAANI_KUM);
		productsLookupMap.get(PLACEHOLDER_LEMON_GRASS_GINGER).put(FULL_DOODH_MILK, LEMON_GRASS_GINGER_FULL_DOODH);

	}

	/**
	 * @return
	 */
	public static DesiChaiConsumptionHelper getInstance(String profile) {
		if (!INSTANCES.containsKey(profile)) {
			synchronized (PROFILE_INSTANCE_CREATOR) {
				if (!INSTANCES.containsKey(profile)) {
					synchronized (PROFILE_INSTANCE_CREATOR_DOUBLE_CHECK) {

						switch (profile) {
						case AppConstants.RECIPE_PROFILE_P0:
							INSTANCES.put(profile, new DesiChaiConsumptionHelperP0());
							break;
						case AppConstants.RECIPE_PROFILE_P1:
						case AppConstants.RECIPE_PROFILE_P2:
						case AppConstants.RECIPE_PROFILE_PP2:
							INSTANCES.put(profile, new DesiChaiConsumptionHelperP2());
							break;
						case AppConstants.RECIPE_PROFILE_P3:
						case AppConstants.RECIPE_PROFILE_PP3:
							INSTANCES.put(profile, new DesiChaiConsumptionHelperP3());
							break;
						case AppConstants.RECIPE_PROFILE_CC1:
						case AppConstants.RECIPE_PROFILE_PC1:
							INSTANCES.put(profile, new DesiChaiConsumptionHelperCC1());
							break;
						case AppConstants.RECIPE_PROFILE_DC0:
							INSTANCES.put(profile, new DesiChaiConsumptionHelperP0());
							break;
							case AppConstants.RECIPE_PROFILE_OP0:
							case AppConstants.RECIPE_PROFILE_ODC0:
								INSTANCES.put(profile, new OatMilkConsumptionHelperP0());
								break;
							case AppConstants.RECIPE_PROFILE_OP1:
							case AppConstants.RECIPE_PROFILE_OP2:
							case AppConstants.RECIPE_PROFILE_OPP2:
								INSTANCES.put(profile, new OatMilkConsumptionHelperP2());
								break;
							case  AppConstants.RECIPE_PROFILE_OP3:
							case AppConstants.RECIPE_PROFILE_OPP3:
								INSTANCES.put(profile, new OatMilkConsumptionHelperP3());
								break;
							case AppConstants.RECIPE_PROFILE_OCC1:
							case AppConstants.RECIPE_PROFILE_OPC1:
								INSTANCES.put(profile, new OatMilkConsumptionHelperCC1());
								break;

							default:
							return INSTANCES.get(AppConstants.DEFAULT_RECIPE_PROFILE);
						}
					}
				}
			}
		}
		return INSTANCES.get(profile);
	}

	public Set<Integer> getProducts() {
		return products;
	}

	public List<Consumable> getConsumption(OrderItem item, List<IngredientVariantDetail> variants) {
		Map<Integer, Consumable> scmProducts = scmProducts();
		Map<Integer, Map<String, Map<String, List<Pair<Integer, BigDecimal>>>>> consumables = consumables();
		List<Consumable> list = new ArrayList<>();
		int productId = item.getProductId();
		String dimension = item.getDimension();
		String sugar = REGULAR_SUGAR;
		String patti = REGULAR_PATTI;
		for (IngredientVariantDetail variant : variants) {
			if (variant.getAlias() != null && variant.getAlias().contains(SUGAR)) {
				sugar = variant.getAlias();
			}
			if (variant.getAlias() != null
					&& (variant.getAlias().contains(PATTI) || variant.getAlias().contains(KADAK))) {
				patti = variant.getAlias();
			}
		}
		String variation = patti + " - " + sugar;
		List<Pair<Integer, BigDecimal>> consumptions = Objects.nonNull(consumables.get(productId) )
				&&  Objects.nonNull(consumables.get(productId).get(dimension))
						? consumables.get(productId).get(dimension).get(variation)
						: null;

		if (consumptions == null) {
			log.error("Error in getting consumption for {} [ {} ] - {} - {}", item.getProductName(),
					item.getDimension(), patti, sugar);
		} else {
			for (Pair<Integer, BigDecimal> record : consumptions) {
				Consumable c = new Consumable();
				Consumable r = scmProducts.get(record.getKey());
				c.setName(r.getName());
				c.setProductId(r.getProductId());
				c.setUom(r.getUom());
				c.setQuantity(AppUtils.multiplyWithScale10(new BigDecimal(item.getQuantity()), record.getValue()));
				list.add(c);
			}
		}
		return list;
	}

	public abstract Map<Integer, Consumable> scmProducts();

	public abstract Map<Integer, Map<String, Map<String, List<Pair<Integer, BigDecimal>>>>> consumables();
	
	public static boolean isPlaceholderProduct(int productId) {
		return placeHolderProducts.containsKey(productId);
	}
	
	public static Integer placeholderIdentifier(int productId) {
		return placeHolderProducts.get(productId) ;
	}
	
	public static Integer getActualProduct(int productId, String identifier) {
		return productsLookupMap.containsKey(productId) ? (productsLookupMap.get(productId).containsKey(identifier)
				? productsLookupMap.get(productId).get(identifier)
				: productsLookupMap.get(productId).get(REGULAR_MILK)) : productId;
	}
}
