/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.util.Constants;

public enum ListTypes {

	DISCOUNT_CODES(AppConstants.RTL_GROUP_DISCOUNT), SUB_CATEGORIES(AppConstants.RTL_GROUP_CATEGORY),
	DIMENSION_CODES(AppConstants.RTL_GROUP_DIMENSION), CHANNEL_PARTNERS("CHANNEL_PARTNER"),
	DELIVERY_PARTNERS("DELIVERY_PARTNER"), COMPLIMENTARY_CODES(AppConstants.RTL_GROUP_COMPLIMENTARY),
	PR_TYPE(AppConstants.RTL_GROUP_PR_TYPE), ITEM_PER_TICKET(AppConstants.RTL_GROUP_ITEM_PER_TICKET),
	ADJUSTMENT_COMMENT(AppConstants.RTL_GROUP_ADJUSTMENT_COMMENT) ;

	private final String group;

	public String getGroup() {
		return group;
	}

	private ListTypes(String group) {
		this.group = group;
	}

}
