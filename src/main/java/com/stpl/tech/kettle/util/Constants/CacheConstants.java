package com.stpl.tech.kettle.util.Constants;

public class CacheConstants {

	public static final String ACL_CACHE = "ACLCacheMap";
	public static final String BRAND_METADATA_CACHE = "MasterDataCache:brandMetaData";
	public static final String EMP_MEAL_PRODUCT_CACHE = "MasterDataCache:employeeMealProducts";
	public static final String EMP_MEAL_DIM_CACHE = "MasterDataCache:employeeMealDimensions";
	public static final String EMP_NAME_DETAIL_CACHE = "MasterDataCache:employees";
	public static final String APP_ATTRIBUTES = "MasterDataCache:applicationAttributeValues";
	public static final String CONFIG_ATTRIBUTES = "MasterDataCache:configAttributes";

	public static final String EXT_API_TOKENS = "MasterHazelCastInstance:externalAPITokenMap";

	public static final String ORDER_BY_CATEGORY = "OrderInfo:orderByCategoryCache";
	public static final String RIDER_MAPPING = "riderInfo:riderMappingCache";
	public static final String EXTERNAL_PARTNER_CACHE = "MasterDataCache:externalPartnerMap";
	public static final String PREAUTH_API_CACHE = "PreAuthenticatedApiCache:preAuthenticatedAPIs";
	public static final String PRODUCT_DETAIL_CACHE = "MasterDataCache:productDetails";
	public static final String UNIT_PRODUCT_DETAIL_CACHE = "MasterDataCache:unitProductDetails";
	public static final String SUBSCRIPTION_PRODUCT_CACHE = "MasterDataCache:subscriptionProduct";
	public static final String PRODUCT_BASIC_CACHE = "MasterDataCache:productBasicDetails";
	public static final String LIST_CATEGORY_CACHE = "MasterDataCache:listCategoryData";
	public static final String DIMENSION_PROFILE_CACHE = "MasterDataCache:dimensionProfileData";
	public static final String LIST_DATA_CACHE = "MasterDataCache:listData";
	public static final String RECIPE_CACHE = "MasterDataCache:recipes";
	public static final String RECIPE_MAP_CACHE = "MasterDataCache:recipeMap";
	public static final String PRODUCT_PROFILE_CACHE = "MasterDataCache:unitProductProfileDetails";
	public static final String SESSION_CACHE = "sessionKeyToDetailMap";
	public static final String STATE_TAX_CACHE = "TaxDataCache:hsnStateTaxDataMap";
	public static final String TAXATION_CACHE = "TaxDataCache:taxations";
	public static final String UNITS_CACHE = "MasterDataCache:units";
	public static final String PAYMENT_MODES_CACHE = "MasterDataCache:paymentModes";
	public static final String CHANNEL_PARTNER_CACHE = "MasterDataCache:channelPartnerMap";
	public static final String CANCELLATION_REASON_CACHE = "MasterDataCache:cancellationReason";
	public static final String PARTNER_BRAND_MAPPING_CACHE = "MasterDataCache:unitPartnerBrandMappingMetaData";
	public static final String UNIT_BASIC_DETAILS_CACHE = "MasterDataCache:unitsBasicDetails";
	public static final String MASTER_DATA_CACHE_CUSTOMER_APPLIED_COUPON_DETAIL = "MasterDataCache:customerAppliedCouponDetailMap";
	public static final String DREAM_FOLKS_VOUCHER_CODES_USED = "MasterDataCache:dreamFolksVoucherCodesUsed";

}
