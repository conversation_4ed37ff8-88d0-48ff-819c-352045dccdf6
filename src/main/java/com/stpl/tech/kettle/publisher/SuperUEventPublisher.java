package com.stpl.tech.kettle.publisher;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.domain.enums.SuperUEventStatus;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.SuperUEvent;

public interface SuperUEventPublisher {
    SuperUEvent getSuperUEvent(Order order);


    boolean pushSuperUEvent(SuperUEvent superUEvent, String env);

    void updateEventTrackStatus(SuperUEventStatus superUEventStatus, Integer eventId);
}
