package com.stpl.tech.kettle.publisher.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.converter.Converters;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.data.kettle.SuperUEventTrack;
import com.stpl.tech.kettle.domain.enums.MenuType;
import com.stpl.tech.kettle.domain.enums.SuperUEventStatus;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.SuperUEvent;
import com.stpl.tech.kettle.publisher.SuperUEventPublisher;
import com.stpl.tech.kettle.repository.kettle.SuperUEventTrackDao;
import com.stpl.tech.kettle.service.CustomerDataService;
import com.stpl.tech.kettle.service.SQSNotificationService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.adapter.JSONSerializer;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Converts;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;

@Service
@Log4j2
public class SuperUEventPublisherImpl implements SuperUEventPublisher{

    @Autowired
    SuperUEventTrackDao superUEventTrackDao;

    @Autowired
    private SQSNotificationService sqsNotificationService;

    @Autowired
    CustomerDataService customerDataService;
    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public SuperUEvent getSuperUEvent(Order order) {
       try {
           log.error("### Pushing event for superU  : "+ order.getOrderId());

           SuperUEventTrack eventTrack = new SuperUEventTrack();
           eventTrack.setOrderId(order.getOrderId());
           eventTrack.setStatus(SuperUEventStatus.PUSHED_TO_QUEUE);

           eventTrack.setEventQPushTime(AppUtils.getCurrentTimestamp());
           eventTrack = superUEventTrackDao.save(eventTrack);
            SuperUEvent superUEvent = Converters.convert(order);
           superUEvent.setCustomerType(customerDataService.getCustomerType(order.getCustomerId(),order.getBrandId()));
           superUEvent.setEventId(eventTrack.getEventId());
           superUEvent.setProductsAndQuantity(getProductAndQuantity(order.getOrders()));
           eventTrack.setMetadata(superUEvent.toString());
          return superUEvent;
       }catch (Exception e) {
           log.error("### Error occurred in superU event push : "+ e.getMessage());
           e.printStackTrace();
       }
        return  null;
    }

    @Override
    public boolean pushSuperUEvent(SuperUEvent superUEvent, String env){
        try{

            sqsNotificationService.publishToSQS(env, JSONSerializer.toJSON(superUEvent), "_SUPERU_PUSH");
            log.error("### Event pushed Successfully for superU  : "+ superUEvent.getOrderId());
            return true;
        }catch (Exception e){
            log.error("####### Error while pushing superU event : "+ e.getMessage());
            e.getStackTrace();
            return  false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void updateEventTrackStatus(SuperUEventStatus superUEventStatus, Integer eventId){
        Optional<SuperUEventTrack> eventTrack =  superUEventTrackDao.findById(eventId);
        if(eventTrack.isPresent()){
            SuperUEventTrack track = eventTrack.get();
            track.setStatus(superUEventStatus);
        }
    }


    private String getProductAndQuantity(List<OrderItem> items){
        StringBuilder sb = new StringBuilder();
        int size = items.size();
        for(int i =0;i<size;i++){
            OrderItem item = items.get(i);
            sb.append(item.getProductName()).append("#").append(item.getQuantity());
            if(i!=size-1){
                sb.append(",");
            }
        }
    return sb.toString();
    }

}
