package com.stpl.tech.kettle.publisher.impl;

import java.util.Objects;

import javax.jms.JMSException;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.stpl.tech.kettle.publisher.CleverTapEventPublisher;
import com.stpl.tech.kettle.service.SQSNotificationService;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.util.EnvType;

import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class CleverTapEventPublisherImpl implements CleverTapEventPublisher {

	@Autowired
	private SQSNotificationService sqsNotificationService;

	@Override
	public void publishCleverTapEvent(String env, Object event, boolean isEnabled) throws JMSException {
		String messageGrpId = Thread.currentThread().getName();
		try {
			if (Objects.nonNull(event)) {
				if (isEnabled) {
					sqsNotificationService.publishToSQSFifo(env, new Gson().toJson(event), "_CLEVERTAP_PUSH.fifo",
							AppUtils.getRegion(EnvType.valueOf(env)),messageGrpId);
				} else {
					sqsNotificationService.publishToSQS(env, new Gson().toJson(event), "_FB_PUSH");
				}
				// log.info("CLEVERTAP EVENT PUBLISHED SUCCESSFULLY :::: {} ", new
				// Gson().toJson(event));
			}
		} catch (Exception e) {
			JSONObject obj = null;
			if (event instanceof String) {
				obj = new JSONObject((String) event).getJSONArray("d").getJSONObject(0);
			} else {
				obj = new JSONObject(event).getJSONArray("d").getJSONObject(0);
			}
			obj.remove("profileData");
			obj.remove("evtData");
			log.error("CLEVERTAP EVENT PUBLISH FAILED :::: {} ", obj.toString());
		}
	}
	private String extractMessageGrpId(Object event){
		try{
			JSONObject obj = null;
			String eventType = "event";
			String profileType = "profile";
			String eventName = "Charged";
			if (event instanceof String) {
				obj = new JSONObject((String) event).getJSONArray("d").getJSONObject(0);
			} else {
				obj = new JSONObject(event).getJSONArray("d").getJSONObject(0);
			}
			if(eventType.toLowerCase().equals(obj.get("type").toString().toLowerCase()) && eventName.toLowerCase().equals(obj.get("evtName").toString().toLowerCase())){
				return "chargedMessage";
			} else if (profileType.toLowerCase().equals(obj.get("type").toString().toLowerCase())) {
				return "profileMessage";
			}
		}catch (Exception e){
			log.info("error in reading event Data : {}",event.toString());
		}
		return "messageGroup1";
	}
}
