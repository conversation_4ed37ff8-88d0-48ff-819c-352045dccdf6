package com.stpl.tech.kettle.delivery.strategy;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.delivery.model.AuthorizationObject;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.master.domain.model.Unit;

public interface DeliveryExecutionStrategy {
    public DeliveryResponse createDelivery(String creationEndpoint, OrderInfo order, EnvironmentProperties props);

    public DeliveryResponse cancelDelivery(String cancelationEndpoint, String taskId, OrderInfo orderInfo, EnvironmentProperties props);

    public String registerMerchant(String registerEndpoint, Unit unit, EnvironmentProperties props);

    public void setAuthorizationObject(AuthorizationObject authorization);
}
