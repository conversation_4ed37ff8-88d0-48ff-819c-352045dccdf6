package com.stpl.tech.kettle.delivery.model;

import org.apache.http.entity.ContentType;

public class DeliveryConstants {
    public static final String REQUEST_OBJECT = "REQUEST_OBJECT";
    public static final String REQUEST_OBJECT_ADAPTER = "REQUEST_OBJECT_ADAPTER";

    public static final String RESPONSE_OBJECT = "RESPONSE_OBJECT";
    public static final String RESPONSE_OBJECT_ADAPTER = "RESPONSE_OBJECT_ADAPTER";

    public static final String EXECUTOR = "EXECUTOR";

    public static final String CREATE_ENDPOINT = "CREATE_ENDPOINT";
    public static final String CANCEL_ENDPOINT = "CANCEL_ENDPOINT";

    public static final String REGISTER_MERCHANT = "REGISTER_MERCHANT";

    public static final String ACCEPTS = ContentType.APPLICATION_JSON.toString();
    public static final String CHARSET = "UTF-8";

    public static final String AUTHORIZATION_TOKEN = "AUTHORIZATION_TOKEN";
    public static final String AUTHORIZATION_TOKEN_SECRET = "AUTHORIZATION_TOKEN_SECRET";
}
