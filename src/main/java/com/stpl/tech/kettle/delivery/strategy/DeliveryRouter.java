package com.stpl.tech.kettle.delivery.strategy;


import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.delivery.model.AuthorizationObject;
import com.stpl.tech.kettle.delivery.model.DeliveryConstants;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.domain.model.DeliveryStatus;
import com.stpl.tech.master.domain.model.Unit;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Component
public class DeliveryRouter {

    private static final String DT_EXECUTOR_CHECK = "DTExecutor";

    @Autowired
    private EnvironmentProperties props;

    DeliveryOperationExecutor operationExecutor = null;

    public DeliveryResponse createDeliveryInSystem(OrderInfo orderObject, Map<String, String> partnerIdMap)
            throws CloneNotSupportedException {
        log.info("Inside creation Request DeliveryRouter class");
        operationExecutor = getExecutor(partnerIdMap, DeliveryConstants.CREATE_ENDPOINT,orderObject.getOrder().getUnitId());
        DeliveryResponse returnResponse = operationExecutor.executeCreateDelivery(orderObject, props);
        return returnResponse != null ? returnResponse : createErrorResponse();
    }

    public String createMerchantIdInSystem(Unit unit, Map<String, String> partnerIdMap) throws JsonProcessingException {
        return getExecutor(partnerIdMap, DeliveryConstants.REGISTER_MERCHANT,unit.getId()).createMerchantWithPartner(unit, props);
    }

    public DeliveryResponse cancelDelivery(String deliveryTaskId, OrderInfo orderInfo, Map<String, String> partnerIdMap) {
        log.info("Inside cancellation request DeliveryRouter class");
        operationExecutor = getExecutor(partnerIdMap, DeliveryConstants.CANCEL_ENDPOINT, orderInfo.getOrder().getUnitId());
        DeliveryResponse returnResponse = operationExecutor.executeCancelDelivery(deliveryTaskId, orderInfo, props);
        return returnResponse != null ? returnResponse : createErrorResponse();
    }

    private DeliveryResponse createErrorResponse() {
        DeliveryResponse error = new DeliveryResponse();
        error.setDeliveryStatus(DeliveryStatus.REQUEST_DECLINED.getDeliveryStatus());
        error.setFailureCode(HttpStatus.BAD_REQUEST.toString());
        error.setFailureMessage("Invalid request");
        return error;
    }

    @SuppressWarnings("unchecked")
    private DeliveryOperationExecutor getExecutor(Map<String, String> partnerIdMap, String requestType, Integer unitId) {

        String endpoint = partnerIdMap.get(requestType);
        log.info("requestType is {} and its value is {}", requestType, endpoint);
        String executorName = partnerIdMap.get(DeliveryConstants.EXECUTOR);
        String token = null;
        String tokenSecret = null;
        if(!executorName.contains(DT_EXECUTOR_CHECK)){
            token = partnerIdMap.get(DeliveryConstants.AUTHORIZATION_TOKEN);
            tokenSecret = partnerIdMap.get(DeliveryConstants.AUTHORIZATION_TOKEN_SECRET);
        }else{
            if(unitId!=null){
                token = partnerIdMap.get(String.valueOf(unitId));
            }
        }

        AuthorizationObject authorization = new AuthorizationObject(token, tokenSecret);
        DeliveryExecutionStrategy strategyObject;
        try {

            Class<? extends DeliveryExecutionStrategy> strategyClass = (Class<? extends DeliveryExecutionStrategy>) Class
                    .forName(executorName);

            strategyObject = strategyClass.newInstance();
            strategyObject.setAuthorizationObject(authorization);
            operationExecutor = DeliveryOperationExecutor.getInstance(strategyObject);
            operationExecutor.setEndpoint(endpoint);
            return operationExecutor;

        } catch (InstantiationException e) {
            log.error("Failed to instantiate", e);
        } catch (IllegalAccessException e) {
            log.info("Illegal Access for this access", e);
        } catch (ClassNotFoundException e) {
            log.error("No class found with name {}", executorName, e);
        } catch (CloneNotSupportedException e) {
            log.error("Clone not supported for {}", executorName, e);
        }
        return null;
    }
}
