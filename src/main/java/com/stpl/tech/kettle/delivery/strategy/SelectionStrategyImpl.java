package com.stpl.tech.kettle.delivery.strategy;

import com.stpl.tech.kettle.cache.impl.UnitDeliverySelectionCache;
import org.springframework.stereotype.Component;


@Component
public class SelectionStrategyImpl implements SelectionStrategy<UnitDeliverySelectionCache, Integer>{

    @Override
    public Integer getSelectionForTicket(UnitDeliverySelectionCache unitDeliveryMappings) {
        return unitDeliveryMappings.getNextPartnerFromCache();
    }

    @Override
    public boolean refreshDeliveryCache(UnitDeliverySelectionCache unitDeliveryMappings) {
        try{
            unitDeliveryMappings.refreshCurrentCache();
            return true;
        }catch (Exception e){
            return false;
        }
    }
}
