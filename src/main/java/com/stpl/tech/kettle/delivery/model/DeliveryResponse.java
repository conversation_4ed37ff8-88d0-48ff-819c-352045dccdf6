package com.stpl.tech.kettle.delivery.model;


import java.io.Serializable;
import java.util.Date;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryResponse implements Serializable {

    private static final long serialVersionUID = -2956299836577946525L;
    
    @Id
    private String _id;

    protected int deliveryPartnerId;

    protected Integer deliveryDetailId;

    protected int unitId;

    protected int orderId;

    protected String generatedOrderId;

    protected String deliveryTaskId;

    protected int deliveryStatus;

    protected Date statusUpdateTime;

    protected String deliveryBoyName;

    protected String deliveryBoyPhoneNum;

    protected Integer deliveryBoyId;

    protected String failureCode;

    protected String failureMessage;

    protected String allotedNo;
    protected boolean byAssemblyScreen = true;


    @Override
    public String toString() {
        return "DeliveryResponse [_id=" + _id + ", deliveryPartnerId=" + deliveryPartnerId + ", deliveryDetailId="
                + deliveryDetailId + ", unitId=" + unitId + ", orderId=" + orderId + ", generatedOrderId="
                + generatedOrderId + ", deliveryTaskId=" + deliveryTaskId + ", deliveryStatus=" + deliveryStatus
                + ", statusUpdateTime=" + statusUpdateTime + ", deliveryBoyName=" + deliveryBoyName
                + ", deliveryBoyPhoneNum=" + deliveryBoyPhoneNum + ", deliveryBoyId=" + deliveryBoyId + ", failureCode="
                + failureCode + ", failureMessage=" + failureMessage + " allotedNo=" + allotedNo + " byAssemblyScreen  "
                + byAssemblyScreen + "]";
    }


}

