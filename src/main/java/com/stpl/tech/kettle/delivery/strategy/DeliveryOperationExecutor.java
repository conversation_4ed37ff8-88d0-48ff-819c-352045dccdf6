package com.stpl.tech.kettle.delivery.strategy;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.master.domain.model.Unit;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class DeliveryOperationExecutor  implements Cloneable{

    private DeliveryExecutionStrategy strategy;
    private String endpoint;

    private DeliveryOperationExecutor() {
    }

    public static DeliveryOperationExecutor getInstance(DeliveryExecutionStrategy strategy)
            throws CloneNotSupportedException {
        DeliveryOperationExecutor executor = null;
        if (executor == null) {
            executor = new DeliveryOperationExecutor();
        }
        executor = (DeliveryOperationExecutor) executor.clone();
        executor.setStrategy(strategy);
        log.info("Name of strategy being used is {}", strategy.getClass().getSimpleName());
        return executor;

    }

    public DeliveryResponse executeCreateDelivery(OrderInfo order, EnvironmentProperties props) {
        log.info("Inside executeCreateDelivery with endpoint {} and orderId {}", endpoint,
                order.getOrder().getOrderId());
        return getStrategy().createDelivery(endpoint, order, props);
    }

    public DeliveryResponse executeCancelDelivery(String deliveryTaskId, OrderInfo orderInfo, EnvironmentProperties props) {
        log.info("Inside executeCancelDelivery with endpoint {} and orderId {}", endpoint, orderInfo);
        return getStrategy().cancelDelivery(endpoint, deliveryTaskId, orderInfo, props);
    }



    public String createMerchantWithPartner(Unit unit, EnvironmentProperties props) {
        return getStrategy().registerMerchant(endpoint, unit, props);
    }

    public DeliveryExecutionStrategy getStrategy() {
        return strategy;
    }

    private void setStrategy(DeliveryExecutionStrategy strategy) {
        this.strategy = strategy;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

}
