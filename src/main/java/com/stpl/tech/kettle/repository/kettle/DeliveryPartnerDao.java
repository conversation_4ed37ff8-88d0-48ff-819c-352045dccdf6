package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.DeliveryPartner;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface DeliveryPartnerDao extends JpaRepository<DeliveryPartner, Integer> {

    public List<DeliveryPartner> findAllByPartnerType(String partnerType);

}
