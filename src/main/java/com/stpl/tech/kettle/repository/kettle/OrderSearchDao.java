package com.stpl.tech.kettle.repository.kettle;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.data.kettle.CustomerAddressInfo;
import com.stpl.tech.kettle.data.kettle.EmployeeMealData;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.TemplateRenderingException;

@Repository
public interface OrderSearchDao {

    public List<EmployeeMealData> getEmployeeMealData(int employeeId);

	public OrderInfo getOrderReceipt(int orderId, boolean includeReceipts, String customerName)
			throws DataNotFoundException, TemplateRenderingException;

	CustomerAddressInfo getOrderAddressInfo(Integer addressId);

}
