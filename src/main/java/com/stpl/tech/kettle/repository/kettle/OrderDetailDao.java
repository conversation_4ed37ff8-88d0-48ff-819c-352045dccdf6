package com.stpl.tech.kettle.repository.kettle;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.master.domain.model.OfferLastRedemptionView;

@Repository
public interface OrderDetailDao extends JpaRepository<OrderDetail, Integer> {

	@Query("select count(*) from OrderDetail o where o.customerId = ?1 and o.offerCode = ?2 and o.orderStatus <> 'CANCELLED' ")
	public Integer checkCouponUsage(int customerId, String code);

	@Query("Select od.orderId from OrderDetail od where od.customerId = ?1 AND od.brandId = 1 ")
	public List<Integer> getCustomerOrderDetails(int customerId);

	public OrderDetail findByGeneratedOrderId(String generatedOrderId);

	@Query(value = "SELECT\n" + "    od.CUSTOMER_ID customerId,\n" + "    MAX(BILLING_SERVER_TIME) lastOrderTime,\n"
			+ "    SUM(CASE\n" + "        WHEN\n" + "            od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE\n"
			+ "                    WHEN\n" + "                        HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5\n"
			+ "                    THEN\n"
			+ "                        SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),\n"
			+ "                            1)\n" + "                    ELSE CURRENT_DATE\n" + "                END),\n"
			+ "                INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')\n" + "        THEN\n"
			+ "            1\n" + "        ELSE 0\n" + "    END) orderToday,\n" + "    SUM(CASE\n" + "        WHEN\n"
			+ "            od.BILLING_SERVER_TIME > :lastTimestamp \n" + "        THEN\n" + "            1\n"
			+ "        ELSE 0\n" + "    END) orderInLastHour FROM\n" + "    ORDER_DETAIL od\n" + "WHERE\n"
			+ "    od.CUSTOMER_ID = :customer\n" + "    and od.OFFER_CODE = :code\n"
			+ "    and (od.BUSINESS_DATE IS NULL OR od.BUSINESS_DATE > :businessDate)\n"
			+ "    and od.ORDER_STATUS <> 'CANCELLED'\n" + "GROUP BY od.CUSTOMER_ID", nativeQuery = true)
	public OfferLastRedemptionView getOrderDetailViaOffer(@Param("customer") Integer customer,
			@Param("code") String code, @Param("businessDate") Date businessDate,
			@Param("lastTimestamp") Date lastTimestamp);

	@Modifying
	@Query("update OrderDetail l set l.customerId = ?1 where l.orderId = ?2")
	public void updateOrderCustomerId(int customerId, Integer orderId);

	@Query("SELECT o.orderId FROM OrderDetail o WHERE o.orderId > ?1 AND o.orderStatus NOT IN (?5) AND o.channelPartnerId NOT IN (?3) and o.customerId NOT IN ?4 order by o.orderId LIMIT ?2")
	List<Integer> getOrderBatch(Integer startOrderId, Integer batchSize, List<Integer> excludingPartnerIds,
			List<Integer> excludeCustomerIds, List<String> orderStatus);

	@Query("SELECT o FROM OrderDetail o WHERE o.unitId = ?1 ORDER BY o.orderId DESC LIMIT ?2")
	List<OrderDetail> findLastNGeneratedOrderIds(Integer unitId, Integer size);

	public OrderDetail findByOrderId(Integer orderId);
}
