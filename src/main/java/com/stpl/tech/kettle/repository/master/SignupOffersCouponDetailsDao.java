package com.stpl.tech.kettle.repository.master;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.master.SignupOffersCouponDetails;

@Repository
public interface SignupOffersCouponDetailsDao extends JpaRepository<SignupOffersCouponDetails, Integer> {

	List<SignupOffersCouponDetails> findAllByCouponStatus(String couponStatus);

}
