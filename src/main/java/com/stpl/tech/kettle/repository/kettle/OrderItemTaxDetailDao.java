package com.stpl.tech.kettle.repository.kettle;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.OrderItemTaxDetail;

@Repository
public interface OrderItemTaxDetailDao extends JpaRepository<OrderItemTaxDetail, Integer> {

    @Query(value = "Select OT from OrderItemTaxDetail OT where OT.orderItem.orderItemId =?1 and OT.taxCode = ?2")
    OrderItemTaxDetail findByOrderItemIdAndTaxCode(Integer orderItemId,String taxCode);

}
