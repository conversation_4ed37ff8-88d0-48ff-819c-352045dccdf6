package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.domain.model.InventoryUpdateEvent;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.master.domain.model.ProductInventory;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitInventoryDao {

    public List<ProductInventory> getUnitInventoryForProducts(int unitId, List<Integer> productIds)
            throws DataNotFoundException;

    public boolean updateUnitInventory(InventoryUpdateEvent updateData, boolean incrementalUpdate, int employeeId,
                                       Integer orderId, boolean isCancellation);
}
