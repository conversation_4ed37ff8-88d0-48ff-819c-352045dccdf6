package com.stpl.tech.kettle.repository.kettle;

import java.util.Objects;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.UnitSequenceId;

@Repository
public interface UnitSequenceIdDao extends JpaRepository<UnitSequenceId, Integer> {

	@Query("SELECT E FROM UnitSequenceId E where E.unitId = ?1 and dataSource = ?2")
	UnitSequenceId getUnitOrderId(int unitId, String source);

	public default UnitSequenceId addUnitSequenceId(int unitId, String source) {
		UnitSequenceId info = new UnitSequenceId(unitId, source, 1);
		return this.save(info);
	}
	
	public default int getNextUnitOrderId(int unitId, String source) {

		UnitSequenceId sequence = getUnitOrderId(unitId, source);
        if(Objects.isNull(sequence)) {
        	sequence = addUnitSequenceId(unitId, source);
        }
        int currentValue = sequence.getNextValue();
        sequence.setNextValue(currentValue + 1);
        return currentValue;
    }

}
