package com.stpl.tech.kettle.repository.master;

import java.util.Date;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.master.DeliveryCouponDetailData;

@Repository
public interface DeliveryCouponDetailDataDao extends JpaRepository<DeliveryCouponDetailData, Integer> {

	@Query("""
			SELECT DC FROM DeliveryCouponDetailData DC WHERE  DC.masterCoupon = ?1
			AND DC.endDate >= ?2 AND DC.brandId = ?3 AND DC.channelPartnerId = ?4
			 AND DC.deliveryCouponStatus = ?5 AND DC.noOfAllocations < DC.maxNoOfDistributions
			 AND DC.isExhausted = ?6 ORDER BY DC.noOfAllocations,DC.deliveryCouponId ASC LIMIT 1""")
	DeliveryCouponDetailData getDeliveryCoupon(String code, Date endDate, Integer brandId, Integer channelPartnerId,
			String status, String isExhausted);

	@Query("""
			SELECT DC FROM DeliveryCouponDetailData DC WHERE
			 DC.masterCoupon = ?1 AND DC.startDate <= ?2 AND DC.endDate >= ?3
			 AND DC.brandId = ?4 AND DC.channelPartnerId = ?5 AND DC.deliveryCouponStatus = ?6 ORDER BY DC.deliveryCouponId ASC LIMIT 1
			""")
	DeliveryCouponDetailData getMasterCoupon(String code, Date startDate, Date endDate, Integer brandId,
			Integer channelPartnerId, String status);

}
