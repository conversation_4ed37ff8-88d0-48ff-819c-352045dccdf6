package com.stpl.tech.kettle.repository.kettle;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.CustomerProductFeedback;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;

@Repository
public interface CustomerProductFeedbackDao extends JpaRepository<CustomerProductFeedback, Integer> {

	@Query(nativeQuery = true, name = "getCustomerDineInView")
	CustomerDineInView getCustomerDineInView(int customerId, Integer brandId, List<Integer> excludeOrderIds);

}
