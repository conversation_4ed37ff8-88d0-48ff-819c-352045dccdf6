package com.stpl.tech.kettle.repository.kettle;

import io.swagger.models.auth.In;
import lombok.Data;
import org.hibernate.metamodel.model.convert.spi.JpaAttributeConverter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.EmployeeMealAllowanceData;

import java.math.BigDecimal;
import java.util.Date;

@Repository
public interface EmployeeMealAllowanceDataDao extends JpaRepository<EmployeeMealAllowanceData,Integer> {
@Query(value = "SELECT SUM(e.amount) FROM EmployeeMealAllowanceData e WHERE e.employeeId= ?1 AND e.ordertime> ?2 AND e.ordertime<= ?3 GROUP BY e.employeeId")
    BigDecimal findSumOfAmountByEmployeeIdAndOrderTime(Integer employeeId, Date mealStartDate, Date lastBusinessDate);

@Query(value = "SELECT SUM(e.amount) FROM EmployeeMealAllowanceData e WHERE e.employeeId= ?1 AND e.ordertime> ?2")
    BigDecimal findSumOfAmountTodayByEmployeeIdAndOrderTime(Integer employeeId,Date businessDate);

}
