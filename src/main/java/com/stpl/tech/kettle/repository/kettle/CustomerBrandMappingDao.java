package com.stpl.tech.kettle.repository.kettle;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.CustomerBrandMapping;

import java.util.List;

@Repository
public interface CustomerBrandMappingDao extends JpaRepository<CustomerBrandMapping, Integer> {

	public CustomerBrandMapping findByCustomerIdAndBrandId(Integer customerId, Integer brandId);

	public List<CustomerBrandMapping> findByCustomerId(Integer customerId);

}
