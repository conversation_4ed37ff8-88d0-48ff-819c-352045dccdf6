package com.stpl.tech.kettle.repository.kettle;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.DeliveryDetail;

@Repository
public interface DeliveryDetailDao extends JpaRepository<DeliveryDetail, Integer> {

    public List<DeliveryDetail> findByGeneratedOrderIdAndDeliveryStatusOrderByStatusUpdateTimeDesc(String generatedOrderId,String deliveryStatus);
	
}
