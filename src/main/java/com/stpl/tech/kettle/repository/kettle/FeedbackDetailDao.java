package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.FeedbackDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
public interface FeedbackDetailDao extends JpaRepository<FeedbackDetail,Integer> {

	@Query("UPDATE FeedbackDetail fd SET fd.feedbackStatus = ?2 WHERE fd.feedbackId = ?1")
	@Modifying
	public void updateFeedbackDetail(Integer feedbackId, String status);

    public FeedbackDetail findByOrderIdAndEventType(int orderId,String eventType);

    @Query(value = "CALL SP_CALCULATE_MONTHLY_NPS_SCORE(?1)",nativeQuery = true )
    public void runNpsProc(Date currentTime);

    public FeedbackDetail findByOrderIdAndEventTypeAndFeedbackStatus(int orderId,String nps,String status);

    @Modifying
    @Query("update FeedbackDetail E set E.customerId = ?2, E.emailId = ?3 where E.feedbackId = ?1")
    public void updateCustomerInfoInFeedbackData(int feedbackId,int customerId,String emailId);

    public FeedbackDetail findByOrderIdAndSource(int orderId,String source);
}
