package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.PartnerOrderData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PartnerOrderDetailDao extends JpaRepository<PartnerOrderData, Integer> {

    @Query("SELECT p.isPriortizedOrder FROM PartnerOrderData p WHERE p.kettleOrderId = :kettleOrderId")
    String findIsPrioritizedByKettleOrderId(@Param("kettleOrderId") Integer kettleOrderId);
}
