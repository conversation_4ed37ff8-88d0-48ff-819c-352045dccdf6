package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.PartnerAttributes;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PartnerAttributesDao extends JpaRepository<PartnerAttributes,Integer> {
     public List<PartnerAttributes> findByPartnerType(String partnerType);
}
