package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.UnitProductInventory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitProductInventoryDao extends JpaRepository<UnitProductInventory,Integer> {
    @Query(value = "SELECT * FROM UNIT_PRODUCT_INVENTORY u WHERE u.UNIT_ID= ?1 AND u.PRODUCT_ID IN(?2)",nativeQuery = true)
    List<UnitProductInventory> findByUnitIdAndProductId(Integer unitId,List<Integer> productIds);

    List<UnitProductInventory> findByUnitIdAndProductInventoryStatus(Integer unitId,String productInventoryStatus);
}
