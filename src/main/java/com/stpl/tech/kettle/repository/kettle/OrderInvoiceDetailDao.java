package com.stpl.tech.kettle.repository.kettle;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.OrderInvoiceDetail;

@Repository
public interface OrderInvoiceDetailDao extends JpaRepository<OrderInvoiceDetail, Integer> {

	public OrderInvoiceDetail findByOrderId(String orderId);
	
}
