package com.stpl.tech.kettle.repository.kettle;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.OrderPaymentDetail;

@Repository
public interface OrderPaymentDetailDao extends JpaRepository<OrderPaymentDetail, Integer> {
	OrderPaymentDetail findByExternalOrderIdAndRequestStatus(String externalOrderId, String requestStatus);
}
