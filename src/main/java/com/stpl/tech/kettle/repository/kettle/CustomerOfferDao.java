package com.stpl.tech.kettle.repository.kettle;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.CustomerOfferDetail;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;

@Repository
public interface CustomerOfferDao extends JpaRepository<CustomerOfferDetail, Integer> {

	public List<CustomerOfferDetail> findAllByCustomerIdAndOfferCode(Integer customerId, String offerCode);

	@Query(nativeQuery = true, name = "getCustomerEmailData")
	CustomerEmailData getCustomerEmailData(@Param("customerId") int customerId, @Param("brandId") int brandId,
			@Param("currentDate") Date currentDate);

	public CustomerOfferDetail findByOfferCodeAndCustomerId(String offerCode , Integer customerId);

}
