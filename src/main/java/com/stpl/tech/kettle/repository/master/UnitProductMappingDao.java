package com.stpl.tech.kettle.repository.master;

import com.stpl.tech.kettle.data.kettle.UnitProductInventory;
import com.stpl.tech.kettle.data.master.UnitProductMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitProductMappingDao extends JpaRepository<UnitProductMapping,Integer> {
    @Query(value = "FROM UnitProductMapping where unitDetail.unitId IN(?1) and productDetail.isInventoryTracked = ?2 and productDetail.productStatus = ?3 and productStatus = ?4")
    List<UnitProductMapping> findByUnitIdAndIsInventoryTrackedAndProductDetailProductStatusAndProductStatus(List<Integer> unitId,String isInventoryTracked,String productDetailStatus,String productStatus);
}
