package com.stpl.tech.kettle.repository.kettle;

import java.util.Date;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.LoyaltyScore;

@Repository
public interface LoyaltyScoreDao extends JpaRepository<LoyaltyScore, Integer> {

	LoyaltyScore findByCustomerId(Integer customerId);

	@Query("UPDATE LoyaltyScore E SET E.lastNPSTime = ?1 where E.customerId = ?2")
	@Modifying
	void updateLastNPSTime(Date updateTime, int customerId);

}
