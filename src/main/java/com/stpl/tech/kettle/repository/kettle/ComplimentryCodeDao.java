package com.stpl.tech.kettle.repository.kettle;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.ComplimentaryCode;

@Repository
public interface ComplimentryCodeDao extends JpaRepository<ComplimentaryCode, Integer> {
    public List<ComplimentaryCode> findAllByStatus(String status);
}
