package com.stpl.tech.kettle.repository.master;

import com.stpl.tech.kettle.data.master.EmployeeUnitMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmployeeUnitMappingDao extends JpaRepository<EmployeeUnitMapping, Integer> {

    public List<EmployeeUnitMapping> findAllByEmployeeIdAndMappingStatus(Integer employeeId, String mappingStatus );
}
