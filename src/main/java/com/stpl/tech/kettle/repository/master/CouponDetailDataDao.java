package com.stpl.tech.kettle.repository.master;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.master.CouponDetailData;

@Repository
public interface CouponDetailDataDao
		extends PagingAndSortingRepository<CouponDetailData, Integer>, JpaRepository<CouponDetailData, Integer> {

	Page<CouponDetailData> findByOfferDetail(Integer offerId, Pageable pageable);

	@Modifying
	@Query("UPDATE CouponDetailData c SET c.usageCount = c.usageCount + 1 WHERE c.couponCode = ?1")
	void updateCouponUsageByOne(String offerCode);

	CouponDetailData findByCouponCode(String couponCode);

	List<CouponDetailData> findByCouponCodeIn(List<String> coupons);

}
