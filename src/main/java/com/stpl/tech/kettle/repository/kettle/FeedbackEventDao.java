package com.stpl.tech.kettle.repository.kettle;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.FeedbackEvent;

@Repository
public interface FeedbackEventDao extends JpaRepository<FeedbackEvent, Integer> {

	@Query("SELECT E FROM FeedbackEvent E where E.eventSource = ?2 and E.feedbackDetail.feedbackId = ?1 order by feedbackEventId desc")
	List<FeedbackEvent> getFeedbackEventInfo(int feedbackId, String qr);

	@Query("SELECT E FROM FeedbackEvent E where E.eventSource = ?2 and E.eventStatus = ?5 and E.eventType IN (?4)and E.feedbackDetail.feedbackStatus  = ?1 and E.feedbackDetail.orderId = ?3 and E.feedbackDetail.customerId = ?6")
	FeedbackEvent getPendingNPSEventsForCustomer(String feedbackStatus, String eventSource, Integer orderId,
			List<String> eventType, String eventStatus, Integer customerId);

	@Query("UPDATE FeedbackEvent f SET eventStatus = ?3 WHERE f.feedbackDetail.feedbackId = ?1 and f.eventType = ?2")
	@Modifying
	public boolean updateFeedbackEventStatus(int feedbackId, String eventType, String status);

	@Query("SELECT f FROM FeedbackEvent f WHERE f.feedbackDetail.customerId = ?1 AND f.eventType = ?2 AND f.eventStatus <> ?3 ORDER BY f.eventTriggerTime DESC")
	public FeedbackEvent availableForNPSEvent(int customerId, String eventType, String eventStatus);
}
