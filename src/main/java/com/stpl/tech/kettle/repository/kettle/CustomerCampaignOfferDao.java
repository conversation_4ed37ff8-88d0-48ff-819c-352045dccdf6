package com.stpl.tech.kettle.repository.kettle;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.CustomerCampaignOfferDetail;

@Repository
public interface CustomerCampaignOfferDao extends JpaRepository<CustomerCampaignOfferDetail, Integer> {

	CustomerCampaignOfferDetail findByCustomerIdAndCouponCode(Integer customerId, String couponCode);

	@Query("FROM CustomerCampaignOfferDetail W WHERE W.customerId = ?1 AND W.status = ?2 and W.couponEndDate >= ?3")
	List<CustomerCampaignOfferDetail> getAlreadyReceivedOffer(int customerId, String status, Date dayBeforeNthDay);

	@Query("SELECT W FROM CustomerCampaignOfferDetail W WHERE W.customerId = ?1 AND W.status = ?2 and W.couponEndDate >= ?3")
	List<CustomerCampaignOfferDetail> findAllByCustomerIdAndStatusAndCouponEndDate(int customerId, String status,
			Date currentDate);
}
