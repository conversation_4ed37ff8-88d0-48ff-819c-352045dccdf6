package com.stpl.tech.kettle.repository.kettle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.CashCardOffer;

@Repository
public interface CashCardOfferDao extends JpaRepository<CashCardOffer, Integer> {

	@Query("SELECT E FROM CashCardOffer E where E.unitId = ?2 and ?3 >= E.startDate and ?3 <= E.endDate and E.offerStatus = ?4 and E.denomination = ?1 order by E.cashCardOfferId desc")
	public CashCardOffer getCashCardOffer(BigDecimal cardAmount, int unitId, Date currentDate, String offerStatus);

	@Query("SELECT E FROM CashCardOffer E where E.unitId = ?1 and ?2 >= E.startDate and ?2 <= E.endDate and E.offerStatus = ?3 and E.partnerId = ?4 order by E.cashCardOfferId desc,E.denomination desc")
	public List<CashCardOffer> getActiveCashCardOffer(Integer unitId,Date currentDate,String offerStatus,Integer partnerId);

}
