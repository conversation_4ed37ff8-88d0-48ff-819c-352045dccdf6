package com.stpl.tech.kettle.repository.kettle;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.OrderStatusEvent;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.TransitionStatus;

@Repository
public interface OrderStatusEventDao extends JpaRepository<OrderStatusEvent, Integer> {

	public Optional<OrderStatusEvent> findByTransitionStatusAndOrderIdAndFromStatusAndToStatus(int orderId,
			String fromStatus, String toStatus, String transitionStatus);

	public default boolean existsOrderStatusEvent(boolean checkExistingState, int orderId, OrderStatus fromStatus,
			OrderStatus toStatus) {
		if (checkExistingState) {
			Optional<OrderStatusEvent> event = findByTransitionStatusAndOrderIdAndFromStatusAndToStatus(orderId,
					fromStatus.name(), toStatus.name(), TransitionStatus.SUCCESS.name());
			return Objects.nonNull(event);
		} else {
			// should return false when no check is done
			return false;
		}
	}

	public OrderStatusEvent findByTransitionStatusAndOrderIdAndToStatus(int orderId, String transitionStatus,
			String toStatus);

	public List<OrderStatusEvent> findAllByTransitionStatusAndOrderIdAndToStatusOrderByOrderStatusId(int orderId,
			String transitionStatus, String toStatus);

	public default Date getLastOrderStatusEventTime(int orderId, OrderStatus fromStatus, Date currentTime) {

		OrderStatusEvent data = findByTransitionStatusAndOrderIdAndToStatus(orderId, TransitionStatus.SUCCESS.name(),
				fromStatus.name());
		return Objects.isNull(data) ? fixOrderStatusEvents(orderId, fromStatus, currentTime)
				: Objects.isNull(data.getUpdateTime()) ? currentTime : data.getUpdateTime();

	}

	public default Date fixOrderStatusEvents(int orderId, OrderStatus fromStatus, Date currentTime) {

		List<OrderStatusEvent> eventList = findAllByTransitionStatusAndOrderIdAndToStatusOrderByOrderStatusId(orderId,
				TransitionStatus.SUCCESS.name(), fromStatus.name());

		for (int i = 1; i < eventList.size(); i++) {
			eventList.get(i).setTransitionStatus(TransitionStatus.FAILURE.name());
		}
		return eventList.get(0).getUpdateTime();
	}

	public OrderStatusEvent findTop1ByOrderIdOrderByOrderIdDesc(int orderId);

}
