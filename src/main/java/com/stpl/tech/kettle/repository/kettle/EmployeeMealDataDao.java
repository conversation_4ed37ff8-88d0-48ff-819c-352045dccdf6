package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.EmployeeMealData;
import io.swagger.models.auth.In;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface EmployeeMealDataDao extends JpaRepository<EmployeeMealData,Integer> {
    @Query(value = "FROM  EmployeeMealData where orderDetail.orderStatus <> ?1 AND employeeId = ?2 AND businessDate = ?3")
    List<EmployeeMealData> findByOrderStatusEmployeeIdAndBusinessDate(String orderStatus, Integer employeeId, Date businessDate);
}
