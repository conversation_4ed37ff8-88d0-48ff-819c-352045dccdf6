package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.PartnerOrderRiderStatesDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PartnerOrderRiderStatesDetailDao extends JpaRepository<PartnerOrderRiderStatesDetail, Integer> {

    Optional<PartnerOrderRiderStatesDetail> findByPartnerOrderId(String partnerOrderId);
    Optional<PartnerOrderRiderStatesDetail> findBykettleOrderId(Integer orderId);
}
