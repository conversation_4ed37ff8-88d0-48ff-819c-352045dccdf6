package com.stpl.tech.kettle.repository.master;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.master.CampaignDetailData;

@Repository
public interface CampaignDetailDataDao extends JpaRepository<CampaignDetailData, Integer> {

	CampaignDetailData findByCampaignIdAndCampaignStatus(Integer campaignId, String status);

	@Query("SELECT cd.campaignId FROM CampaignDetailData cd WHERE cd.linkedCampaignId = ?1 AND cd.campaignStatus = ?2 AND cd.startDate <= ?3 AND cd.endDate >= ?4 AND cd.campaignId <> ?1")
	List<Integer> getLinkedCampaignIds(Integer campaignId, String status, Date startDate, Date endDate);

	CampaignDetailData findByCampaignTokenAndCampaignStatus(String campaignToken, String campaignStatus);

	@Query("SELECT cdd FROM CampaignDetailData cdd WHERE cdd.unitIds LIKE %?1% AND cdd.campaignStatus = ?2 AND cdd.campaignStrategy = ?3 AND cdd.applicableForOrder = ?4 ORDER BY cdd.campaignId DESC LIMIT 1")
	CampaignDetailData getActiveCampaignByUnitId(Integer unitId, String status, String strategy,
			String applicableForOrder);

	@Query("SELECT cdd FROM CampaignDetailData cdd WHERE cdd.region LIKE %?1% AND cdd.campaignStatus = ?2 AND cdd.campaignStrategy = ?3 AND cdd.campaignReach = ?5 AND cdd.applicableForOrder = ?4 ORDER BY cdd.campaignId DESC LIMIT 1")
	CampaignDetailData getActiveCampaignByUnitRegion(String region, String status, String strategy,
			String applicableForOrder, String reachType);

	@Query("SELECT cdd FROM CampaignDetailData cdd WHERE cdd.campaignReach = ?4 AND cdd.campaignStatus = ?2 AND cdd.applicableForOrder = ?3  AND cdd.campaignStrategy= ?1 ORDER	BY cdd.campaignId DESC LIMIT 1")
	CampaignDetailData getActiveCampaignBySystem(String strategy, String status, String applicableForOrder,
			String reachType);
}
