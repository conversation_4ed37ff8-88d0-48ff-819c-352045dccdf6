package com.stpl.tech.kettle.repository.kettle;


import com.stpl.tech.kettle.data.kettle.UnitToDeliveryPartnerMappings;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitToDeliveryPartnerMappingsDao extends JpaRepository<UnitToDeliveryPartnerMappings,Integer> {


    @Query(" FROM UnitToDeliveryPartnerMappings where priority > 0 ")
    public List<UnitToDeliveryPartnerMappings> getDeliveryPartnerPriorityForUnits();


}
