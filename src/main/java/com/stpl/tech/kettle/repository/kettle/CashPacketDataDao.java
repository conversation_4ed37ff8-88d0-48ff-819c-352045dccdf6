package com.stpl.tech.kettle.repository.kettle;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.CashPacketData;

@Repository
public interface CashPacketDataDao extends JpaRepository<CashPacketData, Integer> {

	@Query("SELECT e FROM CashPacketData e WHERE e.customerId = ?2 AND e.referentId = ?1  AND e.eventStatus = ?3 AND e.currentAmount > 0")
	CashPacketData getInitiatedCashPacket(Integer referentId, Integer referrerId, String eventStatus);

	@Query("SELECT e FROM CashPacketData e WHERE e.cashDataId = ?1 AND e.eventStatus = ?2 AND e.currentAmount > 0 order by e.transactionCode, e.expirationDate")
	 List<CashPacketData> getCashPackets(Integer cashDataId,String eventStatus);

}
