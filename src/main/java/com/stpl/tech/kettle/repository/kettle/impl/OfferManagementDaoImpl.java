package com.stpl.tech.kettle.repository.kettle.impl;

import com.stpl.tech.kettle.data.master.CouponDetailData;
import com.stpl.tech.kettle.repository.clm.impl.ClmDataAbstractDaoImpl;
import com.stpl.tech.kettle.repository.kettle.OfferManagementDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.Objects;

@Repository
public class OfferManagementDaoImpl extends ClmDataAbstractDaoImpl implements OfferManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(OfferManagementDaoImpl.class);
    @Override
    public CouponDetailData getCouponWithoutMappings(String couponCode) {
        try {
            Query query = (Query) manager.createQuery("SELECT data FROM CouponDetailData data where data.couponCode = :couponCode");
            query.setParameter("couponCode", couponCode);
            Object o = query.getSingleResult();
            CouponDetailData couponDetailData = (CouponDetailData) o;
            return Objects.isNull(o) ? null : (CouponDetailData) o;
        } catch (Exception e) {
            LOG.info("Error while getting Coupon Code: {} , {}", couponCode, e.getMessage());
        }
        return null;
    }


}
