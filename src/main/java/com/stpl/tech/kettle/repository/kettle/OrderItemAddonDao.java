package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.SubscriptionPlanEvent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.OrderItemAddon;

import java.util.Date;

@Repository
public interface OrderItemAddonDao extends JpaRepository<OrderItemAddon, Integer> {

    @Query(value = "Select OA from OrderItemAddon OA where OA.orderItem.orderItemId =?1 and OA.name = ?2 and OA.type = ?3")
    OrderItemAddon findByOrderItemIdAndNameForFreeOption(Integer orderItemId,String addonName,String addonType);


}
