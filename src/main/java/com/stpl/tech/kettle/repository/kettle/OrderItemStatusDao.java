package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.OrderItemStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface OrderItemStatusDao extends JpaRepository<OrderItemStatus,Integer> {

    List<OrderItemStatus> findAllByTableRequestId(Integer tableRequestId);

    List<OrderItemStatus> findAllByOrderItemId_orderItemIdIn(List<Integer> orderItemIds);

    List<OrderItemStatus> findAllByOrderId(Integer orderId);
}
