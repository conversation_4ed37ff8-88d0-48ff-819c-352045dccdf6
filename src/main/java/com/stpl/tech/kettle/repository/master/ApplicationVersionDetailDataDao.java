package com.stpl.tech.kettle.repository.master;

import com.stpl.tech.kettle.data.master.ApplicationVersionDetailData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ApplicationVersionDetailDataDao extends JpaRepository<ApplicationVersionDetailData,Integer> {

    public String findApplicationVersionByUnitIdAndApplicationNameAndVersionStatusAndTerminal(Integer unitId, String applicationName, String versionStatus,Integer terminal);
}
