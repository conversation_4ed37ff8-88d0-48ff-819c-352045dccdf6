package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.SubscriptionPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;


@Repository
public interface SubscriptionPlanDao extends JpaRepository<SubscriptionPlan,Integer> {

    public SubscriptionPlan findByCustomerIdAndSubscriptionPlanCodeAndStatus(Integer customerId, String code,String status);
    
    SubscriptionPlan findByCustomerIdAndStatus(Integer customerId, String code);

    @Query("select s From SubscriptionPlan s where s.customerId = ?1 and s.eventType <> ?2 order by s.subscriptionPlanId desc limit 1")
    public SubscriptionPlan getSubscriptionByCustomerId(Integer customerId,String cancelSubscription);


}
