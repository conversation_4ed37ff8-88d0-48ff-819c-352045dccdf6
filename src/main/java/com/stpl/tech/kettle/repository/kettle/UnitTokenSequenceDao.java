package com.stpl.tech.kettle.repository.kettle;

import java.util.Objects;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.UnitTokenSequence;

@Repository
public interface UnitTokenSequenceDao extends JpaRepository<UnitTokenSequence, Integer> {

	@Query("SELECT E FROM UnitTokenSequence E where E.unitId = ?1")
	public UnitTokenSequence getUnitTokenSequenceByUnitId(int unitId);

	public default Integer getNextTokenNumber(int unitId, int maxLimit) {

		UnitTokenSequence sequence = getUnitTokenSequenceByUnitId(unitId);
		if (Objects.isNull(sequence)) {
			sequence = addUnitTokenSequence(unitId,1);
		}

		int currentValue = sequence.getNextValue();
		if (currentValue >= maxLimit) {
			// reset
			sequence.setNextValue(1);
		} else {
			sequence.setNextValue(currentValue + 1);
		}
		updateUnitTokenSequence(sequence);
		return currentValue;
	}

	public default UnitTokenSequence addUnitTokenSequence(int unitId, Integer nextValue) {
		return this.save(new UnitTokenSequence(unitId, nextValue));
	}

	public default UnitTokenSequence updateUnitTokenSequence(UnitTokenSequence sequence) {
		return this.save(sequence);
	}

}
