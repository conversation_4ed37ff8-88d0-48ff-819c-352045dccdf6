package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.DroolsDecisionTableData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DroolsDecisionTableDataDao extends JpaRepository<DroolsDecisionTableData,Integer> {

    public DroolsDecisionTableData findTop1ByTypeOrderByCreationTimeDesc(String type);

    public DroolsDecisionTableData findByTypeAndStatus(String type , String status);

    public DroolsDecisionTableData findByTypeAndFileNameAndVersion(String type, String fileName, String version);

    public List<DroolsDecisionTableData> findByTypeOrderById(String type);

    @Query("SELECT e FROM DroolsDecisionTableData e WHERE e.type = :type AND e.status IN :statuses order by e.creationTime desc")
    List<DroolsDecisionTableData> findByTypeAndStatusIn(@Param("type") String type, @Param("statuses") List<String> statuses);

    @Modifying
    @Query("UPDATE DroolsDecisionTableData d SET d.status = :decline where d.type = :type and d.status = :processing and d.fileName <> :fileName")
    public void declineExistingProcessingFile(@Param("type") String type,@Param("decline") String decline,@Param("processing") String processing,@Param("fileName") String fileName);

    public DroolsDecisionTableData findByTypeAndVersion(String type, String version);

    @Modifying
    @Query("UPDATE DroolsDecisionTableData d SET d.isDefault = :status where d.type = :fileType")
    public void setIsDefaultStatus(String fileType, String status);



}
