package com.stpl.tech.kettle.repository.kettle;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.DreamfolksTransactionDetail;

@Repository
public interface DreamfolksTransactionDetailDao extends JpaRepository<DreamfolksTransactionDetail, Integer> {

    @Query("SELECT DISTINCT dt.voucherCode FROM DreamfolksTransactionDetail dt WHERE dt.voucherCode IS NOT NULL")
    List<String> getAllDistinctVoucherCodes();

    /**
     * Update orderId for DreamFolks transaction detail using voucher code
     * @param voucherCode The voucher code to update
     * @param orderId The order ID to set
     * @return Number of rows updated
     */
    @Modifying
    @Query("UPDATE DreamfolksTransactionDetail dt SET dt.orderId = :orderId WHERE dt.voucherCode = :voucherCode")
    int updateOrderIdByVoucherCode(@Param("voucherCode") String voucherCode, @Param("orderId") Integer orderId);



} 