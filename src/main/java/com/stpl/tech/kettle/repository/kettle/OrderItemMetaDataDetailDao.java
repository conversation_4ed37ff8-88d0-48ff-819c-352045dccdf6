package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.OrderItemMetaDataDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrderItemMetaDataDetailDao  extends JpaRepository<OrderItemMetaDataDetail,Integer> {

     public OrderItemMetaDataDetail findTop1ByCustomerIdAndIsSavedChaiOrderByMetadataIdDesc(Integer customerId,String isSavedChai);

     public List<OrderItemMetaDataDetail> findByOrderId(Integer orderId);
     
     public List<OrderItemMetaDataDetail>findByOrderIdIn(List<Integer>orderIds);

}
