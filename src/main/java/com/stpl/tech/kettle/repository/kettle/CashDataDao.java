package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.CashData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CashDataDao extends JpaRepository<CashData,Integer> {
    CashData findByCustomerId(Integer customerId);



}
