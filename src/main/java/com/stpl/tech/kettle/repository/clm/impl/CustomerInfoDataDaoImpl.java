package com.stpl.tech.kettle.repository.clm.impl;

import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.repository.clm.CustomerInfoDataDao;
import com.stpl.tech.util.EnvType;
import jakarta.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import java.util.List;
import java.util.Objects;

@Slf4j
@Repository
public class CustomerInfoDataDaoImpl extends ClmDataAbstractDaoImpl implements CustomerInfoDataDao {

    @Autowired
    private EnvironmentProperties properties;

    @Override
    public List<Object[]> getCustomerWalletInfo(Integer customerId, Integer brandId){

        try {
            Query query = manager.createNativeQuery("CALL CUSTOMER_PROPERTIES_FOR_WALLET_SUGGESTION_DECISION(:customerId,:brandId)");
            query.setParameter("customerId",customerId);
            query.setParameter("brandId",brandId);
            return query.getResultList();
        }
        catch (Exception e){
            log.error("Error fetching customer Wallet Info from One View for customer:"+customerId,e);
        }
        return null;
    }

    @Override
    public  Object[] getCustomerType(Integer customerId, Integer brandId) {
        try {
            String queryString =" SELECT   CUST_VISIT_TYPE , ORDERS_CNT FROM   " +
                        " CLM_CUSTOMER_DROOL_PROPERTIES where CUSTOMER_ID = :customerId and BRAND_ID = :brandId ";

            Query query =  manager.createNativeQuery(queryString);
            query.setParameter("customerId",customerId);
            query.setParameter("brandId",brandId);
            Object[] resultList = (Object[]) query.getSingleResult();
            if(Objects.nonNull(resultList) && resultList.length>0){
                return resultList;
            }else{
                log.info("No Customer Type found for Customer id : {} and brand Id : {}", customerId, brandId);
            }
        }catch (NoResultException e){
            log.error("No Customer Type  found for Customer id : {} and brand Id : {}", customerId, brandId);
        }catch (Exception e){
            log.error("Error while fetching Customer Type for Customer id : {} and brand Id : {}", customerId, brandId, e);
        }
        return null;
    }

    @Override
    public Integer getKettleCustomerId(String partnerCustomerId) {
        try {
            Query query = manager.createNativeQuery("SELECT pm.CUSTOMER_ID FROM PARTNER_ORDER_CUSTOMER_MAPPING pm " +
                    "WHERE pm.PARTNER_CUSTOMER_ID = :partnerCustomerId");
            query.setParameter("partnerCustomerId", partnerCustomerId);
            Integer customerId = (Integer) query.getSingleResult();
            return customerId;
        } catch (Exception e) {
            log.error("Error in fetching Kettle customer for partner customer id : {} and error is : {}",
                    partnerCustomerId,e.getMessage());
        }
        return null;
    }

}