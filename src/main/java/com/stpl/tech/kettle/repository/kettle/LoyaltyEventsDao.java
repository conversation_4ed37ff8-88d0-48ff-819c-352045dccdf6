package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.LoyaltyEvents;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface LoyaltyEventsDao extends JpaRepository<LoyaltyEvents, Integer> {

	public LoyaltyEvents findByOrderIdAndTransactionTypeAndTransactionCodeTypeAndTransactionStatus(Integer orderId, String transactionType,
			String transactionCodeType, String transactionStatus);

	public LoyaltyEvents findByCustomerIdAndOrderIdAndTransactionCodeTypeAndTransactionCodeAndTransactionStatus(
			Integer orderId, Integer customerId, String transactionCodeType, String transactionCode, String transactionStatus);

	public LoyaltyEvents findByOrderIdAndTransactionCodeTypeAndTransactionTypeAndTransactionStatus(
			Integer orderId, String transactionType, String transactionCodeType, String transactionStatus);

	public List<LoyaltyEvents> findAllByCustomerIdAndTransactionCodeAndTransactionStatus(
			Integer orderId, String transactionCode, String transactionStatus);

	public List<LoyaltyEvents> findAllByCustomerIdAndTransactionCodeAndTransactionStatusOrderByTransactionTimeDesc(
			Integer orderId, String transactionCode, String transactionStatus);
	public List<LoyaltyEvents> findAllByCustomerIdAndLoyaltyEventStatusAndTransactionStatus(
			Integer customerId, String loyaltyEventStatus,String transactionStatus);

	public List<LoyaltyEvents> findAllByExpirationTimeLessThanEqualAndLoyaltyEventStatus(Date currenDate,String loyaltyEventStatus);

}
