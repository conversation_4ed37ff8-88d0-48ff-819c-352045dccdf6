package com.stpl.tech.kettle.repository.kettle;

import java.util.Objects;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.InvoiceSequenceId;

@Repository
public interface InvoiceSequenceIdDao extends JpaRepository<InvoiceSequenceId, Integer> {

	InvoiceSequenceId findByStateCodeAndFinancialYear(String stateCode, String financialYear);

	default InvoiceSequenceId addInvoiceSequenceId(String stateCode, String financialYear) {
		InvoiceSequenceId invoiceSequenceId = new InvoiceSequenceId(stateCode, financialYear, 1);
		return save(invoiceSequenceId);
	}

	public default int getNextStateInvoiceId(String stateCode, String financialYear) {
		InvoiceSequenceId invoiceSequenceId = findByStateCodeAndFinancialYear(stateCode, financialYear);
		if (Objects.isNull(invoiceSequenceId)) {
			invoiceSequenceId = addInvoiceSequenceId(stateCode, financialYear);
		}
		int currentValue = invoiceSequenceId.getNextValue();
		invoiceSequenceId.setNextValue(currentValue + 1);
		return currentValue;
	}

}
