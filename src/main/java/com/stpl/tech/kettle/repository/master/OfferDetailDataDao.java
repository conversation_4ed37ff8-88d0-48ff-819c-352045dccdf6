package com.stpl.tech.kettle.repository.master;

import com.stpl.tech.kettle.data.master.OfferDetailData;
import com.stpl.tech.master.domain.model.OfferCategoryType;
import io.swagger.models.auth.In;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface OfferDetailDataDao extends JpaRepository<OfferDetailData, Integer> {
    @Query(value = "SELECT d FROM OfferDetailData d WHERE d.offerType= ?1 AND d.offerStatus= ?2 AND d.offerScope= ?3 AND d.startDate <= ?4 AND d.endDate >= ?4")
    List<OfferDetailData> findByOfferTypeAndOfferStatusAndOfferScopeAndStartDateAndEndDate(String offerType, String offerStatus, String offerScope, Date businessDate);

}
