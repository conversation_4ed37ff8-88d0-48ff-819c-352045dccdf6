package com.stpl.tech.kettle.repository.kettle;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.CashCardDetail;

@Repository
public interface CashCardDetailDao extends JpaRepository<CashCardDetail, Integer> {

	List<CashCardDetail> findAllByCardNumber(String cardNumber);

	CashCardDetail findByCardNumber(String cardNumber);

	CashCardDetail findByPurchaseOrderId(int purchaseOrderId);// TODO card type

	@Query("SELECT ccd  FROM CashCardDetail ccd WHERE ccd.customerId =?1 AND  ccd.cardStatus = ?2 AND ccd.cashPendingAmount > 0 ")
	List<CashCardDetail> findAvailableCashCards(int customerId, String cardStatus);// TODO card type

	@Query("SELECT c From CashCardDetail c where c.customerId = ?1 and c.cashPendingAmount > ?2 and c.cardStatus = ?3")
	List<CashCardDetail> findActiveCashCards(int customerId, BigDecimal cardPendingAmount, String cardStatus);
}
