package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.UnitTableMappingDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitTableMappingDetailDao extends JpaRepository<UnitTableMappingDetail, Integer> {

    public UnitTableMappingDetail findByTableRequestId(Integer tableRequestId);
    public List<UnitTableMappingDetail> findByUnitIdAndTableStatusNot(Integer unitId,String tableStatus);
    public UnitTableMappingDetail findByUnitIdAndTableNumberAndTableStatusNot(Integer unitId,Integer tableNumber,String tableStatus);
}
