package com.stpl.tech.kettle.repository.kettle;

import com.stpl.tech.kettle.data.kettle.ExternalPartnerCardDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ExternalPartnerCardDao extends JpaRepository<ExternalPartnerCardDetail,Integer> {

    public List<ExternalPartnerCardDetail> findByExternalOrderId(String billNo);


    public List<ExternalPartnerCardDetail> findByCardNumberAndPartnerCode(String cardNumber,String partnerCode);


    public List<ExternalPartnerCardDetail> findByCardNumberAndPartnerCodeAndRequestStatus(String cardNumber, String partnerCode, String requestStatus);

}
