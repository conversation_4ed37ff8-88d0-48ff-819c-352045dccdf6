package com.stpl.tech.kettle.repository.kettle;

import java.util.Objects;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.StateSequenceId;

@Repository
public interface StateSequenceIdDao extends JpaRepository<StateSequenceId, Integer> {

	StateSequenceId findByStateId(int stateId);

	public default int getNextStateInvoiceId(int stateId) {
		StateSequenceId sequence = findByStateId(stateId);
		if (Objects.isNull(sequence)) {
			sequence = addStateSequenceId(stateId);
		}
		int currentValue = sequence.getNextValue();
		sequence.setNextValue(currentValue + 1);
		return currentValue;
	}

	public default StateSequenceId addStateSequenceId(int stateId) {
		StateSequenceId info = new StateSequenceId(stateId, 1);
		return save(info);
	}
}
