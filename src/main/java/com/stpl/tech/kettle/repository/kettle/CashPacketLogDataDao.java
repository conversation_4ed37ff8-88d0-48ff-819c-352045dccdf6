package com.stpl.tech.kettle.repository.kettle;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.CashPacketLogData;

@Repository
public interface CashPacketLogDataDao extends JpaRepository<CashPacketLogData, Integer> {

	CashPacketLogData findByCashPacketIdAndTransactionCode(int cashPacketId, String code);

}
