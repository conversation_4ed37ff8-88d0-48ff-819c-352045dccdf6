package com.stpl.tech.kettle.repository.kettle;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.SubscriptionPlanEvent;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

@Repository
public interface SubscriptionPlanEventDao extends JpaRepository<SubscriptionPlanEvent, Integer> {

    Optional<SubscriptionPlanEvent> findById(Integer id);

    @Query(value = "Select S from SubscriptionPlanEvent S where S.customerId =?1 and S.planStartDate <= ?2 and S.planEndDate >= ?2 and status = ?3 order by 1 desc LIMIT ?4")
    SubscriptionPlanEvent addSubscriptionEventSaving(int customerId, Date date, String status,Integer limit);

}
