package com.stpl.tech.kettle.repository.kettle;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.kettle.CustomerInfo;

@Repository
public interface CustomerDao extends JpaRepository<CustomerInfo, Integer> {

	// public Integer checkCouponUsage(int customerId, String code);

	public CustomerInfo findByContactNumber(String contactNumber);

	@Modifying
	@Query("UPDATE CustomerInfo c SET c.isChaayosCustomer = ?2 WHERE c.customerId = ?1 ")
	public void updateIsChaayosCustomer(int customerId, String chaayosCustomer);

	@Query("SELECT o.customerId FROM CustomerInfo o WHERE o.customerId > ?2 and o.customerId NOT IN ?3 and  o.customerAppId IS NULL  order by o.customerId LIMIT ?1")
	List<Integer> getCustomerIdsBatch(int batchSize, Integer customerId, List<Integer> excludeCustomerIds);

	@Procedure("CLEVERTAP_PROFILE_ATTRIBUTES")
	List<Object[]> clevertapUserAttributes(@Param("customerId") Integer customerId);

	@Procedure("CLEVERTAP_CHARGED_EVENT_ATTRIBUTES")
	List<Object[]> clevertapChargedEventAttributes(@Param("offerCode") String offerCode,
			@Param("customerId") Integer customerId, @Param("orderId") Integer orderId,
			@Param("orderSource") String orderSource , @Param("brandId") Integer brandId);

	@Procedure("CLEVERTAP_SUBSCRIPTION_EVENT_ATTRIBUTES")
	List<Object[]> clevertapSubscriptionEventAttributes(@Param("customerId") Integer customerId,
			@Param("orderId") Integer orderId);

	Boolean existsByAddTimeGreaterThanEqualAndCustomerId(Date addTime, Integer customerId);

	CustomerInfo findByCustomerId(Integer customerId);
}
