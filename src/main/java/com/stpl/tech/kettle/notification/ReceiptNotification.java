package com.stpl.tech.kettle.notification;

import java.util.ArrayList;
import java.util.List;

import com.stpl.tech.kettle.exceptions.EmailGenerationException;
import com.stpl.tech.kettle.exceptions.TemplateRenderingException;
import com.stpl.tech.kettle.notification.receipt.OrderEmailReceipt;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;

public class ReceiptNotification extends EmailNotification {

	private final OrderEmailReceipt receipt;

	private final boolean sendToCustomer;

	public ReceiptNotification() {
		this.receipt = null;
		this.sendToCustomer = false;
	}

	public ReceiptNotification(OrderEmailReceipt receipt, boolean sendToCustomer) {
		this.receipt = receipt;
		this.sendToCustomer = sendToCustomer;
	}

	public String subject() {
		return (TransactionUtils.isDev(receipt.getDetail().getEnv()) ? EnvType.DEV.name() + " " : "")
				+ (receipt.isCancellation()
						? "Cancelled Order for cafe " + receipt.getUnit().getName() + " for order "
								+ receipt.getDetail().getOrder().getGenerateOrderId()
						: "Chai Receipt No: " + receipt.getDetail().getOrder().getGenerateOrderId());
	}

	public String body() throws EmailGenerationException {

		try {
			return receipt.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	public String getFromEmail() {
		return receipt.getFromEmail();
	}

	public String[] getToEmails() {
		List<String> emails = new ArrayList<>();
		if (receipt.getToEmail().contains(",")) {
			String[] strs = receipt.getToEmail().split(",");
			for (String s : strs) {
				if (AppUtils.isValidEmail(s)) {
					emails.add(s);
				}
			}
		} else {
			emails.add(receipt.getToEmail());
		}
		if (sendToCustomer) {
			if (AppUtils.isValidEmail(receipt.getDetail().getCustomer().getEmailId()))
				emails.add(receipt.getDetail().getCustomer().getEmailId());
		}
		if (receipt.getDetail().getOrder().getChannelPartner() == AppConstants.BAZAAR_PARTNER_ID) {
			emails.add(AppUtils.isProd(receipt.getDetail().getEnv()) ? "<EMAIL>" : "<EMAIL>");
		}
		return emails.toArray(new String[emails.size()]);
	}

	public void sendEmail() throws EmailGenerationException {
		String[] emails = getToEmails();
		if (emails != null) {
			if (emails.length >= 1) {
				if (emails[0].trim().equals("") || emails[0].trim().equals("<EMAIL>")) {
					return;
				}
			}
			super.sendEmail();
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return receipt.getDetail().getEnv();
	}

}
