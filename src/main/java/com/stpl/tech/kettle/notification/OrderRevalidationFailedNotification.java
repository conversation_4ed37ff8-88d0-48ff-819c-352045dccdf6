package com.stpl.tech.kettle.notification;

import com.stpl.tech.kettle.exceptions.EmailGenerationException;
import com.stpl.tech.kettle.exceptions.TemplateRenderingException;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.util.EnvType;

import java.util.List;
import java.util.Objects;

public class OrderRevalidationFailedNotification extends EmailNotification {
    private EnvType envType;
    private List<String> toEmails;

    private String customerId;

    private String revalidationFailedReason;
    private OfferRevalidationFailedTemplate template;

    public OrderRevalidationFailedNotification(EnvType envType, List<String> toEmails, String customerId,
                                               String revalidationFailedReason, OfferRevalidationFailedTemplate template) {
        this.envType = envType;
        this.toEmails = toEmails;
        this.customerId = customerId;
        this.revalidationFailedReason = revalidationFailedReason;
        this.template = template;
    }

    @Override
    public String[] getToEmails() {
        if (AppUtils.isDev(envType)) {
            return new String[]{"<EMAIL>"};
        } else {
            if (Objects.nonNull(toEmails) && toEmails.size() > 0) {
                toEmails.add("<EMAIL>");
            }
            return this.toEmails.toArray(new String[0]);
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "Order Revalidation Failed for Customer Id : " + customerId + " due to reason : " + revalidationFailedReason + " at " +
                AppUtils.getCurrentTimeISTStringWithoutMS();
        if (AppUtils.isDev(envType)) {
            subject = "[Dev] " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            if (Objects.nonNull(template)) {
                return template.getContent();
            }
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
        return null;
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
