package com.stpl.tech.kettle.notification.receipt;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.master.domain.model.Unit;

public class QRRawPrintReceipt extends OrderRawPrintReceipt {
	private String header;
	private String qr;

	public QRRawPrintReceipt(Unit unit, OrderInfo detail, String basePath, String header, String qrData,
			EnvironmentProperties env) throws DataNotFoundException {
		super(unit, detail, basePath, null, env);
		this.header = header;
		this.qr = qrData;
	}

	@Override
	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getOrder().getOrderId() + "/OrderQRPrint-" + "-"
				+ getOrder().getOrderId() + ".html";
	}

	@Override
	public StringBuilder processData() {
		reset();
		// setDoubleFont();
		// setTallFont();
		left(" ");
		left(" ");
		left(" ");
		center(bold(doubleFont(header)));
		left(" ");
		left(" ");
		left(" ");
		qrAppend(qr(qr));

		left(" ");
		left(" ");
		left(" ");
		separator();
		cut();

		return getSb();
	}

}
