package com.stpl.tech.kettle.notification.slack;

import com.stpl.tech.kettle.exceptions.TemplateRenderingException;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;
import org.apache.velocity.tools.generic.DateTool;
import org.apache.velocity.tools.generic.NumberTool;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.Map;


public abstract class AbstractVelocityTemplate extends AbstractTemplate {
    public String getContent() throws TemplateRenderingException {
        try{
            VelocityEngine ve = new VelocityEngine();
            ve.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
            ve.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
            ve.init();
            Template template = ve.getTemplate(getTemplatePath());
            Map<String, Object> data = getData();
            File f = new File(getFilepath());
            if (!f.exists()) {
                f.getParentFile().mkdirs();
            }
            VelocityContext context = new VelocityContext();
            context.put("data", data);
            context.put("date", new DateTool());
            context.put("number", new NumberTool());
            StringWriter writer = new StringWriter();
            template.merge( context, writer );
            Writer file = new FileWriter(f);
            file.write(writer.toString());
            file.flush();
            file.close();
            return writer.toString();
        }catch (IOException e) {
            throw new TemplateRenderingException("Error while processing template", e);
        }
    }
}
