package com.stpl.tech.kettle.notification.receipt;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.master.domain.model.Unit;

public class OrderPrintReceipt extends OrderReceipt {

	private final String templatePath;

	public OrderPrintReceipt(String urlBasePath, Unit unit, OrderInfo detail, String basePath, String templatePath, String billPromotion) {
		super(urlBasePath, unit, detail, basePath, billPromotion, false);
		this.templatePath = templatePath;
	}

	@Override
	public String getTemplatePath() {
		return templatePath;
	}

	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getDetail().getOrder().getOrderId()
				+ "/OrderPrint-" + getDetail().getOrder().getOrderId() + ".html";
	}

}
