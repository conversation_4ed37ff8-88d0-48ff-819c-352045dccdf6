package com.stpl.tech.kettle.notification.receipt;

import java.util.Map;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.master.domain.model.Unit;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OrderEmailReceipt extends OrderReceipt {

	private String fromEmail;

	private String toEmail;

	private boolean isEmailVerified;

	private String verifyEmailLink;

	private String token;

	public OrderEmailReceipt(String urlBasePath, Unit unit, OrderInfo detail, String fromEmail, String toEmail,
			String basePath, boolean isEmailVerified, String verifyEmailLink, String token, String billPromotion,
			boolean cancellation, CustomerEmailData customerEmailData) {
		super(urlBasePath, unit, detail, basePath, billPromotion, cancellation, customerEmailData);
		this.fromEmail = fromEmail;
		this.toEmail = toEmail;
		this.isEmailVerified = isEmailVerified;
		this.verifyEmailLink = verifyEmailLink;
		this.token = token;
	}

	@Override
	public String getTemplatePath() {
		return "template/NewOrderReceipt.html";
	}

	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getDetail().getOrder().getOrderId()
				+ (isCancellation() ? "/CancelledOrderReceipt-" : "/OrderReceipt-")
				+ getDetail().getOrder().getOrderId() + ".html";
	}


	public Map<String, Object> getData() {
		Map<String, Object> data = super.getData();
		data.put("isEmailVerified", isEmailVerified);
		data.put("verifyEmailLink", verifyEmailLink);
		data.put("token", token);

		return data;
	}
}
