package com.stpl.tech.kettle.notification.receipt;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import org.apache.commons.lang3.StringUtils;

import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.notification.RawPrintData;
import com.stpl.tech.kettle.util.RawPrintHelper;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;

public abstract class RawPrintReceipt extends RawPrintTemplate {

	protected StringBuilder sb;
	protected void add(String s) {

		getSb().append(s);
	}

	protected void center(String s) {
		if (s != null && s.length() > 0) {
			add(RawPrintHelper.centerAlign(s));
		}
	}

	protected void left(String s) {
		if (s != null && s.length() > 0) {
			add(RawPrintHelper.leftAlign(s));
		}
	}
//
//	public void right(String s){
//		if (s != null && s.length() > 0) {
//			add(RawPrintHelper.rightAlign(s));
//		}
//	}

	protected void leftWithoutBreak(String s) {
		if (s != null && s.length() > 0) {
			add(RawPrintHelper.leftAlignWithoutBreak(s));
		}
	}

	protected void leftBold(String s) {
		left(bold(s));
	}

	protected String bold(String s) {
		return RawPrintHelper.bold(s);
	}

	protected String qr(String qr) {
		return RawPrintHelper.qr(qr);
	}

	protected void qrAppend(String qr) {
		add(RawPrintHelper.qr(qr));
	}

	protected String doubleFont(String s) {
		return RawPrintHelper.wideFont(s);
	}

	protected String extraDoubleFont(String s) {
		return RawPrintHelper.doubleFont(s);
	}

	protected String tallFont(String s) {
		return RawPrintHelper.tallFont(s);
	}

	protected void separator() {
		left(RawPrintHelper.SEPARATOR);
	}

	protected String comma() {
		return RawPrintHelper.COMMA;
	}

	protected void appendToBuilder(StringBuilder sb, String text) {
		if (hasValue(text)) {
			sb.append(text.trim().replaceAll("\\s\\s+", " "));
			sb.append(comma());
		}
	}

	protected void appendToBuilderNew(StringBuilder sb, String text) {
		if (hasValue(text)) {
			sb.append(text.trim().replaceAll("\\s\\s+", " "));
			sb.append(" ");
		}
	}

	protected String lpad(String s, int v) {
		return StringUtils.leftPad(s, v);
	}

	protected String lpad(int i, int v) {
		return StringUtils.leftPad(String.valueOf(i), v);
	}

	protected String lpad(BigDecimal s, int v) {
		return StringUtils.leftPad(s.toString(), v);
	}

	protected String rpad(String s, int v) {
		return StringUtils.rightPad(s, v);
	}

	public StringBuilder getSb() {
		if (sb == null) {
			reset();
		}
		return sb;
	}

	public void reset() {
		sb = new StringBuilder();
		sb.append(RawPrintHelper.FONT_A);
		sb.append(RawPrintHelper.STANDARD_FONT);
	}

	public void setTallFont() {
		sb.append(RawPrintHelper.TALL_FONT);
	}

	public void setDoubleFont() {
		sb.append(RawPrintHelper.DOUBLE_FONT);
	}

	protected boolean hasValue(String s) {
		return s != null && s.trim().length() > 0 && !AppConstants.BLANK.equals(s.trim());
	}

	protected boolean hasValue(Integer i) {
		return i != null && i > 0;
	}

	protected boolean hasValue(BigDecimal d) {
		return d != null && BigDecimal.ZERO.compareTo(d) != 0;
	}

	protected String fillWhiteSpace(String s) {
		return String.join(" ",s.split("(?=\\p{Upper})")).trim();
	}

	protected List<String> wordList(String text, int length) {
		String[] st = text.split(" ");
		List<String> list = new ArrayList<String>();
		StringBuilder sb = new StringBuilder();
		for (String s : st) {
			if (hasValue(s)) {
				if ((sb.toString() + s.trim()).length() > length) {
					list.add(sb.toString().trim());
					sb.setLength(0);
				}
				sb.append(s.trim() + " ");
			}
		}
		if (sb.length() > 0) {
			list.add(sb.toString().trim());
		}
		return list;
	}

	public void cut() {
		RawPrintHelper.cut(getSb());
	}

	public void partailCut() {
		RawPrintHelper.partialCut(getSb());
	}

	/**
	 * @param items2
	 */
	protected RawPrintData mergeItems(List<OrderItem> items) {
		RawPrintData data = new RawPrintData(new ArrayList<>(), new HashMap<>());
		Map<String, Integer> map = new HashMap<>();
		for (OrderItem item : items) {
			String productString = getProductString(item);
			if (map.containsKey(productString)) {
				int index = map.get(productString);
				int newCount = item.getQuantity();
				int existingCount = data.getSizeMap().get(index);
				data.getSizeMap().put(index, newCount + existingCount);
			} else {
				data.getItems().add(item);
				int index = data.getItems().size() - 1;
				map.put(productString, index);
				data.getSizeMap().put(index, item.getQuantity());
			}
		}
		return data;
	}

	/**
	 * @param item
	 * @return
	 */
	private String getProductString(OrderItem item) {
		StringBuffer b = new StringBuffer();
		b.append(item.getProductName()).append(item.getDimension());
		addCompositionToBuffer(item, b);
		return b.toString().toLowerCase();
	}

	protected void addCompositionToBuffer(OrderItem item, StringBuffer b) {
		Set<String> set = new TreeSet<>();
		if (item.getComposition().hasDefaultVariant()) {
			for (IngredientVariantDetail variant : item.getComposition().getVariants()) {
				if (variant.getAlias() != null && !variant.isDefaultSetting()) {
					set.add(variant.getAlias());
				}
			}
		}
		if (item.getComposition().getProducts() != null && !item.getComposition().getProducts().isEmpty()) {
			for (IngredientProductDetail product : item.getComposition().getProducts()) {
				if (product.getProduct().getName() != null) {
					set.add(product.getProduct().getName());
				}
			}
		}
		if (item.getComposition().getAddons() != null && !item.getComposition().getAddons().isEmpty()) {
			for (IngredientProductDetail addon : item.getComposition().getAddons()) {
				if (addon.getProduct().getShortCode() != null) {
					set.add(addon.getProduct().getShortCode());
				}
			}
		}
		if (item.getComposition().getOptions() != null && !item.getComposition().getOptions().isEmpty()) {
			for (String option : item.getComposition().getOptions()) {
				set.add(option);
			}
		}
		b.append(StringUtils.join(set, ","));
	}
}
