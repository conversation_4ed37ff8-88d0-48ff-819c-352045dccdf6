package com.stpl.tech.kettle.notification;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.kettle.domain.model.OrderStatus;

public class OrderPushNotification implements FirebaseNotification {
	private Map<String, String> message;
	private String topic;
	private String sendToAndroid;
	public OrderPushNotification(Integer orderId, OrderStatus reason, String source, Integer unitId) {
		this.message = new HashMap<>();
		this.message.put("orderId", orderId.toString());
		this.message.put("unitId", unitId.toString());
		this.message.put("reason", reason.name());
		this.message.put("source", source);
	}

	public void setTopic(String topic) {
		this.topic = topic;
	}

	@Override
	public String getTopic() {
		return topic;
	}

	@Override
	public Object getData() {
		return this.message;
	}

	@Override
	public String getTitle() {
		return "Order Received";
	}

	@Override
	public String getMessage() {
		return "Order Message Received";
	}

	@Override
	public String sendToAndroid() {
		return this.sendToAndroid;
	}

	public void setSendToAndroid(String sendToAndroid) {
		this.sendToAndroid = sendToAndroid;
	}
}