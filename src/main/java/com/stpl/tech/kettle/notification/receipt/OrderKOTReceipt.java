package com.stpl.tech.kettle.notification.receipt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.domain.model.KOTType;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.exceptions.TemplateRenderingException;
import com.stpl.tech.kettle.util.SpringBeanProvider;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.Unit;

public class OrderKOTReceipt extends OrderReceipt {

	private KOTType type;

	private final List<OrderItem> items = new ArrayList<OrderItem>();

	public OrderKOTReceipt(String urlBasePath, Unit unit,OrderInfo detail, KOTType type, String basePath)
			throws DataNotFoundException {
		super(urlBasePath, unit, detail, basePath, null, false);
		this.type = type;
		ProductCache productCache = SpringBeanProvider.getBean(ProductCache.class);
		for (OrderItem item : getDetail().getOrder().getOrders()) {
			Product product = productCache.getProductById(item.getProductId());
			if (product.getType() == type.getId() && !(product.getTaxCode().equals(AppConstants.GIFT_CARD_TAX_CODE))) {
				this.items.add(item);
			}
			if (product.getType() == AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE) {
				for (OrderItem addon : item.getComposition().getMenuProducts()) {
					product = productCache.getProductById(addon.getProductId());
					if (product.getType() == type.getId()) {
						this.items.add(addon);
					}
				}
			}
		}
	}

	@Override
	public String getTemplatePath() {
		return "template/OrderKOTPrint.html";
	}

	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getDetail().getOrder().getOrderId()
				+ "/OrderKOTPrint-" + type.getDescription() + "-" + getDetail().getOrder().getOrderId() + ".html";
	}

	public Map<String, Object> getData() {
		// Build the data-model
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("order", getDetail().getOrder());
		data.put("customer", getDetail().getCustomer());
		data.put("orderItems", items);
		data.put("description", type.getDescription());
		data.put("unit", getUnit());
		return data;
	}

	public String getContent() throws TemplateRenderingException {
		return items.size() > 0 ? super.getContent() : null;
	}
}