package com.stpl.tech.kettle.notification.receipt;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.master.domain.model.Unit;

public class AssemblySlipReceipt extends OrderReceipt {

	public AssemblySlipReceipt(String urlBasePath, Unit unit, OrderInfo detail, String basePath) {
		super(urlBasePath, unit, detail, basePath, null, false);
	}

	@Override
	public String getTemplatePath() {
		return "template/AssemblySlip.html";
	}

	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getDetail().getOrder().getOrderId()
				+ "/AssemblySlip-" + getDetail().getOrder().getOrderId() + ".html";
	}

}
