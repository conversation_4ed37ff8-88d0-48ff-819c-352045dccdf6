package com.stpl.tech.kettle.notification.slack;

import com.stpl.tech.kettle.exceptions.TemplateRenderingException;
import com.stpl.tech.kettle.notification.receipt.ConfigurationFactory;
import freemarker.template.Template;
import freemarker.template.TemplateException;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.Map;

public abstract class AbstractTemplate  implements PrintTemplate {
    public String getContent() throws TemplateRenderingException {

        try {
            // Load template from source folder
            Template template = ConfigurationFactory.getFreemarkerConfiguration().getTemplate(getTemplatePath());
            Map<String, Object> data = getData();
            // File output
            String filePath = getFilepath();
            filePath = filePath == null ? "/tmp/temp.html" : filePath;
            System.out.println("Temp File Name " + filePath);
            File f = new File(filePath);
            if (!f.exists()) {
                f.getParentFile().mkdirs();
            }
            Writer file = new FileWriter(f);
            template.process(data, file);
            file.flush();
            file.close();
            Writer out = new StringWriter();
            template.process(data, out);
            return out.toString();

        } catch (IOException e) {
            throw new TemplateRenderingException("Error while processing template", e);
        } catch (TemplateException e) {
            throw new TemplateRenderingException("Error while processing template", e);
        }
    }

    public abstract String getTemplatePath();

    public abstract String getFilepath();

    public abstract Map<String, Object> getData();
}
