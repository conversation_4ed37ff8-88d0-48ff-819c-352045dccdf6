package com.stpl.tech.kettle.notification;

import com.stpl.tech.kettle.util.Constants.AppConstants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AttachmentData {

	private byte[] attachment;
	private String fileName;
	private String contentType;

	public AttachmentData(String fileName, String contentType) {
		this.contentType = contentType;
		this.fileName = fileName + getFileExtension(contentType);
	}

	private String getFileExtension(String contentType) {
		if (contentType.equals(AppConstants.EXCEL_MIME_TYPE)) {
			return ".xlsx";
		} else if (contentType.equals(AppConstants.CSV_MIME_TYPE)) {
			return ".csv";
		} else if (contentType.equals(AppConstants.ZIP_MIME_TYPE)) {
			return ".zip";
		} else if (contentType.equals(AppConstants.PDF_MIME_TYPE)) {
			return ".pdf";
		} else if (contentType.equals(AppConstants.JSON_MIME_TYPE)) {
			return ".json";
		} else if (contentType.equals(AppConstants.EXCEL_MIME_TYPE_XLS)) {
			return ".xls";
		} else if (contentType.equals(AppConstants.JPEG_MIME_TYPE)) {
			return ".jpeg";
		} else if (contentType.equals(AppConstants.JPG_MIME_TYPE)) {
			return ".jpg";
		} else if (contentType.equals(AppConstants.PNG_MIME_TYPE)) {
			return ".png";
		}
		return ".txt";
	}
}
