package com.stpl.tech.kettle.notification.receipt;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;

import com.stpl.tech.kettle.exceptions.TemplateRenderingException;

public abstract class RawPrintTemplate implements PrintTemplate {

	public String getContent() throws TemplateRenderingException {

		try {
			// File output
			File f = new File(getFilepath());
			if (!f.exists()) {
				f.getParentFile().mkdirs();
			}
			StringBuilder sc = processData();
			if (sc != null) {
				try (PrintWriter out = new PrintWriter(f)) {
					out.println(sc);
				}
			}
			return sc != null ? sc.toString() : null;

		} catch (IOException e) {
			throw new TemplateRenderingException("Error while processing template", e);
		}
	}

	public abstract StringBuilder processData();

	public abstract String getFilepath();

}
