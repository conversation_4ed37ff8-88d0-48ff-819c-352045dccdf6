package com.stpl.tech.kettle.notification;

import com.stpl.tech.kettle.notification.slack.AbstractVelocityTemplate;
import com.stpl.tech.util.EnvType;

import java.util.HashMap;
import java.util.Map;

public class OfferRevalidationFailedTemplate extends AbstractVelocityTemplate {

    private EnvType envType;
    private String basePath;
    private String customerId;

    private String offerCode;
    private String unit;
    private String failureReason;

    public OfferRevalidationFailedTemplate(EnvType envType, String basePath, String customerId, String offerCode, String unit, String failureReason) {
        this.envType = envType;
        this.basePath = basePath;
        this.customerId = customerId;
        this.offerCode = offerCode;
        this.unit = unit;
        this.failureReason = failureReason;
    }

    @Override
    public String getTemplatePath() {
        return "template/OrderRevalidationFailedTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + "ORDER_REVALIDATION_FAILED" + offerCode + "_" + customerId + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> data = new HashMap<>();
        data.put("customerId", customerId);
        data.put("offerCode", offerCode);
        data.put("unitData", unit);
        data.put("failureReason", failureReason);
        return data;
    }
}
