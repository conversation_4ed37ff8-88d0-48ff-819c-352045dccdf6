package com.stpl.tech.kettle.notification.receipt;

import freemarker.template.Configuration;
import freemarker.template.TemplateExceptionHandler;

public class ConfigurationFactory {
	private static Configuration freemarkerConfig;

	// TODO fix this
	public static Configuration getFreemarkerConfiguration() {
		if (freemarkerConfig == null) {
			freemarkerConfig = new Configuration(Configuration.VERSION_2_3_23);
		}
		freemarkerConfig.setClassForTemplateLoading(ConfigurationFactory.class, "/");
		freemarkerConfig.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
		freemarkerConfig.setDefaultEncoding("UTF-8");
		return freemarkerConfig;

	}
}
