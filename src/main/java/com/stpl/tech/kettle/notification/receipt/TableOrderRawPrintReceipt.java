package com.stpl.tech.kettle.notification.receipt;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.NumberToWord;
import com.stpl.tech.kettle.util.RawPrintHelper;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class TableOrderRawPrintReceipt extends RawPrintReceipt{

    private EnvironmentProperties env;
    private OrderInfo detail;
    private String basePath;
    private boolean isReprint = false;
    private Unit unit;
    private String billPromotion;
    List<OrderItem> deliveryPackaging = new ArrayList<>(2);

    public TableOrderRawPrintReceipt(Unit unit, OrderInfo detail, String basePath, String billPromotion,
                                EnvironmentProperties env) {
        super();
        this.detail = detail;
        this.basePath = basePath;
        this.unit = unit;
        this.billPromotion = billPromotion;
        this.env = env;
    }
    @Override
    public void setReprint() {
        isReprint = true;
    }

    @Override
    public StringBuilder processData() {
        reset();
        header();
        invalidBillMark();
        duplicateCopyMark();
        if (!isCOD()) {
            unitDetails();
        }
        separator();
        duplicateCopyMark();
        orderType();
        orderMetadata();
        customerDetails();
        couponDetails();

        if (hasValue(getOrder().getTempCode()) && !isCOD()) {
            left(rpad("Free Wifi Code", 20) + getOrder().getTempCode());
        }

        if (hasValue(getOrder().getTokenNumber()) && !isCOD()) {
            left(rpad("Token Number", 20) + getOrder().getTokenNumber());
        }

        if (hasValue(getOrder().getOrderRemark())) {
            left(rpad("Order Remark", 20) + getOrder().getOrderRemark());
        }

        invalidBillMark();
        cancelledBillMark();
        separator();
        itemDetails();
        separator();
        invalidBillMark();
        transactionDetails();
        separator();
        if (getTransactionDetail().getSavings() != null
                && BigDecimal.ZERO.compareTo(getTransactionDetail().getSavings()) != 0) {
            left(bold(rpad("Net Savings", 36)) + lpad(getTransactionDetail().getSavings(), 10));
            separator();
        }

        if (getOrder().getQrLink() != null && getOrder().getQrLink().length() > 0) {
            center(bold(doubleFont(getOrder().getQrHeader())));
            qrAppend(getOrder().getQrLink());
        }
        if(Objects.nonNull(getTransactionDetail().getServiceCharge()) && getTransactionDetail().getServiceCharge().compareTo(BigDecimal.ZERO) > 0){
            if (hasValue(AppConstants.SERVICE_CHARGE_DESCRIPTION)) {
                List<String> lines = wordList(AppConstants.SERVICE_CHARGE_DESCRIPTION, 46);
                for (String line : lines) {
                    left(line);
                }
            }
            separator();
        }
        
        // GST Text Display - similar to service charge description
        if(env.getShowGstText()){
            String gstText = env.getGstTextDescription();
            if (hasValue(gstText)) {
                List<String> lines = wordList(gstText, 46);
                for (String line : lines) {
                    left(line);
                }
            }
            separator();
        }
        companyDetails(unit.getCompany());

        if (hasValue(billPromotion)) {
            left(billPromotion);
        }

        if (hasValue(getOrder().getTempCode()) && !isCOD()) {
            left("");
            left("Free Wifi is subject to availability");
        }
        left("");
        if (detail.getBrand() != null && detail.getBrand().getTagLine() != null) {
            left(detail.getBrand().getTagLine());
        }
        separator();
        left("  ");
        center("Powered by Kettle Technologies.");

        cut();

        return getSb();
    }

    @Override
    public String getFilepath() {
        return getBasePath() + "/" + getUnit().getId() + "/orders/" + getOrder().getTableRequestId() + "/OrderReceipt-"
                + getOrder().getTableRequestId() + ".html";
    }

    protected String getBasePath() {
        return basePath;
    }

    public Unit getUnit() {
        return unit;
    }

    protected Order getOrder() {
        return detail.getOrder();
    }

    public void reset() {
        sb = new StringBuilder();
        sb.append(RawPrintHelper.FONT_A);
        sb.append(RawPrintHelper.STANDARD_FONT);
    }

    protected void header() {
        center(bold(doubleFont(RawPrintHelper.getOrderSource(getOrder().getSource(), detail.getBrand()))));
        if (detail.getBrand() != null && detail.getBrand().getBillTag() != null) {
            center(bold(detail.getBrand().getBillTag()));
        }
        if (detail.getBrand() != null && detail.getBrand().getDomain() != null) {
//			center(doubleFont("Order Online"));
            center(bold(doubleFont(detail.getBrand().getDomain())));
        }
        if (hasToken()) {
            center(bold(doubleFont("Token : " + getOrder().getTokenNumber())));
        }
        left("");
    }

    protected boolean hasToken() {
        return getOrder().getTokenNumber() != null && getOrder().getTokenNumber() > 0
                && !AppConstants.COD.equals(getOrder().getSource());
    }

    protected void invalidBillMark() {
        if (!AppUtils.isProd(detail.getEnv())) {
            leftBold("This Bill Is Not Valid");
        }
    }

    protected void duplicateCopyMark() {
        if (isReprint) {
            leftBold("Duplicate Copy Of The Bill");
        }
    }

    public OrderInfo getDetail() {
        return detail;
    }

    protected void orderType() {
        if (TransactionUtils.isSpecialOrder(getOrder())) {
            center(doubleFont(getOrderType(getOrder().getOrderType())));
        }
    }

    protected String getOrderType(String orderType) {
        String s = "";
        switch (orderType) {
            case "employee-meal":
                s = "Employee Meal";
                break;
            case "complimentary-order":
                s = "Complimentary Order";
                break;
            case "unsatisfied-customer-order":
                s = "Customer Complimentary";
                break;
            case "paid-employee-meal":
                s = "Employee Meal";
                break;
            default:
                s = "";
        }
        return s;
    }

    protected void orderMetadata() {
        if (hasTable() && !isCOD()) {
            left(rpad("Table No", 20) + getOrder().getTableNumber());
        }
        if (Objects.nonNull(getOrder().getGenerateOrderId())) {
            left(rpad("Order No", 20) + getOrder().getGenerateOrderId());
        }
        left(rpad("Order Time", 20) + AppUtils.getBillPrintFormat(AppUtils.getCurrentTimestamp()));
        if (getOrder().getBillBookNo() != null && getOrder().getBillBookNo() != 0) {
            left(rpad("Bill Book No", 20) + getOrder().getBillBookNo());
        }
    }

    protected boolean hasTable() {
        return getUnit().isTableService() && getOrder().getTableNumber() != null;
    }

    protected boolean isCOD() {
        return AppConstants.COD.equals(getOrder().getSource());
    }

    protected void customerDetails() {
        if (Objects.nonNull(getOrder().getInvoice())) {
//			left(rpad("Company Name", 20) + getOrder().getInvoice().getCompanyName());
            getCompanyName(getOrder().getInvoice().getCompanyName());
            getCompanyAddress(getOrder().getInvoice().getCompanyAddress());
            left(rpad("Company GSTIN", 20) + getOrder().getInvoice().getGstIn());
        } else {
            if (getOrder().getCustomerName() != null) {
                left(rpad("Name", 20) + getOrder().getCustomerName());
            }
            if (hasCustomer()) {
                if (!isCOD()) {
                    left(rpad("Contact", 20) + getCustomer().getCountryCode() + "-"
                            + AppUtils.getCoveredCustomerContact(AppUtils.getOnlyIntegerValueFromString(getCustomer().getContactNumber())));
//                    if (getOrder().getEarnedLoyaltypoints() != 0) {
//                        left(rpad("Earned LoyalTea Points", 25) + getOrder().getEarnedLoyaltypoints());
//                    }
//                    if (getCustomer().getLoyaltyPoints() != 0) {
//                        left(rpad("Total LoyalTea Points", 25) + getCustomer().getLoyaltyPoints());
//                    }
//                    if (getCustomer().getChaayosCash() != null
//                            && getCustomer().getChaayosCash().compareTo(BigDecimal.ZERO) != 0) {
//                        left(rpad("Chaayos Cash", 20) + getCustomer().getChaayosCash());
//                    }
//                    if (getOrder().getPointsRedeemed() != 0) {
//                        left(rpad("Points Redeemed", 20) + Math.abs(getOrder().getPointsRedeemed()));
//                    }
                } else if (getOrder().getChannelPartner() != AppConstants.CHANNEL_PARTNER_ZOMATO
                        && getOrder().getChannelPartner() != AppConstants.CHANNEL_PARTNER_SWIGGY) {
                    left(rpad("Contact", 20) + getCustomer().getCountryCode() + "-" + getCustomer().getContactNumber());
                }
            }
        }
    }

    private void getCompanyName(String companyName) {
        String[] name = companyName.split(" ");
        StringBuilder naam = new StringBuilder();
        for (String a : name) {
            appendToBuilderNew(naam, a);
        }
        List<String> list = wordList(naam.toString(), 27);
        int i = 0;
        if (!list.isEmpty()) {
            for (String s : list) {
                if (i == 0) {
                    left(rpad("Company Name", 20) + s);
                } else {
                    left(rpad(" ", 20) + s);
                }
                i++;
            }
        }
    }

    private void getCompanyAddress(String companyAddress) {
        String[] address = companyAddress.split(" ");
        StringBuilder pata = new StringBuilder();
        for (String a : address) {
            appendToBuilderNew(pata, a);
        }
        List<String> list = wordList(pata.toString(), 27);
        int i = 0;
        if (!list.isEmpty()) {
            for (String s : list) {
                if (i == 0) {
                    left(rpad("Company Address", 20) + s);
                } else {
                    left(rpad(" ", 20) + s);
                }
                i++;
            }
        }
    }

    protected Customer getCustomer() {
        return detail.getCustomer();
    }

    protected boolean hasCustomer() {
        return Objects.nonNull(getOrder().getCustomerId()) && getOrder().getCustomerId() > 5 && Objects.nonNull(getCustomer());
    }

    protected void couponDetails() {
        if (hasValue(getOrder().getOfferCode())) {
            if (AppUtils.isChaayosCashOffer(getOrder().getOfferCode())) {
                left(rpad("Coupon Applied", 20) + "Chaayos Cash");
            } else {
                left(rpad("Coupon Applied", 20) + getOrder().getOfferCode());
            }
        }
    }

    protected void cancelledBillMark() {
        if (OrderStatus.CANCELLED.equals(detail.getOrder().getStatus())) {
            leftBold("Cancelled Bill");
            invalidBillMark();
        }
    }

    protected void itemDetails() {
        Map<String, Pair<Integer, Pair<String,OrderItem>>> paidAddonMap = new HashMap<>();
        left(lpad("Item", 2) + rpad("", 18) + lpad("Qty", 8) + lpad("Price", 6) + rpad("", 4) + lpad("Amount", 6));
        String productName = null;
        Map<String, List<OrderItem>> map = getMapOfOrderItemAndCode(getOrder().getOrders());//map level
        if (Objects.nonNull(map) && !map.isEmpty()) {
            List<OrderItem> paidAddonItems = constructPaidAddonMap(map,paidAddonMap);
            for (Map.Entry<String, List<OrderItem>> entry : map.entrySet()) {
                List<OrderItem> items =  entry.getValue();
                items.removeAll(paidAddonItems);
                if(!CollectionUtils.isEmpty(items)){
                    left(rpad(entry.getKey(), 22));
                    for (OrderItem item : items) {
                        writeProductName(paidAddonMap,item);
                    }
                }
            }
            //other addons
            if(!CollectionUtils.isEmpty(paidAddonMap)){
                for(Pair<Integer,Pair<String,OrderItem>> item : paidAddonMap.values()){
                    OrderItem paidAddonItem = item.getValue().getValue();
                    String key = item.getValue().getKey();
                    left(rpad(key, 22));
                    writeProductName(paidAddonMap,paidAddonItem);
                }
            }
        }
    }

    private List<OrderItem> constructPaidAddonMap(Map<String, List<OrderItem>> map,Map<String, Pair<Integer,Pair<String,OrderItem>>> paidAddonMap){
        List<OrderItem> paidAddonItems = new ArrayList<>();
        for (Map.Entry<String, List<OrderItem>> entry : map.entrySet()) { // constructing paidAddonMap
            String key = entry.getKey();
            if (Objects.nonNull(entry.getValue()) && !entry.getValue().isEmpty()) {
                for (OrderItem item : entry.getValue()) {
                    if (item.getProductId() != AppConstants.DELIVERY_PRODUCT_ID
                            && item.getProductId() != AppConstants.PACKAGING_PRODUCT_ID) {
                        if (item.getProductSubCategory().getId() == 1201) {
                            paidAddonItems.add(item);
                            if (paidAddonMap.containsKey(item.getProductName())) {
                                Pair<Integer, Pair<String,OrderItem>> paidAddon = paidAddonMap.get(item.getProductName());
                                paidAddon.setKey(paidAddon.getKey() + item.getQuantity());
                                paidAddonMap.put(item.getProductName(), paidAddon);
                            } else {
                                paidAddonMap.put(item.getProductName(), new Pair<>(item.getQuantity(), new Pair<>(key,item)));
                            }
                        }
                    }
                }
            }
        }
        return  paidAddonItems;
    }

    private Map<String, List<OrderItem>> getMapOfOrderItemAndCode(List<OrderItem> items) {
        Map<String, List<OrderItem>> map = new HashMap<>();
        for (OrderItem item : items) {
            if (Objects.nonNull(map)) {
                if (!map.isEmpty()) {
                    if (map.containsKey(item.getCode())) {
                        map.get(item.getCode()).add(item);
                    } else {
                        List<OrderItem> orderItems = new ArrayList<>();
                        orderItems.add(item);
                        map.put(item.getCode(), orderItems);
                    }
                } else {
                    List<OrderItem> orderItems = new ArrayList<>();
                    orderItems.add(item);
                    map.put(item.getCode(), orderItems);
                }
            }
        }
        return map;
    }

    protected void writeProductName(Map<String, Pair<Integer,Pair<String,OrderItem>>> paidAddonMap,OrderItem item){
        if (item.getProductId() != AppConstants.DELIVERY_PRODUCT_ID
                && item.getProductId() != AppConstants.PACKAGING_PRODUCT_ID) {
            String productName = "";
            if (markSpecial(item)) {
                productName = productName + "*";
            }
            if(!StringUtils.isEmpty(item.getProductAliasName())){
                productName = productName + item.getProductAliasName();
            }
            else {
                productName = productName + item.getProductName();
            }
            if (!AppConstants.NO_DIMENSION_STRING.equals(item.getDimension())) {
                productName = productName + " " + fillWhiteSpace(item.getDimension());
            }
            getProductName(addComposition(item, true), "", "", "");

            addComboItems(item);
            String price = item.getPrice().multiply(new BigDecimal(item.getQuantity()))
                    .setScale(2, BigDecimal.ROUND_UP).toString();
            BigDecimal itemPrice = item.getPrice();
            List<String > addons = new ArrayList<>();
            if(Objects.nonNull(item.getComposition()) && !CollectionUtils.isEmpty(item.getComposition().getOptions())){
                BigDecimal addonPrice = item.getPrice();
                BigDecimal addonAmount = item.getPrice().multiply(new BigDecimal(item.getQuantity()));
                for(String paidAddon : item.getComposition().getOptions()){
                    addons.add(paidAddon);
                    Pair<Integer,Pair<String,OrderItem>> paidAddonPair = paidAddonMap.get(paidAddon);
                    if(Objects.nonNull(paidAddonPair) && Objects.nonNull(paidAddonPair.getValue()) && Objects.nonNull(paidAddonPair.getValue().getValue())){
                        OrderItem paidAddonItem = paidAddonPair.getValue().getValue();
                        paidAddonPair.setKey(paidAddonPair.getKey()-item.getQuantity());
                        if(paidAddonPair.getKey() == 0){
                            paidAddonMap.remove(paidAddon);
                        }else {
                            paidAddonMap.put(paidAddon,paidAddonPair);
                        }
                        addonPrice = addonPrice.add(paidAddonItem.getPrice());
                        addonAmount = addonAmount.add(paidAddonItem.getPrice().multiply(new BigDecimal(item.getQuantity())));
                    }
                }
                price = addonPrice.setScale(2, BigDecimal.ROUND_UP).toString();
                itemPrice = addonAmount.setScale(2, BigDecimal.ROUND_UP);
            }
            getProductName(productName, price, Integer.toString(item.getQuantity()),
                    itemPrice.toString());
            if(!CollectionUtils.isEmpty(addons)){
                addPaidAddonNames(addons);
            }
        } else {
            deliveryPackaging.add(item);
        }
    }

    protected boolean markSpecial(OrderItem item) {
        if (item.getComplimentaryDetail() != null && item.getComplimentaryDetail().isIsComplimentary()) {
            return true;
        } else if (item.getDiscountDetail() != null && item.getDiscountDetail().getDiscountCode() != null
                && item.getDiscountDetail().getDiscountCode() > 0) {
            return true;
        } else if (item.getDiscountDetail() != null && item.getDiscountDetail().getPromotionalOffer() != null
                && BigDecimal.ZERO.compareTo(item.getDiscountDetail().getPromotionalOffer()) != 0) {
            return true;
        }
        return false;
    }

    private void getProductName(String companyAddress, String price, String qty, String val) {
        String[] address = companyAddress.split(" ");
        StringBuilder pata = new StringBuilder();
        for (String a : address) {
            appendToBuilderNew(pata, a);
        }
        List<String> list = wordList(pata.toString(), 23);
        if (!list.isEmpty()) {
            int i = 0;

            for (String s : list) {

                if (price != null && !price.isEmpty() && i == 0) {
                    left(rpad(s, 0) + rpad("", 23 - s.length()) + lpad(qty, 6) + lpad(val, 8) + lpad(price, 9));
                } else {
                    left(rpad(s, 0));
                }
                i++;
            }

        }

    }

    protected void addComposition(OrderItem item) {
        addComposition(item, false);
    }

    protected String addComposition(OrderItem item, boolean singleString) {
        String customization = "";
        if (item.getComposition().hasDefaultVariant()) {
            for (IngredientVariantDetail variant : item.getComposition().getVariants()) {
                if (variant.getAlias() != null && !variant.isDefaultSetting()) {
                    if (singleString) {
                        customization = customization + variant.getAlias() + ", ";
                    } else {
                        left(" -" + variant.getAlias());
                    }
                }
            }
        }
        if (item.getComposition().getProducts() != null && !item.getComposition().getProducts().isEmpty()) {
            String s = "";
            for (IngredientProductDetail product : item.getComposition().getProducts()) {
                if (product.getProduct().getName() != null) {
                    s = s + product.getProduct().getName() + " -";
                }
            }
            if (s.length() > 0) {
                if (singleString) {
                    customization = customization + s.trim() + ", ";
                } else {
                    left(" -" + s.trim());
                }
            }
        }
        if (item.getComposition().getAddons() != null && !item.getComposition().getAddons().isEmpty()) {
            String s = "";
            for (IngredientProductDetail addon : item.getComposition().getAddons()) {
                if (addon.getProduct().getShortCode() != null) {
                    s = s + addon.getProduct().getShortCode() + ", ";
                }
            }
            if (s.length() > 0) {
                if (singleString) {
                    customization = customization + s.trim().substring(0, s.length() - 1) + ", ";
                } else {
                    left(" -" + s.trim().substring(0, s.length() - 1));
                }
            }
        }
        if (item.getComposition().getOptions() != null && !item.getComposition().getOptions().isEmpty()) {
            String s = "";
            for (String option : item.getComposition().getOptions()) {
                s = s + option + ", ";
            }
            if (s.length() > 0) {
                if (!singleString) {
                    left(" -" + s.trim().substring(0, s.length() - 1));
                }
            }
        }

        return customization.replaceAll("(,,)+", ",").trim().replaceAll(",$", "");
    }

    protected void addComboItems(OrderItem item) {
        if (AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE == item.getProductCategory().getId()) {
            String menuItemName = "";
            String customisation = "";
            for (OrderItem menuItem : item.getComposition().getMenuProducts()) {
                menuItemName = "  *(" + menuItem.getCode() + ")" + menuItem.getProductName();
                if (!AppConstants.NO_DIMENSION_STRING.equals(item.getDimension())) {
                    menuItemName = menuItemName + fillWhiteSpace(menuItem.getDimension());
                }
                left(menuItemName);
                customisation = addComposition(item, true);
                if (customisation != null & customisation.length() > 0) {
                    left("    " + customisation.trim());
                }
            }
        }
    }

    private void addPaidAddonNames(List<String> addonNames) {
        for (String name : addonNames) {
            List<String> list = wordList(name.toString(), 23);
            if (!list.isEmpty()) {
                int i = 0;
                for (String s : list) {
                    if (i == 0) {
                        left(RawPrintHelper.smallFont(rpad("--", 0) + rpad(s, 0) + rpad("", 23 - s.length())));
                    } else {
                        left(RawPrintHelper.smallFont(rpad(s, 0)));
                    }
                    i++;
                }
            }
        }

    }

    protected void transactionDetails() {
        String productName = null;
        for (OrderItem item : deliveryPackaging) {
            productName = "";
            if (markSpecial(item)) {
                productName = productName + "*";
            }
            productName = productName + item.getProductName();
            if (!AppConstants.NO_DIMENSION_STRING.equals(item.getDimension())) {
                productName = productName + " " + fillWhiteSpace(item.getDimension());
            }
            left(rpad(productName, 36) + lpad(
                    item.getPrice().multiply(new BigDecimal(item.getQuantity())).setScale(2, BigDecimal.ROUND_HALF_UP),
                    10));
        }
        left(rpad("Total", 36) + lpad(getTransactionDetail().getTotalAmount(), 10));
        if (hasPromotionalDiscount()) {
            left(rpad("Promotional Offer", 36) + lpad(getDiscountDetail().getPromotionalOffer(), 10));
        }
        if (hasDiscount()) {
            left(rpad("Discount @ " + getDiscountDetail().getDiscount().getPercentage() + " % ", 36)
                    + lpad(getDiscountDetail().getDiscount().getValue(), 10));
        } else if (hasFreebieDiscount()) {
            left(rpad("Discount" , 36)
                    + lpad(getDiscountDetail().getDiscount().getValue(), 10));
        }
        if(Objects.nonNull(getTransactionDetail().getServiceCharge()) && BigDecimal.ZERO.compareTo(getTransactionDetail().getServiceCharge()) != 0){
            left(rpad(AppConstants.SERVICE_CHARGE + " @ " + getTransactionDetail().getServiceChargePercent() + " %", 36) + lpad(getTransactionDetail().getServiceCharge(), 10));
        }
        if (Objects.nonNull(getOrder().getInvoice())
                && getOrder().getInvoice().getTaxType().equals(AppConstants.INTER_STATE)) {
            for (int i = 0; i < getTransactionDetail().getTaxes().size() - 1; i++) {
                if (getTransactionDetail().getTaxes().get(i).getValue()
                        .compareTo(getTransactionDetail().getTaxes().get(i + 1).getValue()) == 0) {
                    left(rpad(
                            AppConstants.IGST + " @ "
                                    + getTransactionDetail().getTaxes().get(i).getPercentage()
                                    .add(getTransactionDetail().getTaxes().get(i + 1).getPercentage())
                                    + " %",
                            36)
                            + lpad(getTransactionDetail().getTaxes().get(i).getValue()
                            .add(getTransactionDetail().getTaxes().get(i + 1).getValue()), 10));
                    i++;
                }
            }
        } else {
            for (TaxDetail tax : getTransactionDetail().getTaxes()) {
                left(rpad(tax.getCode() + " @ " + tax.getPercentage() + " %", 36) + lpad(tax.getValue(), 10));
            }
        }
        if (hasRoundOff()) {
            left(rpad("Round Off", 36) + lpad(getTransactionDetail().getRoundOffValue(), 10));
        }

        leftBold(rpad("Bill Total", 36) + lpad(getTransactionDetail().getPaidAmount(), 10));

        String text = "Rs. "
                + NumberToWord.getInstance().convertNumberToWords(getTransactionDetail().getPaidAmount().intValue());
        for (String s : wordList(text, 46)) {
            leftBold(s);
        }
    }

    protected TransactionDetail getTransactionDetail() {
        return getOrder().getTransactionDetail();
    }

    protected boolean hasPromotionalDiscount() {
        return getDiscountDetail() != null && getDiscountDetail().getPromotionalOffer() != null
                && BigDecimal.ZERO.compareTo(getDiscountDetail().getPromotionalOffer()) != 0;
    }

    protected DiscountDetail getDiscountDetail() {
        return getOrder().getTransactionDetail().getDiscountDetail();
    }

    protected boolean hasDiscount() {
        return getDiscountDetail() != null && getDiscountDetail().getDiscount() != null
                && BigDecimal.ZERO.compareTo(getDiscountDetail().getDiscount().getPercentage()) != 0
                && BigDecimal.ZERO.compareTo(getDiscountDetail().getDiscount().getValue()) != 0;
    }

    protected boolean hasFreebieDiscount(){
        try {
            return getDiscountDetail() != null && getDiscountDetail().getDiscount() != null
                    && BigDecimal.ZERO.compareTo(getDiscountDetail().getDiscount().getPercentage()) == 0
                    && BigDecimal.ZERO.compareTo(getDiscountDetail().getDiscount().getValue()) != 0;
        }catch (Exception e){
            return false;
        }
    }

    protected boolean hasRoundOff() {
        return BigDecimal.ZERO.compareTo(getTransactionDetail().getRoundOffValue()) != 0;
    }

    protected void companyDetails(Company company) {
        left("CIN	" + company.getCin());
        left("GSTIN	" + getUnit().getTin());
        if (getUnit().getFssai() != null) {
            left("FSSAI	" + getUnit().getFssai());
        }
        left(company.getName());
        for (String line : company.getRegisteredAddress().getLine1().split(",")) {
            left(rpad(line.trim(), 20));
        }
        left(company.getRegisteredAddress().getCity() + " " + company.getRegisteredAddress().getZipCode());
    }

    protected void unitDetails() {
        center(" ");
        center(bold(getUnit().getReferenceName()));
        if(Objects.nonNull(getUnit().getCafeTimingTrimmedData())){
            if(getUnit().getCafeTimingTrimmedData().isIscafe24x7()){
                center(bold(doubleFont("OPEN 24x7 ALL DAYS")));
            }else{
                if(Objects.nonNull(getUnit().getCafeTimingTrimmedData().getDineInTimings())){
                    String timingString = null;
                    try {
                        if (getUnit().getCafeTimingTrimmedData().getDineInTimings().containsKey(getDetails().getBrand().getBrandId())) {
                            timingString = getUnit().getCafeTimingTrimmedData().getDineInTimings().
                                    get(getDetails().getBrand().getBrandId()).getTimingString();
                            if (!StringUtils.isEmpty(timingString) && timingString.length() >= 16) {
                                String trimmedString = timingString.substring(11, 16);
                                if (Integer.parseInt(trimmedString.split(":")[0]) == 23) {
                                    if (Integer.parseInt(trimmedString.split(":")[1]) > 50) {
                                        timingString = timingString.replace(trimmedString, "12:00");
                                        timingString = timingString.replace("PM", "AM");
                                    }
                                }
                            }
                        }
                    }finally {
                        if (!StringUtils.isEmpty(timingString)) {
                            center(bold(doubleFont("CAFE")) + " " + bold(doubleFont("TIMINGS")));
                            center(bold(doubleFont(timingString)));
                        }
                    }
                }
            }
        }
        left(getUnit().getAddress().getLine1());
        left(getUnit().getAddress().getLine2());
        left(getUnit().getAddress().getLine3());
        left(getUnit().getAddress().getCity() + ", " + getUnit().getAddress().getState());
    }

    public OrderInfo getDetails() {
        return detail;
    }



}
