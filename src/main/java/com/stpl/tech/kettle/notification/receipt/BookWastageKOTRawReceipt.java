package com.stpl.tech.kettle.notification.receipt;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;

import org.apache.commons.lang3.StringUtils;

import com.stpl.tech.kettle.cache.BrandMetaDataCache;
import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.KOTType;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.notification.RawPrintData;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.RawPrintHelper;
import com.stpl.tech.kettle.util.SpringBeanProvider;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;

public class BookWastageKOTRawReceipt extends RawPrintReceipt {

	private KOTType type;
	private final RawPrintData printData;
	private EnvironmentProperties env;
	private Order detail;
	private String basePath;
	private Brand brand;
	private boolean isReprint = false;
	private Unit unit;

	List<OrderItem> deliveryPackaging = new ArrayList<>(2);

	public BookWastageKOTRawReceipt(Unit unit, Order detail, String basePath, EnvironmentProperties env, KOTType type) {
		super();
		BrandMetaDataCache brandMetaDataCache = SpringBeanProvider.getBean(BrandMetaDataCache.class);
		this.detail = detail;
		this.basePath = basePath;
		this.unit = unit;
		this.type = type;
		this.brand = brandMetaDataCache.getBrandMetaData().get(detail.getBrandId());
		this.env = env;
		ArrayList<OrderItem> items = new ArrayList<>();
		ProductCache productCache = SpringBeanProvider.getBean(ProductCache.class);
		for (OrderItem item : getOrder().getOrders()) {
			if (AppConstants.GIFT_CARD_TAX_CODE.equals(item.getCode())) {
				continue;
			}
			ProductBasicDetail product = productCache.getProductBasicDetailById(item.getProductId());
			if (product.getType() == type.getId() || type.getId() == -1) {
				items.add(item);
			}
			if (product.getType() == AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE) {
				for (OrderItem addon : item.getComposition().getMenuProducts()) {
					product = productCache.getProductBasicDetailById(addon.getProductId());
					if (product.getType() == type.getId()) {
						items.add(addon);
					}
				}
			}
		}
		printData = mergeItems(items);
	}

	@Override
	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getOrder().getOrderId() + "/BookWastageKOTPrint-"
				+ type.getDescription() + "-" + getOrder().getOrderId() + ".html";
	}

	@Override
	public void setReprint() {
		isReprint = true;
	}

	@Override
	public StringBuilder processData() {
		if (printData.getItems().isEmpty()) {
			return null;
		}
		reset();

		if (TransactionUtils.isSpecialOrder(getOrder())) {
			center(bold(doubleFont(RawPrintHelper.getOrderSource(getOrder().getSource(), getBrand()))));
			left("  ");
			unitDetails();
			separator();
//            left(rpad("Type", 5) + bold(doubleFont(type.getDescription())));
//            left("	 ");
		} else {
			left("	-");
			left("	-");
			left("	-");
			left(rpad("Type", 15) + bold(doubleFont(type.getDescription())));
		}
		orderType();
		if (hasValue(getOrder().getTokenNumber()) && !isCOD()) {
			left(rpad("Token Number", 15) + getOrder().getTokenNumber());
		}
		if (hasTable() && !isCOD()) {
			left(rpad("Table No", 15) + getOrder().getTableNumber());
		}
		if (getOrder().getCustomerName() != null) {
			left(rpad("Name", 15) + bold(doubleFont(getOrder().getCustomerName())));
		}
		left(rpad("Order No", 15) + getOrder().getGenerateOrderId());
		if (TransactionUtils.isSpecialOrder(getOrder())) {
			left(rpad("Order Time", 15) + AppUtils.getBillPrintFormat(getOrder().getBillingServerTime()));
		} else {
			left(rpad("Order Time", 15) + AppUtils.getBillPrintTime(getOrder().getBillingServerTime()));
		}
		left(rpad("Source", 15) + bold(orderSource()));
		if (getOrder().getOrderRemark() != null) {
			left(rpad("Order Remark", 15) + getOrder().getOrderRemark());
		}
		separator();
		itemDetails();

		// left("123456789123456789123456789123456789123456789123456789123456789");
		cut();

		return getSb();
	}

//    protected void channelPartnerOrder() {
//        if (isChannelPartnerOrder() && hasValue(getOrder().getSourceId())) {
//            left(rpad("Partner Order No.", 20) + getOrder().getSourceId());
//        }
//    }
//
//    protected boolean isChannelPartnerOrder() {
//        return hasValue(getOrder().getChannelPartner())
//                && (getOrder().getChannelPartner() == AppConstants.CHANNEL_PARTNER_ZOMATO
//                || getOrder().getChannelPartner() == AppConstants.CHANNEL_PARTNER_SWIGGY);
//    }
//    protected void channelPartnerCustomer() {
//        if (isChannelPartnerCustomer() && getChannelPartner().getId()==6 && hasValue(getOrder().getPartnerCustomerId())) {
//            left(rpad(getChannelPartner().getName()+" Customer No.", 20) + getOrder().getPartnerCustomerId());
//        }
//    }
//
//    protected boolean isChannelPartnerCustomer() {
//        return hasValue(getOrder().getChannelPartner())
//                && (getOrder().getChannelPartner() == AppConstants.CHANNEL_PARTNER_SWIGGY);
//    }
//    public String trimToPasscode(String s){
//
//        if (s.length() >= 4) {
//            return s.substring(s.length() - 4);
//        }
//        return "";
//    }
//
//    protected void channelPartnerDetails() {
//        left(rpad("Channel Partner", 20) + getChannelPartner().getName());
//        if (isChannelPartnerOrder() && hasValue(getOrder().getSourceId())) {
//            left(rpad(getChannelPartner().getName()+" Order ID", 20) + getOrder().getSourceId());
//        }
//        if (isChannelPartnerCustomer() && getOrder().getChannelPartner() == AppConstants.CHANNEL_PARTNER_SWIGGY && hasValue(getOrder().getPartnerCustomerId()))  {
//            String partnerCustomerId=trimToPasscode(getOrder().getPartnerCustomerId());
//            left(rpad("Passcode ", 20) + partnerCustomerId);
//        }
//
//
//    }
//
//    private void deliveryDetails() {
//        Address deliveryAddress = null;
//        if (detail.getOrder().getDeliveryAddress() != null && detail.getCustomer().getAddresses() != null) {
//            for (Address address : detail.getCustomer().getAddresses()) {
//                if (detail.getOrder().getDeliveryAddress() == address.getId()) {
//                    deliveryAddress = address;
//                }
//            }
//        }
//
//        if (deliveryAddress == null) {
//            return;
//        }
//
//        StringBuilder address = new StringBuilder();
//        appendToBuilder(address, deliveryAddress.getAddressType());
//        appendToBuilder(address, deliveryAddress.getCompany());
//        appendToBuilder(address, deliveryAddress.getLine1());
//        appendToBuilder(address, deliveryAddress.getLine2());
//        appendToBuilder(address, deliveryAddress.getLine3());
//        appendToBuilder(address, deliveryAddress.getLandmark());
//        appendToBuilder(address, deliveryAddress.getLocality());
//        appendToBuilder(address, deliveryAddress.getCity());
//
//        List<String> list = wordList(address.toString(), 27);
//        int i = 0;
//        if (!list.isEmpty()) {
//            for (String s : list) {
//                if (i == 0) {
//                    left(rpad("Delivery Address", 20) + s);
//                } else {
//                    left(rpad(" ", 20) + s);
//                }
//                i++;
//            }
//        }
//    }
//
//    protected void couponDetails() {
//        if (hasValue(getOrder().getOfferCode())) {
//            if (AppUtils.isChaayosCashOffer(getOrder().getOfferCode())) {
//                left(rpad("Coupon Applied", 20) + "Chaayos Cash");
//            } else {
//                left(rpad("Coupon Applied", 20) + getOrder().getOfferCode());
//            }
//        }
//    }
//
//    protected void companyDetails(Company company) {
//        left("CIN	" + company.getCin());
//        left("GSTIN	" + getUnit().getTin());
//        if(getUnit().getFssai()!=null) {
//            left("FSSAI	" + getUnit().getFssai());
//        }
//        left(company.getName());
//        left(rpad(company.getRegisteredAddress().getLine1(), 10));
//        left(company.getRegisteredAddress().getCity() + " " + company.getRegisteredAddress().getZipCode());
//    }
//
//    protected void settlementDetails() {
//        String mode = null;
//        for (Settlement s : getOrder().getSettlements()) {
//            mode = "";
//            if (s.getMode() == AppConstants.PAYMENT_MODE_DINE_IN_CREDIT) {
//                mode = "Pending";
//            } else if (PaymentCategory.ONLINE.equals(s.getModeDetail().getCategory())) {
//                mode = "Online";
//            } else {
//                mode = s.getModeDetail().getDescription();
//            }
//            left(bold(rpad(mode, 36)) + lpad(s.getAmount(), 10));
//        }
//    }
//
//    protected void transactionDetails() {
//        String productName = null;
//        for (OrderItem item : deliveryPackaging) {
//            productName = "";
//            if (markSpecial(item)) {
//                productName = productName + "*";
//            }
//            productName = productName + item.getProductName();
//            if (!AppConstants.NO_DIMENSION_STRING.equals(item.getDimension())) {
//                productName = productName + " " + fillWhiteSpace(item.getDimension());
//            }
//            left(rpad(productName, 36) + lpad(
//                    item.getPrice().multiply(new BigDecimal(item.getQuantity())).setScale(2, BigDecimal.ROUND_HALF_UP),
//                    10));
//        }
//        left(rpad("Total", 36) + lpad(getTransactionDetail().getTotalAmount(), 10));
//        if (hasPromotionalDiscount()) {
//            left(rpad("Promotional Offer", 36) + lpad(getDiscountDetail().getPromotionalOffer(), 10));
//        }
//        if (hasDiscount()) {
//            left(rpad("Discount @ " + getDiscountDetail().getDiscount().getPercentage() + " % ", 36)
//                    + lpad(getDiscountDetail().getDiscount().getValue(), 10));
//        }
//        if (Objects.nonNull(getOrder().getInvoice()) && getOrder().getInvoice().getTaxType().equals(AppConstants.INTER_STATE)) {
//            for(int i=0;i<getTransactionDetail().getTaxes().size()-1;i++){
//                if(getTransactionDetail().getTaxes().get(i).getValue().compareTo(getTransactionDetail().getTaxes().get(i+1).getValue())==0){
//                    left(rpad(AppConstants.IGST + " @ " + getTransactionDetail().getTaxes().get(i).getPercentage().add(getTransactionDetail().getTaxes().get(i+1).getPercentage()) + " %", 36) + lpad(getTransactionDetail().getTaxes().get(i).getValue().add(getTransactionDetail().getTaxes().get(i+1).getValue()), 10));
//                    i++;
//                }
//            }
//
////				left(rpad(AppConstants.IGST + " @ " + sumPercentage + " %", 36) + lpad(sumTaxValue, 10));
//        }
//        else{
//            for (TaxDetail tax : getTransactionDetail().getTaxes()) {
//                left(rpad(tax.getCode() + " @ " + tax.getPercentage() + " %", 36) + lpad(tax.getValue(), 10));
//            }
//        }
//        if (hasRoundOff()) {
//            left(rpad("Round Off", 36) + lpad(getTransactionDetail().getRoundOffValue(), 10));
//        }
//
//        leftBold(rpad("Bill Total", 36) + lpad(getTransactionDetail().getPaidAmount(), 10));
//
//        String text = "Rs. "
//                + NumberToWord.getInstance().convertNumberToWords(getTransactionDetail().getPaidAmount().intValue());
//        for (String s : wordList(text, 46)) {
//            leftBold(s);
//        }
//    }
//
//    protected boolean hasRoundOff() {
//        return BigDecimal.ZERO.compareTo(getTransactionDetail().getRoundOffValue()) != 0;
//    }
//
//    protected boolean hasDiscount() {
//        return getDiscountDetail() != null && getDiscountDetail().getDiscount() != null
//                && BigDecimal.ZERO.compareTo(getDiscountDetail().getDiscount().getPercentage()) != 0
//                && BigDecimal.ZERO.compareTo(getDiscountDetail().getDiscount().getValue()) != 0;
//    }
//
//    protected boolean hasPromotionalDiscount() {
//        return getDiscountDetail() != null && getDiscountDetail().getPromotionalOffer() != null
//                && BigDecimal.ZERO.compareTo(getDiscountDetail().getPromotionalOffer()) != 0;
//    }
//
//    protected TransactionDetail getTransactionDetail() {
//        return getOrder().getTransactionDetail();
//    }
//
//    protected DiscountDetail getDiscountDetail() {
//        return getOrder().getTransactionDetail().getDiscountDetail();
//    }
//    private void getProductName(String companyAddress, String price,String qty, String val) {
//        String[] address = companyAddress.split(" ");
//        StringBuilder pata = new StringBuilder();
//        for( String a: address){
//            appendToBuilderNew(pata, a);
//        }
//        List<String> list = wordList(pata.toString(), 23);
//        if (!list.isEmpty()) {
//            int i=0;
//
//            for (String s : list) {
//
//                if(price!=null && !price.isEmpty() && i==0){
//                    left(rpad(s, 0)+rpad("", 23-s.length()) +lpad(qty,6) + lpad(val,8)+ lpad(price,9));
//                }
//                else{
//                    left(rpad(s,0));
//                }
//                i++;
//            }
//
//        }
//
//    }

	protected void itemDetails(StringBuilder builder) {
		this.sb = builder;
		itemDetails();
	}

	protected void itemDetails() {
		for (int i = 0; i < printData.getItems().size(); i++) {
			OrderItem item = printData.getItems().get(i);
			left(bold(rpad(item.getProductName(), 38).substring(0, 38) + lpad(item.getDimension().substring(0, 1), 3)
					+ lpad(printData.getSizeMap().get(i), 5)));
			addTakeAway(item);
			addComposition(item);
			addComboItems(item);
			left(" ");
		}
	}

	private Map<String, List<OrderItem>> getMapOfOrderItemAndCode(List<OrderItem> items) {
		Map<String, List<OrderItem>> map = new HashMap<>();
		for (OrderItem item : items) {
			if (Objects.nonNull(map)) {
				if (!map.isEmpty()) {
					if (map.containsKey(item.getCode())) {
						map.get(item.getCode()).add(item);
					} else {
						List<OrderItem> orderItems = new ArrayList<>();
						orderItems.add(item);
						map.put(item.getCode(), orderItems);
					}
				} else {
					List<OrderItem> orderItems = new ArrayList<>();
					orderItems.add(item);
					map.put(item.getCode(), orderItems);
				}
			}
		}
		return map;
	}

	protected void addTakeAway(OrderItem item) {
		if (TransactionUtils.isTakeawayOrder(getOrder().getSource())
				|| item.getTakeAway() != null && item.getTakeAway()) {
			left(bold("    **Take Away"));
		}
	}

	protected void addComboItems(OrderItem combo) {
		if (AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE == combo.getProductId()) {
			for (OrderItem menuItem : combo.getComposition().getMenuProducts()) {
				left(" -" + rpad(menuItem.getProductName(), 36).substring(0, 36)
						+ lpad(menuItem.getDimension().substring(0, 1), 3) + lpad(menuItem.getQuantity(), 5));
				addTakeAway(menuItem);
				addComposition(menuItem);
			}
		}
	}

	protected boolean markSpecial(OrderItem item) {
		if (item.getComplimentaryDetail() != null && item.getComplimentaryDetail().isIsComplimentary()) {
			return true;
		} else if (item.getDiscountDetail() != null && item.getDiscountDetail().getDiscountCode() != null
				&& item.getDiscountDetail().getDiscountCode() > 0) {
			return true;
		} else if (item.getDiscountDetail() != null && item.getDiscountDetail().getPromotionalOffer() != null
				&& BigDecimal.ZERO.compareTo(item.getDiscountDetail().getPromotionalOffer()) != 0) {
			return true;
		}
		return false;
	}

	protected boolean isCOD() {
		return AppConstants.COD.equals(getOrder().getSource());
	}

	protected boolean isDineIn() {
		return AppConstants.CAFE.equals(getOrder().getSource());
	}

	protected boolean isTakeAway() {
		return AppConstants.TAKE_AWAY.equals(getOrder().getSource());
	}

//    protected void header() {
//        center(bold(doubleFont(RawPrintHelper.getOrderSource(getOrder().getSource(), detail.getBrand()))));
//        if(detail.getBrand() != null && detail.getBrand().getBillTag() != null) {
//            center(detail.getBrand().getBillTag());
//        }
//        if(detail.getBrand() != null && detail.getBrand().getDomain() != null) {
//            center(doubleFont("Order Online"));
//            center(bold(doubleFont(detail.getBrand().getDomain())));
//        }
//        if (hasToken()) {
//            center(bold(doubleFont("Token : " + getOrder().getTokenNumber())));
//        }
//        left("");
//    }

	protected void unitDetails() {
		// left(getUnit().getReferenceName());
		left(getUnit().getAddress().getLine2());
		left(getUnit().getAddress().getCity() + ", " + getUnit().getAddress().getState() + rpad("", 15)
				+ AppUtils.getBillPrintFormat(getOrder().getBillingServerTime()).split(" ")[0]);
		// left(rpad("Order Date", 15) +
		// AppUtils.getBillPrintFormat(getOrder().getBillingServerTime()).split("
		// ")[0]);
	}

	protected void orderMetadata() {
		if (hasTable() && !isCOD()) {
			left(rpad("Table No", 20) + getOrder().getTableNumber());
		}
		if (Objects.nonNull(getOrder().getInvoice())) {
			left(rpad("Invoice No", 20) + getOrder().getInvoice().getInvoiceId());
		} else {
			left(rpad("Order No", 20) + getOrder().getGenerateOrderId());
		}
		left(rpad("Order Time", 20) + AppUtils.getBillPrintFormat(getOrder().getBillingServerTime()));
		if (getOrder().getBillBookNo() != null && getOrder().getBillBookNo() != 0) {
			left(rpad("Bill Book No", 20) + getOrder().getBillBookNo());
		}
	}

	protected void orderType() {
		if (TransactionUtils.isSpecialOrder(getOrder())) {
			center(doubleFont(getOrderType(getOrder().getOrderType())));
		}
	}

	protected String getOrderType(String orderType) {
		String s = "";
		switch (orderType) {
		case "employee-meal":
			s = "Employee Meal";
			break;
		case "complimentary-order":
			s = "Complimentary Order";
			break;
		case "unsatisfied-customer-order":
			s = "Customer Complimentary";
			break;
		case "paid-employee-meal":
			s = "Employee Meal";
			break;
		case "wastage-order":
			s = "Wastage Order";
			break;
		default:
			s = "";
		}
		return s;
	}

//    protected void customerDetails() {
//        if (Objects.nonNull(getOrder().getInvoice())){
////			left(rpad("Company Name", 20) + getOrder().getInvoice().getCompanyName());
//            getCompanyName(getOrder().getInvoice().getCompanyName());
//            getCompanyAddress(getOrder().getInvoice().getCompanyAddress());
//            left(rpad("Company GSTIN", 20) + getOrder().getInvoice().getGstIn());
//        }
//        else {
//            if (getOrder().getCustomerName() != null) {
//                left(rpad("Name", 20) + getOrder().getCustomerName());
//            }
//            if (hasCustomer()) {
//                if (!isCOD()) {
//                    left(rpad("Contact", 20) + getCustomer().getCountryCode() + "-"
//                            + AppUtils.getCoveredCustomerContact(getCustomer().getContactNumber()));
//                    if (getOrder().getEarnedLoyaltypoints() != 0) {
//                        left(rpad("Earned LoyalTea Points", 25) + getOrder().getEarnedLoyaltypoints());
//                    }
//                    if (getCustomer().getLoyaltyPoints() != 0) {
//                        left(rpad("Total LoyalTea Points", 25) + getCustomer().getLoyaltyPoints());
//                    }
//                    if (getCustomer().getChaayosCash() != null
//                            && getCustomer().getChaayosCash().compareTo(BigDecimal.ZERO) != 0) {
//                        left(rpad("Chaayos Cash", 20) + getCustomer().getChaayosCash());
//                    }
//                    if (getOrder().getPointsRedeemed() != 0) {
//                        left(rpad("Points Redeemed", 20) + Math.abs(getOrder().getPointsRedeemed()));
//                    }
//                }
//                else if(getOrder().getChannelPartner() != AppConstants.CHANNEL_PARTNER_ZOMATO && getOrder().getChannelPartner() != AppConstants.CHANNEL_PARTNER_SWIGGY) {
//                    left(rpad("Contact", 20) + getCustomer().getCountryCode() + "-" + getCustomer().getContactNumber());
//                }
//            }
//        }
//    }

	private void getCompanyName(String companyName) {
		String[] name = companyName.split(" ");
		StringBuilder naam = new StringBuilder();
		for (String a : name) {
			appendToBuilderNew(naam, a);
		}
		List<String> list = wordList(naam.toString(), 27);
		int i = 0;
		if (!list.isEmpty()) {
			for (String s : list) {
				if (i == 0) {
					left(rpad("Company Name", 20) + s);
				} else {
					left(rpad(" ", 20) + s);
				}
				i++;
			}
		}
	}

	private void getCompanyAddress(String companyAddress) {
		String[] address = companyAddress.split(" ");
		StringBuilder pata = new StringBuilder();
		for (String a : address) {
			appendToBuilderNew(pata, a);
		}
		List<String> list = wordList(pata.toString(), 27);
		int i = 0;
		if (!list.isEmpty()) {
			for (String s : list) {
				if (i == 0) {
					left(rpad("Company Address", 20) + s);
				} else {
					left(rpad(" ", 20) + s);
				}
				i++;
			}
		}
	}

//    protected Customer getCustomer() {
//        return detail.getCustomer();
//    }

	protected boolean hasCustomer() {
		return getOrder().getCustomerId() > 5;
	}

	protected Order getOrder() {
		return detail;
	}

	protected Brand getBrand() {
		return brand;
	}

	protected void duplicateCopyMark() {
		if (isReprint) {
			leftBold("Duplicate Copy Of The Bill");
		}
	}

//    protected void invalidBillMark() {
//        if (!AppUtils.isProd(detail.getEnv())) {
//            leftBold("This Bill Is Not Valid");
//        }
//    }
//
//    protected void cancelledBillMark() {
//        if (OrderStatus.CANCELLED.equals(detail.getOrder().getStatus())) {
//            leftBold("Cancelled Bill");
//            invalidBillMark();
//        }
//    }

	protected boolean hasToken() {
		return getOrder().getTokenNumber() != null && getOrder().getTokenNumber() > 0
				&& !AppConstants.COD.equals(getOrder().getSource());
	}

	protected boolean hasTable() {
		return getUnit().isTableService() && getOrder().getTableNumber() != null;
	}

	public Unit getUnit() {
		return unit;
	}

	public Order getDetails() {
		return detail;
	}

	protected String getBasePath() {
		return basePath;
	}

//    protected IdCodeName getDeliveryPartner() {
//        return detail.getDeliveryPartner();
//    }
//
//    protected IdCodeName getChannelPartner() {
//        return detail.getChannelPartner();
//    }

	protected void addComposition(OrderItem item) {
		addComposition(item, false);
	}

	protected String addComposition(OrderItem item, boolean singleString) {
		String customization = "";
		if (Objects.nonNull(item.getComposition()) && item.getComposition().hasDefaultVariant()) {
			for (IngredientVariantDetail variant : item.getComposition().getVariants()) {
				if (variant.getAlias() != null && !variant.isDefaultSetting()) {
					if (singleString) {
						customization = customization + variant.getAlias() + ", ";
					} else {
						left(" -" + variant.getAlias());
					}
				}
			}
		}
		if (Objects.nonNull(item.getComposition()) && item.getComposition().getProducts() != null
				&& !item.getComposition().getProducts().isEmpty()) {
			String s = "";
			for (IngredientProductDetail product : item.getComposition().getProducts()) {
				if (product.getProduct().getName() != null) {
					s = s + product.getProduct().getName() + " -";
				}
			}
			if (s.length() > 0) {
				if (singleString) {
					customization = customization + s.trim() + ", ";
				} else {
					left(" -" + s.trim());
				}
			}
		}
		if (Objects.nonNull(item.getComposition()) && item.getComposition().getAddons() != null
				&& !item.getComposition().getAddons().isEmpty()) {
			String s = "";
			for (IngredientProductDetail addon : item.getComposition().getAddons()) {
				if (addon.getProduct().getShortCode() != null) {
					s = s + addon.getProduct().getShortCode() + ", ";
				}
			}
			if (s.length() > 0) {
				if (singleString) {
					customization = customization + s.trim().substring(0, s.length() - 1) + ", ";
				} else {
					left(" -" + s.trim().substring(0, s.length() - 1));
				}
			}
		}
		if (Objects.nonNull(item.getComposition()) && item.getComposition().getOptions() != null
				&& !item.getComposition().getOptions().isEmpty()) {
			String s = "";
			for (String option : item.getComposition().getOptions()) {
				s = s + option + ", ";
			}
			if (s.length() > 0) {
				if (!singleString) {
					left(" -" + s.trim().substring(0, s.length() - 1));
				}
			}
		}

		return customization.replaceAll("(,,)+", ",").trim().replaceAll(",$", "");
	}

	protected String orderSource() {
		if (isCOD()) {
			return AppConstants.DELIVERY;
		}
		if (isDineIn()) {
			return AppConstants.DINE_IN;
		}
		if (isTakeAway()) {
			return AppConstants.TAKE_AWAY;
		}
		return AppConstants.DINE_IN;
	}

	public RawPrintData getPrintData() {
		return printData;
	}

	public KOTType getType() {
		return type;
	}

	@Override
	protected RawPrintData mergeItems(List<OrderItem> items) {
		RawPrintData data = new RawPrintData(new ArrayList<>(), new HashMap<>());
		Map<String, Integer> map = new HashMap<>();
		for (OrderItem item : items) {
			String productString = getProductStringWastage(item);
			if (map.containsKey(productString)) {
				int index = map.get(productString);
				int newCount = item.getQuantity();
				int existingCount = data.getSizeMap().get(index);
				data.getSizeMap().put(index, newCount + existingCount);
			} else {
				data.getItems().add(item);
				int index = data.getItems().size() - 1;
				map.put(productString, index);
				data.getSizeMap().put(index, item.getQuantity());
			}
		}
		return data;
	}

	/**
	 * @param item
	 * @return
	 */

	private String getProductStringWastage(OrderItem item) {
		StringBuffer b = new StringBuffer();
		b.append(item.getProductName()).append(item.getDimension());
		addCompositionToBufferWastage(item, b);
		return b.toString().toLowerCase();
	}

	protected void addCompositionToBufferWastage(OrderItem item, StringBuffer b) {
		Set<String> set = new TreeSet<>();
		if (Objects.nonNull(item.getComposition()) && item.getComposition().hasDefaultVariant()) {
			for (IngredientVariantDetail variant : item.getComposition().getVariants()) {
				if (variant.getAlias() != null && !variant.isDefaultSetting()) {
					set.add(variant.getAlias());
				}
			}
		}
		if (Objects.nonNull(item.getComposition()) && item.getComposition().getProducts() != null
				&& !item.getComposition().getProducts().isEmpty()) {
			for (IngredientProductDetail product : item.getComposition().getProducts()) {
				if (product.getProduct().getName() != null) {
					set.add(product.getProduct().getName());
				}
			}
		}
		if (Objects.nonNull(item.getComposition()) && item.getComposition().getAddons() != null
				&& !item.getComposition().getAddons().isEmpty()) {
			for (IngredientProductDetail addon : item.getComposition().getAddons()) {
				if (addon.getProduct().getShortCode() != null) {
					set.add(addon.getProduct().getShortCode());
				}
			}
		}
		if (Objects.nonNull(item.getComposition()) && item.getComposition().getOptions() != null
				&& !item.getComposition().getOptions().isEmpty()) {
			for (String option : item.getComposition().getOptions()) {
				set.add(option);
			}
		}
		b.append(StringUtils.join(set, ","));
	}

}
