package com.stpl.tech.kettle.notification.receipt;

import java.util.List;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.Unit;

public class AssemblySlipRawPrintReceipt extends OrderRawPrintReceipt {

	private final List<OrderKOTRawPrintReceipt> templates;

	public AssemblySlipRawPrintReceipt(Unit unit, OrderInfo detail, String basePath,
			List<OrderKOTRawPrintReceipt> templates, EnvironmentProperties env) {
		super(unit, detail, basePath, null, env);
		this.templates = templates;
	}

	@Override
	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getOrder().getOrderId() + "/AssemblySlip-"
				+ getOrder().getOrderId() + ".html";
	}

	@Override
	public StringBuilder processData() {

		reset();
		if (AppConstants.TAKE_AWAY.equals(getOrder().getSource())) {
			center(bold(doubleFont("Chaayos Take Away")));
		} else {
			center(bold(doubleFont("Assembly Slip " + getOrderType(getOrder().getOrderType()))));
		}
		if (hasValue(getOrder().getTokenNumber()) && !isCOD()) {
			left(rpad("Token Number", 20) + getOrder().getTokenNumber());
		}
		if (hasTable() && !isCOD()) {
			left(rpad("Table No", 20) + getOrder().getTableNumber());
		}
		if (getOrder().getCustomerName() != null) {
			left(rpad("Name", 20) + bold(doubleFont(getOrder().getCustomerName())));
		}
		left(rpad("Order No", 20) + getOrder().getGenerateOrderId());
		left(rpad("Order Time", 20) + AppUtils.getBillPrintFormat(getOrder().getBillingServerTime()));
		left(rpad("Source", 20) + bold(orderSource()));
		if (getOrder().getOrderRemark() != null) {
			left(rpad("Order Remark", 20) + getOrder().getOrderRemark());
		}
		if (isCOD()) {
			channelPartnerDetails();
		}
		if (getCustomer().getId() > 5 && getDeliveryPartner() != null && getDeliveryPartner().getName() != null
				&& getDeliveryPartner().getId() > 1) {
			left(rpad("Delivery Partner", 20) + getDeliveryPartner().getName());
		}
		separator();
		for (OrderKOTRawPrintReceipt template : templates) {
			left(bold(template.getType().getDescription()));
			template.itemDetails(sb);
			separator();
		}
		cut();
		return getSb();
	}

}
