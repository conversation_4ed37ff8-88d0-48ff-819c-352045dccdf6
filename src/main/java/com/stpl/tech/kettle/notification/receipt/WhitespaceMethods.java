package com.stpl.tech.kettle.notification.receipt;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import freemarker.template.TemplateMethodModel;
import freemarker.template.TemplateModelException;

public class WhitespaceMethods  implements TemplateMethodModel {
    @Override
    public Object exec(List args) throws TemplateModelException {
        if (args.size() != 1) {
            throw new TemplateModelException("Wrong arguments");
        }
        String s = (String) args.get(0);
        Matcher m = Pattern.compile("[A-Z]").matcher(s);
        while(m.find()){
            String c = m.group();
            s = s.replace(c," "+c);
        }

        return s;
    }

}
