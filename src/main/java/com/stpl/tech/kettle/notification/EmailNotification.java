package com.stpl.tech.kettle.notification;

import java.io.ByteArrayOutputStream;
import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.BodyPart;
import javax.mail.Session;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.util.ByteArrayDataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClient;
import com.amazonaws.services.simpleemail.model.Body;
import com.amazonaws.services.simpleemail.model.Content;
import com.amazonaws.services.simpleemail.model.Destination;
import com.amazonaws.services.simpleemail.model.Message;
import com.amazonaws.services.simpleemail.model.RawMessage;
import com.amazonaws.services.simpleemail.model.SendEmailRequest;
import com.amazonaws.services.simpleemail.model.SendRawEmailRequest;
import com.stpl.tech.kettle.exceptions.EmailGenerationException;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.notification.MyAWSCredentials;

public abstract class EmailNotification {

	private static final AmazonSimpleEmailServiceClient client;

	private static final Logger LOG = LoggerFactory.getLogger(AmazonSimpleEmailServiceClient.class);

	private static final Region REGION = Region.getRegion(Regions.EU_WEST_1);

	static {

		// Instantiate an Amazon SES client, which will make the service
		// call. The service call requires your AWS credentials.
		// Because we're not providing an argument when instantiating the
		// client, the SDK will attempt to find your AWS credentials
		// using the default credential provider chain. The first place the
		// chain looks for the credentials is in environment variables
		// AWS_ACCESS_KEY_ID and AWS_SECRET_KEY.
		// For more information, see
		// http://docs.aws.amazon.com/AWSSdkDocsJava/latest/DeveloperGuide/credentials.html
		client = new AmazonSimpleEmailServiceClient(new MyAWSCredentials());

		// Choose the AWS region of the Amazon SES endpoint you want to
		// connect to. Note that your sandbox
		// status, sending limits, and Amazon SES identity-related settings
		// are specific to a given AWS
		// region, so be sure to select an AWS region in which you set up
		// Amazon SES. Here, we are using
		// the US West (Oregon) region. Examples of other regions that
		// Amazon SES supports are US_EAST_1
		// and EU_WEST_1. For a complete list, see
		// http://docs.aws.amazon.com/ses/latest/DeveloperGuide/regions.html

		client.setRegion(REGION);

	}

	public void sendEmail() throws EmailGenerationException {

		if (getValidEmails().length == 0) {
			return;
		}

		// Construct an object to contain the recipient address.
		Destination destination = new Destination().withToAddresses(getValidEmails());

		// Create the subject and body of the message.
		Content subject = new Content().withData(subject());
		Content textBody = new Content().withData(body());
		Body body = new Body().withHtml(textBody);

		// Create a message with the specified subject and body.
		Message message = new Message().withSubject(subject).withBody(body);

		// Assemble the email.
		SendEmailRequest request = new SendEmailRequest().withSource(getFromEmail()).withDestination(destination)
				.withMessage(message);
		try {
			// Send the email.
			client.sendEmail(request);
			LOG.info("Email Sent to " + destination.getToAddresses().get(0) + " with " + subject.getData());
		} catch (Exception ex) {
			throw new EmailGenerationException("Error while sending email to " + destination.getToAddresses().get(0)
					+ " with " + subject.getData(), ex);
		}
	}

	public abstract String[] getToEmails();

	public abstract String getFromEmail();

	public abstract String subject();

	public abstract String body() throws EmailGenerationException;

	public abstract EnvType getEnvironmentType();

	public void sendRawMail(List<AttachmentData> attachments) throws EmailGenerationException {
		try {

			if (getValidEmails().length == 0) {
				return;
			}

			// JavaMail representation of the message
			Session s = Session.getInstance(new Properties(), null);
			MimeMessage mimeMessage = new MimeMessage(s);

			// Sender and recipient
			mimeMessage.setFrom(new InternetAddress(getFromEmail()));
			for (String toMail : getValidEmails()) {
				mimeMessage.addRecipient(javax.mail.Message.RecipientType.TO, new InternetAddress(toMail));
			}

			// Subject
			mimeMessage.setSubject(subject());

			// Add a MIME part to the message
			MimeMultipart mimeBodyPart = new MimeMultipart();
			BodyPart part = new MimeBodyPart();
			part.setContent(body(), "text/html");
			mimeBodyPart.addBodyPart(part);

			for (AttachmentData attachment : attachments) {
				// Add a attachment to the message
				BodyPart attachmentPart = new MimeBodyPart();
				DataSource source = new ByteArrayDataSource(attachment.getAttachment(), attachment.getContentType());
				attachmentPart.setDataHandler(new DataHandler(source));
				attachmentPart.setFileName(attachment.getFileName());
				mimeBodyPart.addBodyPart(attachmentPart);
			}

			mimeMessage.setContent(mimeBodyPart);

			// Create Raw message
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			mimeMessage.writeTo(outputStream);
			RawMessage rawMessage = new RawMessage(ByteBuffer.wrap(outputStream.toByteArray()));

			// Send Mail
			SendRawEmailRequest rawEmailRequest = new SendRawEmailRequest(rawMessage);
			rawEmailRequest.setDestinations(Arrays.asList(getValidEmails()));
			rawEmailRequest.setSource(getFromEmail());
			client.sendRawEmail(rawEmailRequest);
			LOG.info("Email Sent");

		} catch (Exception ex) {
			LOG.info("Error while sending email", ex);
			throw new EmailGenerationException("Error while sending email", ex);
		}
	}

	private String[] getValidEmails() {
		if (AppUtils.isProd(getEnvironmentType())) {
			return getToEmails();
		} else {
			List<String> emails = Arrays.asList(getToEmails());
			emails = emails.stream().filter(email -> email.endsWith("chaayos.com")).collect(Collectors.toList());
			return emails.toArray(new String[emails.size()]);
		}
	}

	public void SendRawEmailWithCC(List<AttachmentData> attachments, String[] ccEmails, String[] bccEmails)
			throws EmailGenerationException {
		try {

			if (getValidEmails().length == 0) {
				return;
			}

			// JavaMail representation of the message
			Session s = Session.getInstance(new Properties(), null);
			MimeMessage mimeMessage = new MimeMessage(s);

			// Sender and recipient
			mimeMessage.setFrom(new InternetAddress(getFromEmail()));
			for (String toMail : getValidEmails()) {
				mimeMessage.addRecipient(javax.mail.Message.RecipientType.TO, new InternetAddress(toMail));
			}

			if (Objects.nonNull(ccEmails)) {
				for (String ccEmail : ccEmails) {
					mimeMessage.addRecipient(javax.mail.Message.RecipientType.CC, new InternetAddress(ccEmail));
				}
			}

			if (Objects.nonNull(bccEmails)) {
				for (String bccEmail : bccEmails) {
					mimeMessage.addRecipient(javax.mail.Message.RecipientType.BCC, new InternetAddress(bccEmail));
				}

			}

			// Subject
			mimeMessage.setSubject(subject());

			// Add a MIME part to the message
			MimeMultipart mimeBodyPart = new MimeMultipart();
			BodyPart part = new MimeBodyPart();
			part.setContent(body(), AppConstants.TEXT_MIME_TYPE);
			mimeBodyPart.addBodyPart(part);

			for (AttachmentData attachment : attachments) {
				// Add a attachment to the message
				BodyPart attachmentPart = new MimeBodyPart();
				DataSource source = new ByteArrayDataSource(attachment.getAttachment(), attachment.getContentType());
				attachmentPart.setDataHandler(new DataHandler(source));
				attachmentPart.setFileName(attachment.getFileName());
				mimeBodyPart.addBodyPart(attachmentPart);
			}

			mimeMessage.setContent(mimeBodyPart);

			// Create Raw message
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			mimeMessage.writeTo(outputStream);
			RawMessage rawMessage = new RawMessage(ByteBuffer.wrap(outputStream.toByteArray()));

			// Send Mail
			SendRawEmailRequest rawEmailRequest = new SendRawEmailRequest(rawMessage);
			rawEmailRequest.setDestinations(Arrays.asList(getValidEmails()));
			rawEmailRequest.setSource(getFromEmail());
			client.sendRawEmail(rawEmailRequest);
			LOG.info("Email Sent");

		} catch (Exception ex) {
			LOG.info("Error while sending email", ex);
			throw new EmailGenerationException("Error while sending email", ex);
		}
	}
	public void sendRawMailWithInLineImage(List<AttachmentData> attachments,String imagePath) throws EmailGenerationException {
		try {

			if (getValidEmails().length == 0) {
				return;
			}

			// JavaMail representation of the message
			Session s = Session.getInstance(new Properties(), null);
			MimeMessage mimeMessage = new MimeMessage(s);

			// Sender and recipient
			mimeMessage.setFrom(new InternetAddress(getFromEmail()));
			for (String toMail : getValidEmails()) {
				mimeMessage.addRecipient(javax.mail.Message.RecipientType.TO, new InternetAddress(toMail));
			}

			// Subject
			mimeMessage.setSubject(subject());


			// Add a MIME part to the message
			MimeMultipart mimeBodyPart = new MimeMultipart("related");
			BodyPart part = new MimeBodyPart();
			part.setContent(body(), "text/html");
			mimeBodyPart.addBodyPart(part);

			MimeBodyPart imagePart = new MimeBodyPart();
			DataSource fds = new FileDataSource(imagePath);
			imagePart.setDataHandler(new DataHandler(fds));
			imagePart.setHeader("Content-ID", "<image>");
			mimeBodyPart.addBodyPart(imagePart);

			for (AttachmentData attachment : attachments) {
				// Add a attachment to the message
				BodyPart attachmentPart = new MimeBodyPart();
				DataSource source = new ByteArrayDataSource(attachment.getAttachment(), attachment.getContentType());
				attachmentPart.setDataHandler(new DataHandler(source));
				attachmentPart.setFileName(attachment.getFileName());
				mimeBodyPart.addBodyPart(attachmentPart);
			}

			mimeMessage.setContent(mimeBodyPart);

			// Create Raw message
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			mimeMessage.writeTo(outputStream);
			RawMessage rawMessage = new RawMessage(ByteBuffer.wrap(outputStream.toByteArray()));

			// Send Mail
			SendRawEmailRequest rawEmailRequest = new SendRawEmailRequest(rawMessage);
			rawEmailRequest.setDestinations(Arrays.asList(getValidEmails()));
			rawEmailRequest.setSource(getFromEmail());
			client.sendRawEmail(rawEmailRequest);
			LOG.info("Email Sent");

		} catch (Exception ex) {
			LOG.error("Error while sending email", ex);
			throw new EmailGenerationException("Error while sending email", ex);
		}
	}

}
