package com.stpl.tech.kettle.notification.receipt;

import java.util.ArrayList;
import java.util.Objects;

import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.KOTType;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.notification.RawPrintData;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.RawPrintHelper;
import com.stpl.tech.kettle.util.SpringBeanProvider;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.Unit;

public class OrderKOTRawPrintReceipt  extends OrderRawPrintReceipt {

	private KOTType type;

	private final RawPrintData printData;

	public OrderKOTRawPrintReceipt(Unit unit,  OrderInfo detail, KOTType type,
								   String basePath,  EnvironmentProperties env) throws DataNotFoundException {
		super(unit, detail, basePath, null,env);
	 ProductCache productCache = SpringBeanProvider.getBean(ProductCache.class);
		this.type = type;
		ArrayList<OrderItem> items = new ArrayList<>();
		for (OrderItem item : getOrder().getOrders()) {
			if (AppConstants.GIFT_CARD_TAX_CODE.equals(item.getCode())) {
				continue;
			}
			ProductBasicDetail product	 = productCache.getProductBasicDetailById(item.getProductId());
			if (product.getType() == type.getId() || type.getId() == -1) {
				items.add(item);
			}
			if (product.getType() == AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE) {
				for (OrderItem addon : item.getComposition().getMenuProducts()) {
					product =  productCache.getProductBasicDetailById(addon.getProductId());
					if (product.getType() == type.getId()) {
						items.add(addon);
					}
				}
			}
		}
		printData = mergeItems(items);
	}

	@Override
	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getOrder().getOrderId() + "/OrderKOTPrint-"
				+ type.getDescription() + "-" + getOrder().getOrderId() + ".html";
	}

	@Override
	public StringBuilder processData() {
		if (printData.getItems().isEmpty()) {
			return null;
		}
		reset();

		if(TransactionUtils.isSpecialOrder(getOrder())){
			center(bold(doubleFont(RawPrintHelper.getOrderSource(getOrder().getSource(), getDetails().getBrand()))));
			left("  ");
			unitDetails();
			separator();
//			left(rpad("Type", 5) + bold(doubleFont(type.getDescription())));
			//left("	 ");
		}else{
			left("	-");
			left("	-");
			left("	-");
			left(rpad("Type", 15) + bold(doubleFont(type.getDescription())));
		}
		orderType();
		if(TransactionUtils.isSpecialOrder(getOrder()) && Objects.nonNull( getOrder().getOrderType()) &&  getOrder().getOrderType().equals(AppConstants.ORDER_TYPE_COMPLIMENTARY)){
			for (int i = 0; i < printData.getItems().size(); i++) {
				OrderItem item = printData.getItems().get(i);
				if(Objects.nonNull(item.getComplimentaryDetail()) && item.getComplimentaryDetail().isIsComplimentary() && Objects.nonNull(item.getComplimentaryDetail().getReasonCode())){
					left("	");
					if(item.getComplimentaryDetail().getReasonCode() == 2105){
						left(rpad("Type", 5) + bold(doubleFont("Training")));
					}
					else if(item.getComplimentaryDetail().getReasonCode() == 2106){
						left( bold(doubleFont("Sampling and Marketing")));
					}
					else if(item.getComplimentaryDetail().getReasonCode() == 2107){
						left(rpad("Type", 5) + bold(doubleFont("Charity")));
					}
					else if(item.getComplimentaryDetail().getReasonCode() == 2109){
						left(rpad("Type", 5) + bold(doubleFont("NSO-Dry Run")));
					}
					else if(item.getComplimentaryDetail().getReasonCode() == 2110){
						left(rpad("Type", 5) + bold(doubleFont("Monk-Calibration")));
					}
					break;
				}
			}
		}
		if (hasValue(getOrder().getTokenNumber()) && !isCOD()) {
			left(rpad("Token Number", 15) + getOrder().getTokenNumber());
		}
		if (hasTable() && !isCOD()) {
			left(rpad("Table No", 15) + getOrder().getTableNumber());
		}
		if (getOrder().getCustomerName() != null) {
			left(rpad("Name", 15) + bold(doubleFont(getOrder().getCustomerName())));
		}
		left(rpad("Order No", 15) + getOrder().getGenerateOrderId());
		if(TransactionUtils.isSpecialOrder(getOrder())){
			left(rpad("Order Time", 15) + AppUtils.getBillPrintFormat(getOrder().getBillingServerTime()));
		}else{
			left(rpad("Order Time", 15) + AppUtils.getBillPrintTime(getOrder().getBillingServerTime()));
		}
		left(rpad("Source", 15) + bold(orderSource()));
		if (getOrder().getOrderRemark() != null) {
			left(rpad("Order Remark", 15) + getOrder().getOrderRemark());
		}
		separator();
		itemDetails();

		// left("123456789123456789123456789123456789123456789123456789123456789");
		cut();

		return getSb();
	}

	@Override
	protected void itemDetails() {
		for (int i = 0; i < printData.getItems().size(); i++) {
			OrderItem item = printData.getItems().get(i);
			left(bold(rpad(item.getProductName(), 38).substring(0, 38) + lpad(item.getDimension().substring(0, 1), 3)
					+ lpad(printData.getSizeMap().get(i), 5)));
			addTakeAway(item);
			addComposition(item);
			addComboItems(item);
			left(" ");
		}
	}

	/**
	 * @param item
	 */
	protected void addTakeAway(OrderItem item) {
		if (TransactionUtils.isTakeawayOrder(getOrder().getSource())
				|| item.getTakeAway() != null && item.getTakeAway()) {
			left(bold("    **Take Away"));
		}
	}

	protected void itemDetails(StringBuilder builder) {
		this.sb = builder;
		itemDetails();
	}

	protected void unitDetails() {
		//left(getUnit().getReferenceName());
		left(getUnit().getAddress().getLine2());
		left(getUnit().getAddress().getCity() + ", " + getUnit().getAddress().getState() + rpad("", 15) + AppUtils.getBillPrintFormat(getOrder().getBillingServerTime()).split(" ")[0]);
		//left(rpad("Order Date", 15) + AppUtils.getBillPrintFormat(getOrder().getBillingServerTime()).split(" ")[0]);
	}

	@Override
	protected void addComboItems(OrderItem combo) {
		if (AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE == combo.getProductCategory().getId()) {
			for (OrderItem menuItem : combo.getComposition().getMenuProducts()) {
				left(" -" + rpad(menuItem.getProductName(), 36).substring(0, 36)
						+ lpad(menuItem.getDimension().substring(0, 1), 3) + lpad(menuItem.getQuantity(), 5));
				addTakeAway(menuItem);
				addComposition(menuItem);
			}
		}
	}

	public RawPrintData getPrintData() {
		return printData;
	}

	public KOTType getType() {
		return type;
	}
}
