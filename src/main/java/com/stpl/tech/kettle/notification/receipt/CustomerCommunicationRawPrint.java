package com.stpl.tech.kettle.notification.receipt;

import java.util.Date;
import java.util.Objects;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.CustomerRepeatType;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.exceptions.DataNotFoundException;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.RawPrintHelper;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.master.domain.model.Unit;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class CustomerCommunicationRawPrint extends OrderRawPrintReceipt {


	public CustomerCommunicationRawPrint(Unit unit,  OrderInfo detail,
										 String basePath, EnvironmentProperties env) throws DataNotFoundException {
		super(unit, detail, basePath, null,env);
	}

	@Override
	public String getFilepath() {
		return getBasePath() + "/" + getUnit().getId() + "/orders/" + getOrder().getOrderId() + "/CustomerCommunicationPrint-"
				 + "-" + getOrder().getOrderId() + ".html";
	}

	@Override
	public StringBuilder processData() {
		NextOffer offer = getDetail().getNextOffer();
		NextOffer deliveryOffer =getDetail().getNextDeliveryOffer();
		log.info("Raw Print NBO - {}", offer);
		reset();
		lineBreak();
		center(bold(doubleFont("Chaayos")));
		lineBreak();
		left(bold(doubleFont("Hi " + (getOrder().getCustomerId() <= 5 ? "Chai Lover" : getCustomer().getFirstName()) + ",")));
		if ((offer != null && offer.isAvailable()) || (Objects.nonNull(deliveryOffer) && deliveryOffer.isAvailable())) {
			Date validFrom;
			Date validTill;
			if((offer != null && offer.isAvailable())){
				validFrom = AppUtils.getDate(offer.getValidityFrom(), AppUtils.DATE_FORMAT_STRING);
				validTill = AppUtils.getDate(offer.getValidityTill(), AppUtils.DATE_FORMAT_STRING);
				lineBreak();
				center(bold("----------------------------------------"));
				lineBreak();
				center(bold(extraDoubleFont(offer.getText())));
				center(bold("( applicable at all chaayos cafe )"));
				if (CustomerRepeatType.NEW.name().equals(offer.getCustomerType())
						&& AppConstants.LOYAL_TEA_COUPON_CODE.equals(offer.getOfferCode())) {
					lineBreak();
					center(bold(doubleFont("Second Chai Free")));
					center(bold(("valid from "+AppUtils.getDayMonthYear(validFrom)+" to "+ AppUtils.getDayMonthYear(validTill))));
				} else {
					lineBreak();
					center(bold(doubleFont("Use Code : " + offer.getOfferCode())));
					center(bold(("valid from "+AppUtils.getDayMonthYear(validFrom)+" to "+ AppUtils.getDayMonthYear(validTill))));
					lineBreak();
					center(bold("Note : Offer not applicable on Merchandise"));
				}
				lineBreak();
				center(bold("----------------------------------------"));
			}
			if((Objects.nonNull(deliveryOffer) && deliveryOffer.isAvailable())){
				validFrom = AppUtils.getDate(deliveryOffer.getValidityFrom(), AppUtils.DATE_FORMAT_STRING);
				validTill = AppUtils.getDate(deliveryOffer.getValidityTill(), AppUtils.DATE_FORMAT_STRING);
				lineBreak();
				center(bold("----------------------------------------"));
				lineBreak();
				center(bold(extraDoubleFont(deliveryOffer.getText())));
				center(bold(doubleFont("on " + deliveryOffer.getChannelPartner())));
				center(bold("( applicable at all chaayos cafe on "+deliveryOffer.getChannelPartner()+" )"));
				lineBreak();
				center(bold(doubleFont("Use Code : " + deliveryOffer.getOfferCode())));
				center(bold(("valid from "+AppUtils.getDayMonthYear(validFrom)+" to "+ AppUtils.getDayMonthYear(validTill))));
				lineBreak();
				center(bold("----------------------------------------"));
			}
		}
		else {
			lineBreak();
			lineBreak();
			left(bold(
					"Thank you for being a part of not-so-secret club of chai lovers. Here, your Chai will always be piping hot, your snacks yummy, and you Realxed."));
			lineBreak();
		}
//		center(bold(doubleFont("Scan Me!!!")));
//		lineBreak();
//		qrAppend(qr("https://stpl.page.link/bill_print_app_qr"));
		lineBreak();
		center(bold("Download Chaayos Android and iOS App now"));
		center(bold("and avail amazing benefits!!!"));
		lineBreak();
		cut();
		return getSb();
	}

	private void lineBreak() {
		left(RawPrintHelper.LINE_BREAK);
	}


}
