package com.stpl.tech.kettle.notification.receipt;

import java.math.BigDecimal;
import java.util.Map;

import com.stpl.tech.kettle.data.kettle.TableOrderMappingDetail;
import com.stpl.tech.kettle.data.kettle.UnitTableMappingDetail;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.master.domain.model.PaymentMode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TableSettlementReceipt extends RawPrintReceipt {

    private Map<Integer, BigDecimal> settlementMap;
    private Map<Integer, PaymentMode> paymentModes;
    private String basePath;
    private UnitTableMappingDetail table;

    public TableSettlementReceipt(Map<Integer, BigDecimal> settlementMap, Map<Integer, PaymentMode> paymentModes,
                                  UnitTableMappingDetail table, String basePath) {
        this.settlementMap = settlementMap;
        this.basePath = basePath;
        this.table = table;
        this.paymentModes = paymentModes;
    }

    @Override
    public StringBuilder processData() {
        reset();
        left(rpad("Table Number", 20) + table.getTableNumber());
        left(rpad("Customer Name", 20) + table.getCustomerName());
        left(rpad("Total Amount", 20) + table.getTotalAmount());
        separator();
        leftBold("Orders");
        for(TableOrderMappingDetail map : table.getOrders()) {
            if (!TransactionUtils.isCancelled(map.getOrder().getOrderStatus())) {
                left(rpad(map.getOrder().getGeneratedOrderId(), 36) + lpad(map.getOrder().getSettledAmount(), 10));
            }
        }
        separator();
        leftBold("Settlements");
        BigDecimal amount = null;
        PaymentMode mode = null;
        for (Integer i : settlementMap.keySet()) {
            amount = settlementMap.get(i);
            mode = paymentModes.get(i);
            left(rpad(mode.getName(), 20) + amount);
        }
        cut();
        return getSb();
    }

    @Override
    public String getFilepath() {
        return getBasePath() + "/" + table.getUnitId() + "/tableSettlements/TableSettlementReceipt-"
                + table.getTableRequestId() + ".html";
    }


}
