package com.stpl.tech.kettle;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

import java.io.File;
import java.io.FileInputStream;
import java.net.URL;
import java.util.TimeZone;

@SpringBootApplication(exclude = {
		org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class,
		org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration.class })
@EnableAsync
@ComponentScan(value = {"com.stpl.tech.master.core.external","com.stpl.tech.kettle"})
public class KettleServiceApplication extends SpringBootServletInitializer{

	static {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
	}

	private static final Logger LOG = LoggerFactory.getLogger(KettleServiceApplication.class);

	public static void main(String[] args) {
		SpringApplication.run(KettleServiceApplication.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		return builder.sources(KettleServiceApplication.class);
	}

	@Autowired
	private EnvironmentProperties properties;

	@PostConstruct
	public void initializeFirebaseApp() {
		FileInputStream serviceAccount = null;
		try {
			try {
				URL resource = getClass().getClassLoader().getResource("firebase-" + properties.getEnvironmentType().name().toLowerCase()+".json");
				if (resource != null) {
					File file = new File(resource.getFile());
					serviceAccount = new FileInputStream(file);
				} else {
					LOG.info("No File Found ..!");
					return;
				}
			} catch (Exception e) {
				LOG.info("Error Occurred while loading resources ..!" , e);
				return;
			}
			FirebaseOptions options = FirebaseOptions.builder()
					.setCredentials(GoogleCredentials.fromStream(serviceAccount))
					.build();
			FirebaseApp.initializeApp(options);
		} catch (Exception e) {
			LOG.error("File not found exception :",e);
		}
	}
}
