package com.stpl.tech.master.data.model;


import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

import static jakarta.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "UNIT_PARTNER_BRAND_MAPPING")
@Getter
@Setter
@NoArgsConstructor
public class UnitPartnerBrandMappingData implements java.io.Serializable {

    private static final long serialVersionUID = -9133952113962183323L;
    
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "UNIT_PARTNER_BRAND_MAPPING_ID", unique = true, nullable = false)
    private Integer mappingId;
    
    @Column(name = "RESTAURANT_ID", nullable = false)
    private String restaurantId;
    
    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;
    
    @Column(name = "PARTNER_ID", nullable = false)
    private Integer partnerId;
    
    @Column(name = "PRICE_PROFILE_UNIT_ID", nullable = false)
    private Integer priceProfileUnitId;
    
    @Column(name = "BRAND_ID", nullable = false)
    private Integer brandId;
    
    @Column(name = "MAPPING_STATUS", nullable = false)
    private String status;
    
    @Column(name = "PARTNER_SOURCE_SYSTEM_ID", nullable = false)
    private Integer partnerSourceSystemId ;
    
    @Column(name="SWIGGY_CLOUD_KITCHEN")
    private String swiggyCloudKitchen;
    
    @Column(name = "LIVE_DATE")
    private Date liveDate;

    @Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((mappingId == null) ? 0 : mappingId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UnitPartnerBrandMappingData other = (UnitPartnerBrandMappingData) obj;
		if (mappingId == null) {
			if (other.mappingId != null)
				return false;
		} else if (!mappingId.equals(other.mappingId))
			return false;
		return true;
	}
    
    
}