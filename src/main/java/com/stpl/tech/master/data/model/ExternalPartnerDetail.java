package com.stpl.tech.master.data.model;

import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "EXTERNAL_PARTNER_DETAIL")
@Getter
@Setter
public class ExternalPartnerDetail implements Serializable {

	private static final long serialVersionUID = 8527571093464059631L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "DETAIL_ID", unique = true, nullable = false)
	private int id;

	@Column(name = "PARTNER_NAME", nullable = false, length = 50)
	private String partnerName;

	@Column(name = "PARTNER_CODE", nullable = false, length = 20)
	private String partnerCode;

	@Column(name = "LINKED_PRODUCT_ID", nullable = false)
	private Integer linkedProductId;

	@Column(name = "LINKED_PAYMENT_MODE_ID", nullable = false)
	private Integer linkedPaymentModeId;

	@Column(name = "LINKED_CREDIT_ACCOUNT_ID", nullable = true)
	private Integer linkedCreditAccountId;

	@Column(name = "END_POINT", nullable = false)
	private String endPoint;

	@Column(name = "USERNAME", nullable = false)
	private String username;

	@Column(name = "PASS_CODE", nullable = false)
	private String passCode;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATED_AT", nullable = true)
	private Date creationDate;

	@Column(name = "STATUS", nullable = false)
	private String partnerStatus;

}