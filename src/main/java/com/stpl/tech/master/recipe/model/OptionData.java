package com.stpl.tech.master.recipe.model;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OptionData implements Serializable {

	private static final long serialVersionUID = 9196734885916273962L;

	private int id;
	private String name;
	private String code;
	private String shortCode;
	private String type;
	private Integer productId;
}
