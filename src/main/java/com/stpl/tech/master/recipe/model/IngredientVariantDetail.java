package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.math.BigDecimal;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalSixPrecisionDeserializer;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class IngredientVariantDetail implements Serializable {

    
    private static final long serialVersionUID = -2198505699784866775L;
    @Id
    private String _id;

    private int id;

    private int productId;

    private String alias;
    
    private String name;

    private UnitOfMeasure uom;
    @JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
    private BigDecimal quantity;

    private boolean defaultSetting;
    @Field
    private String status = "ACTIVE";
    @Field
    private boolean captured = true;
    @JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
    private BigDecimal yield = new BigDecimal(100d);

    private String desc;

    private String tag;

    private Integer ingredientVariantDetailId;
}
