package com.stpl.tech.master.recipe.model;

import java.io.Serializable;

import lombok.Builder;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Version;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.master.domain.model.ProductClassification;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductData implements Serializable {

    private static final long serialVersionUID = -926969452764262475L;

    @Id
    private String _id;
    @Version
    @JsonIgnore
    private Long version;

    private String detachAll;

    private int productId;

    private String name;

    private String displayName;

    private String code;

    private String shortCode;

    private String status;

    private int type;

    private int subType;

    private boolean variantLevelOrdering;

    private ProductClassification classification;

    private boolean isInventoryTracked;
    private boolean isAutoProduction;
    private Integer recipeId;

}

