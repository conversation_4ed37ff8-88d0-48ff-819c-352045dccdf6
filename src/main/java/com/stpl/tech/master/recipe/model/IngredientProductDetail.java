package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import lombok.Builder;
import org.springframework.data.annotation.Id;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalSixPrecisionDeserializer;
import com.stpl.tech.master.domain.model.IterationIngredientInstructions;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IngredientProductDetail implements Serializable {

	private static final long serialVersionUID = 2724125928939936517L;
	@Id
	private String _id;

	private int id;
	private String name;
	
    private int productId;

	private ProductData product;

	private BasicInfo dimension;

	private UnitOfMeasure uom;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	private BigDecimal quantity;

	private boolean defaultSetting = false;

	private boolean customize;

	private boolean critical;

	private IngredientDetail ingredient;

	private List<IngredientProductDetail> addons;

	private String status = "ACTIVE";
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	private BigDecimal yield = new BigDecimal(100d);
	private List<IterationIngredientInstructions> instructions;

	private String tag;

	private String desc;

	private Boolean dispensed;

	private String dispenseTag;

	private Boolean showRecipe;

	private Instruction instruction;

	private Boolean display;

	private String displayCode;

	private Integer ingredientProductDetailId;

}
