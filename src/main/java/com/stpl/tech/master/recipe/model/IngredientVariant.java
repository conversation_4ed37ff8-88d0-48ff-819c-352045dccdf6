package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class IngredientVariant implements Serializable {

    
    private static final long serialVersionUID = -3738896337470242421L;
    @Id
    private String _id;


    private ProductData product;

    private UnitOfMeasure uom;

    private String status = "ACTIVE";

    private boolean captured;

    private boolean customize;

    private boolean critical;

    private boolean dispensed;

    private String dispenseTag;

    private List<IngredientVariantDetail> details;

}
