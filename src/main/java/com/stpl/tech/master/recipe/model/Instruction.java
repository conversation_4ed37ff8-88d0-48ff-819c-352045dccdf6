package com.stpl.tech.master.recipe.model;

import java.io.Serializable;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Instruction implements Serializable {

	private static final long serialVersionUID = 4420362729467676254L;
	@Id
	private String _id;

	protected String name;

	protected String color;

}
