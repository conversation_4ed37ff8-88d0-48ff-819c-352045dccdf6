package com.stpl.tech.master.recipe.model;

import java.io.Serializable;

import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Version;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BasicInfo implements Serializable {

    
    private static final long serialVersionUID = 9196734885916273962L;
    @Id
    private String _id;
    @Version
    @JsonIgnore
    private Long version;

    /**
     * Added to avoid a runtime error whereby the detachAll property is checked
     * for existence but not actually used.
     */
    private String detachAll;

    private int infoId;

    private String name;

    private String code;

    private String shortCode;

    private String type;

    private String status;

    private String desc;

}
