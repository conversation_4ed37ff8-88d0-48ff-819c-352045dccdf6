package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RecipeDetail implements Serializable {

	private static final long serialVersionUID = -8770383427147819680L;
	@Id
	private String _id;

	private int recipeId;

	private ProductData product;

	private BasicInfo dimension;

	private String name;

	private String status = "ACTIVE";

	private Date creationDate;

	private Date modificationDate;

	private Date startDate;

	private Date endDate;

	private boolean deliverable;

	private IngredientDetail ingredient;

	private int customizationCount;

	private List<IngredientProductDetail> addons;

	private List<IngredientProductDetail> mandatoryAddons;

	private List<OptionData> options;

	private List<IngredientProductDetail> recommendations;

	private List<IngredientProductDetail> dineInConsumables;

	private List<IngredientProductDetail> deliveryConsumables;

	private List<IngredientProductDetail> takeawayConsumables;

	private int lastUpdatedById;

	private String lastUpdatedByName;

	private Boolean containsCriticalProducts;
	private String notes;

	private List<String> imagesURL;

	private String profile;

	private Boolean dispensed;

}
