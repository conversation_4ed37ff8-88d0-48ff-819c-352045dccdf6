package com.stpl.tech.master.recipe.model;


import java.io.Serializable;
import java.util.List;

import org.springframework.data.annotation.Id;

import com.stpl.tech.master.domain.model.CompositeProductData;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class IngredientDetail implements Serializable {

    
    private static final long serialVersionUID = 2576611384607231273L;
    @Id
    private String _id;


  //  private CompositeProductData compositeProduct;

    private List<IngredientProduct> products;

    private List<IngredientVariant> variants;

    private List<IngredientProductDetail> components;

}

