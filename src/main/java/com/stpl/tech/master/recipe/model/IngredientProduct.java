package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class IngredientProduct implements Serializable {

	private static final long serialVersionUID = -1498182977031669754L;
	@Id
	private String _id;

	protected BasicInfo category;

	protected String display;

	protected String status = "ACTIVE";

	protected boolean captured = true;

	protected boolean critical;

	protected boolean customize;

	protected List<IngredientProductDetail> details;

}
