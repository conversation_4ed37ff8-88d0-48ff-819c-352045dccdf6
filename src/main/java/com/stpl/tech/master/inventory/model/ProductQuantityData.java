package com.stpl.tech.master.inventory.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;

import lombok.NoArgsConstructor;

@NoArgsConstructor
public class ProductQuantityData implements Serializable {

	private static final long serialVersionUID = 6496544487110055105L;
	private int id;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	private BigDecimal q;
	private String u;
	private Date e;
	private BigDecimal p;

	public ProductQuantityData(int productId, BigDecimal quantity, String uom) {
		super();
		this.id = productId;
		this.q = quantity;
		this.u = uom;
		this.e = null;
	}

	public ProductQuantityData(int productId, BigDecimal quantity, String uom, Date expiry, BigDecimal price) {
		super();
		this.id = productId;
		this.q = quantity;
		this.u = uom;
		this.e = expiry;
		this.p = price;
	}

	public int getId() {
		return id;
	}

	public void setId(int productId) {
		this.id = productId;
	}

	public BigDecimal getQ() {
		return q;
	}

	public void setQ(BigDecimal quantity) {
		this.q = quantity;
	}

	public String getU() {
		return u;
	}

	public void setU(String uom) {
		this.u = uom;
	}

	public void addQuantity(BigDecimal quantity) {
		if (Objects.isNull(this.q)) {
			this.q = quantity;
		} else {
			this.q = this.q.add(quantity);
		}
	}

	public Date getE() {
		return e;
	}

	public void setE(Date e) {
		this.e = e;
	}

	public BigDecimal getP() {
		return p;
	}

	public void setP(BigDecimal p) {
		this.p = p;
	}

}
