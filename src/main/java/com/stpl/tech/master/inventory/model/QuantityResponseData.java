package com.stpl.tech.master.inventory.model;


import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Service
@AllArgsConstructor
@NoArgsConstructor
public class QuantityResponseData implements Serializable {


    
    private static final long serialVersionUID = -3924727930581274302L;
    private int unitId;
    private List<ProductQuantityData> details;
    private InventoryAction action;
    private InventorySource source;
    private Integer orderId;
    private Date eventTime;


}
