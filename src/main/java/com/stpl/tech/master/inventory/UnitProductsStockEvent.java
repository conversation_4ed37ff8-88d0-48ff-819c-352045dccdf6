package com.stpl.tech.master.inventory;

import com.stpl.tech.master.domain.model.IdName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UnitProductsStockEvent implements Serializable {
    private static final long serialVersionUID = 7180419820751887692L;
    private Integer unitId;
    private List<String> productIds = new ArrayList<>();
    private List<IdName> productDimensions = new ArrayList<>();
    private Integer partnerId;
    private StockStatus status;

    public UnitProductsStockEvent(Integer unitId, StockStatus status) {
        this.unitId = unitId;
        this.status = status;
    }

    public List<String> getProductIds() {
        if(productIds == null){
            productIds = new ArrayList<>();
        }
        return productIds;
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        UnitProductsStockEvent that = (UnitProductsStockEvent) o;

        return new EqualsBuilder()
                .append(unitId, that.unitId)
                .append(productIds, that.productIds)
                .append(productDimensions, that.productDimensions)
                .append(partnerId, that.partnerId)
                .append(status, that.status)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(unitId)
                .append(productIds)
                .append(productDimensions)
                .append(partnerId)
                .append(status)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "UnitProductsStockEvent{" +
                "unitId=" + unitId +
                ", productIds=" + productIds +
                ", productDimensions=" + productDimensions +
                ", partnerId=" + partnerId +
                ", status=" + status +
                '}';
    }

}
