package com.stpl.tech.master.core.external.cache;

import java.io.Serializable;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ProductRecipeKey implements Serializable {

	private static final long serialVersionUID = 6205916282577327518L;

	private int productId;
	private String dimension;
	private String profile;

	public ProductRecipeKey(int productId, String dimension, String profile) {
		super();
		this.productId = productId;
		this.dimension = dimension;
		this.profile = profile;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;

		if (o == null || getClass() != o.getClass())
			return false;

		ProductRecipeKey that = (ProductRecipeKey) o;

		return new EqualsBuilder().append(productId, that.productId).append(dimension, that.dimension)
				.append(profile, that.profile).isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).append(productId).append(dimension).append(profile).toHashCode();
	}

	@Override
	public String toString() {
		return "ProductRecipeKey{" + "productId=" + productId + ", dimension='" + dimension + '\'' + ", profile='"
				+ profile + '\'' + '}';
	}
}
