package com.stpl.tech.master.core.external.partner.service.impl;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.master.core.external.acl.service.TokenDao;

import io.jsonwebtoken.Claims;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ExternalAPIToken implements TokenDao, Serializable {

	private static final long serialVersionUID = 6770737312282844886L;

	private Integer partnerId;
	private String partnerName;
	private String passCode;
	private String envType;
	private Map<String, Integer> accessAPIs;

	public ExternalAPIToken(Integer partnerId, String partnerName, String passCode, String envType) {
		super();
		this.partnerId = partnerId;
		this.partnerName = partnerName;
		this.passCode = passCode;
		this.envType = envType;
	}

	public ExternalAPIToken(String partnerName, String passCode, String envType) {
		super();
		this.partnerName = partnerName;
		this.passCode = passCode;
		this.envType = envType;
	}

	@Override
	public Map<String, Object> createClaims() {
		// Setting JWT Claims
		Map<String, Object> authClaims = new HashMap<String, Object>();
		authClaims.put("partnerId", partnerId);
		authClaims.put("partnerName", partnerName);
		authClaims.put("passCode", passCode);
		authClaims.put("envType", envType);
		return authClaims;
	}

	@Override
	public void parseClaims(Claims claims) {
		this.partnerId = claims.get("partnerId", Integer.class);
		this.partnerName = claims.get("partnerName", String.class);
		this.passCode = claims.get("passCode", String.class);
		this.envType = claims.get("envType", String.class);
	}
}
