package com.stpl.tech.master.core.external.cache;

import java.io.Serializable;
import java.util.Date;

import com.stpl.tech.kettle.util.AppUtils;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SessionDetail implements Serializable {

	private static final long serialVersionUID = -5216404700526537437L;

	private int userId;

	private Date loginTime;

	private String sessionKey;

	private int unitId;

	private Date lastAccessTime;

	public SessionDetail(int userId, Date loginTime, String sessionKey, int unitId) {
		super();
		this.userId = userId;
		this.loginTime = loginTime;
		this.sessionKey = sessionKey;
		this.unitId = unitId;
		this.lastAccessTime = AppUtils.getCurrentTimestamp();
	}

	@Override
	public String toString() {
		return "SessionDetail [userId=" + userId + ", loginTime=" + loginTime + ", sessionKey=" + sessionKey
				+ ", unitId=" + unitId + "]";
	}

}
