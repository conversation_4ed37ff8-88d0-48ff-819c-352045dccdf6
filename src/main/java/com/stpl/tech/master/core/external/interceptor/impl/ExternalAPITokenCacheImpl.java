package com.stpl.tech.master.core.external.interceptor.impl;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.master.core.external.interceptor.ExternalAPITokenCache;
import com.stpl.tech.kettle.util.ACLUtil;
import com.stpl.tech.kettle.util.Constants.CacheConstants;
import com.stpl.tech.master.core.external.partner.service.impl.ExternalAPIToken;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Log4j2
public class ExternalAPITokenCacheImpl implements ExternalAPITokenCache {

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;
	
	@Autowired
	private ACLUtil aclUtil;
	
	private Map<String, ExternalAPIToken> tokenMap;

	@PostConstruct
	public void createCache() {
		log.info("POST-CONSTRUCT ExternalAPITokenCache - STARTED");
		long time = System.currentTimeMillis();
		tokenMap = instance.getMap(CacheConstants.EXT_API_TOKENS);
		log.info("POST-CONSTRUCT ExternalAPITokenCache took {} ms", System.currentTimeMillis() - time);
	}

	@Override
	public boolean isValidKey(String key) {
		return tokenMap.containsKey(key);
	}

	@Override
	public boolean checkAccess(String key, String requestUrl, String requestMethod) {
		ExternalAPIToken externalAPIToken = tokenMap.get(key);
		return aclUtil.checkPermission(externalAPIToken.getAccessAPIs(),
				aclUtil.convertURIToModule(requestUrl), requestMethod);
	}

}
