package com.stpl.tech.master.core.external.cache.impl;

import com.hazelcast.collection.IList;
import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.master.core.external.cache.PreAuthenticatedApiCache;
import com.stpl.tech.kettle.util.Constants.CacheConstants;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Log4j2
public class PreAuthenticatedApiCacheImpl implements PreAuthenticatedApiCache {

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;

	private IList<String> preAuthenticatedAPIs;

	public IList<String> getPreAuthenticatedAPIs() {
		return preAuthenticatedAPIs;
	}

	public void setPreAuthenticatedAPIs(List<String> preAuthenticatedAPIs) {
		this.preAuthenticatedAPIs.clear();
		this.preAuthenticatedAPIs.addAll(preAuthenticatedAPIs);
	}

	@PostConstruct
	public void loadPreAuthenticatedApiCache() {
		log.info("POST-CONSTRUCT PreAuthenticatedApiCache - STARTED");
		long time = System.currentTimeMillis();
		this.preAuthenticatedAPIs = instance.getList(CacheConstants.PREAUTH_API_CACHE);
		log.info("POST-CONSTRUCT PreAuthenticatedApiCache took {} ms", System.currentTimeMillis() - time);
	}

	@Override
	public String toString() {
		return "PreAuthenticatedApiCache{" + "preAuthenticatedAPIs=" + this.preAuthenticatedAPIs.size() + '}';
	}

}
