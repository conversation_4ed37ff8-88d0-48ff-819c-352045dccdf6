//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.11.16 at 08:32:55 PM IST 
//

package com.stpl.tech.master.domain.model;

import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CancellationReason implements Serializable{
	private static final long serialVersionUID = -5765417020036940292L;
	protected int id;
	protected String code;
	protected String desc;
	protected String status;
	protected UnitCategory source;
	protected boolean noWastage;
	protected boolean partialWastage;
	protected boolean completeWastage;

}
