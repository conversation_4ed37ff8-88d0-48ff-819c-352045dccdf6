package com.stpl.tech.master.domain.model;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class StateTax implements Serializable {

    
    private static final long serialVersionUID = -214713599350642444L;

    private Integer keyId;
    private IdCodeName state;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal igst;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal cgst;
    @JsonDeserialize(using = BigDecimalDeserializer.class)

    private BigDecimal sgst;

    private Date date;

}
