package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProductInventory implements Comparable<ProductInventory> {


    private UnitBasicDetail unit;

    private ProductBasicDetail product;
    private Date lastStockOutTime;
    private Date lastUpdatedTime;
    private InventoryThresholdData thresholdData;

    private int quantity;
    private int expireQuantity;


    @Override
    public int compareTo(ProductInventory o) {
        int result;
        if(this.unit.getName().compareTo(o.unit.getName())==0){
            result = this.product.getDetail().getName().compareTo(o.product.getDetail().getName());
        }else{
            result = this.unit.getName().compareTo(o.unit.getName());
        }
        return result;
    }

}

