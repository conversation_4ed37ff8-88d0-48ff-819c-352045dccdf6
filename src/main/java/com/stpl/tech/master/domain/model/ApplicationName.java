package com.stpl.tech.master.domain.model;

import java.util.HashSet;
import java.util.Set;

public enum ApplicationName {

    KETTLE_SERVICE("KETTLE_SERVICE", 1),
    NEO_SERVICE("NEO_SERVICE", 3),
    DINE_IN("DINE_IN", 15),
    NOAH_SERVICE("NOAH_SERVICE", 2),
    MASTER_SERVICE("MASTER_SERVICE", 4),
    KETTLE_ADMIN("KETTLE_ADMIN", 5),
    KETTLE_CRM("KETTLE_CRM", 6),
    SCM_SERVICE("SCM_SERVICE", 7),
    KETTLE_ANALYTICS("<PERSON>ET<PERSON><PERSON>_ANALYTICS", 8),
    KETTLE_CHECKLIST("KETTLE_CHECKLIST", 9),
    WORKSTATION("WORKSTATION", 10),
    FORMS_SERVICE("FORMS_SERVICE", 11),
    SERVICE_ORDER("SERVICE_ORDER", 12),
    APP_INSTALLER("APP_INSTALLER", 14),
    CHANNEL_PARTNER("CHANNEL_PARTNER", 13),
    KIOSK_SERVICE("KIOSK_SERVICE", 16),
    REKOGNITION_SERVICE("REKOGNITION_SERVICE", 17),
    OFFER_SERVICE("OFFER_SERVICE", 18),
    ATTENDANCE_SERVICE("ATTENDANCE_SERVICE", 19),
    KNOCK_SERVICE("KNOCK_SERVICE", 20),
    KETTLE_OPS("KETTLE_OPS",23);
    private final String value;
    private final int id;

    ApplicationName(String v, int id) {
        value = v;
        this.id = id;
    }

    public String value() {
        return value;
    }

    public int id() {
        return id;
    }

    public static ApplicationName fromValue(String v) {
        for (ApplicationName c : ApplicationName.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

    public static Set<ApplicationName> accessFor(Designation designation) {
        Set<ApplicationName> names = new HashSet<>();
        if (designation.isAdminSystemAccess()) {
            names.add(KETTLE_ADMIN);
        }
        if (designation.isAnalyticsSystemAccess()) {
            names.add(KETTLE_ANALYTICS);
        }
        if (designation.isScmSystemAccess()) {
            names.add(SCM_SERVICE);
            names.add(SERVICE_ORDER);
        }
        if (designation.isCrmSystemAccess()) {
            names.add(KETTLE_CRM);
        }
        if (designation.isClmSystemAccess()) {
            names.add(KETTLE_CRM);
        }
        if (designation.isClmSystemAccess()) {
            names.add(KETTLE_CRM);
        }
        if (designation.isFormsSystemAccess()) {
            names.add(FORMS_SERVICE);
        }
        if (designation.isChannelPartnerSystemAccess()) {
            names.add(CHANNEL_PARTNER);
        }
        if (designation.isAppInstallerAccess()) {
            names.add(APP_INSTALLER);
        }
        if (designation.isTransactionSystemAccess()) {
            names.add(KETTLE_SERVICE);
            names.add(MASTER_SERVICE);
            names.add(KETTLE_CHECKLIST);
            names.add(WORKSTATION);
            names.add(CHANNEL_PARTNER);
            //names.add(OFFER_SERVICE);
        }
        if (designation.isAttendanceAccess()) {
            names.add(ATTENDANCE_SERVICE);
        }
        if (designation.isKnockApplicationAccess()) {
            names.add(KNOCK_SERVICE);
        }
        return names;
    }
}

