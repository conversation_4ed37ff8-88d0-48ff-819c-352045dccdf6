package com.stpl.tech.master.domain.model;



import java.io.Serializable;
import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;

import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TaxDetail implements Serializable {


    
    private static final long serialVersionUID = -1343648428169047915L;
    @Id
    private String _id;

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal percentage;

    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal value;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal total;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal taxable;

    private String type;

    private String code;

}


