package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import org.apache.commons.lang3.builder.HashCodeBuilder;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UnitPartnerBrandKey implements Serializable {

	private static final long serialVersionUID = -93045147658127L;

	private Integer unitId;

	private Integer brandId;

	private Integer partnerId;

	public UnitPartnerBrandKey(int unitId, int brandId, int partnerId) {
		this.unitId = unitId;
		this.brandId = brandId;
		this.partnerId = partnerId;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		UnitPartnerBrandKey that = (UnitPartnerBrandKey) o;
		return unitId != null && brandId != null && partnerId != null && that.unitId != null && that.brandId != null
				&& that.partnerId != null && unitId.intValue() == that.unitId.intValue()
				&& brandId.intValue() == that.brandId.intValue() && partnerId.intValue() == that.partnerId.intValue();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).append(unitId).append(brandId).append(partnerId).toHashCode();
	}

	@Override
	public String toString() {
		return "UnitPartnerBrandKey{" + "unitId=" + unitId + ", brandId=" + brandId + ", partnerId=" + partnerId + '}';
	}

	public String getKey() {
		return unitId + "_" + brandId + "_" + partnerId;
	}
}
