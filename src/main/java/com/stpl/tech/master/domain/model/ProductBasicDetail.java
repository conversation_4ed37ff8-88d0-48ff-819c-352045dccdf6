package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Version;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProductBasicDetail implements Serializable, Comparable<ProductBasicDetail> {


    
    private static final long serialVersionUID = -926969452764262475L;
    @Id
    private String objectId;

	@Version
	@JsonIgnore
	private Long version;

	private String detachAll;

	private IdCodeName detail;
	private int type;
	private int subType;

	private ProductClassification classification;
	private Integer webType;

	private ProductStatus status;

	private String code;
	private boolean isInventoryTracked;
	private boolean employeeMealComponent;

	@Override
	public int compareTo(ProductBasicDetail o) {
		return Integer.compare(this.getDetail().getId(), o.getDetail().getId());
	}

}
