package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TaxProfile implements Serializable, Comparable<TaxProfile> {

    
    private static final long serialVersionUID = -7126186715155990477L;

    private int id;

    private int profileId;

    @JsonDeserialize(using = BigDecimalDeserializer.class)

    private BigDecimal percentage;

    private TaxType type;

    private String name;

    private String status;


    @Override
    public int compareTo(TaxProfile o) {
        return Integer.compare(this.getId(), o.getId());
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((name == null) ? 0 : name.hashCode());
        result = prime * result + ((percentage == null) ? 0 : percentage.hashCode());
        result = prime * result + profileId;
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        TaxProfile other = (TaxProfile) obj;
        if (name == null) {
            if (other.name != null)
                return false;
        } else if (!name.equals(other.name))
            return false;
        if (percentage == null) {
            if (other.percentage != null)
                return false;
        } else if (!percentage.equals(other.percentage))
            return false;
        if (profileId != other.profileId)
            return false;
        return true;
    }

}

