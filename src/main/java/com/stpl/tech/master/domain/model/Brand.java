package com.stpl.tech.master.domain.model;


import com.stpl.tech.kettle.util.comparator.HasId;
import com.stpl.tech.kettle.util.comparator.HasStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Brand implements Serializable, Comparable<Brand>, HasId, HasStatus {

    
    private static final long serialVersionUID = -93045179307038127L;
    private Integer brandId;

    private String brandName;

    private String brandCode;

    private String tagLine;

    private String domain;

    private String billTag;

    private String websiteLink;

    private String status;

    private String supportContact;

    private String supportEmail;

    private String verbiage;

    private Boolean sendEmail;
    private Boolean sendWelcomeMessage;
    private Boolean awardLoyalty;
    private Boolean sendNPS;
    private String feedBackUrl;
    private String feedBackEndpointNPSDeliveryOnlyOrder;
    private Boolean sendFeedbackMessageDeliverySwiggy;
    private String feedbackEndpointDinein;
    private String feedbackEndpointDelivery;
    private String feedbackEndpointLowRatingDinein;
    private String feedbackEndpointLowRatingDelivery;
    private String feedbackEndpointRedirectUrl;
    private String feedbackEndpointNPSCafe;
    private String feedbackEndpointNPSDelivery;

    private String verificationEmailTemplate;

    private String smsId;
    private String internalOrderFeedbackUrl;
    private String chaayosSubscription;
    private String googleAdsCustomerId;
    //monk metaData
    private String monkImageLogoUrl;
    private String monkWelcomeVideoUrl;
    private String monkScreenSaver;
    private String monkBackgroundUrl;
    private String brandContactCode;


    @Override
    public Object objectId() {
        return this.brandId;
    }

    @Override
    public String currentStatus() {
        return this.status;
    }

    @Override
    public int compareTo(Brand o) {
        return o.brandId.compareTo(brandId);
    }


}

