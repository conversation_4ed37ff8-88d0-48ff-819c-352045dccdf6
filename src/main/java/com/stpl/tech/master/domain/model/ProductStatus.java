package com.stpl.tech.master.domain.model;


public enum ProductStatus {

    ACTIVE("ACTIVE"),
    IN_ACTIVE("IN_ACTIVE");
    private final String value;

    ProductStatus(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static ProductStatus fromValue(String v) {
        for (ProductStatus c : ProductStatus.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
