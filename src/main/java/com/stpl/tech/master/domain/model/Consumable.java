package com.stpl.tech.master.domain.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Consumable {

    private int productId;

    private String name;

    private BigDecimal quantity;
    private String uom;
    private boolean addOn;
    private BigDecimal price;
    private BigDecimal cost = BigDecimal.ZERO;
    private  BigDecimal taxableQuantity = BigDecimal.ZERO;
    private  BigDecimal taxPercentage = BigDecimal.ZERO;

}

