package com.stpl.tech.master.domain.model;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public enum OfferMetaDataType {
	PRODUCT("PRODUCT") {
		@Override
		public List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails) {
			Set<Integer> productCategories = productDetails.stream()
					.mapToInt(productDetail -> productDetail.getDetail().getId()).boxed().collect(Collectors.toSet());
			List<Integer> categories = productCategories.stream().filter(p -> convert(mappingValues).contains(p))
					.collect(Collectors.toList());
			return categories;
		}
	},

	PRODUCT_SUB_CATEGORY("PRODUCT_SUB_CATEGORY") {
		@Override
		public List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails) {
			return productDetails.stream().filter(p -> convert(mappingValues).contains(p.getSubType()))
					.mapToInt(p -> p.getDetail().getId()).boxed().collect(Collectors.toList());
		}
	},

	PRODUCT_CATEGORY("PRODUCT_CATEGORY") {
		@Override
		public List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails) {
			Set<Integer> intMappings = convert(mappingValues);
			return productDetails.stream().filter(p -> intMappings.contains(p.getType()))
					.mapToInt(p -> p.getDetail().getId()).boxed().collect(Collectors.toList());
		}
	},

	PRODUCT_QUANTITY("PRODUCT_QUANTITY") {
		@Override
		public List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails) {
			/*
			 * Set<Integer> productCategories = productDetails.stream()
			 * .mapToInt(productDetail -> productDetail.get())
			 * .boxed().collect(Collectors.toSet()); return
			 * productCategories.stream().filter(mappingValues::contains).
			 * collect(Collectors.toList());
			 */
			return null;
		}
	},

	COMBO_ITEM_GROUP("COMBO_ITEM_GROUP") {
		@Override
		public List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails) {

			return null;
		}
	};

	private static Set<Integer> convert(Set<String> mappingValues) {
		return mappingValues.stream().mapToInt(Integer::parseInt).boxed().collect(Collectors.toSet());
	}

	private final String value;

	OfferMetaDataType(String v) {
		value = v;
	}

	public String value() {
		return value;
	}

	public static OfferMetaDataType fromValue(String v) {
		for (OfferMetaDataType c : OfferMetaDataType.values()) {
			if (c.value.equals(v)) {
				return c;
			}
		}
		throw new IllegalArgumentException(v);
	}

	public abstract List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails);
}
