package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CouponCloneResponse implements Serializable {

	private static final long serialVersionUID = -1177814200624419874L;

	private String code;
	private String description;
	private Map<String, CouponData> mappings;
	private IdentifierType identifier;
	private List<String> errors;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	//
	public Map<String, CouponData> getMappings() {
		if (mappings == null) {
			return new HashMap<>();
		}
		return mappings;
	}

	public void setMappings(Map<String, CouponData> mappings) {
		this.mappings = mappings;
	}

	public IdentifierType getIdentifier() {
		return identifier;
	}

	public void setIdentifier(IdentifierType identifier) {
		this.identifier = identifier;
	}

	public List<String> getErrors() {
		if (errors == null) {
			errors = new ArrayList<String>();
		}
		return errors;
	}

	public void setErrors(List<String> errors) {
		this.errors = errors;
	}

}
