package com.stpl.tech.master.domain.model;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.util.Pair;


public class PaymentMode implements Serializable, Comparable<PaymentMode> {
    
    private static final long serialVersionUID = -8269062611081355533L;
    @Id
    private String _id;

    private int id;

    private String name;

    private String description;

    private String type;

    private PaymentCategory category;

    @Field
    private String settlementType;
    @Field
    private boolean generatePull;
    @Field
    private boolean autoPullValidate;
    @Field
    private boolean autoTransfer;
    @Field
    private boolean autoCloseTransfer;
    @Field
    private boolean editable;
    @Field
    private boolean applicableOnDiscountedOrders;
    @Field
    private String validationSource;
    @Field
    private boolean needsSettlementSlip;
    @Field
    private List<DenominationDetail> denominations;


    private String status;

    private String ledgerName;
    @Field
    private List<Pair<String, String>> attributes;
    private BigDecimal commissionRate;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public int getId() {
        return id;
    }

    public void setId(int value) {
        this.id = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String value) {
        this.name = value;
    }


    public String getDescription() {
        return description;
    }

    public void setDescription(String value) {
        this.description = value;
    }

    public String getType() {
        return type;
    }

    public void setType(String value) {
        this.type = value;
    }

    public String getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(String value) {
        this.settlementType = value;
    }

    public boolean isGeneratePull() {
        return generatePull;
    }

    public void setGeneratePull(boolean value) {
        this.generatePull = value;
    }

    public List<DenominationDetail> getDenominations() {
        if (Objects.isNull(denominations)) {
            denominations = new ArrayList<DenominationDetail>();
        }
        return this.denominations;
    }

    public List<Pair<String, String>> getAttributes() {
        if (Objects.isNull(attributes)) {
            attributes = new ArrayList<Pair<String, String>>();
        }
        return this.attributes;
    }

    @Override
    public int compareTo(PaymentMode o) {
        return Integer.compare(this.getId(), o.getId());
    }

    public PaymentCategory getCategory() {
        return category;
    }

    public void setCategory(PaymentCategory category) {
        this.category = category;
    }

    public boolean isAutoPullValidate() {
        return autoPullValidate;
    }

    public void setAutoPullValidate(boolean autoPullValidate) {
        this.autoPullValidate = autoPullValidate;
    }

    public boolean isAutoTransfer() {
        return autoTransfer;
    }

    public void setAutoTransfer(boolean autoTransfer) {
        this.autoTransfer = autoTransfer;
    }

    public boolean isAutoCloseTransfer() {
        return autoCloseTransfer;
    }

    public void setAutoCloseTransfer(boolean autoCloseTransfer) {
        this.autoCloseTransfer = autoCloseTransfer;
    }

    public boolean isEditable() {
        return editable;
    }

    public void setEditable(boolean editable) {
        this.editable = editable;
    }

    public boolean isApplicableOnDiscountedOrders() {
        return applicableOnDiscountedOrders;
    }

    public void setApplicableOnDiscountedOrders(boolean applicableOnDiscountedOrders) {
        this.applicableOnDiscountedOrders = applicableOnDiscountedOrders;
    }

    public boolean isNeedsSettlementSlip() {
        return needsSettlementSlip;
    }

    public void setNeedsSettlementSlip(boolean needsSettlementSlip) {
        this.needsSettlementSlip = needsSettlementSlip;
    }

    public String getValidationSource() {
        return validationSource;
    }

    public void setValidationSource(String validationSource) {
        this.validationSource = validationSource;
    }

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLedgerName() {
        return ledgerName;
    }

    public void setLedgerName(String ledgerName) {
        this.ledgerName = ledgerName;
    }


}
