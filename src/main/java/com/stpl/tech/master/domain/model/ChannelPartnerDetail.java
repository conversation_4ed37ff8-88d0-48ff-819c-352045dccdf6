package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ChannelPartnerDetail extends IdCodeName implements Serializable {

	private static final long serialVersionUID = -3476713812722830735L;
	private Integer creditAccount;
	private BigDecimal commission;
	private BigDecimal taxRate;
	private Boolean apiIntegrated;

}
