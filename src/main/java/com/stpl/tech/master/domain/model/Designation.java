package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Designation implements Serializable, Comparable<Designation> {

	private static final long serialVersionUID = 1056127804090070686L;
	private int id;

	private String name;
	private String description;

	private boolean transactionSystemAccess;

	private boolean scmSystemAccess;

	private boolean adminSystemAccess;

	private boolean clmSystemAccess;

	private boolean analyticsSystemAccess;

	private boolean crmSystemAccess;

	private boolean formsSystemAccess;

	private boolean channelPartnerSystemAccess;

	private boolean appInstallerAccess;

	private int maxAllocatedUnits = -1;

	private boolean attendanceAccess;

	private boolean knockApplicationAccess;

	@Override
	public int compareTo(Designation o) {
		return Integer.compare(this.getId(), o.getId());
	}
}
