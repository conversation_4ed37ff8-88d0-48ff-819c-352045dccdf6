/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.24 at 05:01:33 PM IST 
//

package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(value = {"handler"})
@Document
public class Address implements Serializable {
    private static final long serialVersionUID = 8075917195527246495L;

    @Id
    private String _id;
    @Field
    private int id;
    @Field
    private String name;
    @Field
    private String landmark;
    @Field
    private String line1;
    @Field
    private String line2;
    @Field
    private String line3;
    @Field
    private String subLocality;
    @Field
    private String locality;
    @Field
    private String city;
    @Field
    private String state;
    @Field
    private String country;
    @Field
    private String zipCode;
    @Field
    private String contact1;
    @Field
    private String contact2;
    @Field
    private String addressType;
    @Field
    private String company;
    @Field
    private String latitude;
    @Field
    private String longitude;
    @Field
    private Boolean preferredAddress;
    private String email;
    private String source;
    private String sourceId;
    private String status= ActivityStatus.ACTIVE.name();
}
