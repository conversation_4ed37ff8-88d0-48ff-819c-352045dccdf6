package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Company implements Serializable {

  
    private static final long serialVersionUID = 3612970137451550270L;
    protected int id;

    protected String name;

    protected String description;

    protected String cin;

    protected String serviceTaxNumber;

    protected String websiteAddress;

    protected Address registeredAddress;

    protected String shortCode;

}

