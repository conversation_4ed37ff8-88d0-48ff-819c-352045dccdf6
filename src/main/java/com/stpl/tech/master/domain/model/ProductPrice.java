package com.stpl.tech.master.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;
import com.stpl.tech.master.recipe.model.RecipeDetail;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProductPrice implements Serializable {

    @Serial
    private static final long serialVersionUID = 5243086035594490714L;

    private int id;

    private String dimension;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal price;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal cost;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal codCost;
    private RecipeDetail recipe;

    private Integer buffer;

    private Integer threshold;

    private Boolean customize;

    private String profile;

    private String status = "ACTIVE";

    private Integer recipeId;

    private String aliasProductName;

    private String dimensionDescriptor;

    @JsonProperty("isDeliveryOnlyProduct")
    private Boolean isDeliveryOnlyProduct;

}
