package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OfferLastRedemptionView {
    private Integer customerId;
    private Date lastOrderTime;
    private Integer orderToday;
    private Integer orderInLastHour;
}
