package com.stpl.tech.master.domain.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ComplimentaryReason extends IdCodeName {

	private static final long serialVersionUID = -7489228605277975480L;

	public ComplimentaryReason(int id, String name, String code, String shortCode, String type, String status,
			String category) {
		super(id, name, code, shortCode, type, status);
		this.category = category;
	}

	private String category;

}
