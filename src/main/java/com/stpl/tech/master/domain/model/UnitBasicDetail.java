package com.stpl.tech.master.domain.model;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.stpl.tech.kettle.report.metadata.model.TrueCallerSettings;

import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UnitBasicDetail implements Serializable, Comparable<UnitBasicDetail> {


    
    private static final long serialVersionUID = -2153356827093573640L;
    @Id
    private String _id;

    private int id;

    private String name;
    private String referenceName;

    private UnitStatus status;

    private int noOfTerminal;

    private int noOfTakeawayTerminals;

    private UnitCategory category;

    private String region;

    private String city;

    private String contact;

    private String email;

    private String tin;

    private String address;

    private String latitude;

    private String longitude;

    private UnitSubCategory subCategory;

    private String locationCode;
    private String state;
    private String stateCode;
    private boolean partnerPriced;
    private boolean tokenEnabled;
    private int companyId;
    private TrueCallerSettings trueCallerEnabled;
    private boolean workStationEnabled;
    private boolean live;
    private boolean hotAndColdMerged;
    private boolean liveInventoryEnabled;
    private IdCodeName location;
    private Integer unitManagerId;
    private Integer cafeManagerId;
    private String packagingType;
    private BigDecimal packagingValue;
    private String cafeAppStatus;
    private String cafeNeoStatus;
    private String googleMerchantId;
    private String shortName;
    private String costCenterName;
    private String fssai;
    private Date handOverDate;
    private Integer salesClonedFrom;
    private Date probableOpeningDate;
    private Integer pricingProfile;
    private String unitZone;
    private String f9Enabled;

    private Boolean isClosed;


    /*
     * (non-Javadoc)
     *
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + id;
        return result;
    }

    /*
     * (non-Javadoc)
     *
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof UnitBasicDetail)) {
            return false;
        }
        UnitBasicDetail other = (UnitBasicDetail) obj;
        if (id != other.id) {
            return false;
        }
        return true;
    }

    @Override
    public int compareTo(UnitBasicDetail o) {
        return Integer.compare(this.getId(), o.getId());
    }


}
