package com.stpl.tech.master.domain.model;


public enum UnitStatus {

    ACTIVE("ACTIVE"),
    IN_ACTIVE("IN_ACTIVE");
    private final String value;

    UnitStatus(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static UnitStatus fromValue(String v) {
        for (UnitStatus c : UnitStatus.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}

