package com.stpl.tech.master.domain.model;

import java.math.BigDecimal;


import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OfferWithFreeItemData {

    private int productId;
    private String dimension;
    private int quantity;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    private BigDecimal value;
    private OfferValueType type;


}

