package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CampaignMapping implements Serializable {

	private static final long serialVersionUID = 1L;

	private Integer campaignCouponMappingId;
	private int campaignId;
	private String code;
	private int validityInDays;
	private int journey;
	private String desc;
	private Integer reminderDays;
}
