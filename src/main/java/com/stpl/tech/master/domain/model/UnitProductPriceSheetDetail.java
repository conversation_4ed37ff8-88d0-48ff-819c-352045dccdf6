package com.stpl.tech.master.domain.model;


import com.stpl.tech.kettle.annotation.ExcelField;
import com.stpl.tech.kettle.annotation.ExcelObject;
import com.stpl.tech.kettle.annotation.ExcelSheet;
import com.stpl.tech.kettle.annotation.ParseType;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@ExcelSheet(value = "Product Price Sheet Detail")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Getter
@Setter
@NoArgsConstructor
public class UnitProductPriceSheetDetail implements Serializable {

    private static final long serialVersionUID = 2295422929550338128L;
    @ExcelField
    private String pricingProfileName;
    @ExcelField
    private Integer unitId;
    @ExcelField
    private String unitName;
    @ExcelField
    private Integer productId;
    @ExcelField
    private String productName;
    @ExcelField
    private Integer dimensionCode;
    @ExcelField
    private String dimension;
    @ExcelField
    private String unitCategory;
    @ExcelField
    private Integer pricingProfile;
    @ExcelField
    private Integer brandId;
    @ExcelField
    private String unitRegion;
    @ExcelField
    private String rtlCode;
    @ExcelField
    private String rlCode;
    @ExcelField
    private String productStatus;
    @ExcelField
    private Integer unitProductMappingId;
    @ExcelField
    private String unitProductMappingStatus;
    @ExcelField
    private Integer unitProductPriceId;
    @ExcelField
    private String unitProductPricingStatus;
    @ExcelField
    private BigDecimal price;
    @ExcelField
    private BigDecimal newPrice;

  

    public UnitProductPriceSheetDetail(String pricingProfileName, Integer unitId, String unitName, Integer productId, String productName, Integer dimensionCode, String dimension,
                                       String unitCategory, Integer pricingProfile, Integer brandId, String unitRegion, String rtlCode, String rlCode, String productStatus, Integer unitProductMappingId,
                                       String unitProductMappingStatus, Integer unitProductPriceId, String unitProductPricingStatus, BigDecimal price) {
        this.unitId = unitId;
        this.unitName = unitName;
        this.unitCategory = unitCategory;
        this.pricingProfile = pricingProfile;
        this.pricingProfileName = pricingProfileName;
        this.brandId = brandId;
        this.unitRegion = unitRegion;
        this.productId = productId;
        this.rtlCode = rtlCode;
        this.rlCode = rlCode;
        this.productName = productName;
        this.productStatus = productStatus;
        this.unitProductMappingId = unitProductMappingId;
        this.unitProductMappingStatus = unitProductMappingStatus;
        this.dimensionCode = dimensionCode;
        this.unitProductPriceId = unitProductPriceId;
        this.dimension = dimension;
        this.unitProductPricingStatus = unitProductPricingStatus;
        this.price = price;
    }

    @Override
    public String toString() {
        return "UnitProductPriceSheetDetail{" +
                "pricingProfileName='" + pricingProfileName + '\'' +
                ", unitId=" + unitId +
                ", unitName='" + unitName + '\'' +
                ", productId=" + productId +
                ", productName='" + productName + '\'' +
                ", dimensionCode=" + dimensionCode +
                ", dimension='" + dimension + '\'' +
                ", unitCategory='" + unitCategory + '\'' +
                ", pricingProfile=" + pricingProfile +
                ", brandId=" + brandId +
                ", unitRegion='" + unitRegion + '\'' +
                ", rtlCode='" + rtlCode + '\'' +
                ", rlCode='" + rlCode + '\'' +
                ", productStatus='" + productStatus + '\'' +
                ", unitProductMappingId=" + unitProductMappingId +
                ", unitProductMappingStatus='" + unitProductMappingStatus + '\'' +
                ", unitProductPriceId=" + unitProductPriceId +
                ", unitProductPricingStatus='" + unitProductPricingStatus + '\'' +
                ", price=" + price +
                ", newPrice=" + newPrice +
                '}';
    }
}

