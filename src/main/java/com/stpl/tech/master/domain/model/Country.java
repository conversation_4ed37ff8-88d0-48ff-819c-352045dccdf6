package com.stpl.tech.master.domain.model;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


public class Country implements Serializable {

    
    private static final long serialVersionUID = -2998484615378924128L;
    private int id;

    private String name;

    private String code;

    private String isdCode;

    private List<State> states;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIsdCode() {
        return isdCode;
    }

    public void setIsdCode(String isdCode) {
        this.isdCode = isdCode;
    }

    public List<State> getStates() {
        if (Objects.isNull(states)) {
            states = new ArrayList<>();
        }
        return states;
    }

}
