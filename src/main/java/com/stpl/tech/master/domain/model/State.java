package com.stpl.tech.master.domain.model;


import java.io.IOException;
import java.io.Serializable;
import java.util.List;

import com.hazelcast.nio.serialization.Portable;
import com.hazelcast.nio.serialization.PortableReader;
import com.hazelcast.nio.serialization.PortableWriter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class State implements Serializable {

    
    private static final long serialVersionUID = -4149079365355931538L;

    private int id;

    private String name;

    private String code;
    private boolean isUt;
    private boolean business;
    private Country country;
    private List<Location> locations;
    private String shortCode;


}
