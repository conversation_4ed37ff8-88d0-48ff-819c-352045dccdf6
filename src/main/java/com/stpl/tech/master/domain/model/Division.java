package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Division implements Comparable<Division>, Serializable {



    private static final long serialVersionUID = 427584669946058587L;

    private int id;

    private String name;

    private String description;

    private String category;

    private Company company;


    @Override
    public int compareTo(Division o) {
        return Integer.compare(this.getId(), o.getId());
    }

}

