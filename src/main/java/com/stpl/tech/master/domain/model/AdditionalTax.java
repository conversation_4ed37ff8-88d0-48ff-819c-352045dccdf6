package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalDeserializer;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AdditionalTax implements Serializable {

    
    private static final long serialVersionUID = 2552472799182596857L;
    protected Integer keyId;

    protected IdCodeName state;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    protected BigDecimal tax;

    protected Date date;
    protected TaxApplicability applicability;

    protected String type;



}

