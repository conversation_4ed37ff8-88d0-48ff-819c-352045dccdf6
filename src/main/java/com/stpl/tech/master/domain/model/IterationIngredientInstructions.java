package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class IterationIngredientInstructions implements Serializable {

	private static final long serialVersionUID = 2724125928939936517L;
	@Id
	private String _id;

	protected String instruction;

}