package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CampaignDetail implements Serializable {

	private static final long serialVersionUID = 1L;

	private Integer campaignId;
	private String primaryUrl;
	private String campaignStrategy;
	private String campaignSource;
	private String campaignMedium;
	private String campaignName;
	private String campaignCategory;
	private String campaignDesc;
	private String couponCode;
	private String couponCodeDesc;
	private boolean couponClone;
	private String region;
	private String city;
	private String unitIds;
	private Integer usageLimit;
	private Date startDate;
	private Date endDate;
	private Integer validity;
	private String heroBannerMobile;
	private String heroBannerDesktop;
	private String landingPageDesc;
	private String smsTemplate;
	private String smsReminder;
	private String whatsappTemplate;
	private String whatsappReminder;
	private Integer reminderDayGap;
	private String utmHeading;
	private String utmDesc;
	private String utmImageUrl;
	private String redirectionUrl;
	private String campaignStatus;
	private String campaignToken;
	private String image1;
	private String image2;
	private String image3;
	private String longUrl;
	private String shortUrl;
	private String newCustomerOnly;
	private String campaignReach;
	private Map<String, Map<Integer, CampaignMapping>> mappings;
	private String couponPrefix;
	private Integer linkedCampaignId;
	private Integer couponApplicableAfter;
	private Integer brandId;
	private Boolean applicableForOrder;
	private String parentCampaignStrategy;
	private Integer launchUnitId;

	private Date cafeLaunchDate;
}
