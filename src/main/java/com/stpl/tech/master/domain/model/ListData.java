package com.stpl.tech.master.domain.model;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ListData implements Serializable, Comparable<ListData> {


    private static final long serialVersionUID = 4514671108364575991L;
    protected IdCodeName detail;

    protected List<IdCodeName> content;


    public IdCodeName getDetail() {
        return detail;
    }

    public void setDetail(IdCodeName value) {
        this.detail = value;
    }

    public List<IdCodeName> getContent() {
        if (Objects.isNull(content)) {
            content = new ArrayList<IdCodeName>();
        }
        return this.content;
    }

    @Override
    public int compareTo(ListData o) {
        return Integer.compare(this.getDetail().getId(), o.getDetail().getId());
    }

}
