package com.stpl.tech.master.domain.model;

public enum UnitCategory {

    CAFE("CAFE"),
    DELIVERY("DELIVERY"),
    TAKE_AWAY("TAKE_AWAY"),
    COD("COD"),
    KITCHEN("<PERSON><PERSON><PERSON><PERSON>"),
    WAREHOUSE("WAREHOUSE"),
    OFFICE("OFFICE"),
    CHAI_MONK("CHAI_MONK"),
    EMPLOYEE_MEAL("EMPLOYEE_MEAL");
    private final String value;

    UnitCategory(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static UnitCategory fromValue(String v) {
        for (UnitCategory c : UnitCategory.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}

