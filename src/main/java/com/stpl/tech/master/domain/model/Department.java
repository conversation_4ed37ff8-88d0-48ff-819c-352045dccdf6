package com.stpl.tech.master.domain.model;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class Department implements Serializable, Comparable<Department> {

    
    private static final long serialVersionUID = 6000325545335810420L;
    private int id;

    private String name;

    private String description;

    private Division division;

    private List<Designation> designations;

    public int getId() {
        return id;
    }

    public void setId(int value) {
        this.id = value;
    }


    public String getName() {
        return name;
    }


    public void setName(String value) {
        this.name = value;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String value) {
        this.description = value;
    }


    public Division getDivision() {
        return division;
    }

    public void setDivision(Division value) {
        this.division = value;
    }

    public List<Designation> getDesignations() {
        if (Objects.isNull(designations)) {
            designations = new ArrayList<Designation>();
        }
        return this.designations;
    }

    @Override
    public int compareTo(Department o) {
        return Integer.compare(this.getId(), o.getId());
    }

}

