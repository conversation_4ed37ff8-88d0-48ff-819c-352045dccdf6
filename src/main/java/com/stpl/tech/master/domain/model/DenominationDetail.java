package com.stpl.tech.master.domain.model;


import java.io.Serializable;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DenominationDetail implements Serializable, Comparable<DenominationDetail> {

    
    private static final long serialVersionUID = -2576849608609905519L;
    @Id
    private String _id;


    private int denominationId;

    private String denominationCode;

    private String denominationText;

    private String status;

    private int displayOrder;

    private int denominationValue;

    private int bundleSize;

    private int paymentMode;


    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + denominationId;
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        DenominationDetail other = (DenominationDetail) obj;
        if (denominationId != other.denominationId)
            return false;
        return true;
    }

    @Override
    public int compareTo(DenominationDetail o) {
        return Integer.compare(this.getDenominationId(), o.getDenominationId());
    }
}

