package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;


public class CouponDetail implements Serializable {


    
    private static final long serialVersionUID = 3156411541062422067L;

    private int id;

    private String code;

    private String status;
    private int usage;
    private Date startDate;
    private Date endDate;
    private boolean reusable;
    private boolean reusableByCustomer;

    private Integer maxUsage;

    private Integer maxCustomerUsage;

    private OfferDetail offer;
    private boolean manualOverride;
    private List<CouponMapping> couponMappingList;
    private Map<String, Set<CouponMapping>> mappings;

    public int getId() {
        return id;
    }

    public void setId(int value) {
        this.id = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String value) {
        this.code = value;
    }

    public String getStatus() {
        return status;
    }


    public void setStatus(String value) {
        this.status = value;
    }

    public int getUsage() {
        return usage;
    }

    public void setUsage(int value) {
        this.usage = value;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date value) {
        this.startDate = value;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date value) {
        this.endDate = value;
    }

    public boolean isReusable() {
        return reusable;
    }

    public void setReusable(boolean value) {
        this.reusable = value;
    }

    public boolean isReusableByCustomer() {
        return reusableByCustomer;
    }

    public void setReusableByCustomer(boolean value) {
        this.reusableByCustomer = value;
    }

    public Integer getMaxUsage() {
        return maxUsage;
    }

    public void setMaxUsage(Integer value) {
        this.maxUsage = value;
    }

    public OfferDetail getOffer() {
        return offer;
    }

    public void setOffer(OfferDetail value) {
        this.offer = value;
    }

    public boolean isManualOverride() {
        return manualOverride;
    }

    public void setManualOverride(boolean value) {
        this.manualOverride = value;
    }

    public List<CouponMapping> getCouponMappingList() {
        if (Objects.isNull(couponMappingList)) {
            couponMappingList = new ArrayList<CouponMapping>();
        }
        return this.couponMappingList;
    }

    public void setCouponMappingList(List<CouponMapping> couponMappingList) {
        this.couponMappingList = couponMappingList;
    }

    public Map<String, Set<CouponMapping>> getMappings() {
        if (Objects.isNull(mappings)) {
            mappings = new HashMap<String, Set<CouponMapping>>();
        }
        return this.mappings;
    }

    public Integer getMaxCustomerUsage() {
        return maxCustomerUsage;
    }

    public void setMaxCustomerUsage(Integer maxCustomerUsage) {
        this.maxCustomerUsage = maxCustomerUsage;
    }

}
