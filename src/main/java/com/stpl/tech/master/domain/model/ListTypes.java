package com.stpl.tech.master.domain.model;

import com.stpl.tech.kettle.util.Constants.AppConstants;

public enum ListTypes {
    DISCOUNT_CODES(AppConstants.RTL_GROUP_DISCOUNT), SUB_CATEGORIES(AppConstants.RTL_GROUP_CATEGORY),
    DIMENSION_CODES(AppConstants.RTL_GROUP_DIMENSION), CHANNEL_PARTNERS("CHANNEL_PARTNER"),
    DELIVERY_PARTNERS("DELIVERY_PARTNER"), COMPLIMENTARY_CODES(AppConstants.RTL_GROUP_COMPLIMENTARY),
    PR_TYPE(AppConstants.RTL_GROUP_PR_TYPE), ITEM_PER_TICKET(AppConstants.RTL_GROUP_ITEM_PER_TICKET),
    ADJUSTMENT_COMMENT(AppConstants.RTL_GROUP_ADJUSTMENT_COMMENT) ;

    private final String group;

    public String getGroup() {
        return group;
    }

    private ListTypes(String group) {
        this.group = group;
    }
}
