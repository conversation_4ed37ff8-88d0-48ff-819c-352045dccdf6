package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


public class OfferDetail implements Serializable {

    private static final long serialVersionUID = -6917393831012207579L;

    protected int id;

    protected String category;

    protected String type;

    protected String text;

    protected String description;

    protected Date startDate;

    protected Date endDate;
    protected int minValue;
    protected boolean includeTaxes;

    protected String status;
    protected boolean validateCustomer;
    protected Boolean removeLoyalty;
    protected int minQuantity;
    protected int minLoyalty;
    protected int offerValue;
    protected int minItemCount;

    protected String offerScope;

    protected String emailDomain;
    protected int priority;

    protected IdName accountsCategory;
    protected OfferWithFreeItemData offerWithFreeItem;
    protected boolean isPrepaid;
    protected BigDecimal prepaidAmount;
    protected BigDecimal maxBillValue;
    protected List<MappingIdCodeName> partners;
    protected List<IdCodeName> metaDataMappings;
    protected boolean otpRequired;
    protected BigDecimal maxDiscountAmount;
    protected List<CouponMapping> couponMappingList;
    protected Boolean frequencyApplicable;
    protected String frequencyStrategy;
    protected Integer frequencyCount;
    protected Integer dailyFrequencyCount;
    protected Integer applicableHour;
    protected Boolean autoApplicableforUnit;
    protected String termsAndConditions;
    protected Integer maxQuantity;


    public int getId() {
        return id;
    }


    public void setId(int value) {
        this.id = value;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String value) {
        this.category = value;
    }


    public String getType() {
        return type;
    }


    public void setType(String value) {
        this.type = value;
    }

    public String getText() {
        return text;
    }

    public void setText(String value) {
        this.text = value;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String value) {
        this.description = value;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date value) {
        this.startDate = value;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date value) {
        this.endDate = value;
    }

    public int getMinValue() {
        return minValue;
    }

    public void setMinValue(int value) {
        this.minValue = value;
    }

    public boolean isIncludeTaxes() {
        return includeTaxes;
    }

    public void setIncludeTaxes(boolean value) {
        this.includeTaxes = value;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String value) {
        this.status = value;
    }

    public boolean isValidateCustomer() {
        return validateCustomer;
    }

    public void setValidateCustomer(boolean value) {
        this.validateCustomer = value;
    }

    public int getMinQuantity() {
        return minQuantity;
    }

    public void setMinQuantity(int value) {
        this.minQuantity = value;
    }

    public int getMinLoyalty() {
        return minLoyalty;
    }

    public void setMinLoyalty(int value) {
        this.minLoyalty = value;
    }

    public int getOfferValue() {
        return offerValue;
    }

    public void setOfferValue(int value) {
        this.offerValue = value;
    }

    public int getMinItemCount() {
        return minItemCount;
    }

    public void setMinItemCount(int value) {
        this.minItemCount = value;
    }

    public String getOfferScope() {
        return offerScope;
    }

    public void setOfferScope(String value) {
        this.offerScope = value;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int value) {
        this.priority = value;
    }

    public List<MappingIdCodeName> getPartners() {
        if (Objects.isNull(partners)) {
            partners = new ArrayList<MappingIdCodeName>();
        }
        return this.partners;
    }

    public List<IdCodeName> getMetaDataMappings() {
        if (Objects.isNull(metaDataMappings)) {
            metaDataMappings = new ArrayList<IdCodeName>();
        }
        return this.metaDataMappings;
    }

    public String getEmailDomain() {
        return emailDomain;
    }

    public void setEmailDomain(String emailDomain) {
        this.emailDomain = emailDomain;
    }

    public IdName getAccountsCategory() {
        return accountsCategory;
    }

    public void setAccountsCategory(IdName accountsCategory) {
        this.accountsCategory = accountsCategory;
    }

    public OfferWithFreeItemData getOfferWithFreeItem() {
        return offerWithFreeItem;
    }

    public void setOfferWithFreeItem(OfferWithFreeItemData offerWithFreeItem) {
        this.offerWithFreeItem = offerWithFreeItem;
    }

    public Boolean getRemoveLoyalty() {
        return removeLoyalty;
    }

    public void setRemoveLoyalty(Boolean removeLoyalty) {
        this.removeLoyalty = removeLoyalty;
    }

    public boolean isPrepaid() {
        return isPrepaid;
    }

    public void setPrepaid(boolean prepaid) {
        isPrepaid = prepaid;
    }

    public BigDecimal getPrepaidAmount() {
        return prepaidAmount;
    }

    public void setPrepaidAmount(BigDecimal prepaidAmount) {
        this.prepaidAmount = prepaidAmount;
    }

    public BigDecimal getMaxBillValue() {
        return maxBillValue;
    }

    public void setMaxBillValue(BigDecimal maxBillValue) {
        this.maxBillValue = maxBillValue;
    }

    public boolean getOtpRequired() {
        return otpRequired;
    }

    public void setOtpRequired(boolean otpRequired) {
        this.otpRequired = otpRequired;
    }

    public BigDecimal getMaxDiscountAmount() {
        return maxDiscountAmount;
    }

    public void setMaxDiscountAmount(BigDecimal maxDiscountAmount) {
        this.maxDiscountAmount = maxDiscountAmount;
    }

    public List<CouponMapping> getCouponMappingList() {
        if (Objects.isNull(couponMappingList)) {
            couponMappingList = new ArrayList<>();
        }
        return couponMappingList;
    }

    public void setCouponMappingList(List<CouponMapping> couponMappingList) {
        this.couponMappingList = couponMappingList;
    }

    public Boolean isFrequencyApplicable() {
        return frequencyApplicable;
    }

    public void setFrequencyApplicable(Boolean frequencyApplicable) {
        this.frequencyApplicable = frequencyApplicable;
    }

    public String getFrequencyStrategy() {
        return frequencyStrategy;
    }

    public void setFrequencyStrategy(String frequencyStrategy) {
        this.frequencyStrategy = frequencyStrategy;
    }

    public Integer getFrequencyCount() {
        return frequencyCount;
    }

    public void setFrequencyCount(Integer frequencyCount) {
        this.frequencyCount = frequencyCount;
    }

    public Integer getDailyFrequencyCount() {
        return dailyFrequencyCount;
    }

    public void setDailyFrequencyCount(Integer dailyFrequencyCount) {
        this.dailyFrequencyCount = dailyFrequencyCount;
    }

    public Integer getApplicableHour() {
        return applicableHour;
    }

    public void setApplicableHour(Integer applicableHour) {
        this.applicableHour = applicableHour;
    }


    public Boolean getAutoApplicableforUnit() {
        return autoApplicableforUnit;
    }

    public void setAutoApplicableforUnit(Boolean autoApplicableforUnit) {
        this.autoApplicableforUnit = autoApplicableforUnit;
    }

    public String getTermsAndConditions() {
        return termsAndConditions;
    }

    public void setTermsAndConditions(String termsAndConditions) {
        this.termsAndConditions = termsAndConditions;
    }

    public Boolean getFrequencyApplicable() {
        return frequencyApplicable;
    }

    public Integer getMaxQuantity() {
        return maxQuantity;
    }

    public void setMaxQuantity(Integer maxQuantity) {
        this.maxQuantity = maxQuantity;
    }
}
