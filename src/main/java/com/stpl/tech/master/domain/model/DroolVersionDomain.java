package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DroolVersionDomain implements Serializable {

    private String version;
    private Date creationTime;
    private Integer createdBy;
    private Date lastUpdationTime;
    private Integer updatedBy;
    private String remarks;

}
