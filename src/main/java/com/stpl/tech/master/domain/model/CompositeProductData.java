package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.annotation.Id;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CompositeProductData implements Serializable {

    
    private static final long serialVersionUID = 6220579205051084768L;
    @Id
    private String _id;

    protected int maxQuantity;

    protected List<CompositeIngredientData> details;

}

