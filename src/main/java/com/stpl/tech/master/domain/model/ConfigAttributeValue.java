package com.stpl.tech.master.domain.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
public class ConfigAttributeValue implements Serializable {

	@Serial
	private static final long serialVersionUID = 7904951840764147450L;
	private int id;
	private ConfigAttributeDefinition attributeDef;
	private String attributeValue;
	private String applicationName;

}
