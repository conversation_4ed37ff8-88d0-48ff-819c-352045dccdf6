package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CouponMapping implements Serializable {

	private static final long serialVersionUID = -5375213879043279887l;

	private int id;

	private String type;

	private String value;

	private String dimension;

	private String dataType;

	private String minValue;
	private int group;

	private String status;

	private String source;

	public CouponMapping(String value) {
		super();
		this.value = value;
	}

	public CouponMapping(String value, String dimension) {
		super();
		this.value = value;
		this.dimension = dimension;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((dimension == null) ? 0 : dimension.hashCode());
		result = prime * result + ((value == null) ? 0 : value.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CouponMapping other = (CouponMapping) obj;
		if (dimension == null) {
			if (other.dimension != null)
				return false;
		} else if (!dimension.equals(other.dimension))
			return false;
		if (value == null) {
			if (other.value != null)
				return false;
		} else if (!value.equals(other.value))
			return false;
		return true;
	}

}
