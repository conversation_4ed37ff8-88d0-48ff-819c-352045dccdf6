package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Employee implements Serializable {
    
    private static final long serialVersionUID = -710693116012443391L;
    private int id;

    private String name;

    private String gender;

    private String primaryContact;

    private String secondaryContact;

    private String biometricId;

    private Department department;

    private Designation designation;

    private Address currentAddress;

    private Address permanentAddress;

    private EmploymentType employmentType;

    private EmploymentStatus employmentStatus;

    private Date joiningDate;

    private Date dob;

    private Employee reportingManager;

    private String employeeEmail;

    private String employeeCode;

    private String communicationChannel;

    private Set<ApplicationName> applications;

    private Set<Integer> units;

    private String sdpContact;

    private boolean employeeMealEligible;

    private String reasonForTermination;

    private String hrExecutive;

    private String leaveApprovalAuthority;

    private Integer locCode;

    private String appMappingType;

}
