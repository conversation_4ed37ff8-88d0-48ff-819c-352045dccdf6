package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.sql.Time;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UnitHours implements Serializable {

    private static final long serialVersionUID = -620287446994104940L;

    private int id;

    private int unitId;

    private int dayOfTheWeekNumber;
    private String dayOfTheWeek;
    private int noOfShifts;
    private boolean isOperational;
    private boolean hasDelivery;
    private boolean hasDineIn;
    private boolean hasTakeAway;

    private Time dineInOpeningTime;

    private Time dineInClosingTime;

    private Time deliveryOpeningTime;

    private Time deliveryClosingTime;

    private Time takeAwayOpeningTime;

    private Time takeAwayClosingTime;

    private Time shiftOneHandoverTime;

    private Time shiftTwoHandoverTime;

    private String status;

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((dayOfTheWeek == null) ? 0 : dayOfTheWeek.hashCode());
        result = prime * result + dayOfTheWeekNumber;
        result = prime * result + unitId;
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        UnitHours other = (UnitHours) obj;
        if (dayOfTheWeek == null) {
            if (other.dayOfTheWeek != null)
                return false;
        } else if (!dayOfTheWeek.equals(other.dayOfTheWeek))
            return false;
        if (dayOfTheWeekNumber != other.dayOfTheWeekNumber)
            return false;
        if (unitId != other.unitId)
            return false;
        return true;
    }


}
