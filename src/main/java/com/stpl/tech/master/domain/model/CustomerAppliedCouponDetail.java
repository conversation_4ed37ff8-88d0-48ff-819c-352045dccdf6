package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CustomerAppliedCouponDetail implements Serializable {
    @Serial
    private static final long serialVersionUID = 3156411541062422067L;

    private Boolean couponReusable;

    private Boolean couponCustomerReusable;

    private Integer maxUsage;

    private Integer usageCount;

}
