package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Location implements Serializable {

    private static final long serialVersionUID =3308511746347369835L;
    protected int id;

    protected String name;

    protected String code;

    protected Country country;

    protected State state;

    private boolean business;


}
