package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.Objects;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class IdName implements Serializable {

	private static final long serialVersionUID = -9010774607517134157L;

	protected int id;

	protected String name;

	public IdName(String name) {
		this.name = name;
	}

	public IdName(int id, String name) {
		super();
		this.id = id;
		this.name = name;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof IdName))
			return false;
		IdName idName = (IdName) o;
		return id == idName.id && Objects.equals(name, idName.name);
	}

	@Override
	public int hashCode() {
		return Objects.hash(id, name);
	}
}
