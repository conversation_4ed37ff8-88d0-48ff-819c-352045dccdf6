package com.stpl.tech.master.domain.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;


@Getter
@Setter
@NoArgsConstructor
public class Taxation implements Serializable {
  
	private static final long serialVersionUID = -5062318536944506797L;
	private String category;
    private String code;
    private String name;
    private BigDecimal percentage;
    private String type;

}
