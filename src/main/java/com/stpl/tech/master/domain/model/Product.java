package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


public class Product implements Serializable, Comparable<Product> {

    
    private static final long serialVersionUID = 6254972379595664500L;

    private int id;

    private String name;
    private String productAliasName;

    private String description;
    private boolean hasSizeProfile;
    private boolean hasAddons;
    private int type;
    private int subType;
    private Integer webType;

    private String attribute;

    private String skuCode;

    private BillType billType;

    private ProductClassification classification;

    private String shortCode;
    private boolean inventoryTracked;
    private boolean employeeMealComponent;
    private boolean taxableCogs;

    private String addOnProfile;
    private int dimensionProfileId;

    private ProductStatus status;

    private Date startDate;

    private Date endDate;
    private boolean supportsVariantLevelOrdering;

    private List<ProductPrice> prices;

    private String taxCode;
    private boolean customize;
    private String preparation;
    private BigDecimal prepTime;
    private Integer brandId;
    protected String stationCategoryName;

    /**
     * Gets the value of the id property.
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the name property.
     *
     * @return possible object is {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     *
     * @param value allowed object is {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the description property.
     *
     * @return possible object is {@link String }
     */
    public String getDescription() {
        return description;
    }

    /**
     * Sets the value of the description property.
     *
     * @param value allowed object is {@link String }
     */
    public void setDescription(String value) {
        this.description = value;
    }

    /**
     * Gets the value of the hasSizeProfile property.
     */
    public boolean isHasSizeProfile() {
        return hasSizeProfile;
    }

    /**
     * Sets the value of the hasSizeProfile property.
     */
    public void setHasSizeProfile(boolean value) {
        this.hasSizeProfile = value;
    }

    /**
     * Gets the value of the hasAddons property.
     */
    public boolean isHasAddons() {
        return hasAddons;
    }

    /**
     * Sets the value of the hasAddons property.
     */
    public void setHasAddons(boolean value) {
        this.hasAddons = value;
    }

    /**
     * Gets the value of the type property.
     */
    public int getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     */
    public void setType(int value) {
        this.type = value;
    }

    /**
     * Gets the value of the subType property.
     */
    public int getSubType() {
        return subType;
    }

    /**
     * Sets the value of the webType property.
     */
    public void setWebType(Integer value) {
        this.webType = value;
    }

    /**
     * Gets the value of the webType property.
     */
    public Integer getWebType() {
        return webType;
    }

    /**
     * Sets the value of the subType property.
     */
    public void setSubType(int value) {
        this.subType = value;
    }

    /**
     * Gets the value of the attribute property.
     *
     * @return possible object is {@link String }
     */
    public String getAttribute() {
        return attribute;
    }

    /**
     * Sets the value of the attribute property.
     *
     * @param value allowed object is {@link String }
     */
    public void setAttribute(String value) {
        this.attribute = value;
    }

    /**
     * Gets the value of the skuCode property.
     *
     * @return possible object is {@link String }
     */
    public String getSkuCode() {
        return skuCode;
    }

    /**
     * Sets the value of the skuCode property.
     *
     * @param value allowed object is {@link String }
     */
    public void setSkuCode(String value) {
        this.skuCode = value;
    }

    /**
     * Gets the value of the billType property.
     *
     * @return possible object is {@link BillType }
     */
    public BillType getBillType() {
        return billType;
    }

    /**
     * Sets the value of the billType property.
     *
     * @param value allowed object is {@link BillType }
     */
    public void setBillType(BillType value) {
        this.billType = value;
    }

    /**
     * Gets the value of the inventoryTracked property.
     */
    public boolean isInventoryTracked() {
        return inventoryTracked;
    }

    /**
     * Sets the value of the inventoryTracked property.
     */
    public void setInventoryTracked(boolean value) {
        this.inventoryTracked = value;
    }

    public boolean isEmployeeMealComponent() {
        return employeeMealComponent;
    }

    public void setEmployeeMealComponent(boolean employeeMealComponent) {
        this.employeeMealComponent = employeeMealComponent;
    }

    /**
     * Gets the value of the addOnProfile property.
     *
     * @return possible object is {@link String }
     */
    public String getAddOnProfile() {
        return addOnProfile;
    }

    /**
     * Sets the value of the addOnProfile property.
     *
     * @param value allowed object is {@link String }
     */
    public void setAddOnProfile(String value) {
        this.addOnProfile = value;
    }

    /**
     * Gets the value of the dimensionProfileId property.
     */
    public int getDimensionProfileId() {
        return dimensionProfileId;
    }

    /**
     * Sets the value of the dimensionProfileId property.
     */
    public void setDimensionProfileId(int value) {
        this.dimensionProfileId = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return possible object is {@link ProductStatus }
     */
    public ProductStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value allowed object is {@link ProductStatus }
     */
    public void setStatus(ProductStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the startDate property.
     *
     * @return possible object is {@link String }
     */
    public Date getStartDate() {
        return startDate;
    }

    /**
     * Sets the value of the startDate property.
     *
     * @param value allowed object is {@link String }
     */
    public void setStartDate(Date value) {
        this.startDate = value;
    }

    /**
     * Gets the value of the endDate property.
     *
     * @return possible object is {@link String }
     */
    public Date getEndDate() {
        return endDate;
    }

    /**
     * Sets the value of the endDate property.
     *
     * @param value allowed object is {@link String }
     */
    public void setEndDate(Date value) {
        this.endDate = value;
    }

    /**
     * Gets the value of the supportsVariantLevelOrdering property.
     */
    public boolean isSupportsVariantLevelOrdering() {
        return supportsVariantLevelOrdering;
    }

    /**
     * Sets the value of the supportsVariantLevelOrdering property.
     */
    public void setSupportsVariantLevelOrdering(boolean value) {
        this.supportsVariantLevelOrdering = value;
    }

    /**
     * Gets the value of the prices property.
     *
     * <p>
     * This accessor method returns a reference to the live list, not a
     * snapshot. Therefore any modification you make to the returned list will
     * be present inside the JAXB object. This is why there is not a
     * <CODE>set</CODE> method for the prices property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     *
     * <pre>
     * getPrices().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ProductPrice }
     */
    public List<ProductPrice> getPrices() {
        if (Objects.isNull(prices)) {
            prices = new ArrayList<ProductPrice>();
        }
        return this.prices;
    }

    @Override
    public int compareTo(Product o) {
        return Integer.compare(this.getId(), o.getId());
    }

    public ProductClassification getClassification() {
        return classification;
    }

    public void setClassification(ProductClassification classification) {
        this.classification = classification;
    }

    public String getShortCode() {
        return shortCode;
    }

    public void setShortCode(String shortCode) {
        this.shortCode = shortCode;
    }

    public boolean isCustomize() {
        return customize;
    }

    public void setCustomize(boolean customize) {
        this.customize = customize;
    }

    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public String getPreparation() {
        return preparation;
    }

    public void setPreparation(String preparation) {
        this.preparation = preparation;
    }

    public boolean isTaxableCogs() {
        return taxableCogs;
    }

    public void setTaxableCogs(boolean taxableCogs) {
        this.taxableCogs = taxableCogs;
    }

    public BigDecimal getPrepTime() {
        return prepTime;
    }

    public void setPrepTime(BigDecimal prepTime) {
        this.prepTime = prepTime;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getProductAliasName() {
        return productAliasName;
    }

    public void setProductAliasName(String productAliasName) {
        this.productAliasName = productAliasName;
    }

    public String getStationCategoryName() {
        return stationCategoryName;
    }

    public void setStationCategoryName(String stationCategoryName) {
        this.stationCategoryName = stationCategoryName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Product product = (Product) o;
        return id == product.id &&
                hasSizeProfile == product.hasSizeProfile &&
                hasAddons == product.hasAddons &&
                type == product.type &&
                subType == product.subType &&
                inventoryTracked == product.inventoryTracked &&
                employeeMealComponent == product.employeeMealComponent &&
                dimensionProfileId == product.dimensionProfileId &&
                supportsVariantLevelOrdering == product.supportsVariantLevelOrdering &&
                customize == product.customize &&
                Objects.equals(name, product.name) &&
                Objects.equals(description, product.description) &&
                Objects.equals(webType, product.webType) &&
                Objects.equals(attribute, product.attribute) &&
                Objects.equals(skuCode, product.skuCode) &&
                billType == product.billType &&
                classification == product.classification &&
                Objects.equals(shortCode, product.shortCode) &&
                Objects.equals(addOnProfile, product.addOnProfile) &&
                status == product.status &&
                Objects.equals(startDate, product.startDate) &&
                Objects.equals(endDate, product.endDate) &&
                Objects.equals(prices, product.prices) &&
                Objects.equals(taxCode, product.taxCode) &&
                Objects.equals(preparation, product.preparation);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, description, hasSizeProfile, hasAddons, type, subType, webType, attribute, skuCode, billType, classification, shortCode, inventoryTracked, employeeMealComponent, addOnProfile, dimensionProfileId, status, startDate, endDate, supportsVariantLevelOrdering, prices, taxCode, customize, preparation);
    }
}

