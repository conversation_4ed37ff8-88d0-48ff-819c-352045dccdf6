package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class CouponData implements Serializable {

	private static final long serialVersionUID = -127799093465028061L;

	private String coupon;
	private String startDate;
	private String endDate;
	private Integer usageCount;
	private Integer couponDetailId;
	private Integer offerDetailId;

}
