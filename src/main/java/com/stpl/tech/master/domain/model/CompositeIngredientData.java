package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import org.springframework.data.annotation.Id;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.util.adapter.BigDecimalSixPrecisionDeserializer;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CompositeIngredientData implements Serializable {

    
    private static final long serialVersionUID = -3473314520039812409L;
    @Id
    private String _id;

    private String name;

    private String status;
    @JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
    private BigDecimal discount;

    private BigDecimal internalDiscount;

    private String internalDiscountType;

    protected List<IngredientProductDetail> menuProducts;

}
