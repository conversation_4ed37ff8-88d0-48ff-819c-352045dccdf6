package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class InventoryThresholdData {

    protected int id;

    protected int unitId;

    protected int productId;
    protected int dayOfTheWeek;
    protected int minQuantity;
    protected int maxQuantity;
    protected int avgQuantity;
    protected int totalQuantity;
    protected int totalDays;
    protected String status;
    protected Date lastUpdateTime;

}
