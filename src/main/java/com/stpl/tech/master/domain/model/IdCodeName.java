package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class IdCodeName implements Serializable, Comparable<IdCodeName> {


    
    private static final long serialVersionUID = 9196734885916273962L;

    protected int id;

    protected String name;

    protected String code;

    protected String shortCode;

    protected String type;

    protected String status;

    protected String zone;

    public IdCodeName(int id, String name, String code) {
        super();
        this.id = id;
        this.name = name;
        this.code = code;
    }

    public IdCodeName( String name, String code) {
        super();
        this.name = name;
        this.code = code;
    }

    public IdCodeName(int id, String code) {
        this.id = id;
        this.code = code;
    }

    public IdCodeName(int id, String name, String code, String shortCode, String type, String status) {
        super();
        this.id = id;
        this.name = name;
        this.code = code;
        this.shortCode = shortCode;
        this.type = type;
        this.status = status;
    }

    @Override
    public int compareTo(IdCodeName o) {
        return Integer.compare(this.getId(), o.getId());
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((code == null) ? 0 : code.hashCode());
        result = prime * result + id;
        result = prime * result + ((name == null) ? 0 : name.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        IdCodeName other = (IdCodeName) obj;
        if (code == null) {
            if (other.code != null)
                return false;
        } else if (!code.equals(other.code))
            return false;
        if (id != other.id)
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        } else if (!name.equals(other.name))
            return false;
        return true;
    }

    @Override
    public String toString() {
        return "IdCodeName [id=" + id + ", name=" + name + ", code" + code +"]";
    }

}

