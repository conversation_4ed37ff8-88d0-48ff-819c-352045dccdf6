package com.stpl.tech.master.domain.model;



import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class TaxData implements Serializable{


    
    private static final long serialVersionUID = 8419169198886167579L;

    protected String taxCode;

    protected StateTax state;

    protected List<AdditionalTax> others;


    public StateTax getState() {
        return state;
    }

    public void setState(StateTax state) {
        this.state = state;
    }

    public List<AdditionalTax> getOthers() {
        if (Objects.isNull(others)) {
            others = new ArrayList<>();
        }
        return others;
    }

    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

}
