package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import com.stpl.tech.kettle.domain.model.ScreenType;
import com.stpl.tech.kettle.domain.model.SignInProvider;
import com.stpl.tech.kettle.report.metadata.model.TrueCallerSettings;
import com.stpl.tech.kettle.util.AppUtils;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UserSessionDetail implements Serializable {

	private static final long serialVersionUID = 2582987391485884017L;

	private int userId;
	private String sessionKeyId;
	private int unitId;
	private Integer terminalId;
	private String password;
	private String newPassword;
	private String token;
	private Employee user;
	private String ipAddress;
	private String macAddress;
	private String userAgent;
	private ScreenType screenType;
	private Boolean isDefaultPasscode;
	private String jwtToken;
	private String application;
	private String version;
	private String osVersion;
	private String deviceModel;
	private Map<String, Map<String, Integer>> permissions;
	private Map<String, Map<String, Boolean>> acl;
	private TrueCallerSettings trueCaller;
	private String businessDate;
	private String monkRecipeVersion;
	private Date serverTime;
	private String userEmail;
	private SignInProvider signInProvider;

	public UserSessionDetail(int userId, int unitId, int terminalId) {
		super();
		this.userId = userId;
		this.unitId = unitId;
		this.terminalId = terminalId;
	}

	public UserSessionDetail(int userId, int unitId, int terminalId, String password) {
		super();
		this.userId = userId;
		this.unitId = unitId;
		this.terminalId = terminalId;
		this.password = password;
		this.businessDate = AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate());
	}

	public UserSessionDetail(int userId, String sessinKeyId, int unitId, int terminalId, String password,
			String newPassword) {
		super();
		this.userId = userId;
		this.sessionKeyId = sessinKeyId;
		this.unitId = unitId;
		this.terminalId = terminalId;
		this.password = password;
		this.newPassword = newPassword;
		this.businessDate = AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate());
	}

	public UserSessionDetail(int userId, String sessinKeyId, int unitId, int terminalId, String password,
			String newPassword, String ipAddress, String macAddress, String userAgent) {
		super();
		this.userId = userId;
		this.sessionKeyId = sessinKeyId;
		this.unitId = unitId;
		this.terminalId = terminalId;
		this.password = password;
		this.newPassword = newPassword;
		this.ipAddress = ipAddress;
		this.macAddress = macAddress;
		this.userAgent = userAgent;
		this.businessDate = AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate());
	}

}
