package com.stpl.tech.master.domain.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class MappingIdCodeName extends IdCodeName {

	private static final long serialVersionUID = -1111543507095687616L;

	protected Integer mappingId;

	public MappingIdCodeName(int mappingId, int id, String name, String code) {
		super(id, name, code);
		this.mappingId = mappingId;
	}

	public MappingIdCodeName(int mappingId, int id, String name, String code, String shortCode, String type,
			String status) {
		super(id, name, code, shortCode, type, status);
		this.mappingId = mappingId;
	}

	@Override
	public String toString() {
		return "MappingIdCodeName [mappingId=" + mappingId + ", id=" + id + ", name=" + name + ", code=" + code
				+ ", status=" + status + "]";
	}

}
